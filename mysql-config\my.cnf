[mysqld]
# 基础字符集和引擎设置
character-set-server=utf8mb4
collation-server=utf8mb4_bin
default-storage-engine=INNODB

# 网络和包大小设置
max_allowed_packet=512M
max_connections=100

# 事务和日志设置
transaction-isolation=READ-COMMITTED
binlog_format=row

# SQL模式设置 - 关键：移除NO_AUTO_VALUE_ON_ZERO以符合JIRA要求
sql_mode=STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# 时间戳设置
explicit_defaults_for_timestamp=1

# InnoDB设置 - JIRA必需配置
innodb_default_row_format=DYNAMIC
innodb_large_prefix=ON
innodb_file_format=Barracuda
innodb_log_file_size=2GB
innodb_buffer_pool_size=512M
innodb_file_per_table=1
innodb_flush_log_at_trx_commit=1
innodb_flush_method=O_DIRECT

# 安全设置
skip-ssl

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4

