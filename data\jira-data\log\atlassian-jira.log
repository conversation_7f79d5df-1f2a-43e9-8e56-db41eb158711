2025-09-10 14:00:37,647+0800 localhost-startStop-1 INFO      [c.a.jira.startup.JiraHomeStartupCheck] The jira.home directory '/var/atlassian/application-data/jira' is validated and locked for exclusive use by this instance.
2025-09-10 14:00:37,701+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ****************
    Jira starting...
    ****************
    
2025-09-10 14:00:37,781+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Environment _____________________________
    
         JIRA Build                                    : 8.8.0#808000-sha1:e2c7e59ae165efc6ad6b529150e24d091b9947bf
         Build Date                                    : Thu Mar 19 00:00:00 CST 2020
         JIRA Installation Type                        : Standalone
         Application Server                            : Apache Tomcat/8.5.42 - Servlet API 3.1
         Java Version                                  : 11.0.15 - Eclipse Adoptium
         Current Working Directory                     : /var/atlassian/application-data/jira
         Maximum Allowable Memory                      : 2048MB
         Total Memory                                  : 1024MB
         Free Memory                                   : 790MB
         Used Memory                                   : 234MB
         Memory Pool: CodeHeap 'non-nmethods'          : CodeHeap 'non-nmethods': init = 7598080(7420K) used = 2983552(2913K) committed = 7598080(7420K) max = 7598080(7420K)
         Memory Pool: Metaspace                        : Metaspace: init = 0(0K) used = 27635832(26988K) committed = 28573696(27904K) max = -1(-1K)
         Memory Pool: CodeHeap 'profiled nmethods'     : CodeHeap 'profiled nmethods': init = 33554432(32768K) used = 9231488(9015K) committed = 33554432(32768K) max = 264634368(258432K)
         Memory Pool: Compressed Class Space           : Compressed Class Space: init = 0(0K) used = 2831808(2765K) committed = 3145728(3072K) max = 1073741824(1048576K)
         Memory Pool: G1 Eden Space                    : G1 Eden Space: init = 56623104(55296K) used = 222298112(217088K) committed = 336592896(328704K) max = -1(-1K)
         Memory Pool: G1 Old Gen                       : G1 Old Gen: init = 1017118720(993280K) used = 16067296(15690K) committed = 728760320(711680K) max = 2147483648(2097152K)
         Memory Pool: G1 Survivor Space                : G1 Survivor Space: init = 0(0K) used = 8388608(8192K) committed = 8388608(8192K) max = -1(-1K)
         Memory Pool: CodeHeap 'non-profiled nmethods' : CodeHeap 'non-profiled nmethods': init = 33554432(32768K) used = 2911488(2843K) committed = 33554432(32768K) max = 264638464(258436K)
         JVM Input Arguments                           : --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Xms1024m -Xmx2048m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=512m -Djava.awt.headless=true -Datlassian.standalone=JIRA -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true -Dmail.mime.decodeparameters=true -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory -XX:-OmitStackTraceInFastThrow -Djava.locale.providers=COMPAT -Djira.home=/var/atlassian/application-data/jira -Datlassian.plugins.startup.options=-fg -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:time,uptime:filecount=5,filesize=20M -XX:+ExplicitGCInvokesConcurrent -Dignore.endorsed.dirs= -Dcatalina.base=/opt/atlassian/jira -Dcatalina.home=/opt/atlassian/jira -Djava.io.tmpdir=/opt/atlassian/jira/temp
         Java Compatibility Information                : JIRA version = 8.8.0, Java Version = 11.0.15
    
    ___ Java System Properties _________________
    
         atlassian.plugins.startup.options             : -fg
         atlassian.standalone                          : JIRA
         awt.toolkit                                   : sun.awt.X11.XToolkit
         catalina.base                                 : /opt/atlassian/jira
         catalina.home                                 : /opt/atlassian/jira
         catalina.useNaming                            : true
         common.loader                                 : "${catalina.base}/lib",
                                                         "${catalina.base}/lib/*.jar",
                                                         "${catalina.home}/lib",
                                                         "${catalina.home}/lib/*.jar"
         file.encoding                                 : UTF-8
         ignore.endorsed.dirs                          : 
         java.awt.graphicsenv                          : sun.awt.X11GraphicsEnvironment
         java.awt.headless                             : true
         java.awt.printerjob                           : sun.print.PSPrinterJob
         java.class.version                            : 55.0
         java.home                                     : /opt/java/openjdk
         java.io.tmpdir                                : /opt/atlassian/jira/temp
         java.locale.providers                         : COMPAT
         java.naming.factory.initial                   : org.apache.naming.java.javaURLContextFactory
         java.naming.factory.url.pkgs                  : org.apache.naming
         java.protocol.handler.pkgs                    : org.apache.catalina.webresources
         java.runtime.name                             : OpenJDK Runtime Environment
         java.runtime.version                          : 11.0.15+10
         java.specification.name                       : Java Platform API Specification
         java.specification.vendor                     : Oracle Corporation
         java.specification.version                    : 11
         java.util.logging.config.file                 : /opt/atlassian/jira/conf/logging.properties
         java.util.logging.manager                     : org.apache.juli.ClassLoaderLogManager
         java.vendor                                   : Eclipse Adoptium
         java.vendor.url                               : https://adoptium.net/
         java.vendor.url.bug                           : https://github.com/adoptium/adoptium-support/issues
         java.vendor.version                           : Temurin-11.0.15+10
         java.version                                  : 11.0.15
         java.version.date                             : 2022-04-19
         java.vm.compressedOopsMode                    : 32-bit
         java.vm.info                                  : mixed mode
         java.vm.name                                  : OpenJDK 64-Bit Server VM
         java.vm.specification.name                    : Java Virtual Machine Specification
         java.vm.specification.vendor                  : Oracle Corporation
         java.vm.specification.version                 : 11
         java.vm.vendor                                : Eclipse Adoptium
         java.vm.version                               : 11.0.15+10
         jdk.debug                                     : release
         jdk.tls.ephemeralDHKeySize                    : 2048
         jira.home                                     : /var/atlassian/application-data/jira
         mail.mime.decodeparameters                    : true
         org.apache.catalina.security.SecurityListener.UMASK : 0027
         org.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER : true
         org.dom4j.factory                             : com.atlassian.core.xml.InterningDocumentFactory
         os.arch                                       : amd64
         os.name                                       : Linux
         os.version                                    : ********-microsoft-standard-WSL2
         package.access                                : sun.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.tomcat.
         package.definition                            : sun.,
                                                         java.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.naming.,
                                                         org.apache.tomcat.
         server.loader                                 : 
         shared.loader                                 : 
         sun.arch.data.model                           : 64
         sun.boot.library.path                         : /opt/java/openjdk/lib
         sun.cpu.endian                                : little
         sun.cpu.isalist                               : 
         sun.io.unicode.encoding                       : UnicodeLittle
         sun.java.command                              : org.apache.catalina.startup.Bootstrap start
         sun.java.launcher                             : SUN_STANDARD
         sun.jnu.encoding                              : UTF-8
         sun.management.compiler                       : HotSpot 64-Bit Tiered Compilers
         sun.os.patch.level                            : unknown
         tomcat.util.buf.StringCache.byte.enabled      : true
         tomcat.util.scan.StandardJarScanFilter.jarsToScan : log4j-taglib*.jar,
                                                         log4j-web*.jar,
                                                         log4javascript*.jar,
                                                         slf4j-taglib*.jar
         tomcat.util.scan.StandardJarScanFilter.jarsToSkip : annotations-api.jar,
                                                         ant-junit*.jar,
                                                         ant-launcher.jar,
                                                         ant.jar,
                                                         asm-*.jar,
                                                         aspectj*.jar,
                                                         bootstrap.jar,
                                                         catalina-ant.jar,
                                                         catalina-ha.jar,
                                                         catalina-jmx-remote.jar,
                                                         catalina-storeconfig.jar,
                                                         catalina-tribes.jar,
                                                         catalina-ws.jar,
                                                         catalina.jar,
                                                         cglib-*.jar,
                                                         cobertura-*.jar,
                                                         commons-beanutils*.jar,
                                                         commons-codec*.jar,
                                                         commons-collections*.jar,
                                                         commons-daemon.jar,
                                                         commons-dbcp*.jar,
                                                         commons-digester*.jar,
                                                         commons-fileupload*.jar,
                                                         commons-httpclient*.jar,
                                                         commons-io*.jar,
                                                         commons-lang*.jar,
                                                         commons-logging*.jar,
                                                         commons-math*.jar,
                                                         commons-pool*.jar,
                                                         dom4j-*.jar,
                                                         easymock-*.jar,
                                                         ecj-*.jar,
                                                         el-api.jar,
                                                         geronimo-spec-jaxrpc*.jar,
                                                         h2*.jar,
                                                         hamcrest-*.jar,
                                                         hibernate*.jar,
                                                         httpclient*.jar,
                                                         icu4j-*.jar,
                                                         jasper-el.jar,
                                                         jasper.jar,
                                                         jaspic-api.jar,
                                                         jaxb-*.jar,
                                                         jaxen-*.jar,
                                                         jdom-*.jar,
                                                         jetty-*.jar,
                                                         jmx-tools.jar,
                                                         jmx.jar,
                                                         jsp-api.jar,
                                                         jstl.jar,
                                                         jta*.jar,
                                                         junit-*.jar,
                                                         junit.jar,
                                                         log4j*.jar,
                                                         mail*.jar,
                                                         objenesis-*.jar,
                                                         oraclepki.jar,
                                                         oro-*.jar,
                                                         servlet-api-*.jar,
                                                         servlet-api.jar,
                                                         slf4j*.jar,
                                                         taglibs-standard-spec-*.jar,
                                                         tagsoup-*.jar,
                                                         tomcat-api.jar,
                                                         tomcat-coyote.jar,
                                                         tomcat-dbcp.jar,
                                                         tomcat-i18n-*.jar,
                                                         tomcat-jdbc.jar,
                                                         tomcat-jni.jar,
                                                         tomcat-juli-adapters.jar,
                                                         tomcat-juli.jar,
                                                         tomcat-util-scan.jar,
                                                         tomcat-util.jar,
                                                         tomcat-websocket.jar,
                                                         tools.jar,
                                                         websocket-api.jar,
                                                         wsdl4j*.jar,
                                                         xercesImpl.jar,
                                                         xml-apis.jar,
                                                         xmlParserAPIs-*.jar,
                                                         xmlParserAPIs.jar,
                                                         xom-*.jar
         user.country                                  : US
         user.dir                                      : /var/atlassian/application-data/jira
         user.home                                     : /var/atlassian/application-data/jira
         user.language                                 : en
         user.name                                     : jira
         user.timezone                                 : Asia/Shanghai
    
2025-09-10 14:00:37,932+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:00:38,008+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:00:38,014+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:00:38,336+0800 JIRA-Bootstrap INFO      [c.a.j.c.cache.pauser.NonClusteredReplicationPauserManager] Non-clustered mode: ReplicationPauserManager implemented by NonClusteredReplicationPauserManager 
2025-09-10 14:00:38,339+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.JiraCacheResetter] [jira-cache-reseter] Created and registered for events
2025-09-10 14:00:38,341+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] Created, registered for events and schedulled stats job
2025-09-10 14:00:38,351+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Created
2025-09-10 14:00:38,662+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:00:38,675+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Starting the JIRA Plugin System _________________
    
2025-09-10 14:00:38,677+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup begun
2025-09-10 14:00:43,550+0800 FelixStartLevel WARN      [o.e.g.b.e.internal.support.ExtenderConfiguration] Gemini Blueprint extensions bundle not present, annotation processing disabled.
2025-09-10 14:00:44,821+0800 JIRA-Bootstrap INFO      [c.a.plugin.loaders.ScanningPluginLoader] No plugins found to be deployed
2025-09-10 14:00:46,347+0800 ThreadPoolAsyncTaskExecutor::Thread 6 WARN      [c.a.p.s.scanner.util.ProductFilterUtil] Couldn't detect product, will use ProductFilter.ALL
2025-09-10 14:00:46,662+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 300 seconds remaining
2025-09-10 14:00:47,663+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 298 seconds remaining
2025-09-10 14:00:48,664+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 297 seconds remaining
2025-09-10 14:00:49,873+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup ended
2025-09-10 14:00:49,877+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Starting
2025-09-10 14:00:49,929+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:00:49,948+0800 JIRA-Bootstrap INFO      [c.a.j.instrumentation.external.DatabaseExternalGauges] Installing DBCP monitoring instruments: DatabaseExternalGauges.JiraDbcpInstruments[instruments=[DBCP_MAX, DBCP_ACTIVE, DBCP_IDLE],objectName=com.atlassian.jira:name=BasicDataSource]
2025-09-10 14:00:49,970+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Running Jira startup checks.
2025-09-10 14:00:49,971+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Jira pre-database startup checks completed successfully.
2025-09-10 14:00:50,014+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Database Checklist Launcher on post-database-configured-but-pre-database-activated queue
2025-09-10 14:00:50,015+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Post database-configuration launchers on post-database-activated queue
2025-09-10 14:00:50,020+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.LauncherContextListener] Memory Usage:
    ---------------------------------------------------------------------------------
      Heap memory     :  Used:  255 MiB.  Committed: 1024 MiB.  Max: 2048 MiB
      Non-heap memory :  Used:   93 MiB.  Committed:  136 MiB.  Max: 1536 MiB
    ---------------------------------------------------------------------------------
      TOTAL           :  Used:  348 MiB.  Committed: 1160 MiB.  Max: 3584 MiB
    ---------------------------------------------------------------------------------
2025-09-10 14:02:33,422+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:33,424+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:33,426+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:33,426+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:33,428+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:33,430+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:33,431+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:33,432+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:33,433+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:33,435+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:33,435+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:33,435+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:33,436+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:02:33,447+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:33,449+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:33,449+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:33,450+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:33,451+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:33,451+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:33,451+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:33,452+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:33,452+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:33,452+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:33,452+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:33,452+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:33,453+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:02:33,467+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:33,467+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:33,468+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:33,469+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:33,469+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:33,469+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:33,470+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:33,470+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:33,470+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:33,470+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:33,470+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:33,471+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:33,471+0800 http-nio-8080-exec-2 WARN anonymous 842x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:02:37,897+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:37,898+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:37,898+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:37,898+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:37,899+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:37,904+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:37,904+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:37,905+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:37,905+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:37,905+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:37,905+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:37,906+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:37,906+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:02:37,907+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:37,907+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:37,908+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:37,908+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:37,908+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:37,908+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:37,909+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:37,909+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:37,909+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:37,910+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:37,910+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:37,910+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:37,910+0800 http-nio-8080-exec-7 WARN anonymous 842x7x1 jvlk5a ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:02:40,020+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:40,021+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:40,021+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:40,022+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:40,022+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:40,022+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:40,023+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:40,023+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:40,023+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:40,023+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:40,023+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:40,024+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:40,024+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:02:40,024+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:02:40,025+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:02:40,025+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:02:40,025+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:02:40,025+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 14:02:40,026+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 14:02:40,026+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:02:40,026+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 14:02:40,026+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:02:40,027+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:02:40,027+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:02:40,027+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:02:40,028+0800 http-nio-8080-exec-10 WARN anonymous 842x10x1 jvlk5a ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:05:38,325+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 14:10:38,308+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 14:15:38,293+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 14:17:19,447+0800 localhost-startStop-2 INFO      [c.a.jira.startup.DefaultJiraLauncher] Stopping launchers
2025-09-10 14:17:19,455+0800 localhost-startStop-2 ERROR      [o.a.c.c.C.[Catalina].[localhost].[/]] Exception sending context destroyed event to listener instance of class [com.atlassian.jira.startup.LauncherContextListener]
java.lang.NullPointerException
	at com.atlassian.jira.startup.ClusteringLauncher.stop(ClusteringLauncher.java:49)
	at com.atlassian.jira.startup.DefaultJiraLauncher.stop(DefaultJiraLauncher.java:200)
	at com.atlassian.jira.startup.LauncherContextListener.contextDestroyed(LauncherContextListener.java:211)
	at org.apache.catalina.core.StandardContext.listenerStop(StandardContext.java:4817)
	at org.apache.catalina.core.StandardContext.stopInternal(StandardContext.java:5474)
	... 3 filtered
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-09-10 14:17:31,347+0800 localhost-startStop-1 INFO      [c.a.jira.startup.JiraHomeStartupCheck] The jira.home directory '/var/atlassian/application-data/jira' is validated and locked for exclusive use by this instance.
2025-09-10 14:17:31,408+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ****************
    Jira starting...
    ****************
    
2025-09-10 14:17:31,484+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Environment _____________________________
    
         JIRA Build                                    : 8.13.7#813007-sha1:3e6833b6aa4b98966fa10c8ab5e2a573d1da0bde
         Build Date                                    : Tue May 18 00:00:00 CST 2021
         JIRA Installation Type                        : Standalone
         Application Server                            : Apache Tomcat/8.5.60 - Servlet API 3.1
         Java Version                                  : 11.0.22 - Eclipse Adoptium
         Current Working Directory                     : /var/atlassian/application-data/jira
         Maximum Allowable Memory                      : 2048MB
         Total Memory                                  : 1026MB
         Free Memory                                   : 731MB
         Used Memory                                   : 295MB
         Memory Pool: CodeHeap 'non-nmethods'          : CodeHeap 'non-nmethods': init = 7598080(7420K) used = 3002368(2932K) committed = 7598080(7420K) max = 7598080(7420K)
         Memory Pool: Metaspace                        : Metaspace: init = 0(0K) used = 19944680(19477K) committed = 20840448(20352K) max = -1(-1K)
         Memory Pool: CodeHeap 'profiled nmethods'     : CodeHeap 'profiled nmethods': init = 33554432(32768K) used = 9800448(9570K) committed = 33554432(32768K) max = 264634368(258432K)
         Memory Pool: Compressed Class Space           : Compressed Class Space: init = 0(0K) used = 2166592(2115K) committed = 2490368(2432K) max = 1073741824(1048576K)
         Memory Pool: G1 Eden Space                    : G1 Eden Space: init = 56623104(55296K) used = 283115520(276480K) committed = 667942912(652288K) max = -1(-1K)
         Memory Pool: G1 Old Gen                       : G1 Old Gen: init = 1017118720(993280K) used = 19093464(18645K) committed = 399507456(390144K) max = 2147483648(2097152K)
         Memory Pool: G1 Survivor Space                : G1 Survivor Space: init = 0(0K) used = 8388608(8192K) committed = 8388608(8192K) max = -1(-1K)
         Memory Pool: CodeHeap 'non-profiled nmethods' : CodeHeap 'non-profiled nmethods': init = 33554432(32768K) used = 3349888(3271K) committed = 33554432(32768K) max = 264638464(258436K)
         JVM Input Arguments                           : --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Xms1024m -Xmx2048m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=512m -Djava.awt.headless=true -Datlassian.standalone=JIRA -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true -Dmail.mime.decodeparameters=true -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory -XX:-OmitStackTraceInFastThrow -Djava.locale.providers=COMPAT -Djira.home=/var/atlassian/application-data/jira -Datlassian.plugins.startup.options=-fg -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:tags,time,uptime,level:filecount=5,filesize=20M -XX:+ExplicitGCInvokesConcurrent -Dignore.endorsed.dirs= -Dcatalina.base=/opt/atlassian/jira -Dcatalina.home=/opt/atlassian/jira -Djava.io.tmpdir=/opt/atlassian/jira/temp
         Java Compatibility Information                : JIRA version = 8.13.7, Java Version = 11.0.22
    
    ___ Java System Properties _________________
    
         atlassian.plugins.startup.options             : -fg
         atlassian.standalone                          : JIRA
         awt.toolkit                                   : sun.awt.X11.XToolkit
         catalina.base                                 : /opt/atlassian/jira
         catalina.home                                 : /opt/atlassian/jira
         catalina.useNaming                            : true
         common.loader                                 : "${catalina.base}/lib",
                                                         "${catalina.base}/lib/*.jar",
                                                         "${catalina.home}/lib",
                                                         "${catalina.home}/lib/*.jar"
         file.encoding                                 : UTF-8
         ignore.endorsed.dirs                          : 
         java.awt.graphicsenv                          : sun.awt.X11GraphicsEnvironment
         java.awt.headless                             : true
         java.awt.printerjob                           : sun.print.PSPrinterJob
         java.class.version                            : 55.0
         java.home                                     : /opt/java/openjdk
         java.io.tmpdir                                : /opt/atlassian/jira/temp
         java.locale.providers                         : COMPAT
         java.naming.factory.initial                   : org.apache.naming.java.javaURLContextFactory
         java.naming.factory.url.pkgs                  : org.apache.naming
         java.protocol.handler.pkgs                    : org.apache.catalina.webresources
         java.runtime.name                             : OpenJDK Runtime Environment
         java.runtime.version                          : 11.0.22+7
         java.specification.maintenance.version        : 2
         java.specification.name                       : Java Platform API Specification
         java.specification.vendor                     : Oracle Corporation
         java.specification.version                    : 11
         java.util.logging.config.file                 : /opt/atlassian/jira/conf/logging.properties
         java.util.logging.manager                     : org.apache.juli.ClassLoaderLogManager
         java.vendor                                   : Eclipse Adoptium
         java.vendor.url                               : https://adoptium.net/
         java.vendor.url.bug                           : https://github.com/adoptium/adoptium-support/issues
         java.vendor.version                           : Temurin-11.0.22+7
         java.version                                  : 11.0.22
         java.version.date                             : 2024-01-16
         java.vm.compressedOopsMode                    : 32-bit
         java.vm.info                                  : mixed mode, sharing
         java.vm.name                                  : OpenJDK 64-Bit Server VM
         java.vm.specification.name                    : Java Virtual Machine Specification
         java.vm.specification.vendor                  : Oracle Corporation
         java.vm.specification.version                 : 11
         java.vm.vendor                                : Eclipse Adoptium
         java.vm.version                               : 11.0.22+7
         jdk.debug                                     : release
         jdk.tls.ephemeralDHKeySize                    : 2048
         jira.home                                     : /var/atlassian/application-data/jira
         mail.mime.decodeparameters                    : true
         org.apache.catalina.security.SecurityListener.UMASK : 0027
         org.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER : true
         org.dom4j.factory                             : com.atlassian.core.xml.InterningDocumentFactory
         os.arch                                       : amd64
         os.name                                       : Linux
         os.version                                    : ********-microsoft-standard-WSL2
         package.access                                : sun.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.tomcat.
         package.definition                            : sun.,
                                                         java.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.naming.,
                                                         org.apache.tomcat.
         server.loader                                 : 
         shared.loader                                 : 
         sun.arch.data.model                           : 64
         sun.boot.library.path                         : /opt/java/openjdk/lib
         sun.cpu.endian                                : little
         sun.cpu.isalist                               : 
         sun.io.unicode.encoding                       : UnicodeLittle
         sun.java.command                              : org.apache.catalina.startup.Bootstrap start
         sun.java.launcher                             : SUN_STANDARD
         sun.jnu.encoding                              : UTF-8
         sun.management.compiler                       : HotSpot 64-Bit Tiered Compilers
         sun.os.patch.level                            : unknown
         tomcat.util.buf.StringCache.byte.enabled      : true
         tomcat.util.scan.StandardJarScanFilter.jarsToScan : log4j-taglib*.jar,
                                                         log4j-web*.jar,
                                                         log4javascript*.jar,
                                                         slf4j-taglib*.jar
         tomcat.util.scan.StandardJarScanFilter.jarsToSkip : annotations-api.jar,
                                                         ant-junit*.jar,
                                                         ant-launcher.jar,
                                                         ant.jar,
                                                         asm-*.jar,
                                                         aspectj*.jar,
                                                         bootstrap.jar,
                                                         catalina-ant.jar,
                                                         catalina-ha.jar,
                                                         catalina-jmx-remote.jar,
                                                         catalina-storeconfig.jar,
                                                         catalina-tribes.jar,
                                                         catalina-ws.jar,
                                                         catalina.jar,
                                                         cglib-*.jar,
                                                         cobertura-*.jar,
                                                         commons-beanutils*.jar,
                                                         commons-codec*.jar,
                                                         commons-collections*.jar,
                                                         commons-daemon.jar,
                                                         commons-dbcp*.jar,
                                                         commons-digester*.jar,
                                                         commons-fileupload*.jar,
                                                         commons-httpclient*.jar,
                                                         commons-io*.jar,
                                                         commons-lang*.jar,
                                                         commons-logging*.jar,
                                                         commons-math*.jar,
                                                         commons-pool*.jar,
                                                         dom4j-*.jar,
                                                         easymock-*.jar,
                                                         ecj-*.jar,
                                                         el-api.jar,
                                                         geronimo-spec-jaxrpc*.jar,
                                                         h2*.jar,
                                                         hamcrest-*.jar,
                                                         hibernate*.jar,
                                                         httpclient*.jar,
                                                         icu4j-*.jar,
                                                         jasper-el.jar,
                                                         jasper.jar,
                                                         jaspic-api.jar,
                                                         jaxb-*.jar,
                                                         jaxen-*.jar,
                                                         jdom-*.jar,
                                                         jetty-*.jar,
                                                         jmx-tools.jar,
                                                         jmx.jar,
                                                         jsp-api.jar,
                                                         jstl.jar,
                                                         jta*.jar,
                                                         junit-*.jar,
                                                         junit.jar,
                                                         log4j*.jar,
                                                         mail*.jar,
                                                         objenesis-*.jar,
                                                         oraclepki.jar,
                                                         oro-*.jar,
                                                         servlet-api-*.jar,
                                                         servlet-api.jar,
                                                         slf4j*.jar,
                                                         taglibs-standard-spec-*.jar,
                                                         tagsoup-*.jar,
                                                         tomcat-api.jar,
                                                         tomcat-coyote.jar,
                                                         tomcat-dbcp.jar,
                                                         tomcat-i18n-*.jar,
                                                         tomcat-jdbc.jar,
                                                         tomcat-jni.jar,
                                                         tomcat-juli-adapters.jar,
                                                         tomcat-juli.jar,
                                                         tomcat-util-scan.jar,
                                                         tomcat-util.jar,
                                                         tomcat-websocket.jar,
                                                         tools.jar,
                                                         websocket-api.jar,
                                                         wsdl4j*.jar,
                                                         xercesImpl.jar,
                                                         xml-apis.jar,
                                                         xmlParserAPIs-*.jar,
                                                         xmlParserAPIs.jar,
                                                         xom-*.jar
         user.country                                  : US
         user.dir                                      : /var/atlassian/application-data/jira
         user.home                                     : /var/atlassian/application-data/jira
         user.language                                 : en
         user.name                                     : jira
         user.timezone                                 : Asia/Shanghai
    
2025-09-10 14:17:31,638+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:17:31,703+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:17:31,715+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:17:32,114+0800 http-nio-8080-exec-1 ERROR anonymous 857x1x1 - ********** /secure/SetupDatabase!default.jspa [c.atlassian.instrumentation.Instrument] Unable to snapshot thread local operations (implementation of OpTimerFactory is not a ThreadLocalOpTimerFactory): null
2025-09-10 14:17:34,778+0800 JIRA-Bootstrap INFO      [c.a.j.c.cache.pauser.NonClusteredReplicationPauserManager] Non-clustered mode: ReplicationPauserManager implemented by NonClusteredReplicationPauserManager 
2025-09-10 14:17:34,781+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.JiraCacheResetter] [jira-cache-reseter] Created and registered for events
2025-09-10 14:17:34,786+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] Created, registered for events and schedulled stats job
2025-09-10 14:17:34,796+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Created
2025-09-10 14:17:35,211+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:17:35,222+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Starting the JIRA Plugin System _________________
    
2025-09-10 14:17:35,224+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup begun
2025-09-10 14:17:40,445+0800 FelixStartLevel WARN      [o.e.g.b.e.internal.support.ExtenderConfiguration] Gemini Blueprint extensions bundle not present, annotation processing disabled.
2025-09-10 14:17:41,335+0800 JIRA-Bootstrap INFO      [c.a.plugin.loaders.ScanningPluginLoader] No plugins found to be deployed
2025-09-10 14:17:43,454+0800 ThreadPoolAsyncTaskExecutor::Thread 2 WARN      [c.a.p.s.scanner.util.ProductFilterUtil] Couldn't detect product, will use ProductFilter.ALL
2025-09-10 14:17:43,631+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 300 seconds remaining
2025-09-10 14:17:44,631+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 298 seconds remaining
2025-09-10 14:17:45,633+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 297 seconds remaining
2025-09-10 14:17:46,635+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 296 seconds remaining
2025-09-10 14:17:47,796+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup ended
2025-09-10 14:17:47,804+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Starting
2025-09-10 14:17:47,877+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 14:17:47,894+0800 JIRA-Bootstrap INFO      [c.a.j.instrumentation.external.DatabaseExternalGauges] Installing DBCP monitoring instruments: DatabaseExternalGauges.JiraDbcpInstruments[instruments=[DBCP_MAX, DBCP_ACTIVE, DBCP_IDLE],objectName=com.atlassian.jira:name=BasicDataSource]
2025-09-10 14:17:47,931+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Running Jira startup checks.
2025-09-10 14:17:47,932+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Jira pre-database startup checks completed successfully.
2025-09-10 14:17:47,984+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Database Checklist Launcher on post-database-configured-but-pre-database-activated queue
2025-09-10 14:17:47,985+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Post database-configuration launchers on post-database-activated queue
2025-09-10 14:17:47,986+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.LauncherContextListener] Startup is complete. Jira is ready to serve.
2025-09-10 14:17:47,988+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.LauncherContextListener] Memory Usage:
    ---------------------------------------------------------------------------------
      Heap memory     :  Used:  576 MiB.  Committed: 1026 MiB.  Max: 2048 MiB
      Non-heap memory :  Used:   90 MiB.  Committed:  131 MiB.  Max: 1536 MiB
    ---------------------------------------------------------------------------------
      TOTAL           :  Used:  665 MiB.  Committed: 1157 MiB.  Max: 3584 MiB
    ---------------------------------------------------------------------------------
2025-09-10 14:17:50,831+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:17:50,832+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:17:50,832+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:17:50,832+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:17:50,832+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:17:50,833+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:17:50,833+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:17:50,834+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:17:50,834+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:17:50,834+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:17:50,834+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:17:50,835+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:17:50,835+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:17:50,844+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:17:50,845+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:17:50,845+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:17:50,846+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:17:50,846+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:17:50,846+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:17:50,847+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:17:50,847+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:17:50,847+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:17:50,847+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:17:50,848+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:17:50,849+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:17:50,849+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:17:50,869+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:17:50,870+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:17:50,871+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:17:50,871+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:17:50,871+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:17:50,872+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:17:50,872+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:17:50,872+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:17:50,872+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:17:50,873+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:17:50,873+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:17:50,873+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:17:50,875+0800 http-nio-8080-exec-9 WARN anonymous 857x17x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:19:30,061+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:19:30,062+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:19:30,062+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:19:30,063+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:19:30,063+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:19:30,063+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:19:30,064+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:19:30,064+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:19:30,064+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:19:30,064+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:19:30,064+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:19:30,065+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:19:30,065+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:19:30,066+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:19:30,066+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:19:30,066+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:19:30,067+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:19:30,067+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:19:30,067+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:19:30,067+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:19:30,067+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:19:30,067+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:19:30,068+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:19:30,068+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:19:30,068+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:19:30,068+0800 http-nio-8080-exec-4 WARN anonymous 859x217x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:20:17,627+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:20:17,628+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:20:17,628+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:20:17,628+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:20:17,628+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:20:17,629+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:20:17,629+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:20:17,629+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:20:17,629+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:20:17,630+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:20:17,630+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:20:17,630+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:20:17,630+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:20:17,630+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:20:17,631+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:20:17,631+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:20:17,631+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:20:17,631+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:20:17,631+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:20:17,632+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:20:17,632+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:20:17,632+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:20:17,632+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:20:17,632+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:20:17,633+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:20:17,633+0800 http-nio-8080-exec-1 WARN anonymous 860x222x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:20:22,644+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:20:22,645+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:20:22,646+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:20:22,649+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:20:22,650+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:20:22,651+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:20:22,651+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:20:22,651+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:20:22,651+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:20:22,651+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:20:22,652+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:20:22,652+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:20:22,652+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:20:22,652+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:20:22,653+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:20:22,653+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:20:22,653+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:20:22,653+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:20:22,653+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:20:22,654+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:20:22,654+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:20:22,654+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:20:22,654+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:20:22,654+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:20:22,655+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:20:22,655+0800 http-nio-8080-exec-8 WARN anonymous 860x227x1 1hc32k5 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:20:24,246+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:20:24,247+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:20:24,247+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:20:24,248+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:20:24,248+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:20:24,248+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:20:24,249+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:20:24,250+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:20:24,250+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:20:24,250+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:20:24,250+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:20:24,250+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:20:24,250+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:20:24,251+0800 http-nio-8080-exec-9 WARN anonymous 860x235x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:22:34,771+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:38, numberEventsInTransactions:1914, numberOfPluginEnableEvents:37
2025-09-10 14:27:34,756+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:38, numberEventsInTransactions:1914, numberOfPluginEnableEvents:37
2025-09-10 14:32:10,597+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:32:10,598+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:32:10,599+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:32:10,599+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:32:10,599+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:32:10,600+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:32:10,600+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:32:10,600+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:32:10,601+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:32:10,601+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:32:10,601+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:32:10,602+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:32:10,602+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:32:10,602+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 14:32:10,603+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 14:32:10,603+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 14:32:10,603+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 14:32:10,603+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 14:32:10,603+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 14:32:10,603+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 14:32:10,604+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 14:32:10,604+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 14:32:10,604+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 14:32:10,604+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 14:32:10,604+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 14:32:10,604+0800 http-nio-8080-exec-3 WARN anonymous 872x243x1 1hc32k5 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 14:32:34,742+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:38, numberEventsInTransactions:1914, numberOfPluginEnableEvents:37
