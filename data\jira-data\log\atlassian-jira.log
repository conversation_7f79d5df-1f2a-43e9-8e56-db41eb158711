2025-09-10 15:28:37,468+0800 localhost-startStop-1 INFO      [c.a.jira.startup.JiraHomeStartupCheck] The jira.home directory '/var/atlassian/application-data/jira' is validated and locked for exclusive use by this instance.
2025-09-10 15:28:37,550+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ****************
    Jira starting...
    ****************
    
2025-09-10 15:28:37,646+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Environment _____________________________
    
         JIRA Build                                    : 8.13.7#813007-sha1:3e6833b6aa4b98966fa10c8ab5e2a573d1da0bde
         Build Date                                    : Tue May 18 00:00:00 CST 2021
         JIRA Installation Type                        : Standalone
         Application Server                            : Apache Tomcat/8.5.60 - Servlet API 3.1
         Java Version                                  : 11.0.22 - Eclipse Adoptium
         Current Working Directory                     : /var/atlassian/application-data/jira
         Maximum Allowable Memory                      : 2048MB
         Total Memory                                  : 1026MB
         Free Memory                                   : 768MB
         Used Memory                                   : 258MB
         Memory Pool: CodeHeap 'non-nmethods'          : CodeHeap 'non-nmethods': init = 7598080(7420K) used = 3001856(2931K) committed = 7598080(7420K) max = 7598080(7420K)
         Memory Pool: Metaspace                        : Metaspace: init = 0(0K) used = 19963296(19495K) committed = 20840448(20352K) max = -1(-1K)
         Memory Pool: CodeHeap 'profiled nmethods'     : CodeHeap 'profiled nmethods': init = 33554432(32768K) used = 9818112(9588K) committed = 33554432(32768K) max = 264634368(258432K)
         Memory Pool: Compressed Class Space           : Compressed Class Space: init = 0(0K) used = 2168104(2117K) committed = 2490368(2432K) max = 1073741824(1048576K)
         Memory Pool: G1 Eden Space                    : G1 Eden Space: init = 56623104(55296K) used = 245366784(239616K) committed = 397410304(388096K) max = -1(-1K)
         Memory Pool: G1 Old Gen                       : G1 Old Gen: init = 1017118720(993280K) used = 19438416(18982K) committed = 670040064(654336K) max = 2147483648(2097152K)
         Memory Pool: G1 Survivor Space                : G1 Survivor Space: init = 0(0K) used = 8388608(8192K) committed = 8388608(8192K) max = -1(-1K)
         Memory Pool: CodeHeap 'non-profiled nmethods' : CodeHeap 'non-profiled nmethods': init = 33554432(32768K) used = 3642112(3556K) committed = 33554432(32768K) max = 264638464(258436K)
         JVM Input Arguments                           : --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Xms1024m -Xmx2048m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=512m -Djava.awt.headless=true -Datlassian.standalone=JIRA -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true -Dmail.mime.decodeparameters=true -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory -XX:-OmitStackTraceInFastThrow -Djava.locale.providers=COMPAT -Djira.home=/var/atlassian/application-data/jira -Datlassian.plugins.startup.options=-fg -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:tags,time,uptime,level:filecount=5,filesize=20M -XX:+ExplicitGCInvokesConcurrent -Dignore.endorsed.dirs= -Dcatalina.base=/opt/atlassian/jira -Dcatalina.home=/opt/atlassian/jira -Djava.io.tmpdir=/opt/atlassian/jira/temp
         Java Compatibility Information                : JIRA version = 8.13.7, Java Version = 11.0.22
    
    ___ Java System Properties _________________
    
         atlassian.plugins.startup.options             : -fg
         atlassian.standalone                          : JIRA
         awt.toolkit                                   : sun.awt.X11.XToolkit
         catalina.base                                 : /opt/atlassian/jira
         catalina.home                                 : /opt/atlassian/jira
         catalina.useNaming                            : true
         common.loader                                 : "${catalina.base}/lib",
                                                         "${catalina.base}/lib/*.jar",
                                                         "${catalina.home}/lib",
                                                         "${catalina.home}/lib/*.jar"
         file.encoding                                 : UTF-8
         ignore.endorsed.dirs                          : 
         java.awt.graphicsenv                          : sun.awt.X11GraphicsEnvironment
         java.awt.headless                             : true
         java.awt.printerjob                           : sun.print.PSPrinterJob
         java.class.version                            : 55.0
         java.home                                     : /opt/java/openjdk
         java.io.tmpdir                                : /opt/atlassian/jira/temp
         java.locale.providers                         : COMPAT
         java.naming.factory.initial                   : org.apache.naming.java.javaURLContextFactory
         java.naming.factory.url.pkgs                  : org.apache.naming
         java.protocol.handler.pkgs                    : org.apache.catalina.webresources
         java.runtime.name                             : OpenJDK Runtime Environment
         java.runtime.version                          : 11.0.22+7
         java.specification.maintenance.version        : 2
         java.specification.name                       : Java Platform API Specification
         java.specification.vendor                     : Oracle Corporation
         java.specification.version                    : 11
         java.util.logging.config.file                 : /opt/atlassian/jira/conf/logging.properties
         java.util.logging.manager                     : org.apache.juli.ClassLoaderLogManager
         java.vendor                                   : Eclipse Adoptium
         java.vendor.url                               : https://adoptium.net/
         java.vendor.url.bug                           : https://github.com/adoptium/adoptium-support/issues
         java.vendor.version                           : Temurin-11.0.22+7
         java.version                                  : 11.0.22
         java.version.date                             : 2024-01-16
         java.vm.compressedOopsMode                    : 32-bit
         java.vm.info                                  : mixed mode, sharing
         java.vm.name                                  : OpenJDK 64-Bit Server VM
         java.vm.specification.name                    : Java Virtual Machine Specification
         java.vm.specification.vendor                  : Oracle Corporation
         java.vm.specification.version                 : 11
         java.vm.vendor                                : Eclipse Adoptium
         java.vm.version                               : 11.0.22+7
         jdk.debug                                     : release
         jdk.tls.ephemeralDHKeySize                    : 2048
         jira.home                                     : /var/atlassian/application-data/jira
         mail.mime.decodeparameters                    : true
         org.apache.catalina.security.SecurityListener.UMASK : 0027
         org.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER : true
         org.dom4j.factory                             : com.atlassian.core.xml.InterningDocumentFactory
         os.arch                                       : amd64
         os.name                                       : Linux
         os.version                                    : ********-microsoft-standard-WSL2
         package.access                                : sun.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.tomcat.
         package.definition                            : sun.,
                                                         java.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.naming.,
                                                         org.apache.tomcat.
         server.loader                                 : 
         shared.loader                                 : 
         sun.arch.data.model                           : 64
         sun.boot.library.path                         : /opt/java/openjdk/lib
         sun.cpu.endian                                : little
         sun.cpu.isalist                               : 
         sun.io.unicode.encoding                       : UnicodeLittle
         sun.java.command                              : org.apache.catalina.startup.Bootstrap start
         sun.java.launcher                             : SUN_STANDARD
         sun.jnu.encoding                              : UTF-8
         sun.management.compiler                       : HotSpot 64-Bit Tiered Compilers
         sun.os.patch.level                            : unknown
         tomcat.util.buf.StringCache.byte.enabled      : true
         tomcat.util.scan.StandardJarScanFilter.jarsToScan : log4j-taglib*.jar,
                                                         log4j-web*.jar,
                                                         log4javascript*.jar,
                                                         slf4j-taglib*.jar
         tomcat.util.scan.StandardJarScanFilter.jarsToSkip : annotations-api.jar,
                                                         ant-junit*.jar,
                                                         ant-launcher.jar,
                                                         ant.jar,
                                                         asm-*.jar,
                                                         aspectj*.jar,
                                                         bootstrap.jar,
                                                         catalina-ant.jar,
                                                         catalina-ha.jar,
                                                         catalina-jmx-remote.jar,
                                                         catalina-storeconfig.jar,
                                                         catalina-tribes.jar,
                                                         catalina-ws.jar,
                                                         catalina.jar,
                                                         cglib-*.jar,
                                                         cobertura-*.jar,
                                                         commons-beanutils*.jar,
                                                         commons-codec*.jar,
                                                         commons-collections*.jar,
                                                         commons-daemon.jar,
                                                         commons-dbcp*.jar,
                                                         commons-digester*.jar,
                                                         commons-fileupload*.jar,
                                                         commons-httpclient*.jar,
                                                         commons-io*.jar,
                                                         commons-lang*.jar,
                                                         commons-logging*.jar,
                                                         commons-math*.jar,
                                                         commons-pool*.jar,
                                                         dom4j-*.jar,
                                                         easymock-*.jar,
                                                         ecj-*.jar,
                                                         el-api.jar,
                                                         geronimo-spec-jaxrpc*.jar,
                                                         h2*.jar,
                                                         hamcrest-*.jar,
                                                         hibernate*.jar,
                                                         httpclient*.jar,
                                                         icu4j-*.jar,
                                                         jasper-el.jar,
                                                         jasper.jar,
                                                         jaspic-api.jar,
                                                         jaxb-*.jar,
                                                         jaxen-*.jar,
                                                         jdom-*.jar,
                                                         jetty-*.jar,
                                                         jmx-tools.jar,
                                                         jmx.jar,
                                                         jsp-api.jar,
                                                         jstl.jar,
                                                         jta*.jar,
                                                         junit-*.jar,
                                                         junit.jar,
                                                         log4j*.jar,
                                                         mail*.jar,
                                                         objenesis-*.jar,
                                                         oraclepki.jar,
                                                         oro-*.jar,
                                                         servlet-api-*.jar,
                                                         servlet-api.jar,
                                                         slf4j*.jar,
                                                         taglibs-standard-spec-*.jar,
                                                         tagsoup-*.jar,
                                                         tomcat-api.jar,
                                                         tomcat-coyote.jar,
                                                         tomcat-dbcp.jar,
                                                         tomcat-i18n-*.jar,
                                                         tomcat-jdbc.jar,
                                                         tomcat-jni.jar,
                                                         tomcat-juli-adapters.jar,
                                                         tomcat-juli.jar,
                                                         tomcat-util-scan.jar,
                                                         tomcat-util.jar,
                                                         tomcat-websocket.jar,
                                                         tools.jar,
                                                         websocket-api.jar,
                                                         wsdl4j*.jar,
                                                         xercesImpl.jar,
                                                         xml-apis.jar,
                                                         xmlParserAPIs-*.jar,
                                                         xmlParserAPIs.jar,
                                                         xom-*.jar
         user.country                                  : US
         user.dir                                      : /var/atlassian/application-data/jira
         user.home                                     : /var/atlassian/application-data/jira
         user.language                                 : en
         user.name                                     : jira
         user.timezone                                 : Asia/Shanghai
    
2025-09-10 15:28:37,818+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 15:28:37,898+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 15:28:37,907+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 15:28:38,236+0800 JIRA-Bootstrap INFO      [c.a.j.c.cache.pauser.NonClusteredReplicationPauserManager] Non-clustered mode: ReplicationPauserManager implemented by NonClusteredReplicationPauserManager 
2025-09-10 15:28:38,239+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.JiraCacheResetter] [jira-cache-reseter] Created and registered for events
2025-09-10 15:28:38,243+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] Created, registered for events and schedulled stats job
2025-09-10 15:28:38,254+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Created
2025-09-10 15:28:38,556+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 15:28:38,579+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Starting the JIRA Plugin System _________________
    
2025-09-10 15:28:38,583+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup begun
2025-09-10 15:28:43,671+0800 FelixStartLevel WARN      [o.e.g.b.e.internal.support.ExtenderConfiguration] Gemini Blueprint extensions bundle not present, annotation processing disabled.
2025-09-10 15:28:44,550+0800 JIRA-Bootstrap INFO      [c.a.plugin.loaders.ScanningPluginLoader] No plugins found to be deployed
2025-09-10 15:28:46,044+0800 ThreadPoolAsyncTaskExecutor::Thread 2 WARN      [c.a.p.s.scanner.util.ProductFilterUtil] Couldn't detect product, will use ProductFilter.ALL
2025-09-10 15:28:46,245+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 300 seconds remaining
2025-09-10 15:28:47,246+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 298 seconds remaining
2025-09-10 15:28:48,247+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 297 seconds remaining
2025-09-10 15:28:49,451+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup ended
2025-09-10 15:28:49,459+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Starting
2025-09-10 15:28:49,529+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 15:28:49,551+0800 JIRA-Bootstrap INFO      [c.a.j.instrumentation.external.DatabaseExternalGauges] Installing DBCP monitoring instruments: DatabaseExternalGauges.JiraDbcpInstruments[instruments=[DBCP_MAX, DBCP_ACTIVE, DBCP_IDLE],objectName=com.atlassian.jira:name=BasicDataSource]
2025-09-10 15:28:49,587+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Running Jira startup checks.
2025-09-10 15:28:49,588+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Jira pre-database startup checks completed successfully.
2025-09-10 15:28:49,629+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Database Checklist Launcher on post-database-configured-but-pre-database-activated queue
2025-09-10 15:28:49,630+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Post database-configuration launchers on post-database-activated queue
2025-09-10 15:28:49,643+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.LauncherContextListener] Startup is complete. Jira is ready to serve.
2025-09-10 15:28:49,645+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.LauncherContextListener] Memory Usage:
    ---------------------------------------------------------------------------------
      Heap memory     :  Used:  531 MiB.  Committed: 1026 MiB.  Max: 2048 MiB
      Non-heap memory :  Used:   87 MiB.  Committed:  128 MiB.  Max: 1536 MiB
    ---------------------------------------------------------------------------------
      TOTAL           :  Used:  618 MiB.  Committed: 1154 MiB.  Max: 3584 MiB
    ---------------------------------------------------------------------------------
2025-09-10 15:29:11,017+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:11,017+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:11,017+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:11,018+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:11,018+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:11,019+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:11,019+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:11,020+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:11,020+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:11,021+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:11,021+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:11,022+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:11,022+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:11,023+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:11,023+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:11,024+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:11,024+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:11,024+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:11,025+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:11,026+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:11,027+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:11,026+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:11,027+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:11,028+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:11,028+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:11,029+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:11,036+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:11,037+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:11,037+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:11,038+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:11,041+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:11,041+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:11,041+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:11,041+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:11,042+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:11,042+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:11,042+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:11,042+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:11,042+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:11,054+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:11,054+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:11,058+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:11,058+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:11,059+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:11,060+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:11,060+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:11,060+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:11,061+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:11,061+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:11,062+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:11,062+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:11,062+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:11,063+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:11,063+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:11,064+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:11,064+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:11,064+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:11,066+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:11,067+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:11,067+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:11,067+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:11,068+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:11,068+0800 http-nio-8080-exec-4 WARN anonymous 929x4x2 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:11,068+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:11,069+0800 http-nio-8080-exec-2 WARN anonymous 929x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:16,551+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:16,552+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:16,552+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:16,552+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:16,553+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:16,553+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:16,553+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:16,553+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:16,554+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:16,554+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:16,554+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:16,554+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:16,554+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:16,555+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:16,555+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:16,555+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:16,555+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:16,555+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:16,556+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:16,557+0800 http-nio-8080-exec-2 WARN anonymous 929x208x1 tmycd8 ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:18,682+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:18,683+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:18,686+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:18,686+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:18,687+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:18,687+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:18,687+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:18,687+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:18,688+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:18,688+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:18,689+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:18,689+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:18,689+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 15:29:18,690+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 15:29:18,690+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 15:29:18,690+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 15:29:18,691+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 15:29:18,691+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc]
2025-09-10 15:29:18,692+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.994e478d48]
2025-09-10 15:29:18,692+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 15:29:18,692+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9]
2025-09-10 15:29:18,693+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 15:29:18,693+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 15:29:18,693+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 15:29:18,693+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 15:29:18,693+0800 http-nio-8080-exec-4 WARN anonymous 929x216x1 tmycd8 ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
