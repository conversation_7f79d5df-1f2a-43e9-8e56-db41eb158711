#!/bin/bash

# JIRA环境镜像导入脚本
# 使用方法：在目标服务器上运行此脚本来加载所有必需的Docker镜像

echo "开始导入JIRA环境镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误：Docker未运行或无法访问"
    exit 1
fi

# 导入JIRA镜像（包含破解代理）
echo "正在导入 atlassian/jira:8.13.7 镜像..."
if [ -f "atlassian-jira-8.13.7.tar" ]; then
    docker load -i atlassian-jira-8.13.7.tar
    echo "✅ JIRA镜像导入完成"
else
    echo "❌ 错误：找不到 atlassian-jira-8.13.7.tar 文件"
    exit 1
fi

# 导入MySQL镜像
echo "正在导入 mysql:5.7 镜像..."
if [ -f "mysql-5.7.tar" ]; then
    docker load -i mysql-5.7.tar
    echo "✅ MySQL镜像导入完成"
else
    echo "❌ 错误：找不到 mysql-5.7.tar 文件"
    exit 1
fi

# 验证镜像导入
echo ""
echo "验证导入的镜像："
docker images | grep -E "(atlassian/jira|mysql)" | grep -E "(8.13.7|5.7)"

echo ""
echo "🎉 所有镜像导入完成！"
echo "现在您可以运行 'docker-compose up -d' 来启动JIRA环境"
