********** 1008x1x1 - [10/Sep/2025:16:48:43 +0800] "GET / HTTP/1.1" 302 - 141 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "-"
********** 1008x2x1 - [10/Sep/2025:16:48:45 +0800] "GET /secure/SetupMode!default.jspa HTTP/1.1" 200 5907 1842 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "-"
********** 1008x7x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.fb15cffa72/com.atlassian.auiplugin:split_aui.splitchunk.fb15cffa72.css HTTP/1.1" 200 254 30 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x3x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.056561461c/com.atlassian.auiplugin:split_aui.splitchunk.056561461c.css HTTP/1.1" 200 994 41 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x4x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.fbbef27525/com.atlassian.auiplugin:split_aui.splitchunk.fbbef27525.css HTTP/1.1" 200 2565 42 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x8x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c.css HTTP/1.1" 200 347 28 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x5x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.949297951c/com.atlassian.auiplugin:split_aui.splitchunk.949297951c.css HTTP/1.1" 200 451 35 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x6x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.d7c46c2734/com.atlassian.auiplugin:split_aui.splitchunk.d7c46c2734.css HTTP/1.1" 200 1383 33 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x9x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.b2ecdd4179/com.atlassian.auiplugin:split_aui.splitchunk.b2ecdd4179.css HTTP/1.1" 200 447 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x14x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.141abf7fb1/com.atlassian.auiplugin:split_aui.splitchunk.141abf7fb1.css HTTP/1.1" 200 347 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x10x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.462ee5f9ef/com.atlassian.auiplugin:split_aui.splitchunk.462ee5f9ef.css HTTP/1.1" 200 843 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x13x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.50dca3e042/com.atlassian.auiplugin:split_aui.splitchunk.50dca3e042.css HTTP/1.1" 200 4567 8 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x11x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.862f26d10a/com.atlassian.auiplugin:split_aui.splitchunk.862f26d10a.css HTTP/1.1" 200 1350 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x12x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.908fe798b4/com.atlassian.auiplugin:split_aui.splitchunk.908fe798b4.css HTTP/1.1" 200 4364 9 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x17x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.3db8886820/com.atlassian.auiplugin:split_aui.splitchunk.3db8886820.css HTTP/1.1" 200 332 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x18x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.b548966f06/com.atlassian.auiplugin:split_aui.splitchunk.b548966f06.css HTTP/1.1" 200 1748 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x16x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.4a667cc1ee/com.atlassian.auiplugin:split_aui.splitchunk.4a667cc1ee.css HTTP/1.1" 200 420 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x15x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.7da3927366/com.atlassian.auiplugin:split_aui.splitchunk.7da3927366.css HTTP/1.1" 200 1865 9 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x20x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.5b8c290363/com.atlassian.auiplugin:split_aui.splitchunk.5b8c290363.css HTTP/1.1" 200 429 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x19x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.b652d2668a/com.atlassian.auiplugin:split_aui.splitchunk.b652d2668a.css HTTP/1.1" 200 2037 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x22x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.f5244b0828/com.atlassian.auiplugin:split_aui.splitchunk.f5244b0828.css HTTP/1.1" 200 1415 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x23x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.d925afe2c0/com.atlassian.auiplugin:split_aui.splitchunk.d925afe2c0.css HTTP/1.1" 200 1390 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x21x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.async-header/com.atlassian.auiplugin:split_aui.component.async-header.css HTTP/1.1" 200 197 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x24x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.component.dropdown1/com.atlassian.auiplugin:split_aui.deprecated.component.dropdown1.css HTTP/1.1" 200 1220 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x25x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.component.dialog1/com.atlassian.auiplugin:split_aui.deprecated.component.dialog1.css HTTP/1.1" 200 1256 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x26x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.tabs/com.atlassian.auiplugin:split_aui.component.tabs.css HTTP/1.1" 200 909 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x27x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.a2b2c71491/com.atlassian.auiplugin:split_aui.splitchunk.a2b2c71491.css HTTP/1.1" 200 477 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x32x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.label/com.atlassian.auiplugin:split_aui.pattern.label.css HTTP/1.1" 200 599 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x28x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.lozenge/com.atlassian.auiplugin:split_aui.pattern.lozenge.css HTTP/1.1" 200 461 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x29x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f.css HTTP/1.1" 200 506 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x30x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.16f099a0da/com.atlassian.auiplugin:split_aui.splitchunk.16f099a0da.css HTTP/1.1" 200 715 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x31x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:icons/jira.webresources:icons.css HTTP/1.1" 200 2981 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x33x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00.css HTTP/1.1" 200 521 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x36x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:a11y-overrides/jira.webresources:a11y-overrides.css HTTP/1.1" 200 614 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x34x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00.css HTTP/1.1" 200 487 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x38x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.3d2cb1af14/com.atlassian.auiplugin:split_aui.splitchunk.3d2cb1af14.css HTTP/1.1" 200 484 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x37x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/ca6f1534023a7bdcd8ee4e5cf856fdd0-CDN/-2o1ton/813007/l7q7us/2.1.1/_/download/batch/com.atlassian.plugins.issue-status-plugin:issue-status-resources/com.atlassian.plugins.issue-status-plugin:issue-status-resources.css HTTP/1.1" 200 1725 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x39x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.flag/com.atlassian.auiplugin:split_aui.component.flag.css HTTP/1.1" 200 543 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x41x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:list-styles/jira.webresources:list-styles.css HTTP/1.1" 200 773 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x40x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:inline-layer/jira.webresources:inline-layer.css HTTP/1.1" 200 311 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x42x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:frother-queryable-dropdown-select/jira.webresources:frother-queryable-dropdown-select.css HTTP/1.1" 200 790 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x43x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:frother-singleselect/jira.webresources:frother-singleselect.css HTTP/1.1" 200 1125 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x44x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.multi-step-progress/com.atlassian.auiplugin:split_aui.pattern.multi-step-progress.css HTTP/1.1" 200 700 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x46x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-setup/jira.webresources:jira-setup.css HTTP/1.1" 200 1234 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x45x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.dialog2/com.atlassian.auiplugin:split_aui.component.dialog2.css HTTP/1.1" 200 1274 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x49x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/3.1.5/_/download/batch/com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager/com.atlassian.plugins.atlassian-plugins-webresource-rest:web-resource-manager.js HTTP/1.1" 200 322 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x48x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:almond-analytics/jira.webresources:almond-analytics.js HTTP/1.1" 200 663 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x47x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:almond/jira.webresources:almond.js HTTP/1.1" 200 1743 9 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x51x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:common-runtime/com.atlassian.auiplugin:common-runtime.js HTTP/1.1" 200 980 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x35x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/0c8e1524a7636d8b7da0a0508e3f5f2c-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:global-static/jira.webresources:global-static.css HTTP/1.1" 200 41345 26 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x54x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc/com.atlassian.auiplugin:split_aui.splitchunk.739b9ec8cc.js HTTP/1.1" 200 374 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x53x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.444efc83be/com.atlassian.auiplugin:split_aui.splitchunk.444efc83be.js HTTP/1.1" 200 399 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x50x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/*******/_/download/batch/com.atlassian.plugins.jquery:jquery/com.atlassian.plugins.jquery:jquery.js HTTP/1.1" 200 33592 11 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x52x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1/com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1.js HTTP/1.1" 200 337 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x56x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76/com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76.js HTTP/1.1" 200 920 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x55x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.994e478d48/com.atlassian.auiplugin:split_aui.splitchunk.994e478d48.js HTTP/1.1" 200 490 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x57x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91/com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91.js HTTP/1.1" 200 539 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x62x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.core/com.atlassian.auiplugin:split_aui.core.js HTTP/1.1" 200 416 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x58x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe/com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe.js HTTP/1.1" 200 389 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x59x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060/com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060.js HTTP/1.1" 200 374 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x61x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72/com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72.js HTTP/1.1" 200 476 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x60x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9/com.atlassian.auiplugin:split_aui.splitchunk.2e16019fb9.js HTTP/1.1" 200 481 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x63x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/3.1.5/_/download/batch/com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path/com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.js HTTP/1.1" 200 373 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x65x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/0.4.0/_/download/batch/com.atlassian.frontend.atlassian-frontend-runtime-plugin:focus-visible/com.atlassian.frontend.atlassian-frontend-runtime-plugin:focus-visible.js HTTP/1.1" 200 1559 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x67x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/0.4.0/_/download/batch/com.atlassian.frontend.atlassian-frontend-runtime-plugin:fetch/com.atlassian.frontend.atlassian-frontend-runtime-plugin:fetch.js HTTP/1.1" 200 3296 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x66x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:_polyfills/jira.webresources:_polyfills.js HTTP/1.1" 200 1620 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x68x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jquery/jira.webresources:jquery.js HTTP/1.1" 200 5279 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x64x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/0.4.0/_/download/batch/com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js/com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js.js HTTP/1.1" 200 26227 8 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x69x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:top-same-origin-window/jira.webresources:top-same-origin-window.js HTTP/1.1" 200 330 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x72x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jquery-plugin-scrollintoview/jira.webresources:jquery-plugin-scrollintoview.js HTTP/1.1" 200 934 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x73x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.056561461c/com.atlassian.auiplugin:split_aui.splitchunk.056561461c.js HTTP/1.1" 200 357 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x71x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.fbbef27525/com.atlassian.auiplugin:split_aui.splitchunk.fbbef27525.js HTTP/1.1" 200 350 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x70x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--894c8113d9/com.atlassian.auiplugin:split_aui.splitchunk.vendors--894c8113d9.js HTTP/1.1" 200 5507 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x74x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.949297951c/com.atlassian.auiplugin:split_aui.splitchunk.949297951c.js HTTP/1.1" 200 373 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x75x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.dd803a46b4/com.atlassian.auiplugin:split_aui.splitchunk.dd803a46b4.js HTTP/1.1" 200 1570 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x76x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.fb15cffa72/com.atlassian.auiplugin:split_aui.splitchunk.fb15cffa72.js HTTP/1.1" 200 350 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x78x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.d7c46c2734/com.atlassian.auiplugin:split_aui.splitchunk.d7c46c2734.js HTTP/1.1" 200 352 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x77x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.e54c7c7304/com.atlassian.auiplugin:split_aui.splitchunk.e54c7c7304.js HTTP/1.1" 200 367 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x80x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c.js HTTP/1.1" 200 358 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x79x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.f1e06f97a4/com.atlassian.auiplugin:split_aui.splitchunk.f1e06f97a4.js HTTP/1.1" 200 623 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x81x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.f673ef53ac/com.atlassian.auiplugin:split_aui.splitchunk.f673ef53ac.js HTTP/1.1" 200 372 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x85x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.d0110a864f/com.atlassian.auiplugin:split_aui.splitchunk.d0110a864f.js HTTP/1.1" 200 865 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x84x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.908fe798b4/com.atlassian.auiplugin:split_aui.splitchunk.908fe798b4.js HTTP/1.1" 200 388 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x86x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.afa5039e04/com.atlassian.auiplugin:split_aui.splitchunk.afa5039e04.js HTTP/1.1" 200 658 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x82x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.462ee5f9ef/com.atlassian.auiplugin:split_aui.splitchunk.462ee5f9ef.js HTTP/1.1" 200 370 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x83x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.8659b532c1/com.atlassian.auiplugin:split_aui.splitchunk.8659b532c1.js HTTP/1.1" 200 407 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x87x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.26116b3cbd/com.atlassian.auiplugin:split_aui.splitchunk.26116b3cbd.js HTTP/1.1" 200 691 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x91x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.6d6f245ed3/com.atlassian.auiplugin:split_aui.splitchunk.6d6f245ed3.js HTTP/1.1" 200 3514 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x88x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.862f26d10a/com.atlassian.auiplugin:split_aui.splitchunk.862f26d10a.js HTTP/1.1" 200 375 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x90x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.bff3715233/com.atlassian.auiplugin:split_aui.splitchunk.bff3715233.js HTTP/1.1" 200 1396 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x89x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.c750721820/com.atlassian.auiplugin:split_aui.splitchunk.c750721820.js HTTP/1.1" 200 462 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x92x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.50dca3e042/com.atlassian.auiplugin:split_aui.splitchunk.50dca3e042.js HTTP/1.1" 200 392 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x93x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.b2ecdd4179/com.atlassian.auiplugin:split_aui.splitchunk.b2ecdd4179.js HTTP/1.1" 200 366 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x95x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.7da3927366/com.atlassian.auiplugin:split_aui.splitchunk.7da3927366.js HTTP/1.1" 200 370 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x96x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.4a667cc1ee/com.atlassian.auiplugin:split_aui.splitchunk.4a667cc1ee.js HTTP/1.1" 200 357 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x97x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.7253827cf8/com.atlassian.auiplugin:split_aui.splitchunk.7253827cf8.js HTTP/1.1" 200 387 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x94x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.141abf7fb1/com.atlassian.auiplugin:split_aui.splitchunk.141abf7fb1.js HTTP/1.1" 200 381 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x98x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.3db8886820/com.atlassian.auiplugin:split_aui.splitchunk.3db8886820.js HTTP/1.1" 200 370 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x99x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.side-effects/com.atlassian.auiplugin:split_aui.side-effects.js HTTP/1.1" 200 1325 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x104x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.9c48cc20a9/com.atlassian.auiplugin:split_aui.splitchunk.9c48cc20a9.js HTTP/1.1" 200 379 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x105x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--9c48cc20a9/com.atlassian.auiplugin:split_aui.splitchunk.vendors--9c48cc20a9.js HTTP/1.1" 200 6784 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x103x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--d2297af84a/com.atlassian.auiplugin:split_aui.splitchunk.vendors--d2297af84a.js HTTP/1.1" 200 2536 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x101x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--95c789edf5/com.atlassian.auiplugin:split_aui.splitchunk.vendors--95c789edf5.js HTTP/1.1" 200 397 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x102x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-layer-interactions/jira.webresources:jira-layer-interactions.js HTTP/1.1" 200 407 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x100x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--084821f40b/com.atlassian.auiplugin:split_aui.splitchunk.vendors--084821f40b.js HTTP/1.1" 200 6527 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x109x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.084821f40b/com.atlassian.auiplugin:split_aui.splitchunk.084821f40b.js HTTP/1.1" 200 1893 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x106x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.5f851f97df/com.atlassian.auiplugin:split_aui.splitchunk.5f851f97df.js HTTP/1.1" 200 1526 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x110x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.5b8c290363/com.atlassian.auiplugin:split_aui.splitchunk.5b8c290363.js HTTP/1.1" 200 398 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x107x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.baa83dbaf9/com.atlassian.auiplugin:split_aui.splitchunk.baa83dbaf9.js HTTP/1.1" 200 447 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x111x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.36cd9d521c/com.atlassian.auiplugin:split_aui.splitchunk.36cd9d521c.js HTTP/1.1" 200 348 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x108x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.ed86a19e01/com.atlassian.auiplugin:split_aui.splitchunk.ed86a19e01.js HTTP/1.1" 200 1250 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x112x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.e5af17b48e/com.atlassian.auiplugin:split_aui.splitchunk.e5af17b48e.js HTTP/1.1" 200 585 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x116x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.f5244b0828/com.atlassian.auiplugin:split_aui.splitchunk.f5244b0828.js HTTP/1.1" 200 397 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x114x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.b652d2668a/com.atlassian.auiplugin:split_aui.splitchunk.b652d2668a.js HTTP/1.1" 200 369 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x117x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.b548966f06/com.atlassian.auiplugin:split_aui.splitchunk.b548966f06.js HTTP/1.1" 200 453 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x113x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.f154095da3/com.atlassian.auiplugin:split_aui.splitchunk.f154095da3.js HTTP/1.1" 200 487 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x115x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.4248e12b20/com.atlassian.auiplugin:split_aui.splitchunk.4248e12b20.js HTTP/1.1" 200 4664 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x121x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.async-header/com.atlassian.auiplugin:split_aui.component.async-header.js HTTP/1.1" 200 2714 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x118x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.d925afe2c0/com.atlassian.auiplugin:split_aui.splitchunk.d925afe2c0.js HTTP/1.1" 200 326 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x119x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--6372529f0d/com.atlassian.auiplugin:split_aui.splitchunk.vendors--6372529f0d.js HTTP/1.1" 200 608 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x122x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.89403fec76/com.atlassian.auiplugin:split_aui.splitchunk.89403fec76.js HTTP/1.1" 200 642 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x123x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.component.dropdown1/com.atlassian.auiplugin:split_aui.deprecated.component.dropdown1.js HTTP/1.1" 200 4214 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x120x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.component.inline-dialog1/com.atlassian.auiplugin:split_aui.deprecated.component.inline-dialog1.js HTTP/1.1" 200 3697 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x124x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.6e1680ef6a/com.atlassian.auiplugin:split_aui.splitchunk.6e1680ef6a.js HTTP/1.1" 200 3792 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x125x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.component.dialog1/com.atlassian.auiplugin:split_aui.deprecated.component.dialog1.js HTTP/1.1" 200 534 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x126x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.tabs/com.atlassian.auiplugin:split_aui.component.tabs.js HTTP/1.1" 200 3544 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x127x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--20e849aab3/com.atlassian.auiplugin:split_aui.splitchunk.vendors--20e849aab3.js HTTP/1.1" 200 8116 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x129x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.489a76bc13/com.atlassian.auiplugin:split_aui.splitchunk.489a76bc13.js HTTP/1.1" 200 1061 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x128x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.20e849aab3/com.atlassian.auiplugin:split_aui.splitchunk.20e849aab3.js HTTP/1.1" 200 385 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x130x1 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.behaviour.progressive-data-set/com.atlassian.auiplugin:split_aui.behaviour.progressive-data-set.js HTTP/1.1" 200 476 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x134x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:deprecator/jira.webresources:deprecator.js HTTP/1.1" 200 983 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x132x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:aui-core-amd-shim/jira.webresources:aui-core-amd-shim.js HTTP/1.1" 200 428 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x131x2 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/5.0.0/_/download/batch/com.atlassian.soy.soy-template-plugin:soy-deps/com.atlassian.soy.soy-template-plugin:soy-deps.js HTTP/1.1" 200 12676 12 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x138x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:legacy-namespacing/jira.webresources:legacy-namespacing.js HTTP/1.1" 200 1202 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x136x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-logger/jira.webresources:jira-logger.js HTTP/1.1" 200 439 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x137x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:underscore-1.5.2/com.atlassian.plugin.jslibs:underscore-1.5.2.js HTTP/1.1" 200 5168 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x139x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:underscore-1.5.2/jira.webresources:underscore-1.5.2.js HTTP/1.1" 200 304 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x142x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:backbone-1.0.0/jira.webresources:backbone-1.0.0.js HTTP/1.1" 200 321 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x140x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:backbone-queryparams-lib/jira.webresources:backbone-queryparams-lib.js HTTP/1.1" 200 2136 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x141x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:backbone-1.0.0-factory/com.atlassian.plugin.jslibs:backbone-1.0.0-factory.js HTTP/1.1" 200 6661 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x143x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:marionette-1.6.1-factory/com.atlassian.plugin.jslibs:marionette-1.6.1-factory.js HTTP/1.1" 200 6833 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x145x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.a2b2c71491/com.atlassian.auiplugin:split_aui.splitchunk.a2b2c71491.js HTTP/1.1" 200 369 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x146x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.badge/com.atlassian.auiplugin:split_aui.pattern.badge.js HTTP/1.1" 200 368 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x144x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:marionette/jira.webresources:marionette.js HTTP/1.1" 200 546 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x147x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f.js HTTP/1.1" 200 394 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x150x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.af1935061c/com.atlassian.auiplugin:split_aui.splitchunk.af1935061c.js HTTP/1.1" 200 670 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x149x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.button/com.atlassian.auiplugin:split_aui.component.button.js HTTP/1.1" 200 540 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x148x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.lozenge/com.atlassian.auiplugin:split_aui.pattern.lozenge.js HTTP/1.1" 200 421 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x151x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.label/com.atlassian.auiplugin:split_aui.pattern.label.js HTTP/1.1" 200 416 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x152x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.table/com.atlassian.auiplugin:split_aui.pattern.table.js HTTP/1.1" 200 400 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x154x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--be1eb78c1a/com.atlassian.auiplugin:split_aui.splitchunk.vendors--be1eb78c1a.js HTTP/1.1" 200 2244 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x153x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.16f099a0da/com.atlassian.auiplugin:split_aui.splitchunk.16f099a0da.js HTTP/1.1" 200 387 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x155x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00.js HTTP/1.1" 200 333 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x157x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00.js HTTP/1.1" 200 378 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x158x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.tooltip/com.atlassian.auiplugin:split_aui.component.tooltip.js HTTP/1.1" 200 402 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x156x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.be1eb78c1a/com.atlassian.auiplugin:split_aui.splitchunk.be1eb78c1a.js HTTP/1.1" 200 724 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x162x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.behaviour.key-code/com.atlassian.auiplugin:split_aui.behaviour.key-code.js HTTP/1.1" 200 410 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x161x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-formatter/jira.webresources:jira-formatter.js HTTP/1.1" 200 429 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x160x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-metadata/jira.webresources:jira-metadata.js HTTP/1.1" 200 983 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x165x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-key-code/jira.webresources:jira-key-code.js HTTP/1.1" 200 266 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x163x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-smart-ajax/jira.webresources:jira-smart-ajax.js HTTP/1.1" 200 1418 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x164x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-data-stores/jira.webresources:jira-data-stores.js HTTP/1.1" 200 572 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x159x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/2.1.1/_/download/batch/com.atlassian.plugins.issue-status-plugin:issue-status-resources/com.atlassian.plugins.issue-status-plugin:issue-status-resources.js?locale=en-US HTTP/1.1" 200 1250 19 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x167x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.ec75f48bb8/com.atlassian.auiplugin:split_aui.splitchunk.ec75f48bb8.js HTTP/1.1" 200 1666 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x168x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.flag/com.atlassian.auiplugin:split_aui.component.flag.js HTTP/1.1" 200 1374 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x166x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.3d2cb1af14/com.atlassian.auiplugin:split_aui.splitchunk.3d2cb1af14.js HTTP/1.1" 200 483 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x169x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/3.1.5/_/download/batch/com.atlassian.plugins.atlassian-plugins-webresource-plugin:data/com.atlassian.plugins.atlassian-plugins-webresource-plugin:data.js HTTP/1.1" 200 361 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x171x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:wrm-data/jira.webresources:wrm-data.js HTTP/1.1" 200 268 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x170x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:wrm-context-path/jira.webresources:wrm-context-path.js HTTP/1.1" 200 280 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x173x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:aui-extensions/jira.webresources:aui-extensions.js HTTP/1.1" 200 316 2 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x175x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-objects/jira.webresources:jira-objects.js HTTP/1.1" 200 482 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x174x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-urlhelpers/jira.webresources:jira-urlhelpers.js HTTP/1.1" 200 335 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x176x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-events/jira.webresources:jira-events.js HTTP/1.1" 200 839 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x172x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:messages/jira.webresources:messages.js?locale=en-US HTTP/1.1" 200 2762 16 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x178x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-base-control/jira.webresources:jira-base-control.js HTTP/1.1" 200 1716 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x177x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:keyboard-event-extensions/jira.webresources:keyboard-event-extensions.js HTTP/1.1" 200 801 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x180x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:escape-css-selector-polyfill/jira.webresources:escape-css-selector-polyfill.js HTTP/1.1" 200 415 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x179x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:browser-properties/jira.webresources:browser-properties.js HTTP/1.1" 200 1125 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x182x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:legacy-table-utils/jira.webresources:legacy-table-utils.js HTTP/1.1" 200 552 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x184x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:cookie/jira.webresources:cookie.js HTTP/1.1" 200 355 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x181x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jquery-escape-selector-polyfill/jira.webresources:jquery-escape-selector-polyfill.js HTTP/1.1" 200 317 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x183x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:cookie-1.0.0/com.atlassian.plugin.jslibs:cookie-1.0.0.js HTTP/1.1" 200 814 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x187x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.vendor.underscore/com.atlassian.auiplugin:split_aui.vendor.underscore.js HTTP/1.1" 200 486 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x185x3 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:content-retrievers/jira.webresources:content-retrievers.js HTTP/1.1" 200 1319 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x186x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:inline-layer/jira.webresources:inline-layer.js HTTP/1.1" 200 4712 9 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x190x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:select-model/jira.webresources:select-model.js HTTP/1.1" 200 3443 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x191x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:frother-select-handlers/jira.webresources:frother-select-handlers.js HTTP/1.1" 200 2621 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x189x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:list/jira.webresources:list.js HTTP/1.1" 200 6621 11 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x192x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.spinner/com.atlassian.auiplugin:split_aui.component.spinner.js HTTP/1.1" 200 440 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x193x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.86ad04cc86/com.atlassian.auiplugin:split_aui.splitchunk.86ad04cc86.js HTTP/1.1" 200 708 4 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x188x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:util-lite/jira.webresources:util-lite.js HTTP/1.1" 200 6417 22 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x194x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.behaviour.jquery-spin/com.atlassian.auiplugin:split_aui.deprecated.behaviour.jquery-spin.js HTTP/1.1" 200 490 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x196x5 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.multi-step-progress/com.atlassian.auiplugin:split_aui.pattern.multi-step-progress.js HTTP/1.1" 200 423 5 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x197x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:frother-singleselect/jira.webresources:frother-singleselect.js HTTP/1.1" 200 3404 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x198x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.dialog2/com.atlassian.auiplugin:split_aui.component.dialog2.js HTTP/1.1" 200 1423 6 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x195x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:frother-queryable-dropdown-select/jira.webresources:frother-queryable-dropdown-select.js?locale=en-US HTTP/1.1" 200 3415 14 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x135x6 - [10/Sep/2025:16:48:45 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-setup-soy-templates/jira.webresources:jira-setup-soy-templates.js?locale=en-US HTTP/1.1" 200 7823 152 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x199x4 - [10/Sep/2025:16:48:45 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-setup/jira.webresources:jira-setup.js HTTP/1.1" 200 12514 17 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x133x4 - [10/Sep/2025:16:48:46 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:soy/com.atlassian.auiplugin:soy.js?locale=en-US HTTP/1.1" 200 12361 270 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x200x1 - [10/Sep/2025:16:48:46 +0800] "GET /s/-2o1ton/813007/l7q7us/9.1.4/_/download/resources/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c/assets/adgs-icons.woff HTTP/1.1" 200 25452 8 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x201x1 - [10/Sep/2025:16:48:46 +0800] "GET /favicon.ico HTTP/1.1" 503 5 3 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x202x1 - [10/Sep/2025:16:48:48 +0800] "GET /secure/SetupMode!getInstalledLocales.jspa?_=1757494125696 HTTP/1.1" 200 537 50 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x203x1 - [10/Sep/2025:16:48:51 +0800] "GET /secure/SetupMode!getLanguageTexts.jspa?localeForTexts=zh_CN&_=1757494125697 HTTP/1.1" 200 326 348 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x204x1 - [10/Sep/2025:16:48:52 +0800] "POST /secure/SetupMode!changeLanguage.jspa HTTP/1.1" 200 44 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x205x1 - [10/Sep/2025:16:48:52 +0800] "GET /secure/SetupMode!default.jspa HTTP/1.1" 200 6177 37 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x208x3 - [10/Sep/2025:16:48:52 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/2.1.1/_/download/batch/com.atlassian.plugins.issue-status-plugin:issue-status-resources/com.atlassian.plugins.issue-status-plugin:issue-status-resources.js?locale=zh-CN HTTP/1.1" 200 1278 9 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x209x4 - [10/Sep/2025:16:48:52 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:messages/jira.webresources:messages.js?locale=zh-CN HTTP/1.1" 200 2778 9 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x210x5 - [10/Sep/2025:16:48:52 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:frother-queryable-dropdown-select/jira.webresources:frother-queryable-dropdown-select.js?locale=zh-CN HTTP/1.1" 200 3415 7 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x207x2 - [10/Sep/2025:16:48:52 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/1.0/_/download/batch/jira.webresources:jira-setup-soy-templates/jira.webresources:jira-setup-soy-templates.js?locale=zh-CN HTTP/1.1" 200 8318 82 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x206x1 - [10/Sep/2025:16:48:52 +0800] "GET /s/2ef63d9e7053125de84769cc70af3ef8-CDN/-2o1ton/813007/l7q7us/9.1.4/_/download/batch/com.atlassian.auiplugin:soy/com.atlassian.auiplugin:soy.js?locale=zh-CN HTTP/1.1" 200 12402 149 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x211x1 - [10/Sep/2025:16:48:52 +0800] "GET /favicon.ico HTTP/1.1" 503 5 1 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x212x1 - [10/Sep/2025:16:48:56 +0800] "POST /secure/SetupMode.jspa HTTP/1.1" 302 - 14 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x213x1 - [10/Sep/2025:16:48:56 +0800] "GET /secure/SetupDatabase!default.jspa HTTP/1.1" 200 6715 253 "http://localhost:8080/secure/SetupMode!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1008x214x1 - [10/Sep/2025:16:48:56 +0800] "GET /favicon.ico HTTP/1.1" 503 5 3 "http://localhost:8080/secure/SetupDatabase!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1009x215x1 - [10/Sep/2025:16:49:18 +0800] "GET /secure/SetupDatabase!connectionCheck.jspa?databaseOption=external&databaseType=mysql57&jdbcHostname=mysql&jdbcPort=3306&jdbcDatabase=jiradb&jdbcSid=&jdbcUsername=root&jdbcPassword=iie.ac.cn&schemaName=&atl_token=bootstrapping_3e569f2b900375949471bdda3e22bfc74af72325_lout&testingConnection=false&_=1757494136773 HTTP/1.1" 200 932 365 "http://localhost:8080/secure/SetupDatabase!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1009x216x1 - [10/Sep/2025:17:02:53 +0800] "POST /secure/SetupDatabase.jspa HTTP/1.1" 302 - 811117 "http://localhost:8080/secure/SetupDatabase!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1022x217x1 - [10/Sep/2025:17:02:56 +0800] "GET /secure/SetupApplicationProperties!default.jspa HTTP/1.1" 200 4256 2301 "http://localhost:8080/secure/SetupDatabase!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1022x218x1 - [10/Sep/2025:17:02:56 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 503 40 3 "http://localhost:8080/secure/SetupApplicationProperties!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1022x219x1 - [10/Sep/2025:17:02:56 +0800] "GET /favicon.ico HTTP/1.1" 503 5 5 "http://localhost:8080/secure/SetupApplicationProperties!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1023x220x1 - [10/Sep/2025:17:03:36 +0800] "POST /secure/SetupApplicationProperties.jspa HTTP/1.1" 302 - 2002 "http://localhost:8080/secure/SetupApplicationProperties!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1023x221x1 - [10/Sep/2025:17:03:36 +0800] "GET /secure/SetupLicense!default.jspa HTTP/1.1" 200 3892 31 "http://localhost:8080/secure/SetupApplicationProperties!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1023x222x1 - [10/Sep/2025:17:03:36 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 503 40 2 "http://localhost:8080/secure/SetupLicense!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1023x223x1 - [10/Sep/2025:17:03:36 +0800] "GET /favicon.ico HTTP/1.1" 503 5 4 "http://localhost:8080/secure/SetupLicense!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1025x224x1 - [10/Sep/2025:17:05:01 +0800] "POST /secure/SetupLicense!validateLicense.jspa HTTP/1.1" 200 541 122 "http://localhost:8080/secure/SetupLicense!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1025x225x1 - [10/Sep/2025:17:07:12 +0800] "POST /secure/SetupLicense.jspa HTTP/1.1" 302 - 131422 "http://localhost:8080/secure/SetupLicense!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1027x226x1 - [10/Sep/2025:17:07:14 +0800] "GET /secure/SetupAdminAccount!default.jspa HTTP/1.1" 200 3956 1254 "http://localhost:8080/secure/SetupLicense!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1027x227x1 - [10/Sep/2025:17:07:14 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 503 40 4 "http://localhost:8080/secure/SetupAdminAccount!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1027x228x1 - [10/Sep/2025:17:07:14 +0800] "GET /favicon.ico HTTP/1.1" 503 5 4 "http://localhost:8080/secure/SetupAdminAccount!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x229x1 - [10/Sep/2025:17:08:46 +0800] "POST /secure/SetupAdminAccount.jspa HTTP/1.1" 200 5722 2120 "http://localhost:8080/secure/SetupAdminAccount!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x233x4 - [10/Sep/2025:17:08:46 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:create-user-popup-picker/jira.webresources:create-user-popup-picker.js?locale=zh-CN HTTP/1.1" 200 689 7 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x231x2 - [10/Sep/2025:17:08:46 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:autocomplete/jira.webresources:autocomplete.css HTTP/1.1" 200 658 19 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x235x6 - [10/Sep/2025:17:08:46 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:autocomplete/jira.webresources:autocomplete.js HTTP/1.1" 200 5543 5 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x237x5 - [10/Sep/2025:17:08:46 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:groupbrowser/jira.webresources:groupbrowser.js HTTP/1.1" 200 325 3 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x234x5 - [10/Sep/2025:17:08:46 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:share-types/jira.webresources:share-types.css HTTP/1.1" 200 608 17 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x230x1 - [10/Sep/2025:17:08:46 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:groupbrowser/jira.webresources:groupbrowser.css HTTP/1.1" 200 192 28 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x232x3 - [10/Sep/2025:17:08:46 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:group-pickers/jira.webresources:group-pickers.css HTTP/1.1" 200 244 23 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x236x4 - [10/Sep/2025:17:08:46 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:user-pickers/jira.webresources:user-pickers.js?locale=zh-CN HTTP/1.1" 200 2141 9 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x240x3 - [10/Sep/2025:17:08:46 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:ajax-favourite-control/jira.webresources:ajax-favourite-control.js HTTP/1.1" 200 1443 9 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x243x6 - [10/Sep/2025:17:08:46 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:manageshared/jira.webresources:manageshared.js HTTP/1.1" 200 1834 11 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x239x2 - [10/Sep/2025:17:08:46 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/11.0.21/_/download/batch/com.atlassian.jira.jira-mail-plugin:verifymailserverconnection/com.atlassian.jira.jira-mail-plugin:verifymailserverconnection.js?locale=zh-CN HTTP/1.1" 200 728 13 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x241x4 - [10/Sep/2025:17:08:46 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:share-types/jira.webresources:share-types.js?locale=zh-CN HTTP/1.1" 200 6531 41 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x244x4 - [10/Sep/2025:17:08:46 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 503 40 3 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x238x6 - [10/Sep/2025:17:08:46 +0800] "GET /s/3fb48b109f559af66cfe09d96d3cc250-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:group-label-lozenge/jira.webresources:group-label-lozenge.js?locale=zh-CN HTTP/1.1" 200 655 66 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x242x5 - [10/Sep/2025:17:08:46 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:group-pickers/jira.webresources:group-pickers.js?locale=zh-CN HTTP/1.1" 200 1651 55 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x245x1 - [10/Sep/2025:17:08:46 +0800] "GET /favicon.ico HTTP/1.1" 503 5 3 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1028x246x1 - [10/Sep/2025:17:09:25 +0800] "POST /secure/SetupMailNotifications.jspa HTTP/1.1" 302 - 30110 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x249x3 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/3.0.4/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component.css HTTP/1.1" 200 197 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x247x1 test [10/Sep/2025:17:09:38 +0800] "GET /secure/WelcomeToJIRA.jspa HTTP/1.1" 200 12422 13384 "http://localhost:8080/secure/SetupAdminAccount.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x250x4 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements/com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements.css HTTP/1.1" 200 215 10 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x251x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:loading-sequence/com.atlassian.jira.jira-onboarding-assets-plugin:loading-sequence.css HTTP/1.1" 200 165 12 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x252x4 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:choose-language-component/com.atlassian.jira.jira-onboarding-assets-plugin:choose-language-component.css HTTP/1.1" 200 191 12 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x253x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component.css HTTP/1.1" 200 304 12 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x254x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:avatar-picker-component/com.atlassian.jira.jira-onboarding-assets-plugin:avatar-picker-component.css HTTP/1.1" 200 329 16 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x256x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:calendar-lib/jira.webresources:calendar-lib.css HTTP/1.1" 200 875 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x255x4 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component.css HTTP/1.1" 200 502 13 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x258x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:decorator-resources/com.atlassian.jira.jira-onboarding-assets-plugin:decorator-resources.css HTTP/1.1" 200 379 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x262x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/3.0.4/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib.js HTTP/1.1" 200 314 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x261x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/3.0.4/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component.js?locale=zh-CN HTTP/1.1" 200 2948 22 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x264x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements/com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements.js?locale=zh-CN HTTP/1.1" 200 1481 45 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x265x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:promise/com.atlassian.jira.jira-onboarding-assets-plugin:promise.js HTTP/1.1" 200 19094 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x266x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:sequence-controller-component/com.atlassian.jira.jira-onboarding-assets-plugin:sequence-controller-component.js HTTP/1.1" 200 1193 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x267x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:analytics/com.atlassian.jira.jira-onboarding-assets-plugin:analytics.js HTTP/1.1" 200 460 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x268x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:sequence-common/com.atlassian.jira.jira-onboarding-assets-plugin:sequence-common.js HTTP/1.1" 200 380 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x269x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:loading-sequence/com.atlassian.jira.jira-onboarding-assets-plugin:loading-sequence.js?locale=zh-CN HTTP/1.1" 200 977 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x270x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:choose-language-component/com.atlassian.jira.jira-onboarding-assets-plugin:choose-language-component.js?locale=zh-CN HTTP/1.1" 200 2867 16 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x271x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:avatar-picker-component/com.atlassian.jira.jira-onboarding-assets-plugin:avatar-picker-component.js?locale=zh-CN HTTP/1.1" 200 1822 16 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x260x4 - [10/Sep/2025:17:09:38 +0800] "GET /s/3fb48b109f559af66cfe09d96d3cc250-CDN/-c1s2yr/813007/1am8j4d/aae1242f5fc81cc6a5bb8bc963ccda29/_/download/contextbatch/js/atl.global,-_super/batch.js?locale=zh-CN HTTP/1.1" 200 1054 154 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x263x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements-lib/com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements-lib.js?locale=zh-CN HTTP/1.1" 200 2092 143 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x272x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component.js?locale=zh-CN HTTP/1.1" 200 1942 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x273x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-sequence/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-sequence.js HTTP/1.1" 200 360 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x275x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:jira-first-use-flow-new/com.atlassian.jira.jira-onboarding-assets-plugin:jira-first-use-flow-new.js HTTP/1.1" 200 780 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x276x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:calendar-lib/jira.webresources:calendar-lib.js HTTP/1.1" 200 10689 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x277x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:calendar/jira.webresources:calendar.js HTTP/1.1" 200 512 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x274x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component.js?locale=zh-CN HTTP/1.1" 200 2595 15 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x278x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-project-issuetype-fields/jira.webresources:jira-project-issuetype-fields.js?locale=zh-CN HTTP/1.1" 200 1175 10 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x280x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.jira-onboarding-assets-plugin:decorator-resources/com.atlassian.jira.jira-onboarding-assets-plugin:decorator-resources.js?locale=zh-CN HTTP/1.1" 200 381 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x282x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/-c1s2yr/813007/1am8j4d/_/images/jira-software.png HTTP/1.1" 200 6175 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x283x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:bigpipe-js/jira.webresources:bigpipe-js.js HTTP/1.1" 200 748 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x284x6 - [10/Sep/2025:17:09:38 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:bigpipe-init/jira.webresources:bigpipe-init.js HTTP/1.1" 200 324 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x279x5 - [10/Sep/2025:17:09:38 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-fields/jira.webresources:jira-fields.js?locale=zh-CN HTTP/1.1" 200 5582 36 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x259x4 - [10/Sep/2025:17:09:39 +0800] "GET /s/3cf5ae57d0c5f05e7e48bf37bc002033-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/js/atl.general,jira.global,-_super/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 95496 1067 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x248x2 - [10/Sep/2025:17:09:40 +0800] "GET /s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 52277 1690 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x281x5 test [10/Sep/2025:17:09:40 +0800] "GET /rest/api/1.0/shortcuts/813007/a2718889c0d966db2459b0176d3e1fea/shortcuts.js HTTP/1.1" ************ "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x285x3 test [10/Sep/2025:17:09:40 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 418 459 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x257x5 - [10/Sep/2025:17:09:40 +0800] "GET /s/00ba47a4d897492b93ffc773c10b38b9-CDN/-c1s2yr/813007/1am8j4d/6a79bee72fb49a41f1f4962c4b1699b5/_/download/contextbatch/css/jira.global.look-and-feel,-_super/batch.css HTTP/1.1" 200 2097 2178 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x286x2 - [10/Sep/2025:17:09:40 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-atl.general/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 8052 96 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x289x3 - [10/Sep/2025:17:09:40 +0800] "GET /s/-c1s2yr/813007/1am8j4d/_/images/fav-jsw.png HTTP/1.1" 200 4785 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x288x2 test [10/Sep/2025:17:09:40 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 23 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x287x2 test [10/Sep/2025:17:09:41 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495380255 HTTP/1.1" 200 57 238 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1029x290x1 test [10/Sep/2025:17:09:41 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 339 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
127.0.0.1 1029x292x2 - [10/Sep/2025:17:09:59 +0800] "GET /rest/auditing/latest/statistics/database/usage HTTP/1.1" 200 55 159 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "-"
127.0.0.1 1029x291x1 - [10/Sep/2025:17:09:59 +0800] "GET /rest/gadgets/1.0/g/feed HTTP/1.1" 200 16026 585 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "-"
********** 1031x293x1 test [10/Sep/2025:17:11:06 +0800] "DELETE /rest/api/2/mypreferences?key=jira.user.locale HTTP/1.1" 404 104 735 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x295x2 test [10/Sep/2025:17:11:06 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x294x1 test [10/Sep/2025:17:11:07 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/avatar HTTP/1.1" 200 40 206 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x296x1 test [10/Sep/2025:17:11:09 +0800] "GET /rest/api/latest/user/avatars?username=test&atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin&_=1757495380256 HTTP/1.1" 200 763 45 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x297x1 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10335 HTTP/1.1" 200 824 24 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x300x4 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10337 HTTP/1.1" 200 1046 22 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x299x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10338 HTTP/1.1" 200 1189 22 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x302x6 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10334 HTTP/1.1" 200 1218 24 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x298x2 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10339 HTTP/1.1" 200 1067 21 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x303x2 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10340 HTTP/1.1" 200 1074 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x304x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10341 HTTP/1.1" 200 1448 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x305x4 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10342 HTTP/1.1" 200 890 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x306x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10343 HTTP/1.1" 200 1157 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x307x4 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10344 HTTP/1.1" 200 1115 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x308x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10345 HTTP/1.1" 200 1840 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x309x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10346 HTTP/1.1" 200 1250 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x310x4 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10347 HTTP/1.1" 200 1281 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x301x5 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10336 HTTP/1.1" 200 1279 61 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x311x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10348 HTTP/1.1" 200 1256 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x312x2 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10349 HTTP/1.1" 200 987 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x313x2 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10350 HTTP/1.1" 200 1337 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x314x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10351 HTTP/1.1" 200 1095 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x315x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10352 HTTP/1.1" 200 1164 10 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x316x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10500 HTTP/1.1" 200 1898 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x317x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10501 HTTP/1.1" 200 1453 10 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x319x3 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10503 HTTP/1.1" 200 1058 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x320x4 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10505 HTTP/1.1" 200 1250 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x322x6 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10506 HTTP/1.1" 200 1652 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x321x5 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10504 HTTP/1.1" 200 2328 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x318x2 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10502 HTTP/1.1" 200 886 12 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x323x6 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10507 HTTP/1.1" 200 2380 7 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x324x2 test [10/Sep/2025:17:11:09 +0800] "GET /secure/useravatar?avatarId=10508 HTTP/1.1" 200 1399 10 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x325x1 test [10/Sep/2025:17:11:11 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 44 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x326x1 test [10/Sep/2025:17:11:26 +0800] "PUT /rest/api/latest/user/avatar?username=test&atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin HTTP/1.1" 204 - 76 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x327x1 test [10/Sep/2025:17:11:26 +0800] "GET /secure/useravatar?avatarId=10336&size=small HTTP/1.1" 200 1279 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x328x1 test [10/Sep/2025:17:11:26 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x331x3 test [10/Sep/2025:17:11:28 +0800] "GET /download/resources/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component/cyoa-create.svg HTTP/1.1" 200 2680 3 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x330x2 test [10/Sep/2025:17:11:28 +0800] "GET /download/resources/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component/cyoa-search.svg HTTP/1.1" 200 2989 3 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x332x4 test [10/Sep/2025:17:11:28 +0800] "GET /download/resources/com.atlassian.jira.jira-onboarding-assets-plugin:choose-your-own-adventure-component/cyoa-import.svg HTTP/1.1" 200 2370 3 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x329x1 test [10/Sep/2025:17:11:28 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/cyoa HTTP/1.1" 200 40 83 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x333x1 test [10/Sep/2025:17:11:31 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x334x1 test [10/Sep/2025:17:11:36 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 43 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x338x4 test [10/Sep/2025:17:11:55 +0800] "GET /images/icons/wait.gif HTTP/1.1" 200 1553 12 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x336x2 test [10/Sep/2025:17:11:56 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/cyoa:return HTTP/1.1" 200 40 293 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x337x3 test [10/Sep/2025:17:11:56 +0800] "GET /rest/project-templates/1.0/templates?_=1757495380257 HTTP/1.1" 200 2984 289 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x339x2 - [10/Sep/2025:17:11:56 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:basic-software-development-template/icon.png HTTP/1.1" 200 6123 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x343x6 - [10/Sep/2025:17:11:56 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira-core-task-management/icon.svg HTTP/1.1" 200 3119 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x340x3 - [10/Sep/2025:17:11:56 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:gh-scrum-template/icon.png HTTP/1.1" 200 6475 10 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x342x5 - [10/Sep/2025:17:11:56 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira************************/icon.svg HTTP/1.1" 200 3587 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x341x4 - [10/Sep/2025:17:11:56 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:gh-kanban-template/icon.png HTTP/1.1" 200 2469 11 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x344x3 - [10/Sep/2025:17:11:56 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira-core-process-management/icon.svg HTTP/1.1" 200 2425 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x335x1 test [10/Sep/2025:17:11:56 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/complete HTTP/1.1" 200 40 331 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1031x345x1 test [10/Sep/2025:17:11:56 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x346x1 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira************************resources/images/icons/svg/subtask.svg HTTP/1.1" 200 1172 7 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x349x3 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:project-template-images/images/issuetypes/svg/improvement.svg HTTP/1.1" 200 1245 7 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x350x5 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:project-template-images/images/issuetypes/svg/epic.svg HTTP/1.1" 200 1646 8 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x348x4 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira************************resources/images/icons/svg/bug.svg HTTP/1.1" 200 1040 8 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x347x2 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira************************resources/images/icons/svg/newfeature.svg HTTP/1.1" 200 1280 9 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x351x6 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/7.0.5/_/download/resources/com.atlassian.jira-core-project-templates:jira************************resources/images/icons/svg/task.svg HTTP/1.1" 200 1162 10 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x352x2 - [10/Sep/2025:17:12:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:project-template-images/images/workflows/project-template-software-dev-workflow.svg HTTP/1.1" 200 4543 4 "http://localhost:8080/s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/4e75b4be6a9acd5af3fad979058f0511/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x353x1 test [10/Sep/2025:17:12:06 +0800] "GET /rest/api/latest/project?_=1757495380258 HTTP/1.1" 200 44 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x354x2 - [10/Sep/2025:17:12:06 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.13.7/_/download/resources/com.pyxis.greenhopper.jira:basic-software-development-template/backgroundIcon.gif HTTP/1.1" 200 1118 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x355x1 test [10/Sep/2025:17:12:06 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x356x1 - [10/Sep/2025:17:12:11 +0800] "GET /s/-c1s2yr/813007/1am8j4d/1.0/_/images/icons/wait.gif HTTP/1.1" 200 1553 2 "http://localhost:8080/s/e5c1649af5514387325e68e3a17f02ee-CDN/-c1s2yr/813007/1am8j4d/02b7c52c8ae4a017e743a4da5996c5bb/_/download/contextbatch/css/_super/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x357x2 test [10/Sep/2025:17:12:11 +0800] "POST /rest/project-templates/1.0/templates HTTP/1.1" 400 173 60 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x358x1 test [10/Sep/2025:17:12:11 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 6 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x359x1 test [10/Sep/2025:17:12:52 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x362x3 test [10/Sep/2025:17:12:54 +0800] "GET /rest/project-templates/1.0/templates?_=1757495380259 HTTP/1.1" 200 2984 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x360x1 test [10/Sep/2025:17:12:55 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/complete HTTP/1.1" 200 40 1151 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x361x2 test [10/Sep/2025:17:12:55 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/cyoa:return HTTP/1.1" 200 40 1194 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1032x363x1 test [10/Sep/2025:17:12:57 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x364x1 test [10/Sep/2025:17:13:04 +0800] "GET /rest/api/latest/project?_=1757495380260 HTTP/1.1" 200 44 3 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x365x1 test [10/Sep/2025:17:13:07 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x366x1 test [10/Sep/2025:17:13:11 +0800] "GET /rest/api/latest/projectvalidate/key?key=AB&_=1757495380261 HTTP/1.1" 200 69 14 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x367x1 test [10/Sep/2025:17:13:12 +0800] "GET /rest/api/latest/projectvalidate/key?key=ABC&_=1757495380262 HTTP/1.1" 200 69 5 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x368x1 test [10/Sep/2025:17:13:12 +0800] "GET /rest/api/latest/projectvalidate/key?key=ABCD&_=1757495380263 HTTP/1.1" 200 69 3 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x369x1 test [10/Sep/2025:17:13:29 +0800] "POST /rest/project-templates/1.0/templates HTTP/1.1" 200 123 13378 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x373x4 - [10/Sep/2025:17:13:29 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:calendar-localisation-moment/jira.webresources:calendar-localisation-moment.js HTTP/1.1" 200 399 5 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x375x6 - [10/Sep/2025:17:13:29 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:calendar-zh/jira.webresources:calendar-zh.js HTTP/1.1" 200 1736 7 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x374x5 - [10/Sep/2025:17:13:29 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:calendar-en/jira.webresources:calendar-en.js HTTP/1.1" 200 1818 8 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x376x4 test [10/Sep/2025:17:13:29 +0800] "GET /rest/api/1.0/shortcuts/813007/a2718889c0d966db2459b0176d3e1fea/shortcuts.js?context=issuenavigation&context=issueaction HTTP/1.1" 200 1094 9 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x377x4 - [10/Sep/2025:17:13:30 +0800] "GET /s/8eb377e3df76350b206a69613307aaa5-CDN/-c1s2yr/813007/1am8j4d/01453d8e890d681d73ebdf3ee889c580/_/download/contextbatch/js/com.atlassian.jira.projects.sidebar.init,-_super,-jira.view.issue,-project.issue.navigator/batch.js?jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 6080 92 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x379x5 test [10/Sep/2025:17:13:30 +0800] "GET /secure/useravatar?size=small&avatarId=10336 HTTP/1.1" 200 1279 9 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x378x5 - [10/Sep/2025:17:13:30 +0800] "GET /s/bf0b011b4c100f79e3cec3958c04c67e-CDN/-c1s2yr/813007/1am8j4d/01453d8e890d681d73ebdf3ee889c580/_/download/contextbatch/css/com.atlassian.jira.projects.sidebar.init,-_super,-jira.view.issue,-project.issue.navigator/batch.css?jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 8269 520 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x371x2 - [10/Sep/2025:17:13:31 +0800] "GET /s/b443ffbb152858d7297fd117b9afb0e9-CDN/-c1s2yr/813007/1am8j4d/b7a8daa930c69e63322fa74553887ca5/_/download/contextbatch/css/project.issue.navigator,jira.view.issue,jira.global,atl.general,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 91675 1345 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x380x3 test [10/Sep/2025:17:13:31 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 16 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x372x3 - [10/Sep/2025:17:13:31 +0800] "GET /s/46f1da5c972f5f1858565817e30a0c10-CDN/-c1s2yr/813007/1am8j4d/b7a8daa930c69e63322fa74553887ca5/_/download/contextbatch/js/project.issue.navigator,jira.view.issue,jira.global,atl.general,-_super/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 *********** "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x381x2 test [10/Sep/2025:17:13:31 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 465 11 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x382x2 - [10/Sep/2025:17:13:31 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-project.issue.navigator,-jira.view.issue,-atl.general/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 8052 59 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x384x3 test [10/Sep/2025:17:13:31 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 773 92 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x388x6 - [10/Sep/2025:17:13:31 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/tinymce-amd.js?batch=false HTTP/1.1" 200 104 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x389x6 - [10/Sep/2025:17:13:31 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/themes/modern/theme.js?batch=false HTTP/1.1" 200 5263 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x386x4 - [10/Sep/2025:17:13:31 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/tinymce.js?batch=false HTTP/1.1" 200 155048 22 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x387x5 - [10/Sep/2025:17:13:32 +0800] "GET /s/67473fee8454e64522f9bf4fbd0a6087-CDN/-c1s2yr/813007/1am8j4d/16841d75d29cf8f0c3cea22ad5c1e71d/_/download/contextbatch/js/jira.rich.editor.api,jira.project.sidebar,jira.rich.editor,-_super,-jira.view.issue,-jira.global,-atl.general,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 71798 528 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x385x3 - [10/Sep/2025:17:13:32 +0800] "GET /s/90643dea748af73fdb65b136fddf2127-CDN/-c1s2yr/813007/1am8j4d/16841d75d29cf8f0c3cea22ad5c1e71d/_/download/contextbatch/css/jira.rich.editor.api,jira.project.sidebar,jira.rich.editor,-_super,-jira.view.issue,-jira.global,-atl.general,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 61235 1048 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x383x3 test [10/Sep/2025:17:13:33 +0800] "GET /rest/inline-create/1.0/context/bootstrap?query=project%20%3D%20%22ABCD%22%20AND%20resolution%20%3D%20Unresolved%20ORDER%20BY%20priority%20DESC%2C%20updated%20DESC&&_=1757495611198 HTTP/1.1" ************ "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x390x2 test [10/Sep/2025:17:13:33 +0800] "GET /secure/viewavatar?size=xsmall&avatarId=10303&avatarType=issuetype HTTP/1.1" 200 335 6 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x370x1 - [10/Sep/2025:17:13:34 +0800] "GET /browse/ABCD HTTP/1.1" 200 13748 5050 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x391x1 test [10/Sep/2025:17:13:34 +0800] "GET /secure/projectavatar?avatarId=10324 HTTP/1.1" 200 1575 10 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x394x3 test [10/Sep/2025:17:13:34 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495611199 HTTP/1.1" 200 428 24 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x392x1 test [10/Sep/2025:17:13:34 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 487 69 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x395x2 - [10/Sep/2025:17:13:34 +0800] "GET /s/b927756b58089a45e3f5cbb45a805b5b-CDN/-c1s2yr/813007/1am8j4d/e3cd17bc00b1765e59ab08e45fd9d30f/_/download/contextbatch/css/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-jira.view.issue,-jira.global,-atl.general,-project.issue.navigator,-jira.rich.editor.api/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 3717 28 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x393x2 test [10/Sep/2025:17:13:34 +0800] "PUT /rest/projects/1.0/project/ABCD/lastVisited HTTP/1.1" 200 40 179 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x396x3 - [10/Sep/2025:17:13:34 +0800] "GET /s/b9ba08bd5e21c971822ac46e4b818cfc-CDN/-c1s2yr/813007/1am8j4d/e3cd17bc00b1765e59ab08e45fd9d30f/_/download/contextbatch/js/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-jira.view.issue,-jira.global,-atl.general,-project.issue.navigator,-jira.rich.editor.api/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 29496 209 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x397x1 test [10/Sep/2025:17:13:35 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 5 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x398x1 test [10/Sep/2025:17:13:37 +0800] "POST /rest/troubleshooting/1.0/dismissNotification HTTP/1.1" 204 - 332 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x399x1 test [10/Sep/2025:17:13:37 +0800] "GET /rest/troubleshooting/1.0/dismissNotification/test/1?_=1757495611200 HTTP/1.1" 415 40 7 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x400x1 test [10/Sep/2025:17:13:39 +0800] "POST /rest/helptips/1.0/tips HTTP/1.1" 204 - 224 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x401x1 test [10/Sep/2025:17:13:40 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 6 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x402x1 test [10/Sep/2025:17:13:45 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 44 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x403x1 test [10/Sep/2025:17:13:45 +0800] "GET /secure/admin/user/UserBrowser.jspa HTTP/1.1" 200 12449 327 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x407x5 test [10/Sep/2025:17:13:45 +0800] "GET /download/contextbatch/js/jira.admin.userbrowser,atl.admin,jira.global,jira.admin,-_super/batch.js?agile_global_admin_condition=true&cache=false&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 2356 9 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x409x6 test [10/Sep/2025:17:13:45 +0800] "GET /rest/api/1.0/shortcuts/813007/a2718889c0d966db2459b0176d3e1fea/shortcuts.js?context=admin HTTP/1.1" 200 556 10 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x410x6 test [10/Sep/2025:17:13:45 +0800] "GET /secure/useravatar?size=xsmall&avatarId=10336 HTTP/1.1" 200 1279 8 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x404x2 - [10/Sep/2025:17:13:45 +0800] "GET /s/50181fc744c1c0e9b8ff7f8fd41c805b-CDN/-c1s2yr/813007/1am8j4d/37e028c03fe40894f5805085bc8a2652/_/download/contextbatch/css/browse-user,-_super/batch.css HTTP/1.1" 200 831 39 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x406x4 - [10/Sep/2025:17:13:45 +0800] "GET /s/16398900bcd76b0a962d571cc6460b13-CDN/-c1s2yr/813007/1am8j4d/37e028c03fe40894f5805085bc8a2652/_/download/contextbatch/js/browse-user,-_super/batch.js?locale=zh-CN HTTP/1.1" 200 12251 90 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x405x3 - [10/Sep/2025:17:13:45 +0800] "GET /s/5c2414df7a33fcb5b1cb6d10d82aa0cf-CDN/-c1s2yr/813007/1am8j4d/8bdc62a9191b9f450924109e1a1b1e3f/_/download/contextbatch/css/jira.admin.userbrowser,atl.admin,jira.global,jira.admin,-_super/batch.css?agile_global_admin_condition=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 51627 117 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x411x2 test [10/Sep/2025:17:13:45 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 10 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x408x6 - [10/Sep/2025:17:13:46 +0800] "GET /s/a3441c4d25b105da121ef1d82b2c2624-CDN/-c1s2yr/813007/1am8j4d/8bdc62a9191b9f450924109e1a1b1e3f/_/download/contextbatch/js/jira.admin.userbrowser,atl.admin,jira.global,jira.admin,-_super/batch.js?agile_global_admin_condition=true&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 71045 509 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x412x1 test [10/Sep/2025:17:13:46 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 408 8 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x415x3 test [10/Sep/2025:17:13:46 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495625778 HTTP/1.1" 200 57 22 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x413x1 - [10/Sep/2025:17:13:46 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-atl.admin/batch.js?agile_global_admin_condition=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 8052 65 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x416x2 test [10/Sep/2025:17:13:46 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x414x2 test [10/Sep/2025:17:13:47 +0800] "GET /rest/plugins/1.0/notifications?_=1757495625777 HTTP/1.1" 200 3960 1618 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x417x1 test [10/Sep/2025:17:13:47 +0800] "GET /rest/plugins/1.0/notifications/JIRAUSER10000?_=1757495625779 HTTP/1.1" 200 5878 7 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x418x1 - [10/Sep/2025:17:13:47 +0800] "GET /s/-c1s2yr/813007/1am8j4d/4.1.8/_/download/resources/com.atlassian.upm.atlassian-************************-plugin:upm-web-resources//images/plugin-icon-default.png HTTP/1.1" 200 2276 3 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x419x1 - [10/Sep/2025:17:13:47 +0800] "GET /s/-c1s2yr/813007/1am8j4d/4.1.8/_/download/resources/com.atlassian.upm.atlassian-************************-plugin%3Aupm-web-resources/images/marketplace-icon-sprite.png HTTP/1.1" 200 1563 4 "http://localhost:8080/s/5c2414df7a33fcb5b1cb6d10d82aa0cf-CDN/-c1s2yr/813007/1am8j4d/8bdc62a9191b9f450924109e1a1b1e3f/_/download/contextbatch/css/jira.admin.userbrowser,atl.admin,jira.global,jira.admin,-_super/batch.css?agile_global_admin_condition=true&healthcheck-resources=true&jag=true&jaguser=true" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x420x1 test [10/Sep/2025:17:13:48 +0800] "POST /rest/helptips/1.0/tips HTTP/1.1" 204 - 80 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x421x1 test [10/Sep/2025:17:13:49 +0800] "POST /rest/onboarding/1/postsetup_announcement/announced HTTP/1.1" 200 40 222 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x422x1 test [10/Sep/2025:17:13:50 +0800] "PUT /rest/flags/1.0/flags/com.atlassian.jira.reindex.required/dismiss HTTP/1.1" 204 - 260 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x423x1 test [10/Sep/2025:17:13:51 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x424x1 test [10/Sep/2025:17:13:52 +0800] "GET /secure/admin/user/AddUser!default.jspa HTTP/1.1" 200 10972 122 "http://localhost:8080/secure/admin/user/UserBrowser.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x426x2 test [10/Sep/2025:17:13:52 +0800] "GET /download/contextbatch/js/atl.admin,jira.global,jira.admin,-_super/batch.js?agile_global_admin_condition=true&cache=false&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 2356 7 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x425x2 - [10/Sep/2025:17:13:52 +0800] "GET /s/a9edae5261c258a1aca2cd4c25020141-CDN/-c1s2yr/813007/1am8j4d/be545533d5568655a04bc8e26cf7c074/_/download/contextbatch/css/atl.admin,jira.global,jira.admin,-_super/batch.css?agile_global_admin_condition=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 51243 98 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x428x2 test [10/Sep/2025:17:13:52 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 10 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x427x3 - [10/Sep/2025:17:13:53 +0800] "GET /s/4922951bac6d7b9527d1329250db8149-CDN/-c1s2yr/813007/1am8j4d/be545533d5568655a04bc8e26cf7c074/_/download/contextbatch/js/atl.admin,jira.global,jira.admin,-_super/batch.js?agile_global_admin_condition=true&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 68539 437 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x429x1 test [10/Sep/2025:17:13:53 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 408 14 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x430x1 test [10/Sep/2025:17:13:53 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495632889 HTTP/1.1" 200 57 8 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1033x431x1 test [10/Sep/2025:17:13:53 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x432x1 test [10/Sep/2025:17:14:25 +0800] "POST /secure/admin/user/AddUser.jspa HTTP/1.1" 302 - 1027 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x433x1 test [10/Sep/2025:17:14:25 +0800] "GET /secure/admin/user/UserBrowser.jspa?createdUser=admin HTTP/1.1" 200 12754 121 "http://localhost:8080/secure/admin/user/AddUser!default.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x434x1 test [10/Sep/2025:17:14:25 +0800] "GET /download/contextbatch/js/jira.admin.userbrowser,atl.admin,jira.global,jira.admin,-_super/batch.js?agile_global_admin_condition=true&cache=false&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 2356 7 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x435x1 test [10/Sep/2025:17:14:25 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 408 6 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x436x1 test [10/Sep/2025:17:14:25 +0800] "GET /rest/plugins/1.0/notifications?_=1757495665401 HTTP/1.1" 200 3960 9 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x439x3 test [10/Sep/2025:17:14:25 +0800] "GET /rest/plugins/1.0/notifications/JIRAUSER10000?_=1757495665403 HTTP/1.1" 200 5878 5 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x437x2 test [10/Sep/2025:17:14:25 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495665402 HTTP/1.1" 200 57 21 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x438x3 test [10/Sep/2025:17:14:25 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 15 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x440x1 test [10/Sep/2025:17:14:25 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x441x1 test [10/Sep/2025:17:14:55 +0800] "GET /secure/admin/ViewApplicationProperties.jspa HTTP/1.1" 200 13622 134 "http://localhost:8080/secure/admin/user/UserBrowser.jspa?createdUser=admin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x442x1 test [10/Sep/2025:17:14:55 +0800] "GET /download/contextbatch/js/atl.admin,jira.global,jira.admin,-_super/batch.js?agile_global_admin_condition=true&cache=false&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 2356 9 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x443x1 test [10/Sep/2025:17:14:55 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 408 10 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x444x1 test [10/Sep/2025:17:14:55 +0800] "GET /rest/plugins/1.0/notifications?_=1757495695568 HTTP/1.1" 200 3960 6 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x447x3 test [10/Sep/2025:17:14:55 +0800] "GET /rest/plugins/1.0/notifications/JIRAUSER10000?_=1757495695570 HTTP/1.1" 200 5878 6 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x445x2 test [10/Sep/2025:17:14:55 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495695569 HTTP/1.1" 200 57 16 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x446x2 test [10/Sep/2025:17:14:55 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 10 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1034x448x1 test [10/Sep/2025:17:14:56 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 49 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
127.0.0.1 1035x452x4 - [10/Sep/2025:17:15:00 +0800] "GET /rest/gadgets/1.0/g/messagebundle/und/gadget.common%2Cgadget.issuetable%2Cgadget.assignedtome%2Cgadget.issuetable.common HTTP/1.1" 200 2832 23 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "-"
127.0.0.1 1035x453x4 - [10/Sep/2025:17:15:00 +0800] "GET /rest/gadgets/1.0/g/messagebundle/zh_CN/gadget.common%2Cgadget.issuetable%2Cgadget.assignedtome%2Cgadget.issuetable.common HTTP/1.1" 200 3403 14 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "-"
127.0.0.1 1035x454x4 - [10/Sep/2025:17:15:00 +0800] "GET /rest/gadgets/1.0/g/messagebundle/und/gadget.common%2Cgadget.activity.stream HTTP/1.1" 200 4042 7 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "-"
127.0.0.1 1035x455x4 - [10/Sep/2025:17:15:00 +0800] "GET /rest/gadgets/1.0/g/messagebundle/zh_CN/gadget.common%2Cgadget.activity.stream HTTP/1.1" 200 4722 7 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "-"
********** 1034x449x1 test [10/Sep/2025:17:15:00 +0800] "GET /secure/MyJiraHome.jspa HTTP/1.1" 200 11938 921 "http://localhost:8080/secure/admin/ViewApplicationProperties.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x450x2 - [10/Sep/2025:17:15:01 +0800] "GET /s/92ac6d27658c149c9b82452d3f6d5a96-CDN/-c1s2yr/813007/1am8j4d/083c3ff001c7e1c5a3bf39da08e6104a/_/download/contextbatch/css/atl.dashboard,jira.global,atl.general,jira.dashboard,-_super/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 *********** "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x456x2 test [10/Sep/2025:17:15:01 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 10 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x451x3 - [10/Sep/2025:17:15:02 +0800] "GET /s/d055b2390553310098d1f69b6561283c-CDN/-c1s2yr/813007/1am8j4d/083c3ff001c7e1c5a3bf39da08e6104a/_/download/contextbatch/js/atl.dashboard,jira.global,atl.general,jira.dashboard,-_super/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 *********** "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x457x1 test [10/Sep/2025:17:15:02 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 425 9 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x463x6 test [10/Sep/2025:17:15:02 +0800] "GET /images/intro-illustration.png HTTP/1.1" 200 33862 3 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x462x5 test [10/Sep/2025:17:15:02 +0800] "POST /plugins/servlet/gadgets/dashboard-diagnostics HTTP/1.1" 200 276 17 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x465x7 test [10/Sep/2025:17:15:02 +0800] "GET /rest/troubleshooting/1.0/check/test?_=1757495701898 HTTP/1.1" 200 57 11 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x458x1 - [10/Sep/2025:17:15:02 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-atl.dashboard,-atl.general/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 8052 72 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x466x5 test [10/Sep/2025:17:15:03 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 545 37 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x468x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/b927756b58089a45e3f5cbb45a805b5b-CDN/-c1s2yr/813007/1am8j4d/e7bd201ab3e385f17c37cbd771cccc6e/_/download/contextbatch/css/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-atl.dashboard,-jira.global,-atl.general/batch.css?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true HTTP/1.1" 200 3948 37 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x469x7 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1cc9abd68c6ca782e55c31c5ae89771d/_/download/contextbatch/js/com.atlassian.jira.plugins.jira-development-integration-plugin:8,-_super/batch.js?locale=zh-CN HTTP/1.1" 200 839 39 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x467x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1cc9abd68c6ca782e55c31c5ae89771d/_/download/contextbatch/css/com.atlassian.jira.plugins.jira-development-integration-plugin:8,-_super/batch.css HTTP/1.1" 200 147 57 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x461x4 test [10/Sep/2025:17:15:03 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12312 148 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x464x7 test [10/Sep/2025:17:15:03 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12314 146 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x460x3 test [10/Sep/2025:17:15:03 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12308 158 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x472x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:icons/jira.webresources:icons.css HTTP/1.1" 200 2982 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x474x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:list-styles/jira.webresources:list-styles.css HTTP/1.1" 200 773 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x471x3 test [10/Sep/2025:17:15:03 +0800] "GET /plugins/servlet/gadgets/js/auth-refresh.js?v=5d172772d3da3e73329fba55c6e2a2bd&container=atlassian&debug=0 HTTP/1.1" 200 8108 12 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x476x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:inline-layer/jira.webresources:inline-layer.css HTTP/1.1" 200 311 5 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x475x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:dropdown/jira.webresources:dropdown.css HTTP/1.1" 200 188 5 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x477x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.lozenge/com.atlassian.auiplugin:split_aui.pattern.lozenge.css HTTP/1.1" 200 461 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x480x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00.css HTTP/1.1" 200 521 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x478x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00.css HTTP/1.1" 200 487 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x479x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/ca6f1534023a7bdcd8ee4e5cf856fdd0-CDN/-c1s2yr/813007/1am8j4d/2.1.1/_/download/batch/com.atlassian.plugins.issue-status-plugin:issue-status-resources/com.atlassian.plugins.issue-status-plugin:issue-status-resources.css HTTP/1.1" 200 1725 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x483x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f.css HTTP/1.1" 200 506 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x482x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-singleselect/jira.webresources:frother-singleselect.css HTTP/1.1" 200 1125 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x481x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-queryable-dropdown-select/jira.webresources:frother-queryable-dropdown-select.css HTTP/1.1" 200 790 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x484x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-checkbox-multiselect/jira.webresources:frother-checkbox-multiselect.css HTTP/1.1" 200 1270 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x485x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:select-pickers/jira.webresources:select-pickers.css HTTP/1.1" 200 226 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x486x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-multiselect/jira.webresources:frother-multiselect.css HTTP/1.1" 200 1398 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x487x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/5a8934dddef6166c6cde34def237b72e-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.gadgets:core-gadget-resources/com.atlassian.jira.gadgets:core-gadget-resources.css HTTP/1.1" 200 3090 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x490x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:aui-extensions/jira.webresources:aui-extensions.js HTTP/1.1" 200 316 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x491x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-events/jira.webresources:jira-events.js HTTP/1.1" 200 839 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x492x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.behaviour.key-code/com.atlassian.auiplugin:split_aui.behaviour.key-code.js?locale=zh-CN HTTP/1.1" 200 410 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x493x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-key-code/jira.webresources:jira-key-code.js HTTP/1.1" 200 266 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x494x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-urlhelpers/jira.webresources:jira-urlhelpers.js HTTP/1.1" 200 335 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x489x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/641889ef0650de7c6579bf6f654582dc/_/download/contextbatch/js/jira.webresources:aui-extensions-experimental/batch.js HTTP/1.1" 200 318 25 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x495x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-smart-ajax/jira.webresources:jira-smart-ajax.js?locale=zh-CN HTTP/1.1" 200 1697 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x496x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-objects/jira.webresources:jira-objects.js HTTP/1.1" 200 482 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x497x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:keyboard-event-extensions/jira.webresources:keyboard-event-extensions.js HTTP/1.1" 200 801 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x498x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-base-control/jira.webresources:jira-base-control.js HTTP/1.1" 200 1716 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x499x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:browser-properties/jira.webresources:browser-properties.js HTTP/1.1" 200 1125 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x500x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:top-same-origin-window/jira.webresources:top-same-origin-window.js HTTP/1.1" 200 330 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x501x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:escape-css-selector-polyfill/jira.webresources:escape-css-selector-polyfill.js HTTP/1.1" 200 415 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x473x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/css/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.css HTTP/1.1" 200 34014 75 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x502x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jquery-escape-selector-polyfill/jira.webresources:jquery-escape-selector-polyfill.js HTTP/1.1" 200 317 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x504x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:wrm-context-path/jira.webresources:wrm-context-path.js HTTP/1.1" 200 280 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x503x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:legacy-table-utils/jira.webresources:legacy-table-utils.js HTTP/1.1" 200 552 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x505x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:cookie-1.0.0/com.atlassian.plugin.jslibs:cookie-1.0.0.js HTTP/1.1" 200 814 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x507x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:cookie/jira.webresources:cookie.js HTTP/1.1" 200 355 5 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x508x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.vendor.backbone/com.atlassian.auiplugin:split_aui.vendor.backbone.js?locale=zh-CN HTTP/1.1" 200 512 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x509x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:backbone-1.0.0-factory/com.atlassian.plugin.jslibs:backbone-1.0.0-factory.js HTTP/1.1" 200 6661 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x511x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:backbone-1.0.0/jira.webresources:backbone-1.0.0.js HTTP/1.1" 200 321 1 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x510x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:backbone-queryparams-lib/jira.webresources:backbone-queryparams-lib.js HTTP/1.1" 200 2136 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x513x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jira-data-stores/jira.webresources:jira-data-stores.js HTTP/1.1" 200 572 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x506x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:util-lite/jira.webresources:util-lite.js?locale=zh-CN HTTP/1.1" 200 6417 15 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x512x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/5.0.0/_/download/batch/com.atlassian.soy.soy-template-plugin:soy-deps/com.atlassian.soy.soy-template-plugin:soy-deps.js HTTP/1.1" 200 12676 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x514x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:parse-uri/jira.webresources:parse-uri.js HTTP/1.1" 200 882 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x515x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:number-formatter/jira.webresources:number-formatter.js HTTP/1.1" 200 281 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x518x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jquery-plugin-scrollintoview/jira.webresources:jquery-plugin-scrollintoview.js HTTP/1.1" 200 934 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x517x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:content-retrievers/jira.webresources:content-retrievers.js HTTP/1.1" 200 1319 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x516x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:util/jira.webresources:util.js?locale=zh-CN HTTP/1.1" 200 4691 7 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x521x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:jquery-plugin-textoverflow/jira.webresources:jquery-plugin-textoverflow.js HTTP/1.1" 200 1028 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x519x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:inline-layer/jira.webresources:inline-layer.js HTTP/1.1" 200 4712 5 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x522x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:feature-flags/jira.webresources:feature-flags.js HTTP/1.1" 200 403 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x523x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:dropdown/jira.webresources:dropdown.js HTTP/1.1" 200 4722 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x524x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.pattern.lozenge/com.atlassian.auiplugin:split_aui.pattern.lozenge.js?locale=zh-CN HTTP/1.1" 200 421 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x520x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:list/jira.webresources:list.js?locale=zh-CN HTTP/1.1" 200 6774 12 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x525x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--be1eb78c1a/com.atlassian.auiplugin:split_aui.splitchunk.vendors--be1eb78c1a.js?locale=zh-CN HTTP/1.1" 200 2244 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x526x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.vendors--23f50a6f00.js?locale=zh-CN HTTP/1.1" 200 333 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x527x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.be1eb78c1a/com.atlassian.auiplugin:split_aui.splitchunk.be1eb78c1a.js?locale=zh-CN HTTP/1.1" 200 724 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x528x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00/com.atlassian.auiplugin:split_aui.splitchunk.23f50a6f00.js?locale=zh-CN HTTP/1.1" 200 378 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x529x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.tooltip/com.atlassian.auiplugin:split_aui.component.tooltip.js?locale=zh-CN HTTP/1.1" 200 402 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x531x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:select-model/jira.webresources:select-model.js HTTP/1.1" 200 3443 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x533x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f/com.atlassian.auiplugin:split_aui.splitchunk.57d1ac075f.js?locale=zh-CN HTTP/1.1" 200 394 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x530x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/3fb48b109f559af66cfe09d96d3cc250-CDN/-c1s2yr/813007/1am8j4d/2.1.1/_/download/batch/com.atlassian.plugins.issue-status-plugin:issue-status-resources/com.atlassian.plugins.issue-status-plugin:issue-status-resources.js?locale=zh-CN HTTP/1.1" 200 1278 6 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x532x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-select-handlers/jira.webresources:frother-select-handlers.js?locale=zh-CN HTTP/1.1" 200 2635 7 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x534x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.splitchunk.86ad04cc86/com.atlassian.auiplugin:split_aui.splitchunk.86ad04cc86.js?locale=zh-CN HTTP/1.1" 200 708 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x535x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.component.spinner/com.atlassian.auiplugin:split_aui.component.spinner.js?locale=zh-CN HTTP/1.1" 200 440 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x536x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/9.1.4/_/download/batch/com.atlassian.auiplugin:split_aui.deprecated.behaviour.jquery-spin/com.atlassian.auiplugin:split_aui.deprecated.behaviour.jquery-spin.js?locale=zh-CN HTTP/1.1" 200 490 2 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x538x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-singleselect/jira.webresources:frother-singleselect.js?locale=zh-CN HTTP/1.1" 200 3412 8 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x537x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-queryable-dropdown-select/jira.webresources:frother-queryable-dropdown-select.js?locale=zh-CN HTTP/1.1" 200 3420 10 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x540x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-checkbox-multiselect/jira.webresources:frother-checkbox-multiselect.js?locale=zh-CN HTTP/1.1" 200 3406 5 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x539x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:frother-multiselect/jira.webresources:frother-multiselect.js?locale=zh-CN HTTP/1.1" 200 4122 12 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x541x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:select-pickers/jira.webresources:select-pickers.js?locale=zh-CN HTTP/1.1" 200 2191 6 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x542x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:init-on-dcl/jira.webresources:init-on-dcl.js HTTP/1.1" 200 268 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x459x2 test [10/Sep/2025:17:15:03 +0800] "GET /rest/gadget/1.0/issueTable/jql?num=10&tableContext=jira.table.cols.dashboard&addDefault=true&enableSorting=true&paging=true&showActions=true&jql=assignee+%3D+currentUser()+AND+resolution+%3D+unresolved+ORDER+BY+priority+DESC%2C+created+ASC&sortBy=&startIndex=0&_=1757495701897 HTTP/1.1" *********** "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x543x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/8.13.7/_/download/batch/com.atlassian.jira.gadgets:core-gadget-resources/com.atlassian.jira.gadgets:core-gadget-resources.js?locale=zh-CN HTTP/1.1" 200 4115 11 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x470x8 - [10/Sep/2025:17:15:03 +0800] "GET /s/b9ba08bd5e21c971822ac46e4b818cfc-CDN/-c1s2yr/813007/1am8j4d/e7bd201ab3e385f17c37cbd771cccc6e/_/download/contextbatch/js/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-atl.dashboard,-jira.global,-atl.general/batch.js?agile_global_admin_condition=true&baseurl-check-resources=true&healthcheck-resources=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 35837 269 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x544x2 test [10/Sep/2025:17:15:03 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x488x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/125df8a4596843cfed1da17871db27f2-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/js/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.js?locale=zh-CN HTTP/1.1" 200 204072 582 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x549x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams:streamsWebResources/images/throbber.gif HTTP/1.1" 200 1565 5 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/css/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x548x4 - [10/Sep/2025:17:15:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams:streamsWebResources/images/gadget-loading.gif HTTP/1.1" 200 6623 6 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/css/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x550x6 - [10/Sep/2025:17:15:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams:streamsWebResources/images/feed-icon.png HTTP/1.1" 200 776 7 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/css/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x551x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams:streamsWebResources/images/full-view-icon.png HTTP/1.1" 200 313 5 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/css/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x552x5 - [10/Sep/2025:17:15:03 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams:streamsWebResources/images/list-view-icon.png HTTP/1.1" 200 333 5 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/b030a7ff1d5e96266f184f74b647e9d0/_/download/contextbatch/css/jira.webresources:almond,jira.webresources:aui-core-amd-shim,jira.webresources:jira-metadata,jira.webresources:jquery-livestamp,com.atlassian.analytics.analytics-client:js-events,com.atlassian.gadgets.publisher:ajs-gadgets,com.atlassian.streams:streamsGadgetResources,com.atlassian.auiplugin:ajs-underscorejs,com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x547x3 test [10/Sep/2025:17:15:04 +0800] "GET /plugins/servlet/streams?maxResults=5&relativeLinks=true&_=1757495703730 HTTP/1.1" 200 481 409 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
127.0.0.1 1035x553x3 test [10/Sep/2025:17:15:04 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 415 40 18 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "hpo79w"
********** 1035x545x1 test [10/Sep/2025:17:15:04 +0800] "POST /plugins/servlet/gadgets/makeRequest HTTP/1.1" *********** "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x546x2 test [10/Sep/2025:17:15:07 +0800] "GET /rest/activity-stream/1.0/preferences?_=1757495703729 HTTP/1.1" 200 136 4209 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x554x1 - [10/Sep/2025:17:15:07 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams.streams-jira-plugin:date-zh-CN/date.js?callback=ActivityStreams.loadDateJs&_=1757495703731 HTTP/1.1" 200 1050 5 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AREELYoJjrqUKnJA5fRSfIgyqXBxEDQSxchdwtz9rQbBTrOQR6YW155BRxgJg1u99jxtDevXqJEfhwRUQH3ojOMAyuTD7Amc4%2FUk51YeUnSIaaf0xalVWnDn3%2FN3lnY88yS%2FjkLIYSZTXWutRuaEl36DQE6jCSu%2F9qn0jVlJANVh4rQ0EqRJEQFaNNjNHW5zk1W27kCZe4XyGTIw6F46tLUPhpoM8iufrNrDnEI8NX1hKqE258CefLis3n5z5tQOPMINwl45ICZZaq5%2BPW4Ph9QbIxecQt3TFx%2BvyAcGM%2Fu1W0KoxM5W6mKvN8M3LD9lS42qdeQ%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x555x1 test [10/Sep/2025:17:15:08 +0800] "GET /logout?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin HTTP/1.1" 302 - 7 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x556x1 test [10/Sep/2025:17:15:08 +0800] "GET /secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin HTTP/1.1" 200 7416 90 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "10afwhz"
********** 1035x560x4 - [10/Sep/2025:17:15:08 +0800] "GET /rest/api/1.0/shortcuts/813007/76bc0cd335128d402098dbbfee835c3f/shortcuts.js HTTP/1.1" 200 510 3 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x558x2 - [10/Sep/2025:17:15:08 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/5b5e816f7a93f9c0b9de084a716a2b7e/_/download/contextbatch/css/jira.login,-_super/batch.css HTTP/1.1" 200 1042 39 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x557x1 - [10/Sep/2025:17:15:08 +0800] "GET /s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/7cf1da997cc419812bc5e309ac8093d8/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&jag=true HTTP/1.1" 200 52277 245 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x561x2 - [10/Sep/2025:17:15:08 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 7 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x559x3 - [10/Sep/2025:17:15:09 +0800] "GET /s/f87aeaa092bc5ca2c0bfdfea983a5cf6-CDN/-c1s2yr/813007/1am8j4d/7cf1da997cc419812bc5e309ac8093d8/_/download/contextbatch/js/atl.general,jira.global,-_super/batch.js?agile_global_admin_condition=true&jag=true&locale=zh-CN HTTP/1.1" 200 90683 834 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x562x1 - [10/Sep/2025:17:15:09 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 393 6 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x563x1 - [10/Sep/2025:17:15:09 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-atl.general/batch.js?agile_global_admin_condition=true&jag=true HTTP/1.1" 200 8052 86 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x564x1 - [10/Sep/2025:17:15:09 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 9 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x565x1 - [10/Sep/2025:17:15:10 +0800] "GET /login.jsp HTTP/1.1" 200 7838 61 "http://localhost:8080/secure/Logout!default.jspa?atl_token=BT7F-94WN-7LVA-HOI3_2f501d67c29afaf7506caf6cd0a67e860b3ed18a_lin" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x566x1 - [10/Sep/2025:17:15:10 +0800] "GET /s/21784b80cba4e35aa11d23ba6b6b0e78-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:captcha/jira.webresources:captcha.css HTTP/1.1" 200 563 3 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x567x2 - [10/Sep/2025:17:15:10 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:captcha/jira.webresources:captcha.js HTTP/1.1" 200 529 2 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x568x1 - [10/Sep/2025:17:15:10 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 393 10 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x569x1 - [10/Sep/2025:17:15:10 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 21 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x570x1 - [10/Sep/2025:17:15:11 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x571x1 - [10/Sep/2025:17:15:18 +0800] "POST /login.jsp HTTP/1.1" 302 - 608 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "pgc63j"
********** 1035x572x1 admin [10/Sep/2025:17:15:18 +0800] "GET / HTTP/1.1" 302 - 47 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x573x1 admin [10/Sep/2025:17:15:18 +0800] "GET /secure/WelcomeToJIRA.jspa HTTP/1.1" 200 11985 180 "http://localhost:8080/login.jsp" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x574x2 - [10/Sep/2025:17:15:19 +0800] "GET /s/2a574c669cffd039bf2e7324cbbc1b15-CDN/-c1s2yr/813007/1am8j4d/8dff39f8cfd195188e6e86dd27cfe2a6/_/download/contextbatch/css/atl.general,jira.global,-_super/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true HTTP/1.1" 200 52277 202 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x576x2 admin [10/Sep/2025:17:15:19 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 8 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x575x2 - [10/Sep/2025:17:15:19 +0800] "GET /s/f87aeaa092bc5ca2c0bfdfea983a5cf6-CDN/-c1s2yr/813007/1am8j4d/8dff39f8cfd195188e6e86dd27cfe2a6/_/download/contextbatch/js/atl.general,jira.global,-_super/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 91404 615 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x577x1 admin [10/Sep/2025:17:15:19 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 398 9 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x578x1 - [10/Sep/2025:17:15:19 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-atl.general/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true HTTP/1.1" 200 8052 90 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x579x1 admin [10/Sep/2025:17:15:20 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x580x1 admin [10/Sep/2025:17:15:21 +0800] "DELETE /rest/api/2/mypreferences?key=jira.user.locale HTTP/1.1" 404 104 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x581x1 admin [10/Sep/2025:17:15:21 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/avatar HTTP/1.1" 200 40 114 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x585x4 admin [10/Sep/2025:17:15:22 +0800] "GET /download/resources/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component/explore.svg HTTP/1.1" 200 2237 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x587x6 admin [10/Sep/2025:17:15:22 +0800] "GET /download/resources/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component/create.svg HTTP/1.1" 200 2680 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x586x5 admin [10/Sep/2025:17:15:22 +0800] "GET /download/resources/com.atlassian.jira.jira-onboarding-assets-plugin:next-step-component/search.svg HTTP/1.1" 200 2989 4 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x582x1 admin [10/Sep/2025:17:15:22 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/cyoa HTTP/1.1" 200 40 60 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x584x3 admin [10/Sep/2025:17:15:22 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/complete HTTP/1.1" 200 40 122 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x583x2 admin [10/Sep/2025:17:15:22 +0800] "POST /rest/onboarding/1.0/flow/cyoaFirstUseFlow/sequence/current/nextStep HTTP/1.1" 200 40 122 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x588x1 admin [10/Sep/2025:17:15:24 +0800] "GET /secure/BrowseProjects.jspa?selectedCategory=all HTTP/1.1" 200 12277 194 "http://localhost:8080/secure/WelcomeToJIRA.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x591x3 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:backbone-1.3.3/jira.webresources:backbone-1.3.3.js HTTP/1.1" 200 301 3 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x592x4 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:backbone.paginator-2.0.2-factory/com.atlassian.plugin.jslibs:backbone.paginator-2.0.2-factory.js HTTP/1.1" 200 4236 2 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x589x1 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:underscore-1.8.3/com.atlassian.plugin.jslibs:underscore-1.8.3.js HTTP/1.1" 200 6064 4 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x590x2 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:backbone-1.3.3-factory/com.atlassian.plugin.jslibs:backbone-1.3.3-factory.js HTTP/1.1" 200 7625 4 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x593x5 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:backbone-paginator/jira.webresources:backbone-paginator.js HTTP/1.1" 200 350 2 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x594x1 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:backbone.radio-2.0.0-factory/com.atlassian.plugin.jslibs:backbone.radio-2.0.0-factory.js HTTP/1.1" 200 1602 5 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x597x3 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:project-type-keys/jira.webresources:project-type-keys.js HTTP/1.1" 200 322 5 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x595x1 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:backbone.radio-2.0/jira.webresources:backbone.radio-2.0.js HTTP/1.1" 200 318 5 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x598x4 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:marionette-3.1.0-factory/com.atlassian.plugin.jslibs:marionette-3.1.0-factory.js HTTP/1.1" 200 10027 6 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x596x2 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/2.1.0/_/download/batch/com.atlassian.plugin.jslibs:marionette-4.1.2-factory/com.atlassian.plugin.jslibs:marionette-4.1.2-factory.js HTTP/1.1" 200 9659 6 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x599x5 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:marionette-4.1/jira.webresources:marionette-4.1.js HTTP/1.1" 200 540 6 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x600x1 - [10/Sep/2025:17:15:24 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:marionette-3.1/jira.webresources:marionette-3.1.js HTTP/1.1" 200 538 4 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x602x2 - [10/Sep/2025:17:15:24 +0800] "GET /s/af31ffc6d02c7967b049c419f312442c-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:navigation-utils/jira.webresources:navigation-utils.js?locale=zh-CN HTTP/1.1" 200 888 28 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x603x3 - [10/Sep/2025:17:15:24 +0800] "GET /s/3fb48b109f559af66cfe09d96d3cc250-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:empty-search-results/jira.webresources:empty-search-results.js?locale=zh-CN HTTP/1.1" 200 741 29 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x601x1 - [10/Sep/2025:17:15:24 +0800] "GET /s/3fb48b109f559af66cfe09d96d3cc250-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:pagination-view/jira.webresources:pagination-view.js?locale=zh-CN HTTP/1.1" 200 1349 33 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x605x2 admin [10/Sep/2025:17:15:24 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 398 13 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x604x4 - [10/Sep/2025:17:15:24 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/1.0/_/download/batch/jira.webresources:browseprojects/jira.webresources:browseprojects.js?locale=zh-CN HTTP/1.1" 200 10818 77 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x606x2 admin [10/Sep/2025:17:15:24 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 51 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x607x1 admin [10/Sep/2025:17:15:24 +0800] "GET /secure/projectavatar?size=small HTTP/1.1" 200 1575 6 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x608x1 admin [10/Sep/2025:17:15:25 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 47 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x609x1 - [10/Sep/2025:17:15:26 +0800] "GET /browse/ABCD HTTP/1.1" 200 12678 347 "http://localhost:8080/secure/BrowseProjects.jspa?selectedCategory=all" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x610x2 - [10/Sep/2025:17:15:27 +0800] "GET /s/b443ffbb152858d7297fd117b9afb0e9-CDN/-c1s2yr/813007/1am8j4d/cc8684ee2557c268cf8e6fbb27c3518d/_/download/contextbatch/css/project.issue.navigator,jira.view.issue,jira.global,atl.general,-_super/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 91675 939 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x612x2 admin [10/Sep/2025:17:15:27 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 10 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x611x3 - [10/Sep/2025:17:15:28 +0800] "GET /s/0b3682dbb7cad2ea71815045edd2c84c-CDN/-c1s2yr/813007/1am8j4d/cc8684ee2557c268cf8e6fbb27c3518d/_/download/contextbatch/js/project.issue.navigator,jira.view.issue,jira.global,atl.general,-_super/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 346345 1958 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x613x1 admin [10/Sep/2025:17:15:28 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 444 11 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x615x2 admin [10/Sep/2025:17:15:28 +0800] "GET /rest/inline-create/1.0/context/bootstrap?query=project%20%3D%20%22ABCD%22%20AND%20resolution%20%3D%20Unresolved%20ORDER%20BY%20priority%20DESC%2C%20updated%20DESC&&_=1757495727542 HTTP/1.1" 200 613 9 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x614x1 - [10/Sep/2025:17:15:28 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-project.issue.navigator,-jira.view.issue,-atl.general/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 8052 66 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x616x1 admin [10/Sep/2025:17:15:28 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 780 18 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x619x3 admin [10/Sep/2025:17:15:29 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 44 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x618x2 - [10/Sep/2025:17:15:29 +0800] "GET /s/986f597785cf73708ba7ab2acd25bfd2-CDN/-c1s2yr/813007/1am8j4d/8ae30bde912d6cc9f9d754b693e78815/_/download/contextbatch/js/jira.rich.editor.api,jira.project.sidebar,jira.rich.editor,com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-jira.view.issue,-jira.global,-atl.general,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 100849 835 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x617x1 - [10/Sep/2025:17:15:29 +0800] "GET /s/f48e30aa6cdded574158e532d8ddf9c0-CDN/-c1s2yr/813007/1am8j4d/8ae30bde912d6cc9f9d754b693e78815/_/download/contextbatch/css/jira.rich.editor.api,jira.project.sidebar,jira.rich.editor,com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-jira.view.issue,-jira.global,-atl.general,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 64327 906 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x620x1 admin [10/Sep/2025:17:15:29 +0800] "PUT /rest/projects/1.0/project/ABCD/lastVisited HTTP/1.1" 200 40 74 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x621x1 admin [10/Sep/2025:17:15:33 +0800] "GET /secure/MyJiraHome.jspa HTTP/1.1" 200 11082 291 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x622x2 - [10/Sep/2025:17:15:34 +0800] "GET /s/92ac6d27658c149c9b82452d3f6d5a96-CDN/-c1s2yr/813007/1am8j4d/13c469253e49760bafbf5b5c122bea8b/_/download/contextbatch/css/atl.dashboard,jira.global,atl.general,jira.dashboard,-_super/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true HTTP/1.1" 200 124694 1079 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x624x2 admin [10/Sep/2025:17:15:34 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 9 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x623x3 - [10/Sep/2025:17:15:35 +0800] "GET /s/1f54568aa7876e0d79f766a6720914bd-CDN/-c1s2yr/813007/1am8j4d/13c469253e49760bafbf5b5c122bea8b/_/download/contextbatch/js/atl.dashboard,jira.global,atl.general,jira.dashboard,-_super/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 427586 2174 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x625x1 admin [10/Sep/2025:17:15:35 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 406 10 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x627x2 admin [10/Sep/2025:17:15:35 +0800] "GET /rest/gadget/1.0/issueTable/jql?num=10&tableContext=jira.table.cols.dashboard&addDefault=true&enableSorting=true&paging=true&showActions=true&jql=assignee+%3D+currentUser()+AND+resolution+%3D+unresolved+ORDER+BY+priority+DESC%2C+created+ASC&sortBy=&startIndex=0&_=1757495734685 HTTP/1.1" 200 245 8 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x629x4 admin [10/Sep/2025:17:15:35 +0800] "POST /plugins/servlet/gadgets/dashboard-diagnostics HTTP/1.1" 200 276 4 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x628x3 admin [10/Sep/2025:17:15:35 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12308 10 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x630x4 admin [10/Sep/2025:17:15:35 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12311 7 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x631x4 admin [10/Sep/2025:17:15:35 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12312 8 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x626x1 - [10/Sep/2025:17:15:35 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/be9ff0efea54a6453d2ac60b92912a09/_/download/contextbatch/js/browser-metrics-plugin.contrib,-_super,-atl.dashboard,-atl.general/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true HTTP/1.1" 200 8052 60 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x632x1 admin [10/Sep/2025:17:15:35 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 524 15 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x636x4 admin [10/Sep/2025:17:15:35 +0800] "GET /rest/activity-stream/1.0/preferences?_=1757495735893 HTTP/1.1" 200 136 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x638x5 - [10/Sep/2025:17:15:35 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams.streams-jira-plugin:date-zh-CN/date.js?callback=ActivityStreams.loadDateJs&_=1757495735895 HTTP/1.1" 200 1050 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x637x4 admin [10/Sep/2025:17:15:35 +0800] "GET /plugins/servlet/streams?maxResults=5&relativeLinks=true&_=1757495735894 HTTP/1.1" 200 481 15 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
127.0.0.1 1035x639x4 admin [10/Sep/2025:17:15:35 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 415 40 4 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "1pw2r36"
********** 1035x635x3 - [10/Sep/2025:17:15:35 +0800] "GET /s/b927756b58089a45e3f5cbb45a805b5b-CDN/-c1s2yr/813007/1am8j4d/e7bd201ab3e385f17c37cbd771cccc6e/_/download/contextbatch/css/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-atl.dashboard,-jira.global,-atl.general/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true HTTP/1.1" 200 3948 46 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x633x2 admin [10/Sep/2025:17:15:35 +0800] "POST /plugins/servlet/gadgets/makeRequest HTTP/1.1" 200 291 57 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3Aeu7OdPc1Qmz%2BliPYq7w0UHYzca2qNfDTv4o09hf7tPbq9y71YiVAYQ3eC0RWFMPTBJcXyM6zV42sFHd9Ix3RH1mfLIlIhKG33cMYczKn9RexCnkEKSnu2NKj9pZY8LDsmDK1erXtUeDmVvGMH1ehd%2BIjEpr9umyq81wKSooMSONtwVEUNDRewYbzCNJerpNoic5hw7k78JB4gILKMJW3HyCxgcB%2BKVHO4pXWsgBns%2FPNnevVjz2rO2L7J7iRQRBZ0kEFf3POJfNMruV%2BdYXb6ljdZ%2BmZcpdV5QGqeyWUJ0PGTDUdrqqFRnZoD4u9ob7x93rrGA%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x640x2 admin [10/Sep/2025:17:15:36 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 5 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x634x2 - [10/Sep/2025:17:15:36 +0800] "GET /s/b9ba08bd5e21c971822ac46e4b818cfc-CDN/-c1s2yr/813007/1am8j4d/e7bd201ab3e385f17c37cbd771cccc6e/_/download/contextbatch/js/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-atl.dashboard,-jira.global,-atl.general/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&locale=zh-CN HTTP/1.1" 200 35837 1083 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x641x1 admin [10/Sep/2025:17:15:38 +0800] "GET /rest/api/1.0/menus/home_link?inAdminMode=false&_=1757495734686 HTTP/1.1" 200 279 8 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x642x1 admin [10/Sep/2025:17:15:39 +0800] "GET /secure/MyJiraHome.jspa HTTP/1.1" 200 11028 64 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x643x1 admin [10/Sep/2025:17:15:39 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 6 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x644x1 admin [10/Sep/2025:17:15:39 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 526 14 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x645x1 admin [10/Sep/2025:17:15:39 +0800] "GET /rest/gadget/1.0/issueTable/jql?num=10&tableContext=jira.table.cols.dashboard&addDefault=true&enableSorting=true&paging=true&showActions=true&jql=assignee+%3D+currentUser()+AND+resolution+%3D+unresolved+ORDER+BY+priority+DESC%2C+created+ASC&sortBy=&startIndex=0&_=1757495739688 HTTP/1.1" 200 245 9 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x647x3 admin [10/Sep/2025:17:15:39 +0800] "POST /plugins/servlet/gadgets/dashboard-diagnostics HTTP/1.1" 200 276 6 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x646x2 admin [10/Sep/2025:17:15:39 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12314 13 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x648x3 admin [10/Sep/2025:17:15:39 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12313 9 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x649x3 admin [10/Sep/2025:17:15:39 +0800] "GET /plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh HTTP/1.1" 200 12310 10 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x650x1 admin [10/Sep/2025:17:15:39 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 420 53 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x652x2 admin [10/Sep/2025:17:15:39 +0800] "GET /rest/activity-stream/1.0/preferences?_=1757495739821 HTTP/1.1" 200 136 4 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x654x3 - [10/Sep/2025:17:15:39 +0800] "GET /s/-c1s2yr/813007/1am8j4d/8.2.2/_/download/resources/com.atlassian.streams.streams-jira-plugin:date-zh-CN/date.js?callback=ActivityStreams.loadDateJs&_=1757495739823 HTTP/1.1" 200 1050 3 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x653x3 admin [10/Sep/2025:17:15:39 +0800] "GET /plugins/servlet/streams?maxResults=5&relativeLinks=true&_=1757495739822 HTTP/1.1" 200 481 16 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
127.0.0.1 1035x655x2 admin [10/Sep/2025:17:15:39 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 415 40 4 "-" "Apache-HttpClient/4.5.13 (Java/11.0.22)" "eh79em"
********** 1035x651x2 admin [10/Sep/2025:17:15:39 +0800] "POST /plugins/servlet/gadgets/makeRequest HTTP/1.1" 200 290 53 "http://localhost:8080/plugins/servlet/gadgets/ifr?container=atlassian&mid=10003&country=CN&lang=zh&view=default&view-params=%7B%22writable%22%3A%22false%22%7D&st=atlassian%3AguwKaWikVUDxlwwAlzzqltTdTcR%2Fr5NqhvWDmdGyo44T3gHyK6rMqEopVKHx6UvC6kw0ap0W65mF45PwiC2zyx0dRyTmIxbcrcXl9pJLq2yfUk5IhgBMd3W3mbkTCiOTaKn97tVhevH8%2FH0NhonzyIwu9Im3NCxd8xkDcC1M4pp3Zq8dfej0dR5ermZDiHqCA2Nb6VhYqxv1f9EwAar%2BeHnrbFLVQQvxkgdTynHhC70dCILNLpI%2Bs3TshNzSIHHJk1RrvttkemjBgMTOEZh3J7MywrOpuMPNXkvVit54XIqWbRLCxnB%2FpW2Ya6oN1OH9BBy1Vw%3D%3D&up_isConfigured=true&up_isReallyConfigured=false&up_title=Your+Company+Jira&up_titleRequired=true&up_numofentries=5&up_refresh=false&up_maxProviderLabelCharacters=50&up_rules=&up_renderingContext=&up_keys=__all_projects__&up_itemKeys=&up_username=&url=http%3A%2F%2Flocalhost%3A8080%2Frest%2Fgadgets%2F1.0%2Fg%2Fcom.atlassian.streams.streams-jira-plugin%3Aactivitystream-gadget%2Fgadgets%2Factivitystream-gadget.xml&libs=auth-refresh" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x656x1 admin [10/Sep/2025:17:15:40 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 48 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x657x1 admin [10/Sep/2025:17:15:43 +0800] "POST /rest/helptips/1.0/tips HTTP/1.1" 204 - 390 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x658x1 admin [10/Sep/2025:17:15:45 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x659x1 admin [10/Sep/2025:17:15:45 +0800] "GET /rest/api/1.0/menus/browse_link?inAdminMode=false&_=1757495739689 HTTP/1.1" 200 2104 20 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x660x1 admin [10/Sep/2025:17:15:45 +0800] "GET /secure/projectavatar?pid=10000&avatarId=10324&size=small HTTP/1.1" 200 1575 5 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x661x2 - [10/Sep/2025:17:15:45 +0800] "GET /s/-c1s2yr/813007/1am8j4d/1.0/_/images/icons/sprites/icons_module.png HTTP/1.1" 200 5400 2 "http://localhost:8080/s/e5c1649af5514387325e68e3a17f02ee-CDN/-c1s2yr/813007/1am8j4d/02b7c52c8ae4a017e743a4da5996c5bb/_/download/contextbatch/css/_super/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x663x2 admin [10/Sep/2025:17:15:46 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 444 6 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x664x2 admin [10/Sep/2025:17:15:46 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 694 53 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x662x1 - [10/Sep/2025:17:15:46 +0800] "GET /browse/ABCD HTTP/1.1" 200 12680 200 "http://localhost:8080/secure/Dashboard.jspa" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x666x2 admin [10/Sep/2025:17:15:46 +0800] "GET /rest/inline-create/1.0/context/bootstrap?query=project%20%3D%20%22ABCD%22%20AND%20resolution%20%3D%20Unresolved%20ORDER%20BY%20priority%20DESC%2C%20updated%20DESC&&_=1757495746163 HTTP/1.1" 200 613 9 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x665x2 - [10/Sep/2025:17:15:46 +0800] "GET /s/f71776af1e83a3ab9b99b4aa37aab3d4-CDN/-c1s2yr/813007/1am8j4d/87f098d29b34668cf53ddac56a977e60/_/download/contextbatch/js/jira.rich.editor.api,jira.rich.editor,-_super,-jira.view.issue,-jira.global,-atl.general,-project.issue.navigator/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 28395 186 "http://localhost:8080/projects/ABCD/issues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x667x1 admin [10/Sep/2025:17:15:46 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 596 13 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x669x2 - [10/Sep/2025:17:15:46 +0800] "GET /s/b927756b58089a45e3f5cbb45a805b5b-CDN/-c1s2yr/813007/1am8j4d/e3cd17bc00b1765e59ab08e45fd9d30f/_/download/contextbatch/css/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-jira.view.issue,-jira.global,-project.issue.navigator,-atl.general,-jira.rich.editor.api/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 3717 21 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x671x4 - [10/Sep/2025:17:15:46 +0800] "GET /s/b9ba08bd5e21c971822ac46e4b818cfc-CDN/-c1s2yr/813007/1am8j4d/e3cd17bc00b1765e59ab08e45fd9d30f/_/download/contextbatch/js/com.atlassian.jira.plugins.jira-development-integration-plugin:0,-_super,-jira.view.issue,-jira.global,-project.issue.navigator,-atl.general,-jira.rich.editor.api/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 29496 310 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x670x3 - [10/Sep/2025:17:15:46 +0800] "GET /s/9bc76dadc307ad954c1e618b94767a9d-CDN/-c1s2yr/813007/1am8j4d/dda42559b67ceef0db5f0f1e62d61d48/_/download/contextbatch/js/jira.project.sidebar,-_super,-jira.view.issue,-jira.global,-atl.general,-jira.rich.editor.api,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 43830 383 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x672x2 admin [10/Sep/2025:17:15:46 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x668x1 - [10/Sep/2025:17:15:47 +0800] "GET /s/90643dea748af73fdb65b136fddf2127-CDN/-c1s2yr/813007/1am8j4d/dda42559b67ceef0db5f0f1e62d61d48/_/download/contextbatch/css/jira.project.sidebar,-_super,-jira.view.issue,-jira.global,-atl.general,-jira.rich.editor.api,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator/batch.css?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&richediton=true HTTP/1.1" 200 61235 770 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x673x1 admin [10/Sep/2025:17:15:47 +0800] "PUT /rest/projects/1.0/project/ABCD/lastVisited HTTP/1.1" 200 40 99 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x676x3 admin [10/Sep/2025:17:15:51 +0800] "GET /images/icons/issuetypes/epic.svg HTTP/1.1" 200 587 5 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x675x1 admin [10/Sep/2025:17:15:51 +0800] "GET /secure/viewavatar?size=xsmall&avatarId=10310&avatarType=issuetype HTTP/1.1" 200 556 6 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x674x2 admin [10/Sep/2025:17:15:51 +0800] "GET /secure/viewavatar?size=xsmall&avatarId=10311&avatarType=issuetype HTTP/1.1" 200 435 9 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x677x4 admin [10/Sep/2025:17:15:51 +0800] "GET /secure/viewavatar?size=xsmall&avatarId=10318&avatarType=issuetype HTTP/1.1" 200 500 10 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x678x1 admin [10/Sep/2025:17:15:51 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x679x1 admin [10/Sep/2025:17:15:56 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x681x2 admin [10/Sep/2025:17:15:58 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 86 52 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x680x1 admin [10/Sep/2025:17:15:58 +0800] "POST /rest/inline-create/1.0/context/resolve HTTP/1.1" 200 106 86 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x682x2 admin [10/Sep/2025:17:15:58 +0800] "POST /rest/webResources/1.0/resources HTTP/1.1" 200 444 49 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x683x1 - [10/Sep/2025:17:15:58 +0800] "GET /s/59910608a4f9cde45d6b9b8ebb307516-CDN/-c1s2yr/813007/1am8j4d/3f56d1b7720088203adf2d7a146659d3/_/download/contextbatch/js/jira.create.issue,-_super,-jira.view.issue,-jira.project.sidebar,-com.atlassian.jira.projects.sidebar.init,-project.issue.navigator,-atl.general,-jira.rich.editor.api,-jira.rich.editor,-com.atlassian.jira.plugins.jira-development-integration-plugin:0/batch.js?agile_global_admin_condition=true&jag=true&jaguser=true&jira.create.linked.issue=true&locale=zh-CN&richediton=true HTTP/1.1" 200 2785 43 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x684x1 admin [10/Sep/2025:17:15:58 +0800] "POST /secure/QuickCreateIssue!default.jspa?decorator=none HTTP/1.1" 200 4285 396 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x685x1 admin [10/Sep/2025:17:15:58 +0800] "POST /secure/QuickCreateIssue!default.jspa?decorator=none HTTP/1.1" 200 4292 76 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x687x2 admin [10/Sep/2025:17:15:58 +0800] "GET /rest/api/2/mypreferences?key=jira.editor.user.mode&_=1757495746164 HTTP/1.1" 404 109 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x688x2 admin [10/Sep/2025:17:15:58 +0800] "GET /rest/api/2/mypreferences?key=jira.editor.user.mode&_=1757495746165 HTTP/1.1" 404 109 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x689x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/priorities/medium.svg HTTP/1.1" 200 705 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x691x4 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/smile.gif HTTP/1.1" 200 385 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x690x3 admin [10/Sep/2025:17:15:58 +0800] "GET /secure/projectavatar?size=xsmall&avatarId=10324 HTTP/1.1" 200 1575 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x693x4 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/tongue.gif HTTP/1.1" 200 375 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x692x5 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/sad.gif HTTP/1.1" 200 385 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x694x2 - [10/Sep/2025:17:15:58 +0800] "GET /s/-c1s2yr/813007/1am8j4d/9.1.4/_/download/resources/com.atlassian.auiplugin:split_aui.splitchunk.56dfb54d0c/assets/atlassian-icons.woff HTTP/1.1" 200 59848 4 "http://localhost:8080/s/e5c1649af5514387325e68e3a17f02ee-CDN/-c1s2yr/813007/1am8j4d/02b7c52c8ae4a017e743a4da5996c5bb/_/download/contextbatch/css/_super/batch.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x695x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/biggrin.gif HTTP/1.1" 200 618 5 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x696x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/wink.gif HTTP/1.1" 200 377 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x697x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/thumbs_up.gif HTTP/1.1" 200 567 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x698x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/thumbs_down.gif HTTP/1.1" 200 562 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x699x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/information.gif HTTP/1.1" 200 144 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x700x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/check.gif HTTP/1.1" 200 217 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x701x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/error.gif HTTP/1.1" 200 138 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x702x3 admin [10/Sep/2025:17:15:58 +0800] "GET /rest/api/2/mypreferences?key=jira.editor.user.mode&_=1757495746166 HTTP/1.1" 404 109 5 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x703x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/add.gif HTTP/1.1" 200 147 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x704x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/warning.gif HTTP/1.1" 200 345 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x705x4 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/forbidden.gif HTTP/1.1" 200 87 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x706x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/help_16.gif HTTP/1.1" 200 226 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x707x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/lightbulb_on.gif HTTP/1.1" 200 243 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x709x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/star_yellow.gif HTTP/1.1" 200 356 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x708x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/lightbulb.gif HTTP/1.1" 200 238 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x710x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/star_red.gif HTTP/1.1" 200 159 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x711x2 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/star_green.gif HTTP/1.1" 200 159 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x713x4 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/star_blue.gif HTTP/1.1" 200 159 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x714x3 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/flag.gif HTTP/1.1" 200 551 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x715x4 admin [10/Sep/2025:17:15:58 +0800] "GET /images/icons/emoticons/flag_grey.gif HTTP/1.1" 200 322 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x716x3 admin [10/Sep/2025:17:15:58 +0800] "GET /secure/useravatar?size=xsmall&avatarId=10123 HTTP/1.1" 200 456 5 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x718x4 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/plugins/autoresize/plugin.js HTTP/1.1" 200 1254 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x717x3 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/plugins/lists/plugin.js HTTP/1.1" 200 4848 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x719x3 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/plugins/table/plugin.js HTTP/1.1" 200 15140 6 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x720x4 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/plugins/paste/plugin.js HTTP/1.1" 200 9511 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x721x4 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/plugins/textpattern/plugin.js HTTP/1.1" 200 1993 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x712x3 admin [10/Sep/2025:17:15:58 +0800] "PUT /rest/api/2/mypreferences?key=jira.editor.user.mode HTTP/1.1" 204 - 38 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x722x2 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/skins/lightgray/skin.min.css HTTP/1.1" 200 8018 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x723x2 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/skins/lightgray/content.min.css HTTP/1.1" 200 1226 4 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x724x3 - [10/Sep/2025:17:15:58 +0800] "GET /s/00ba47a4d897492b93ffc773c10b38b9-CDN/-c1s2yr/813007/1am8j4d/6a79bee72fb49a41f1f4962c4b1699b5/_/download/contextbatch/css/jira.global.look-and-feel%2C-_super/batch.css HTTP/1.1" 200 2097 15 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x726x3 - [10/Sep/2025:17:15:58 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/skins/lightgray/fonts/tinymce.woff HTTP/1.1" 200 17512 3 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/skins/lightgray/skin.min.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x725x4 - [10/Sep/2025:17:15:59 +0800] "GET /s/6c3e1c64aa196e5ad40b42c924e7f58c-CDN/-c1s2yr/813007/1am8j4d/8e721fe4565b605b84c724d540cbc39c/_/download/contextbatch/css/jira.rich.editor.content%2C-_super/batch.css HTTP/1.1" 200 13947 235 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x727x2 - [10/Sep/2025:17:15:59 +0800] "GET /s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/skins/lightgray/img/loader.gif HTTP/1.1" 200 2620 3 "http://localhost:8080/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-c1s2yr/813007/1am8j4d/4.2.12/_/download/resources/com.atlassian.jira.plugins.jira-editor-plugin:tinymce/skins/lightgray/skin.min.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x730x4 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/tongue.png HTTP/1.1" 200 736 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x728x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/sad.png HTTP/1.1" 200 778 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x732x6 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/wink.png HTTP/1.1" 200 737 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x729x3 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/smile.png HTTP/1.1" 200 752 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x731x5 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/biggrin.png HTTP/1.1" 200 827 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x733x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/thumbs_up.png HTTP/1.1" 200 696 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x734x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/thumbs_down.png HTTP/1.1" 200 704 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x735x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/information.png HTTP/1.1" 200 295 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x736x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/check.png HTTP/1.1" 200 1038 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x737x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/error.png HTTP/1.1" 200 297 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x738x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/warning.png HTTP/1.1" 200 1052 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x739x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/add.png HTTP/1.1" 200 302 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x740x3 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/forbidden.png HTTP/1.1" 200 244 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x741x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/help_16.png HTTP/1.1" 200 613 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x742x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/lightbulb_on.png HTTP/1.1" 200 1053 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x743x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/lightbulb.png HTTP/1.1" 200 1047 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x744x3 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/star_yellow.png HTTP/1.1" 200 1057 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x745x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/star_red.png HTTP/1.1" 200 347 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x746x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/star_green.png HTTP/1.1" 200 347 2 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x747x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/star_blue.png HTTP/1.1" 200 347 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x748x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/flag.png HTTP/1.1" 200 594 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x749x2 admin [10/Sep/2025:17:15:59 +0800] "GET /images/icons/emoticons/flag_grey.png HTTP/1.1" 200 544 3 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x750x2 admin [10/Sep/2025:17:16:01 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 48 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1035x686x1 admin [10/Sep/2025:17:16:02 +0800] "POST /rest/quickedit/1.0/userpreferences/create HTTP/1.1" 200 40 3438 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x751x2 admin [10/Sep/2025:17:16:06 +0800] "GET /images/icons/priorities/highest.svg HTTP/1.1" 200 361 20 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x752x1 admin [10/Sep/2025:17:16:06 +0800] "GET /images/icons/priorities/high.svg HTTP/1.1" 200 207 20 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x753x3 admin [10/Sep/2025:17:16:06 +0800] "GET /images/icons/priorities/lowest.svg HTTP/1.1" 200 385 17 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x754x4 admin [10/Sep/2025:17:16:06 +0800] "GET /images/icons/priorities/low.svg HTTP/1.1" 200 219 17 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x755x1 admin [10/Sep/2025:17:16:07 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 46 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x756x1 admin [10/Sep/2025:17:16:20 +0800] "POST /secure/QuickCreateIssue.jspa?decorator=none HTTP/1.1" 200 5670 9669 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x757x1 admin [10/Sep/2025:17:16:21 +0800] "POST /rest/issueNav/1/issueTable HTTP/1.1" 200 498 671 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x758x1 admin [10/Sep/2025:17:16:21 +0800] "GET /rest/inline-create/1.0/context/bootstrap?query=project%20%3D%20%22ABCD%22%20ORDER%20BY%20created%20DESC&&_=1757495746167 HTTP/1.1" 200 613 7 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x759x2 admin [10/Sep/2025:17:16:21 +0800] "POST /rest/issueNav/1/issueTable/stable HTTP/1.1" 200 411 12 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x760x1 admin [10/Sep/2025:17:16:21 +0800] "POST /rest/orderbycomponent/latest/orderByOptions/primary HTTP/1.1" 200 141 121 "http://localhost:8080/projects/ABCD/issues/?filter=allopenissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x761x2 admin [10/Sep/2025:17:16:21 +0800] "GET /secure/AjaxIssueAction!default.jspa?issueKey=ABCD-1&decorator=none&prefetch=false&shouldUpdateCurrentProject=false&loadFields=false&_=1757495746168 HTTP/1.1" 200 7091 623 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x764x3 admin [10/Sep/2025:17:16:21 +0800] "GET /secure/useravatar?size=small&avatarId=10123 HTTP/1.1" 200 655 3 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x762x1 admin [10/Sep/2025:17:16:21 +0800] "GET /secure/AjaxIssueEditAction!default.jspa?decorator=none&issueId=10000&_=1757495746169 HTTP/1.1" 200 4582 108 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x763x2 admin [10/Sep/2025:17:16:22 +0800] "GET /rest/bamboo/latest/deploy/ABCD/ABCD-1?_=1757495746170 HTTP/1.1" 200 44 165 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x765x1 admin [10/Sep/2025:17:16:22 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 6 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x766x1 admin [10/Sep/2025:17:16:35 +0800] "POST /rest/api/1.0/render HTTP/1.1" 200 52 4 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
********** 1036x767x1 admin [10/Sep/2025:17:16:37 +0800] "POST /rest/analytics/1.0/publish/bulk HTTP/1.1" 200 40 3 "http://localhost:8080/projects/ABCD/issues/ABCD-1?filter=allissues" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0" "11a0558"
