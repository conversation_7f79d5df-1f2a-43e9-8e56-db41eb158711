10-Sep-2025 15:28:33.101 WARNING [main] org.apache.catalina.startup.SetAllPropertiesRule.begin [SetAllPropertiesRule]{Server/Service/Connector} Setting property 'proxyPort' to '' did not find a matching property.
10-Sep-2025 15:28:33.144 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/8.5.60
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Nov 12 2020 17:31:55 UTC
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: 8.5.60.0
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.6.87.2-microsoft-standard-WSL2
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /opt/java/openjdk
10-Sep-2025 15:28:33.145 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           11.0.22+7
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Eclipse Adoptium
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /opt/atlassian/jira
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /opt/atlassian/jira
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
10-Sep-2025 15:28:33.146 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xms1024m
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xmx2048m
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:InitialCodeCacheSize=32m
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:ReservedCodeCacheSize=512m
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.awt.headless=true
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Datlassian.standalone=JIRA
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dmail.mime.decodeparameters=true
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:-OmitStackTraceInFastThrow
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.locale.providers=COMPAT
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djira.home=/var/atlassian/application-data/jira
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Datlassian.plugins.startup.options=-fg
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
10-Sep-2025 15:28:33.147 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:tags,time,uptime,level:filecount=5,filesize=20M
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:+ExplicitGCInvokesConcurrent
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/opt/atlassian/jira
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/opt/atlassian/jira
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/opt/atlassian/jira/temp
10-Sep-2025 15:28:33.148 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The Apache Tomcat Native library which allows using OpenSSL was not found on the java.library.path: [/usr/java/packages/lib:/usr/lib64:/lib64:/lib:/usr/lib]
10-Sep-2025 15:28:33.208 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
10-Sep-2025 15:28:33.225 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 460 ms
10-Sep-2025 15:28:33.253 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
10-Sep-2025 15:28:33.253 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.60
10-Sep-2025 15:28:37.740 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
10-Sep-2025 15:28:37.748 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
10-Sep-2025 15:28:37.755 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 4530 ms
