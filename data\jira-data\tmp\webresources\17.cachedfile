WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'com.atlassian.plugin.jslibs:marionette-1.6.1-factory', location = 'factories/marionette/1.6.1/marionette-1.6.1-factory.js' */
/*
 Includes BabySitter
 https://github.com/marionettejs/backbone.babysitter/

 Includes Wreqr
 https://github.com/marionettejs/backbone.wreqr/
*/
'use strict';(function(p){define("atlassian/libs/factories/marionette-1.6.1",function(){return function(h,m){var q=m.Marionette;p.call({_:h,Backbone:m});h=m.Marionette;h.VERSION="1.6.1";m.Marionette=q;return h}})})(function(){var p=this.Backbone,h=this._,m=function(h,e){var d=function(d){this._views={};this._indexByModel={};this._indexByCustom={};this._updateLength();e.each(d,this.add,this)};e.extend(d.prototype,{add:function(d,b){var g=d.cid;this._views[g]=d;d.model&&(this._indexByModel[d.model.cid]=
g);b&&(this._indexByCustom[b]=g);this._updateLength()},findByModel:function(d){return this.findByModelCid(d.cid)},findByModelCid:function(d){return this.findByCid(this._indexByModel[d])},findByCustom:function(d){return this.findByCid(this._indexByCustom[d])},findByIndex:function(d){return e.values(this._views)[d]},findByCid:function(d){return this._views[d]},remove:function(d){var b=d.cid;d.model&&delete this._indexByModel[d.model.cid];e.any(this._indexByCustom,function(d,a){if(d===b)return delete this._indexByCustom[a],
!0},this);delete this._views[b];this._updateLength()},call:function(d){this.apply(d,e.tail(arguments))},apply:function(d,b){e.each(this._views,function(g){e.isFunction(g[d])&&g[d].apply(g,b||[])})},_updateLength:function(){this.length=e.size(this._views)}});e.each("forEach each map find detect filter select reject every all some any include contains invoke toArray first initial rest last without isEmpty pluck".split(" "),function(l){d.prototype[l]=function(){var b=[e.values(this._views)].concat(e.toArray(arguments));
return e[l].apply(e,b)}});return d}(p,h),q=function(h,e,d){e={};e.Handlers=function(d,b){var g=function(a){this.options=a;this._wreqrHandlers={};b.isFunction(this.initialize)&&this.initialize(a)};g.extend=d.Model.extend;b.extend(g.prototype,d.Events,{setHandlers:function(a){b.each(a,function(a,f){var c=null;b.isObject(a)&&!b.isFunction(a)&&(c=a.context,a=a.callback);this.setHandler(f,a,c)},this)},setHandler:function(a,c,b){this._wreqrHandlers[a]={callback:c,context:b};this.trigger("handler:add",a,
c,b)},hasHandler:function(a){return!!this._wreqrHandlers[a]},getHandler:function(a){var c=this._wreqrHandlers[a];if(!c)throw Error("Handler not found for '"+a+"'");return function(){var a=Array.prototype.slice.apply(arguments);return c.callback.apply(c.context,a)}},removeHandler:function(a){delete this._wreqrHandlers[a]},removeAllHandlers:function(){this._wreqrHandlers={}}});return g}(h,d);e.CommandStorage=function(){var e=function(b){this.options=b;this._commands={};d.isFunction(this.initialize)&&
this.initialize(b)};d.extend(e.prototype,h.Events,{getCommands:function(b){var d=this._commands[b];d||(d={command:b,instances:[]},this._commands[b]=d);return d},addCommand:function(b,d){this.getCommands(b).instances.push(d)},clearCommands:function(b){this.getCommands(b).instances=[]}});return e}();e.Commands=function(e){return e.Handlers.extend({storageType:e.CommandStorage,constructor:function(b){this.options=b||{};this._initializeStorage(this.options);this.on("handler:add",this._executeCommands,
this);var d=Array.prototype.slice.call(arguments);e.Handlers.prototype.constructor.apply(this,d)},execute:function(b,d){b=arguments[0];d=Array.prototype.slice.call(arguments,1);this.hasHandler(b)?this.getHandler(b).apply(this,d):this.storage.addCommand(b,d)},_executeCommands:function(b,e,a){var c=this.storage.getCommands(b);d.each(c.instances,function(c){e.apply(a,c)});this.storage.clearCommands(b)},_initializeStorage:function(b){b=b.storageType||this.storageType;this.storage=d.isFunction(b)?new b:
b}})}(e);e.RequestResponse=function(d){return d.Handlers.extend({request:function(){var b=arguments[0],d=Array.prototype.slice.call(arguments,1);return this.getHandler(b).apply(this,d)}})}(e);e.EventAggregator=function(d,b){var e=function(){};e.extend=d.Model.extend;b.extend(e.prototype,d.Events);return e}(h,d);return e}(p,p.Marionette,h);(function(h,e,d){function l(a,c){a=Error(a);a.name=c||"Error";throw a;}var b={};e.Marionette=b;b.$=e.$;var g=Array.prototype.slice;b.extend=e.Model.extend;b.getOption=
function(a,c){if(a&&c)return a.options&&c in a.options&&void 0!==a.options[c]?a.options[c]:a[c]};b.triggerMethod=function(){function a(a,c,b){return b.toUpperCase()}var c=/(^|:)(\w)/gi;return function(b){var f=this["on"+b.replace(c,a)];d.isFunction(this.trigger)&&this.trigger.apply(this,arguments);if(d.isFunction(f))return f.apply(this,d.tail(arguments))}}();b.MonitorDOMRefresh=function(a){function c(c){c._isShown&&c._isRendered&&a.contains(c.el)&&d.isFunction(c.triggerMethod)&&c.triggerMethod("dom:refresh")}
return function(a){a.listenTo(a,"show",function(){a._isShown=!0;c(a)});a.listenTo(a,"render",function(){a._isRendered=!0;c(a)})}}(document.documentElement);(function(a){function c(a,c,b,f){f=f.split(/\s+/);d.each(f,function(d){var f=a[d];f||l("Method '"+d+"' was configured as an event handler, but does not exist.");a.listenTo(c,b,f,a)})}function b(a,c,b,d){a.listenTo(c,b,d,a)}function k(a,c,b,f){f=f.split(/\s+/);d.each(f,function(d){a.stopListening(c,b,a[d],a)})}function e(a,c,b,d){a.stopListening(c,
b,d,a)}function n(a,c,b,f,k){c&&b&&(d.isFunction(b)&&(b=b.call(a)),d.each(b,function(b,e){d.isFunction(b)?f(a,c,e,b):k(a,c,e,b)}))}a.bindEntityEvents=function(a,d,f){n(a,d,f,b,c)};a.unbindEntityEvents=function(a,c,b){n(a,c,b,e,k)}})(b);b.Callbacks=function(){this._deferred=b.$.Deferred();this._callbacks=[]};d.extend(b.Callbacks.prototype,{add:function(a,c){this._callbacks.push({cb:a,ctx:c});this._deferred.done(function(b,d){c&&(b=c);a.call(b,d)})},run:function(a,c){this._deferred.resolve(c,a)},reset:function(){var a=
this._callbacks;this._deferred=b.$.Deferred();this._callbacks=[];d.each(a,function(a){this.add(a.cb,a.ctx)},this)}});b.Controller=function(a){this.triggerMethod=b.triggerMethod;this.options=a||{};d.isFunction(this.initialize)&&this.initialize(this.options)};b.Controller.extend=b.extend;d.extend(b.Controller.prototype,e.Events,{close:function(){this.stopListening();this.triggerMethod("close");this.unbind()}});b.Region=function(a){this.options=a||{};this.el=b.getOption(this,"el");if(!this.el){var c=
Error("An 'el' must be specified for a region.");c.name="NoElError";throw c;}this.initialize&&(c=Array.prototype.slice.apply(arguments),this.initialize.apply(this,c))};d.extend(b.Region,{buildRegion:function(a,c){var b="string"===typeof a,k="string"===typeof a.selector,e="undefined"===typeof a.regionType,n="function"===typeof a;if(!n&&!b&&!k)throw Error("Region must be specified as a Region type, a selector string or an object with selector property");var g,h;b&&(g=a);a.selector&&(g=a.selector,delete a.selector);
n&&(h=a);!n&&e&&(h=c);a.regionType&&(h=a.regionType,delete a.regionType);if(b||n)a={};a.el=g;c=new h(a);a.parentEl&&(c.getEl=function(c){var b=a.parentEl;d.isFunction(b)&&(b=b());return b.find(c)});return c}});d.extend(b.Region.prototype,e.Events,{show:function(a){this.ensureEl();var c=a.isClosed||d.isUndefined(a.$el),f=a!==this.currentView;f&&this.close();a.render();(f||c)&&this.open(a);this.currentView=a;b.triggerMethod.call(this,"show",a);b.triggerMethod.call(a,"show")},ensureEl:function(){this.$el&&
0!==this.$el.length||(this.$el=this.getEl(this.el))},getEl:function(a){return b.$(a)},open:function(a){this.$el.empty().append(a.el)},close:function(){var a=this.currentView;a&&!a.isClosed&&(a.close?a.close():a.remove&&a.remove(),b.triggerMethod.call(this,"close",a),delete this.currentView)},attachView:function(a){this.currentView=a},reset:function(){this.close();delete this.$el}});b.Region.extend=b.extend;b.RegionManager=function(a){var c=a.Controller.extend({constructor:function(c){this._regions=
{};a.Controller.prototype.constructor.call(this,c)},addRegions:function(a,c){var b={};d.each(a,function(a,f){"string"===typeof a&&(a={selector:a});a.selector&&(a=d.defaults({},a,c));a=this.addRegion(f,a);b[f]=a},this);return b},addRegion:function(c,b){var f=d.isObject(b),e=d.isString(b),k=!!b.selector;b=e||f&&k?a.Region.buildRegion(b,a.Region):d.isFunction(b)?a.Region.buildRegion(b,a.Region):b;this._store(c,b);this.triggerMethod("region:add",c,b);return b},get:function(a){return this._regions[a]},
removeRegion:function(a){this._remove(a,this._regions[a])},removeRegions:function(){d.each(this._regions,function(a,c){this._remove(c,a)},this)},closeRegions:function(){d.each(this._regions,function(a,c){a.close()},this)},close:function(){this.removeRegions();var c=Array.prototype.slice.call(arguments);a.Controller.prototype.close.apply(this,c)},_store:function(a,c){this._regions[a]=c;this._setLength()},_remove:function(a,c){c.close();delete this._regions[a];this._setLength();this.triggerMethod("region:remove",
a,c)},_setLength:function(){this.length=d.size(this._regions)}});d.each("forEach each map find detect filter select reject every all some any include contains invoke toArray first initial rest last without isEmpty pluck".split(" "),function(a){c.prototype[a]=function(){var c=[d.values(this._regions)].concat(d.toArray(arguments));return d[a].apply(d,c)}});return c}(b);b.TemplateCache=function(a){this.templateId=a};d.extend(b.TemplateCache,{templateCaches:{},get:function(a){var c=this.templateCaches[a];
c||(c=new b.TemplateCache(a),this.templateCaches[a]=c);return c.load()},clear:function(){var a,c=g.call(arguments),b=c.length;if(0<b)for(a=0;a<b;a++)delete this.templateCaches[c[a]];else this.templateCaches={}}});d.extend(b.TemplateCache.prototype,{load:function(){if(this.compiledTemplate)return this.compiledTemplate;var a=this.loadTemplate(this.templateId);return this.compiledTemplate=this.compileTemplate(a)},loadTemplate:function(a){var c=b.$(a).html();c&&0!==c.length||l("Could not find template: '"+
a+"'","NoTemplateError");return c},compileTemplate:function(a){return d.template(a)}});b.Renderer={render:function(a,c){if(!a)throw a=Error("Cannot render the template since it's false, null or undefined."),a.name="TemplateNotFoundError",a;return("function"===typeof a?a:b.TemplateCache.get(a))(c)}};b.View=e.View.extend({constructor:function(a){d.bindAll(this,"render");var c=Array.prototype.slice.apply(arguments);this.options=d.extend({},d.result(this,"options"),d.isFunction(a)?a.call(this):a);this.events=
this.normalizeUIKeys(d.result(this,"events"));e.View.prototype.constructor.apply(this,c);b.MonitorDOMRefresh(this);this.listenTo(this,"show",this.onShowCalled,this)},triggerMethod:b.triggerMethod,getTemplate:function(){return b.getOption(this,"template")},mixinTemplateHelpers:function(a){a=a||{};var c=b.getOption(this,"templateHelpers");d.isFunction(c)&&(c=c.call(this));return d.extend(a,c)},normalizeUIKeys:function(a){if("undefined"!==typeof a)return d.each(d.keys(a),function(c){var b=c.split("@ui.");
2===b.length&&(a[b[0]+this.ui[b[1]]]=a[c],delete a[c])},this),a},configureTriggers:function(){if(this.triggers){var a={},c=this.normalizeUIKeys(d.result(this,"triggers"));d.each(c,function(c,b){var f=d.isObject(c),e=f?c.event:c;a[b]=function(a){if(a){var b=a.preventDefault,d=a.stopPropagation,k=f?c.stopPropagation:d;(f?c.preventDefault:b)&&b&&b.apply(a);k&&d&&d.apply(a)}this.triggerMethod(e,{view:this,model:this.model,collection:this.collection})}},this);return a}},delegateEvents:function(a){this._delegateDOMEvents(a);
b.bindEntityEvents(this,this.model,b.getOption(this,"modelEvents"));b.bindEntityEvents(this,this.collection,b.getOption(this,"collectionEvents"))},_delegateDOMEvents:function(a){a=a||this.events;d.isFunction(a)&&(a=a.call(this));var c={},b=this.configureTriggers();d.extend(c,a,b);e.View.prototype.delegateEvents.call(this,c)},undelegateEvents:function(){var a=Array.prototype.slice.call(arguments);e.View.prototype.undelegateEvents.apply(this,a);b.unbindEntityEvents(this,this.model,b.getOption(this,
"modelEvents"));b.unbindEntityEvents(this,this.collection,b.getOption(this,"collectionEvents"))},onShowCalled:function(){},close:function(){this.isClosed||!1===this.triggerMethod("before:close")||(this.isClosed=!0,this.triggerMethod("close"),this.unbindUIElements(),this.remove())},bindUIElements:function(){if(this.ui){this._uiBindings||(this._uiBindings=this.ui);var a=d.result(this,"_uiBindings");this.ui={};d.each(d.keys(a),function(c){this.ui[c]=this.$(a[c])},this)}},unbindUIElements:function(){this.ui&&
this._uiBindings&&(d.each(this.ui,function(a,c){delete this.ui[c]},this),this.ui=this._uiBindings,delete this._uiBindings)}});b.ItemView=b.View.extend({constructor:function(){b.View.prototype.constructor.apply(this,g.call(arguments))},serializeData:function(){var a={};this.model?a=this.model.toJSON():this.collection&&(a={items:this.collection.toJSON()});return a},render:function(){this.isClosed=!1;this.triggerMethod("before:render",this);this.triggerMethod("item:before:render",this);var a=this.serializeData();
a=this.mixinTemplateHelpers(a);var c=this.getTemplate();a=b.Renderer.render(c,a);this.$el.html(a);this.bindUIElements();this.triggerMethod("render",this);this.triggerMethod("item:rendered",this);return this},close:function(){this.isClosed||(this.triggerMethod("item:before:close"),b.View.prototype.close.apply(this,g.call(arguments)),this.triggerMethod("item:closed"))}});b.CollectionView=b.View.extend({itemViewEventPrefix:"itemview",constructor:function(a){this._initChildViewStorage();b.View.prototype.constructor.apply(this,
g.call(arguments));this._initialEvents();this.initRenderBuffer()},initRenderBuffer:function(){this.elBuffer=document.createDocumentFragment();this._bufferedChildren=[]},startBuffering:function(){this.initRenderBuffer();this.isBuffering=!0},endBuffering:function(){this.isBuffering=!1;this.appendBuffer(this,this.elBuffer);this._triggerShowBufferedChildren();this.initRenderBuffer()},_triggerShowBufferedChildren:function(){this._isShown&&(d.each(this._bufferedChildren,function(a){b.triggerMethod.call(a,
"show")}),this._bufferedChildren=[])},_initialEvents:function(){this.collection&&(this.listenTo(this.collection,"add",this.addChildView,this),this.listenTo(this.collection,"remove",this.removeItemView,this),this.listenTo(this.collection,"reset",this.render,this))},addChildView:function(a,c,b){this.closeEmptyView();c=this.getItemView(a);b=this.collection.indexOf(a);this.addItemView(a,c,b)},onShowCalled:function(){this.children.each(function(a){b.triggerMethod.call(a,"show")})},triggerBeforeRender:function(){this.triggerMethod("before:render",
this);this.triggerMethod("collection:before:render",this)},triggerRendered:function(){this.triggerMethod("render",this);this.triggerMethod("collection:rendered",this)},render:function(){this.isClosed=!1;this.triggerBeforeRender();this._renderChildren();this.triggerRendered();return this},_renderChildren:function(){this.startBuffering();this.closeEmptyView();this.closeChildren();this.isEmpty(this.collection)?this.showEmptyView():this.showCollection();this.endBuffering()},showCollection:function(){var a;
this.collection.each(function(c,b){a=this.getItemView(c);this.addItemView(c,a,b)},this)},showEmptyView:function(){var a=this.getEmptyView();if(a&&!this._showingEmptyView){this._showingEmptyView=!0;var b=new e.Model;this.addItemView(b,a,0)}},closeEmptyView:function(){this._showingEmptyView&&(this.closeChildren(),delete this._showingEmptyView)},getEmptyView:function(){return b.getOption(this,"emptyView")},getItemView:function(a){(a=b.getOption(this,"itemView"))||l("An `itemView` must be specified",
"NoItemViewError");return a},addItemView:function(a,c,f){var e=b.getOption(this,"itemViewOptions");d.isFunction(e)&&(e=e.call(this,a,f));a=this.buildItemView(a,c,e);this.addChildViewEventForwarding(a);this.triggerMethod("before:item:added",a);this.children.add(a);this.renderItemView(a,f);this._isShown&&!this.isBuffering&&b.triggerMethod.call(a,"show");this.triggerMethod("after:item:added",a);return a},addChildViewEventForwarding:function(a){var c=b.getOption(this,"itemViewEventPrefix");this.listenTo(a,
"all",function(){var f=g.call(arguments),e=f[0],h=this.getItemEvents();f[0]=c+":"+e;f.splice(1,0,a);"undefined"!==typeof h&&d.isFunction(h[e])&&h[e].apply(this,f);b.triggerMethod.apply(this,f)},this)},getItemEvents:function(){return d.isFunction(this.itemEvents)?this.itemEvents.call(this):this.itemEvents},renderItemView:function(a,b){a.render();this.appendHtml(this,a,b)},buildItemView:function(a,b,f){a=d.extend({model:a},f);return new b(a)},removeItemView:function(a){a=this.children.findByModel(a);
this.removeChildView(a);this.checkEmpty()},removeChildView:function(a){a&&(this.stopListening(a),a.close?a.close():a.remove&&a.remove(),this.children.remove(a));this.triggerMethod("item:removed",a)},isEmpty:function(a){return!this.collection||0===this.collection.length},checkEmpty:function(){this.isEmpty(this.collection)&&this.showEmptyView()},appendBuffer:function(a,b){a.$el.append(b)},appendHtml:function(a,b,d){a.isBuffering?(a.elBuffer.appendChild(b.el),a._bufferedChildren.push(b)):a.$el.append(b.el)},
_initChildViewStorage:function(){this.children=new m},close:function(){this.isClosed||(this.triggerMethod("collection:before:close"),this.closeChildren(),this.triggerMethod("collection:closed"),b.View.prototype.close.apply(this,g.call(arguments)))},closeChildren:function(){this.children.each(function(a){this.removeChildView(a)},this);this.checkEmpty()}});b.CompositeView=b.CollectionView.extend({constructor:function(){b.CollectionView.prototype.constructor.apply(this,g.call(arguments))},_initialEvents:function(){this.once("render",
function(){this.collection&&(this.listenTo(this.collection,"add",this.addChildView,this),this.listenTo(this.collection,"remove",this.removeItemView,this),this.listenTo(this.collection,"reset",this._renderChildren,this))})},getItemView:function(a){(a=b.getOption(this,"itemView")||this.constructor)||l("An `itemView` must be specified","NoItemViewError");return a},serializeData:function(){var a={};this.model&&(a=this.model.toJSON());return a},render:function(){this.isRendered=!0;this.isClosed=!1;this.resetItemViewContainer();
this.triggerBeforeRender();var a=this.renderModel();this.$el.html(a);this.bindUIElements();this.triggerMethod("composite:model:rendered");this._renderChildren();this.triggerMethod("composite:rendered");this.triggerRendered();return this},_renderChildren:function(){this.isRendered&&(this.triggerMethod("composite:collection:before:render"),b.CollectionView.prototype._renderChildren.call(this),this.triggerMethod("composite:collection:rendered"))},renderModel:function(){var a=this.serializeData();a=this.mixinTemplateHelpers(a);
var c=this.getTemplate();return b.Renderer.render(c,a)},appendBuffer:function(a,b){this.getItemViewContainer(a).append(b)},appendHtml:function(a,b,d){a.isBuffering?(a.elBuffer.appendChild(b.el),a._bufferedChildren.push(b)):this.getItemViewContainer(a).append(b.el)},getItemViewContainer:function(a){if("$itemViewContainer"in a)return a.$itemViewContainer;var c;(c=b.getOption(a,"itemViewContainer"))?(c=d.isFunction(c)?c.call(this):c,c=a.$(c),0>=c.length&&l("The specified `itemViewContainer` was not found: "+
a.itemViewContainer,"ItemViewContainerMissingError")):c=a.$el;return a.$itemViewContainer=c},resetItemViewContainer:function(){this.$itemViewContainer&&delete this.$itemViewContainer}});b.Layout=b.ItemView.extend({regionType:b.Region,constructor:function(a){a=a||{};this._firstRender=!0;this._initializeRegions(a);b.ItemView.prototype.constructor.call(this,a)},render:function(){this.isClosed&&this._initializeRegions();this._firstRender?this._firstRender=!1:this.isClosed||this._reInitializeRegions();
var a=Array.prototype.slice.apply(arguments);return b.ItemView.prototype.render.apply(this,a)},close:function(){if(!this.isClosed){this.regionManager.close();var a=Array.prototype.slice.apply(arguments);b.ItemView.prototype.close.apply(this,a)}},addRegion:function(a,b){var c={};c[a]=b;return this._buildRegions(c)[a]},addRegions:function(a){this.regions=d.extend({},this.regions,a);return this._buildRegions(a)},removeRegion:function(a){delete this.regions[a];return this.regionManager.removeRegion(a)},
_buildRegions:function(a){var c=this,d={regionType:b.getOption(this,"regionType"),parentEl:function(){return c.$el}};return this.regionManager.addRegions(a,d)},_initializeRegions:function(a){this._initRegionManager();a=d.isFunction(this.regions)?this.regions(a):this.regions||{};this.addRegions(a)},_reInitializeRegions:function(){this.regionManager.closeRegions();this.regionManager.each(function(a){a.reset()})},_initRegionManager:function(){this.regionManager=new b.RegionManager;this.listenTo(this.regionManager,
"region:add",function(a,b){this[a]=b;this.trigger("region:add",a,b)});this.listenTo(this.regionManager,"region:remove",function(a,b){delete this[a];this.trigger("region:remove",a,b)})}});b.AppRouter=e.Router.extend({constructor:function(a){e.Router.prototype.constructor.apply(this,g.call(arguments));this.options=a||{};var c=b.getOption(this,"appRoutes"),d=this._getController();this.processAppRoutes(d,c)},appRoute:function(a,b){var c=this._getController();this._addAppRoute(c,a,b)},processAppRoutes:function(a,
b){if(b){var c=d.keys(b).reverse();d.each(c,function(c){this._addAppRoute(a,c,b[c])},this)}},_getController:function(){return b.getOption(this,"controller")},_addAppRoute:function(a,b,e){var c=a[e];if(!c)throw Error("Method '"+e+"' was not found on the controller");this.route(b,e,d.bind(c,a))}});b.Application=function(a){this._initRegionManager();this._initCallbacks=new b.Callbacks;this.vent=new q.EventAggregator;this.commands=new q.Commands;this.reqres=new q.RequestResponse;this.submodules={};d.extend(this,
a);this.triggerMethod=b.triggerMethod};d.extend(b.Application.prototype,e.Events,{execute:function(){var a=Array.prototype.slice.apply(arguments);this.commands.execute.apply(this.commands,a)},request:function(){var a=Array.prototype.slice.apply(arguments);return this.reqres.request.apply(this.reqres,a)},addInitializer:function(a){this._initCallbacks.add(a)},start:function(a){this.triggerMethod("initialize:before",a);this._initCallbacks.run(a,this);this.triggerMethod("initialize:after",a);this.triggerMethod("start",
a)},addRegions:function(a){return this._regionManager.addRegions(a)},closeRegions:function(){this._regionManager.closeRegions()},removeRegion:function(a){this._regionManager.removeRegion(a)},getRegion:function(a){return this._regionManager.get(a)},module:function(a,c){var d=b.Module;c&&(d=c.moduleClass||d);var e=g.call(arguments);e.unshift(this);return d.create.apply(d,e)},_initRegionManager:function(){this._regionManager=new b.RegionManager;this.listenTo(this._regionManager,"region:add",function(a,
b){this[a]=b});this.listenTo(this._regionManager,"region:remove",function(a,b){delete this[a]})}});b.Application.extend=b.extend;b.Module=function(a,c,e){this.moduleName=a;this.options=d.extend({},this.options,e);this.initialize=e.initialize||this.initialize;this.submodules={};this._setupInitializersAndFinalizers();this.app=c;this.startWithParent=!0;this.triggerMethod=b.triggerMethod;d.isFunction(this.initialize)&&this.initialize(this.options)};b.Module.extend=b.extend;d.extend(b.Module.prototype,
e.Events,{initialize:function(){},addInitializer:function(a){this._initializerCallbacks.add(a)},addFinalizer:function(a){this._finalizerCallbacks.add(a)},start:function(a){this._isInitialized||(d.each(this.submodules,function(b){b.startWithParent&&b.start(a)}),this.triggerMethod("before:start",a),this._initializerCallbacks.run(a,this),this._isInitialized=!0,this.triggerMethod("start",a))},stop:function(){this._isInitialized&&(this._isInitialized=!1,b.triggerMethod.call(this,"before:stop"),d.each(this.submodules,
function(a){a.stop()}),this._finalizerCallbacks.run(void 0,this),this._initializerCallbacks.reset(),this._finalizerCallbacks.reset(),b.triggerMethod.call(this,"stop"))},addDefinition:function(a,b){this._runModuleDefinition(a,b)},_runModuleDefinition:function(a,c){a&&(c=d.flatten([this,this.app,e,b,b.$,d,c]),a.apply(this,c))},_setupInitializersAndFinalizers:function(){this._initializerCallbacks=new b.Callbacks;this._finalizerCallbacks=new b.Callbacks}});d.extend(b.Module,{create:function(a,b,e){var c=
a,f=g.call(arguments);f.splice(0,3);b=b.split(".");var h=[];h[b.length-1]=e;d.each(b,function(b,d){var g=c;c=this._getModule(g,b,a,e);this._addModuleDefinition(g,c,h[d],f)},this);return c},_getModule:function(a,c,e,g,h){h=b.Module;var f=d.extend({},g);g&&(h=g.moduleClass||h);g=a[c];g||(g=new h(c,e,f),a[c]=g,a.submodules[c]=g);return g},_addModuleDefinition:function(a,b,e,g){if(d.isFunction(e)){var c=e;e=!0}else d.isObject(e)?(c=e.define,e="undefined"!==typeof e.startWithParent?e.startWithParent:!0):
e=!0;c&&b.addDefinition(c,g);b.startWithParent=b.startWithParent&&e;b.startWithParent&&!b.startWithParentIsConfigured&&(b.startWithParentIsConfigured=!0,a.addInitializer(function(a){b.startWithParent&&b.start(a)}))}});b.Wreqr=q;b.ChildViewContainer=m;return b})(this,p,h)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:marionette', location = '/static/lib/marionette/backbone.marionette.mixins.js' */
define("jira/marionette/marionette.mixins",[],function(){"use strict";return{viewExtensions:{unwrapTemplate:function(){if(this.$el.parent().length){var e=this.$el.children();this.$el.replaceWith(e),this.setElement(e)}else this.setElement(this.$el.children())}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:marionette', location = '/static/lib/amd-shims/marionette-amd.js' */
define("marionette",["require"],function(e){"use strict";var t=e("atlassian/libs/factories/marionette-1.6.1"),i=e("backbone"),n=e("underscore"),r=t(n,i),a=e("jira/marionette/marionette.mixins");return n.extend(r.View.prototype,a.viewExtensions),r}),AJS.namespace("Backbone.Marionette",null,require("marionette"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:indeterminate-checkbox-ie', location = '/static/lib/internal/util/indeterminate-checkbox-ie-fix.js' */
define("internal/util/indeterminate-checkbox-ie-fix",["internal/util/navigator","jira/util/key-code","jira/skate","jquery"],function(e,i,n,t){function r(e){return"mouseup"===e.type||"click"===e.type||"keyup"===e.type&&e.keyCode===i.SPACE}e.isIE()&&n("indeterminate-ie-fix",{type:n.type.CLASSNAME,insert:function(e){var i,n=t(e);n.on("keyup mouseup",function(e){r(e)&&(i=n.is(":indeterminate"))}),n.click(function(e){r(e)&&i&&(n.prop("checked",!0),n.trigger("change"))})}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:application-selector', location = '/includes/jira/application-selector/application.js' */
define("jira/admin/application-selector/application",["jira/jquery/deferred","marionette","underscore","aui/inline-dialog2","internal/util/indeterminate-checkbox-ie-fix"],function(i,e,n){"use strict";var t=e.ItemView.extend({initialize:function(){this.bindUIElements()},ui:{label:".application-label",effectiveWarning:".application-picker-applications__effective-warning"},changeInlineWarning:function(i){var e="";e=i===t.WARNINGS.EFFECTIVE?this.ui.effectiveWarning.prop("id"):this._getNonIndeterminateWarningId(),this.triggerMethod("inline:warning:change",{controls:e})},setIndeterminate:function(i,e){e=n.isObject(e)?e:{},this.triggerMethod("indeterminate:change",{indeterminate:i}),i?!0!==e.silent&&this.displayWarning(t.WARNINGS.EFFECTIVE):this.changeInlineWarning(t.WARNINGS.NONEFFECTIVE)},getEffectiveWarning:function(){var e=new i;return setTimeout(function(){var i=this.ui.effectiveWarning[0];i&&i.hasAttribute("resolved")?e.resolve(i):e.reject()}.bind(this)),e},showEffectiveWarning:function(){this.getEffectiveWarning().then(function(i){i.open=!0})},hideEffectiveWarning:function(){this.getEffectiveWarning().then(function(i){i.open=!1})},displayWarning:function(i){switch(i){case t.WARNINGS.EFFECTIVE:this.changeInlineWarning(i),this.showEffectiveWarning()}}},{TOGGLE_EVENT:"application:toggle",WARNING_DIALOG_OPENED_EVENT:"dialog:opened",WARNINGS:{EFFECTIVE:"effective",NONEFFECTIVE:"noneffective"}});return t});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:application-selector', location = '/includes/jira/application-selector/application-critical.js' */
define("jira/admin/application-selector/application-critical",["jira/admin/application-selector/application","underscore"],function(i,n){return i.extend({ui:n.extend({},i.prototype.ui,{criticalWarning:".application-warning"}),getApplicationKey:function(){return this.ui.criticalWarning.data("key")},isDisabled:function(){return this.ui.label.hasClass("disabled")},setDisabled:function(i){return this.ui.label.toggleClass("disabled",i),this},isIndeterminateButNotEffective:function(){return!1},onIndeterminateChange:function(i){this.ui.criticalWarning.toggleClass("effective",i.indeterminate),this.setDisabled(i.indeterminate)},onInlineWarningChange:function(i){this.ui.criticalWarning.attr("aria-controls",i.controls)},_getNonIndeterminateWarningId:function(){return this.ui.criticalWarning.data("warningId")},isSelected:function(){return!1},setSelected:function(){}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:application-selector', location = '/includes/jira/application-selector/application-selectable.js' */
define("jira/admin/application-selector/application-selectable",["jira/admin/application-selector/application","underscore"],function(i,e){return i.extend({ui:e.extend({},i.prototype.ui,{checkbox:"input[type=checkbox]",undefinedApplicationDialog:".application-not-defined-dialog"}),events:{"change @ui.checkbox":function(e,n){var t=!0;if(n&&!n.manual&&(t=!1),this.hideEffectiveWarning(),this.trigger(i.TOGGLE_EVENT,{manual:t}),!this.isDefined()){var a=this.getUndefinedWarning();this.isSelected()?(this.trigger(i.WARNING_DIALOG_OPENED_EVENT,{type:i.WARNINGS.NONEFFECTIVE}),!0!==this.options.disableUndefinedWarningDisplaying&&this.displayWarning(i.WARNINGS.NONEFFECTIVE)):a.open=!1}}},getApplicationKey:function(){return this.ui.checkbox.data("key")},getEffective:function(){return this.ui.checkbox.data("effective")||[]},isDisabled:function(){return this.ui.checkbox.prop("disabled")},setDisabled:function(i){return this.ui.checkbox.prop("disabled",i),this},isDefined:function(){return"true"===this.ui.checkbox.attr("data-defined")},isSelected:function(){return this.ui.checkbox.prop("checked")},setSelected:function(i){i!==this.isSelected()&&(this.ui.checkbox.prop("checked",i),this.ui.checkbox.trigger("change",{manual:!1}))},isIndeterminateButNotEffective:function(){return"indeterminate"===this.ui.checkbox.data("indeterminate")},onIndeterminateChange:function(i){this.ui.checkbox.prop("indeterminate",i.indeterminate)},getUndefinedWarning:function(){return this.ui.undefinedApplicationDialog.get(0)},onInlineWarningChange:function(i){this.ui.checkbox.attr("aria-controls",i.controls)},_getNonIndeterminateWarningId:function(){return this.ui.undefinedApplicationDialog.prop("id")},displayWarning:function(e){if(e===i.WARNINGS.NONEFFECTIVE){this.changeInlineWarning(i.WARNINGS.NONEFFECTIVE);this.getUndefinedWarning().open=!0}else i.prototype.displayWarning.call(this,e)}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:application-selector', location = '/includes/jira/application-selector/application-selector.js' */
define("jira/admin/application-selector",["jira/admin/application-selector/application","jira/admin/application-selector/application-critical","jira/admin/application-selector/application-selectable","jira/skate","marionette","underscore","jquery"],function(e,i,t,n,s,c,a){"use strict";return s.CollectionView.extend({itemEvents:c.object([[e.TOGGLE_EVENT,function(e,i){this.numberOfAppsSelectedExcludingCore()?this._makeCoreIncluded():this.hasCore()&&this.core!==i&&this._makeCoreAvailable(),this.selectEffectiveApplications()}]]),initialize:function(s){this.disableEffectiveAccess=s.disableEffectiveAccess,this.disableUndefinedWarningDisplaying=s.disableUndefinedWarningDisplaying;var c=a(s.el).find(".application");c.length&&(n.init(s.el),c.each(function(e,n){var s,c=a(n),l=c.data("indeterminate"),r={el:n.parentNode,disableUndefinedWarningDisplaying:this.disableUndefinedWarningDisplaying};s=c.hasClass("application-warning")?new i(r):new t(r),l&&s.setIndeterminate(!0,{silent:"effective"!==l}),this.addChildViewEventForwarding(s),this.children.add(s,s.getApplicationKey())}.bind(this)),this.core=this.children.findByCustom("jira-core"),this.hasCore()&&(this.listenTo(this.core,e.TOGGLE_EVENT,function(e){e.manual&&(this.coreRemainChecked=this.core.isSelected())}),this.coreRemainDisabled=this.core.isDisabled(),this.coreRemainChecked=this.core.isSelected()),this.numberOfAppsSelectedExcludingCore()>0&&this._makeCoreIncluded(),this.selectEffectiveApplications())},disableAllApplications:function(){this.children.each(function(e){e.setDisabled(!0)})},selectApplicationsBasedOnURL:function(e){var i=this._getApplicationKeysToPreselectFromURL(e);i.length&&(this.deselectAll(),i.forEach(function(e){var i=this.getByKey(e);i&&!i.isDisabled()&&i.setSelected(!0)}.bind(this))),this.selectEffectiveApplications()},selectEffectiveApplications:function(){if(!this.disableEffectiveAccess){var e=c.flatten(this.children.toArray().filter(function(e){return e.isSelected()&&!e.isDisabled()}).map(function(e){return e.getEffective()})).map(this.getByKey,this);this.children.each(function(i){!i.isSelected()&&e.indexOf(i)>=0?i.setIndeterminate(!0):i.setIndeterminate(!1)},this)}},_getApplicationKeysToPreselectFromURL:function(e){var i=[];e=e||location.search;var t=e.split("?")[1];if(t){t.split("&").forEach(function(e){var t=e.split("=");"application"===t[0]&&i.push(t[1])}.bind(this))}return i},numberOfAppsSelectedExcludingCore:function(){var e=0,i=this;return this.children.each(function(t){i.hasCore()&&t!==i.core&&(t.isSelected()||t.isIndeterminateButNotEffective())&&(e+=1)}),e},deselectAll:function(){this.getAll().each(function(e){e.setSelected(!1)})},getAll:function(){return this.children},getByKey:function(e){return this.children.findByCustom(e)},hasCore:function(){return!!this.core},_makeCoreIncluded:function(){this.hasCore()&&(this.core.setDisabled(!0),this.core.setSelected(!0))},_makeCoreAvailable:function(){this.hasCore()&&(this.coreRemainDisabled||this.core.setDisabled(!1),this.coreRemainChecked||this.core.setSelected(!1))}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:application-selector', location = '/includes/jira/application-selector/application-selector.soy' */
// This file was automatically generated from application-selector.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.ApplicationSelector.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.ApplicationSelector == 'undefined') { JIRA.Templates.ApplicationSelector = {}; }


JIRA.Templates.ApplicationSelector.applicationSelector = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  var output = '<fieldset class="' + ((opt_data.additionalClasses != null) ? soy.$$escapeHtml(opt_data.additionalClasses) : '') + ' application-picker">' + ((opt_data.legend) ? '<legend><span>' + soy.$$escapeHtml(opt_data.legend) + '</span></legend>' : '') + '<div class="application-picker-applications">';
  if (opt_data.selectableApplications) {
    var applicationList16 = opt_data.selectableApplications;
    var applicationListLen16 = applicationList16.length;
    for (var applicationIndex16 = 0; applicationIndex16 < applicationListLen16; applicationIndex16++) {
      var applicationData16 = applicationList16[applicationIndex16];
      output += JIRA.Templates.ApplicationSelector.applicationCheckbox({application: applicationData16, showInfoMessages: opt_data.showInfoMessages, isEnabled: opt_data.isEnabled, enableSelectedDeselectableApps: opt_data.enableSelectedDeselectableApps});
    }
  }
  output += '</div></fieldset>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ApplicationSelector.applicationSelector.soyTemplateName = 'JIRA.Templates.ApplicationSelector.applicationSelector';
}


JIRA.Templates.ApplicationSelector.applicationCheckbox = function(opt_data, opt_ignored) {
  var output = '';
  var enableSelectedDeselectableApplications__soy25 = opt_data.enableSelectedDeselectableApps != null ? opt_data.enableSelectedDeselectableApps : false;
  var isApplicationEnabled__soy26 = opt_data.isEnabled != null ? opt_data.isEnabled : true;
  var isApplicationEnabled__soy27 = isApplicationEnabled__soy26 == true && (opt_data.application.selectable != false || enableSelectedDeselectableApplications__soy25 == true && opt_data.application.deselectable == true);
  var isApplicationCritical__soy28 = opt_data.showInfoMessages != false && ! isApplicationEnabled__soy27;
  var randomId__soy29 = Math.floor(Math.random() * 10000);
  output += '<div class="checkbox">';
  if (isApplicationCritical__soy28) {
    output += aui.inlineDialog2.inlineDialog2({id: 'inline-dialog2-' + soy.$$escapeHtml(opt_data.application.key) + '-' + soy.$$escapeHtml(randomId__soy29), alignment: 'right middle', respondsTo: 'toggle', extraClasses: 'application-picker-dialog application-warning-dialog application-warning-dialog-' + soy.$$escapeHtml(opt_data.application.key), content: '' + soy.$$filterNoAutoescape(opt_data.application.messageMarkup)}) + '<a href="#" id="' + soy.$$escapeHtml(opt_data.application.key) + '-' + soy.$$escapeHtml(randomId__soy29) + '" class="application application-warning application-warning-' + soy.$$escapeHtml(opt_data.application.key) + ' aui-icon aui-iconfont-warning" aria-controls="inline-dialog2-' + soy.$$escapeHtml(opt_data.application.key) + '-' + soy.$$escapeHtml(randomId__soy29) + '" data-key="' + soy.$$escapeHtml(opt_data.application.key) + '" data-aui-trigger data-warning-id="inline-dialog2-' + soy.$$escapeHtml(opt_data.application.key) + '-' + soy.$$escapeHtml(randomId__soy29) + '"></a> ';
  } else {
    var indeterminate__soy65 = '' + ((opt_data.application.effectiveOfOtherApplication) ? 'effective' : (opt_data.application.indeterminate) ? 'indeterminate' : '');
    output += aui.form.input({type: 'checkbox', id: soy.$$escapeHtml(opt_data.application.key) + '-' + soy.$$escapeHtml(randomId__soy29), name: 'selectedApplications', value: opt_data.application.key, isChecked: opt_data.application.selected == true, isDisabled: ! isApplicationEnabled__soy27, extraClasses: 'application application-' + soy.$$escapeHtml(opt_data.application.key) + ' indeterminate-ie-fix', extraAttributes: {'data-key': opt_data.application.key, 'data-defined': opt_data.application.defined, 'data-effective': opt_data.application.effectiveApplicationsJson, 'data-indeterminate': indeterminate__soy65, 'aria-controls': 'inline-dialog2-' + opt_data.application.key + '-not-defined-' + randomId__soy29}}) + ((! opt_data.application.defined) ? JIRA.Templates.ApplicationSelector.undefinedWarning({application: opt_data.application, randomId: randomId__soy29}) : '');
  }
  output += aui.form.label({forField: soy.$$escapeHtml(opt_data.application.key) + '-' + soy.$$escapeHtml(randomId__soy29), content: '' + ((opt_data.application.displayName) ? soy.$$escapeHtml(opt_data.application.displayName) : soy.$$escapeHtml(opt_data.application.name)), extraClasses: 'application-label'}) + ((opt_data.application.effectiveApplications != null) ? JIRA.Templates.ApplicationSelector.effectiveWarning({criticalApplication: isApplicationCritical__soy28, application: opt_data.application}) : '') + '</div>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ApplicationSelector.applicationCheckbox.soyTemplateName = 'JIRA.Templates.ApplicationSelector.applicationCheckbox';
}


JIRA.Templates.ApplicationSelector.effectiveWarning = function(opt_data, opt_ignored) {
  var output = '';
  var safeName__soy110 = '' + soy.$$escapeHtml(opt_data.application.name);
  var warningContent__soy113 = '' + ((opt_data.criticalApplication) ? soy.$$filterNoAutoescape(AJS.format('{0}\u7684\u7ec4\u7684\u9ed8\u8ba4\u5206\u914d\u7ed9\u5176\u4ed6\u5e94\u7528\u7a0b\u5e8f\u4e2d, \u4f46\u60a8\u65e0\u6cd5\u521b\u5efa{0}\u7684\u7528\u6237\u3002{1}{2}{3}\u7684\u66f4\u65b0\u60a8\u7684\u9ed8\u8ba4\u7ec4\u3002{4}',safeName__soy110,'<a href="',"",'/secure/admin/ApplicationAccess.jspa">','</a>')) : soy.$$filterNoAutoescape(AJS.format('{0}\u7684\u7ec4\u7684\u9ed8\u8ba4\u5206\u914d\u7ed9\u5176\u4ed6\u5e94\u7528\u7a0b\u5e8f\u3002{1}{2}{3}\u7684\u66f4\u65b0\u60a8\u7684\u9ed8\u8ba4\u7ec4\u3002{4}',safeName__soy110,'<a href="',"",'/secure/admin/ApplicationAccess.jspa">','</a>')));
  output += aui.inlineDialog2.inlineDialog2({id: soy.$$escapeHtml(opt_data.application.key) + '-effective-warning', alignment: 'right middle', respondsTo: 'toggle', extraClasses: 'application-picker-dialog application-picker-applications__effective-warning ' + ((warningContent__soy113) ? 'critical-application' : ''), content: warningContent__soy113});
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ApplicationSelector.effectiveWarning.soyTemplateName = 'JIRA.Templates.ApplicationSelector.effectiveWarning';
}


JIRA.Templates.ApplicationSelector.undefinedWarning = function(opt_data, opt_ignored) {
  var output = '';
  var safeName__soy134 = '' + soy.$$escapeHtml(opt_data.application.name);
  output += aui.inlineDialog2.inlineDialog2({id: 'inline-dialog2-' + soy.$$escapeHtml(opt_data.application.key) + '-not-defined-' + soy.$$escapeHtml(opt_data.randomId), alignment: 'right middle', respondsTo: 'toggle', extraClasses: 'application-picker-dialog application-not-defined-dialog application-not-defined-dialog-' + soy.$$escapeHtml(opt_data.application.key), content: '' + soy.$$filterNoAutoescape(AJS.format('{0}\u7684\u8bb8\u53ef\u8bc1, \u4f46\u672a\u5b89\u88c5\u7684\u5b9e\u4f8b\u3002{1}\u5e94\u7528\u7a0b\u5e8f\u7ba1\u7406{2}\u6765\u5411\u60a8\u7684\u7528\u6237\u63d0\u4f9b\u5b8c\u5168\u8bbf\u95ee\u5176\u529f\u80fd\u3002',safeName__soy134,'<a href="' + "" + '/plugins/servlet/applications/versions-licenses">','</a>'))});
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ApplicationSelector.undefinedWarning.soyTemplateName = 'JIRA.Templates.ApplicationSelector.undefinedWarning';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.splitchunk.e8173e0382', location = 'aui.chunk.8228e810c0a5732c7415--8ceb68991b3c4f528c9a.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.splitchunk.e8173e0382"],{"3vMk":function(n,t,i){"use strict";i.r(t);i("rSV2"),i("YQ7q"),i("6fSn"),i("Nu/Z"),i("98E3"),i("U8Ze")},"98E3":function(n,t,i){},U8Ze:function(n,t,i){"use strict";i.d(t,"c",function(){return j}),i.d(t,"b",function(){return T}),i.d(t,"a",function(){return v}),i.d(t,"d",function(){return y});var a=i("+x/D"),o=i("TmQU"),r=i("4dFR"),c=(i("tYoR"),i("bPPT"));const e="_aui-form-notification-initialised",u="data-aui-notification-wait",f="data-aui-notification-info",s="data-aui-notification-error",d=[s,"data-aui-notification-success",u,f];function l(n){p(n)||(!function(n){n.addClass(e),v(n)}(n),b(n))}function p(n){return n.hasClass(e)}function v(n,t){t=t||m(n),h(n)===f&&n.after(function(n){if(n.length>1){let t=n.map(n=>"<li>".concat(n,"</li>")).join("");return'<div class="description"><ul>'.concat(t,"</ul></div>")}return'<div class="description">'.concat(n,"</div>")}(t))}function m(n){var t=h(n),i=t?n.attr(t):"";return""===i?i:function(n){var t;try{t=JSON.parse(n)}catch(i){t=[n]}return t}(i)}function h(n){var t;return d.some(function(i){if(n.is("["+i+"]"))return t=i,!0}),t}function b(n){const t=Object(a.a)(n);if(!p(t))return;const i=h(t);y(t,i===u);const o=m(t);o&&i===s?T(t,o):function(n){return n.constructor.prototype.hasOwnProperty("jquery")}(n)||n.hasAttribute(s)||t.parent().find(".error").remove()}function T(n,t){let i=j(n,"error");i.length>0&&i.remove(),n.after(function(n){let t=n.map(n=>'<li><span class="aui-icon aui-icon-small aui-iconfont-error aui-icon-notification">'.concat(n,"</span>").concat(n,"</li>")).join("");return'<div class="error"><ul>'.concat(t,"</ul></div>")}(t))}function j(n,t){return n.parent().find(".".concat(t))}function y(n,t){t&&!function(n){return n.next("aui-spinner").length>0}(n)?n.after('<aui-spinner class="form-notification-spinner" size="small"></aui-spinner>'):n.parent().find("aui-spinner").remove()}const O=Object(c.c)("data-aui-notification-field attribute",{deprecationType:"ATTRIBUTE",alternativeName:"HTML markup"});Object(r.a)("data-aui-notification-field",{attached:function(n){O(),l(Object(a.a)(n))},attributes:function(){const n={};return d.forEach(function(t){n[t]=b}),n}(),type:r.a.type.ATTRIBUTE}),Object(o.a)("aui/form-notification")}}]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.component.form-notification', location = 'aui.chunk.c7fb0d0f0cf9f8f47dd8--efb8cb7f8fbd8533ebcf.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.component.form-notification"],[],[["3vMk","runtime","aui.splitchunk.vendors--894c8113d9","aui.splitchunk.vendors--95c789edf5","aui.splitchunk.vendors--9c48cc20a9","aui.splitchunk.vendors--be1eb78c1a","aui.splitchunk.vendors--23f50a6f00","aui.splitchunk.0d131bcbf1","aui.splitchunk.fbbef27525","aui.splitchunk.444efc83be","aui.splitchunk.739b9ec8cc","aui.splitchunk.056561461c","aui.splitchunk.949297951c","aui.splitchunk.dd803a46b4","aui.splitchunk.d7c46c2734","aui.splitchunk.e54c7c7304","aui.splitchunk.fb15cffa72","aui.splitchunk.56dfb54d0c","aui.splitchunk.f673ef53ac","aui.splitchunk.9c48cc20a9","aui.splitchunk.908fe798b4","aui.splitchunk.462ee5f9ef","aui.splitchunk.26116b3cbd","aui.splitchunk.ed86a19e01","aui.splitchunk.50dca3e042","aui.splitchunk.be1eb78c1a","aui.splitchunk.23f50a6f00","aui.splitchunk.e8173e0382"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:add-user', location = '/includes/jira/admin/add-user/add-user-init.js' */
require(["jira/admin/application-selector","jira/skate","jquery","aui/inline-dialog2","aui/form-notification"],function(i,a,n){n(function(){var o=n(".application-picker"),t=o[0],e=new i({el:o});t&&a.init(t),e.selectApplicationsBasedOnURL();var l=n(".send-email-help");l.tooltip({gravity:"w",trigger:"manual",className:"aui-form-notification-tooltip aui-form-notification-tooltip-info"}),n("#sendEmail").focus(function(){l.tipsy("show")}).blur(function(){l.tipsy("hide")})})});
}catch(e){WRMCB(e)};