WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:rte-saving-kb-shortcuts', location = 'js/editor-saving-kb-shortcuts.js' */
define("jira/editor/saving-kb-shortcuts",["jquery","jira/editor/analytics","jira/util/navigator","jira/util/data/meta","jira/util/formatter"],function(c,h,l,a,g){function e(m){return(m.ctrlKey||(l.isMac()&&m.metaKey))&&(m.keyCode===13||(!l.isMac()&&(m.keyCode===10)))}function b(){return(a.get("keyboard-accesskey-modifier")||"").split("+").map(function(m){return m.trim().toLowerCase()+"Key"})}function k(){var m="S";return m&&m.length===1?m.toUpperCase().charCodeAt(0):0}var j=k();var f=b();function i(m){if(!j||j!==m.keyCode){return false}return f.every(function(n){return m[n]})}function d(m){m.getInstance().relayEvent("keydown",function(n){if(e(n)||i(n)){n.preventDefault();m.serialize().then(function(){h.sendEvent("editor.instance.submit");c(m.$textarea.prop("form")).submit()})}},true)}return{enable:d}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:schema-builder', location = 'js/editor-schema-builder.js' */
define("jira/editor/schema-builder",["jira/editor/tinymce","jquery","underscore","jira/util/logger"],function(n,b,m,o){var e=function(){this.noBreak=[];this.noNest=[];this.customElementChildren={};this.customElementAttrs={};this.validChildren=[];this.unwantedAttributes=[];this.extendedValidElements=[];this.baseSchema="html5";this.validElements=[]};e.prototype.withCustomElement=function(r,s,q){if(!f(r)){o.error("Could not add custom element: incorrect `name` given:",r,"(see https://www.w3.org/TR/custom-elements/#valid-custom-element-name)");return this}if(!c(s)){o.error("Could not add custom element: incorrect `children` given:",s);return this}if(!g(q)){o.error("Could not add custom element: incorrect `attributes` given:",q);return this}if(false===s){s=[]}else{if(undefined===s){s=["p","#comment"]}else{s.push("#comment")}}if(!q){q=[]}this.customElementChildren[r]=m.uniq(s);this.customElementAttrs[r]=m.uniq(q);return this};e.prototype.withBaseSchema=function i(q){this.baseSchema=q;return this};e.prototype.withValidElements=function l(q){this.validElements=q;return this};e.prototype.withNoBreak=function p(q){this.noBreak.push.apply(this.noBreak,q);return this};e.prototype.withNoSelfNest=function h(q){this.noNest.push.apply(this.noNest,q.map(function(r){return[r,r]}));return this};e.prototype.setValidChildren=function d(r,q){this.validChildren.push(r+"["+q+"]");return this};e.prototype.withExtendedValidElements=function(q){this.extendedValidElements.push.apply(this.extendedValidElements,q);return this};e.prototype.withNoNest=function h(q){this.noNest.push.apply(this.noNest,q);return this};e.prototype.withUnwantedAttributes=function j(q){this.unwantedAttributes.push.apply(this.unwantedAttributes,q);return this};e.prototype.build=function k(){var A=this.baseSchema;var r=[];var s=[];var q=[];var t=[];var y={};this.noBreak.forEach(function(C){v(C)});this.noNest.forEach(function(C){u.apply(null,C)});r.push.apply(r,Object.keys(this.customElementChildren));b.each(this.customElementChildren,function(C,D){y[C]=D.reduce(function(E,F){E[F]={};return E},{})});s.push.apply(s,this.validChildren);q=a(this.validElements,this.unwantedAttributes,A);t.push.apply(t,m.map(this.customElementAttrs,function(D,C){return C+(D.length?"["+D.join("|")+"]":"")}));t.push.apply(t,this.extendedValidElements);return{getValidChildren:function x(){return s.join(",")},getValidElements:function B(){return q.join(",")},getExtendedValidElements:function z(){return t.join(",")},getCustomElements:function(){return r.join(",")},getType:function w(){return A},fixChildren:function(C){b.extend((C||{}).children,y)}};function u(C,D){s.push("-"+C+"["+D+"]")}function v(C){u(C,"br")}};return e;function a(t,r,s){var u=new n.html.Schema({schema:s});var q=u.elements;q.img.attributesRequired=["src"];q.img.attributes.imagetext={};q.img.attributesOrder.push("imagetext");return t.map(function(x){var w=q[x];if(!w){return false}var z=(w.removeEmptyAttrs)?"!":"";var y="";if(w.paddEmpty){y="#"}else{if(w.removeEmpty){y="-"}}var v=w.attributesOrder.map(function(A){var B="";if((w.attributesRequired||[]).indexOf(A)!==-1){B="!"}return B+A}).filter(function(A){return r.indexOf(A)===-1});return y+x+z+"["+v.join("|")+"]"}).filter(function(v){return !!v})}function f(q){return typeof q==="string"&&q.indexOf("-")>0}function c(q){if(m.isArray(q)){return m.every(q,function(r){return typeof r==="string"})}return false===q||undefined===q}function g(q){if(m.isArray(q)){return m.every(q,function(r){return typeof r==="string"})}return undefined===q}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:schema-builder', location = 'js/editor-supported-operations.js' */
define("jira/richeditor/editor-supported-operations",["underscore"],function(b){var g=["bold","italic","underline","bullet-list","numbered-list","h1","h2","h3","h4","h5","h6","paragraph","paragraph-quote","block-quote","monospace","delete","superscript","subscript","cite","hr","color","icon","attachment","code","panel","noformat","table","undo","redo"];var f=["br","span","a","p","div"];var a=d(g,f);return{getWikiOperations:function c(){return g},getRequiredHtmlTags:function e(){return a},mapWikiOperationToHtmlTags:d};function d(j,h){var l={bold:["strong","b"],italic:["em","i"],underline:["ins","u"],"bullet-list":["li","ul"],"numbered-list":["li","ol"],paragraph:[],"paragraph-quote":["blockquote"],"block-quote":["blockquote"],monospace:["samp","tt","code"],"delete":["del"],superscript:["sup"],subscript:["sub"],color:["font"],icon:["img"],attachment:["a","img","sup"],code:["pre"],panel:["b"],noformat:["pre"],table:["table","thead","tbody","tr","th","td","tfoot","caption","colgroup","col"],undo:[],redo:[]};var k=b.map(j,function(m){return l[m]||[m]});k.push(h||[]);var i=b.flatten(k);return b.uniq(i)}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:html-browser-converter', location = 'js/util/html-browser-converter.js' */
define("jira/editor/util/html-browser-converter",[],function(){return{convert:function(a){if(!a){return""}try{const b=document.createElement("template");b.innerHTML=a;return b.innerHTML}catch(c){return""}},}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:schema', location = 'js/editor-schema.js' */
define("jira/editor/schema",["jira/editor/schema-builder","jira/richeditor/editor-supported-operations","jira/editor/tinymce","jira/editor/util/html-browser-converter","jquery"],function(e,a,k,l,d){var b=["strong","b","em","i","tt","code","samp","sub","sup","del","cite","panel-title"];var h=[["td","table"],["th","table"],["td","p"],["th","p"]];var g=[];var c=["strong/b"];g.push("xml:lang");g.push("xml:space");var f={};f.getSchemaSpecBuilder=function(m){if(undefined===m){m=new e()}return m.withValidElements(a.getRequiredHtmlTags()).withNoBreak(b).withNoSelfNest(b).withNoNest(h).withUnwantedAttributes(g).withExtendedValidElements(c).withCustomElement("panel-title",["span","#text"],["style"]).setValidChildren("pre","br|span|#text|pre|p|panel-title")};f.getPasteSchemaSpecBuilder=function(m){if(undefined===m){m=f.getSchemaSpecBuilder()}return m.withUnwantedAttributes(["contenteditable"])};f.getPasteInsidePreSchemaSpecBuilder=function(m){if(undefined===m){m=new e()}return m.withValidElements(["br","span","p","pre"]).withUnwantedAttributes(["style"])};f.getSchemaFromSpec=function(n){var m=new k.html.Schema({schema:n.getType(),custom_elements:n.getCustomElements(),valid_children:n.getValidChildren(),valid_elements:n.getValidElements(),extended_valid_elements:n.getExtendedValidElements()});n.fixChildren(m);return m};function j(m){if("textContent" in document.body){m=m.replace(/(<br>|<br \/>)/g,"\n")}var n=d("<div>").addClass("richeditor-sanitize").append(m)[0];try{d(n).appendTo(document.body);return AJS.escapeHTML(n.textContent||n.innerText)}finally{d(n).remove()}}f.sanitizeHtml=function i(o,p,t){var m=d(p.selection.getStart()).add(p.selection.getEnd()).add(p.selection.getNode());o=l.convert(o);if(m.is("pre, pre > *")){o=m.clone().html(j(o)).prop("outerHTML")}if(m.is("panel-title, panel-title > *")){o=j(o)}var q=f.getSchemaFromSpec(t);var r=new k.html.DomParser(p.settings,q);var n=new k.html.Serializer(p.settings,q);var s=r.parse(o,{forced_root_block:undefined});return n.serialize(s)};return f});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:i18n', location = '/js/editor-i18n.js' */
define("jira/editor/i18n",["jira/editor/tinymce"],function(a){a.addI18n("en",{Cut:"\u526a\u5207","Header 2":"\u6807\u9898 2","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"\u60a8\u7684\u6d4f\u89c8\u5668\u4e0d\u652f\u6301\u76f4\u63a5\u8bbf\u95ee\u526a\u8d34\u677f\u3002\u8bf7\u6539\u4e3a\u4f7f\u7528Ctrl+X/C/V\u952e\u76d8\u5feb\u6377\u952e\u3002",Div:"Div",Paste:"\u7c98\u8d34",Close:"\u5173\u95ed","Font Family":"\u5b57\u4f53\u7cfb\u5217",Pre:"\u9884","Align right":"\u53f3\u5bf9\u9f50","New document":"\u65b0\u6587\u4ef6",Blockquote:"\u5f15\u6587\u533a\u5757","Numbered list":"\u6570\u5b57\u5217\u8868","Increase indent":"\u589e\u52a0\u7f29\u8fdb",Formats:"\u683c\u5f0f",Headers:"\u6807\u9898","Select all":"\u5168\u9009","Header 3":"\u6807\u9898 3",Blocks:"\u533a\u5757",Undo:"\u64a4\u9500",Strikethrough:"\u5220\u9664\u7ebf","Bullet list":"\u7f16\u53f7\u5217\u8868","Header 1":"\u6807\u9898 1",Superscript:"\u4e0a\u6807","Clear formatting":"\u6e05\u9664\u683c\u5f0f\u5316","Font Sizes":"\u5b57\u4f53\u5927\u5c0f",Subscript:"\u4e0b\u6807","Header 6":"\u6807\u9898 6",Redo:"\u91cd\u505a",Paragraph:"\u6bb5\u843d",Ok:"\u786e\u5b9a",Bold:"\u9ed1\u4f53",Code:"\u4ee3\u7801",Italic:"\u659c\u4f53","Align center":"\u4e2d\u95f4\u5bf9\u9f50","Header 5":"\u6807\u9898 5","Decrease indent":"\u51cf\u5c11\u7f29\u8fdb","Header 4":"\u6807\u9898 4","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"\u7c98\u8d34\u73b0\u5728\u662f\u7eaf\u6587\u5b57\u6a21\u5f0f\u3002\u5185\u5bb9\u73b0\u5728\u5c06\u88ab\u7c98\u8d34\u4e3a\u7eaf\u6587\u5b57\uff0c\u76f4\u5230\u60a8\u5207\u6362\u5230\u5173\u95ed\u6b64\u9009\u9879\u3002",Underline:"\u4e0b\u5212\u7ebf",Cancel:"\u53d6\u6d88",Justify:"\u8bc1\u660e",Inline:"\u5185\u5d4c",Copy:"\u590d\u5236","Align left":"\u5de6\u5bf9\u9f50","Visual aids":"\u53ef\u89c6\u52a9\u624b","Lower Greek":"\u5c0f\u5199\u5e0c\u814a\u6587",Square:"\u65b9\u5f62",Default:"\u9ed8\u8ba4","Lower Alpha":"\u5c0f\u5199\u5b57\u6bcd",Circle:"\u5706",Disc:"\u5149\u76d8","Upper Alpha":"\u5927\u5199\u5b57\u6bcd","Upper Roman":"\u5927\u5199\u7f57\u9a6c\u5b57\u6bcd","Lower Roman":"\u5c0f\u5199\u7f57\u9a6c\u5b57\u6bcd",Name:"\u540d\u79f0",Anchor:"\u951a\u70b9","You have unsaved changes are you sure you want to navigate away?":"\u60a8\u6709\u672a\u4fdd\u5b58\u7684\u66f4\u6539\u3002\u60a8\u786e\u5b9a\u8981\u79bb\u5f00\u5417?","Restore last draft":"\u6062\u590d\u4e0a\u6b21\u8349\u7a3f","Special character":"\u7279\u6b8a\u5b57\u7b26","Source code":"\u6e90\u4ee3\u7801","Right to left":"\u4ece\u53f3\u5230\u5de6","Left to right":"\u4ece\u5de6\u5230\u53f3",Emoticons:"\u8868\u60c5\u56fe\u6807",Robots:"\u673a\u5668\u4eba","Document properties":"\u6587\u4ef6\u5c5e\u6027",Title:"\u6807\u9898",Keywords:"\u5173\u952e\u5b57",Encoding:"\u7f16\u7801",Description:"\u63cf\u8ff0",Author:"\u4f5c\u8005",Fullscreen:"\u5168\u5c4f","Horizontal line":"\u6c34\u5e73\u7ebf","Horizontal space":"\u6c34\u5e73\u7a7a\u95f4","Insert/edit image":"\u63d2\u5165/\u7f16\u8f91\u56fe\u50cf",General:"\u901a\u7528\u914d\u7f6e",Advanced:"\u9ad8\u7ea7",Source:"\u6e90\u7801",Border:"\u8fb9\u754c","Constrain proportions":"\u9650\u5236\u6bd4\u4f8b","Vertical space":"\u5782\u76f4\u7a7a\u95f4","Image description":"\u56fe\u50cf\u8bf4\u660e",Style:"\u6837\u5f0f",Dimensions:"\u5c3a\u5bf8","Insert image":"\u63d2\u5165\u56fe\u50cf","Insert date/time":"\u63d2\u5165\u65e5\u671f/\u65f6\u95f4","Remove link":"\u5220\u9664\u94fe\u63a5",Url:"\u7f51\u5740","Text to display":"\u8981\u663e\u793a\u7684\u6587\u5b57",Anchors:"\u951a\u70b9","Insert link":"\u63d2\u5165\u94fe\u63a5","New window":"\u65b0\u5efa\u7a97\u53e3",None:"\u65e0","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"\u60a8\u8f93\u5165\u7684URL\u4f3c\u4e4e\u662f\u4e00\u4e2a\u5916\u90e8\u94fe\u63a5\u3002\u60a8\u60f3\u8981\u6dfb\u52a0\u6240\u9700\u7684http://\u524d\u7f00\u5417?",Target:"\u76ee\u6807","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"\u60a8\u8f93\u5165\u7684URL\u4f3c\u4e4e\u662f\u4e00\u4e2a\u7535\u90ae\u5730\u5740\u3002\u60a8\u60f3\u8981\u6dfb\u52a0\u6240\u9700\u7684\u90ae\u5740\u524d\u7f00\u5417?","Insert/edit link":"\u63d2\u5165/\u7f16\u8f91\u94fe\u63a5","Insert/edit video":"\u63d2\u5165/\u7f16\u8f91\u89c6\u9891",Poster:"\u6d77\u62a5","Alternative source":"\u5176\u5b83\u6e90","Paste your embed code below:":"\u7c98\u8d34\u60a8\u7684\u5d4c\u5165\u4ee3\u7801\u5982\u4e0b\uff1a","Insert video":"\u63d2\u5165\u89c6\u9891",Embed:"\u5d4c\u5165","Nonbreaking space":"\u4e0d\u95f4\u65ad\u7a7a\u683c","Page break":"\u5206\u9875\u7b26","Paste as text":"\u7c98\u8d34\u4e3a\u6587\u5b57",Preview:"\u9884\u89c8",Print:"\u6253\u5370",Save:"\u4fdd\u5b58","Could not find the specified string.":"\u65e0\u6cd5\u627e\u5230\u6307\u5b9a\u7684\u5b57\u7b26\u4e32\u3002",Replace:"\u66f4\u6362",Next:"\u4e0b\u4e00\u6b65","Whole words":"\u6574\u4e2a\u5b57","Find and replace":"\u67e5\u627e\u548c\u66ff\u6362","Replace with":"\u66ff\u6362\u4e3a",Find:"\u67e5\u627e","Replace all":"\u5168\u90e8\u66ff\u6362","Match case":"\u5339\u914d\u5927\u5c0f\u5199",Prev:"\u4e0a\u4e00\u9875",Spellcheck:"\u62fc\u5199\u68c0\u67e5",Finish:"\u5b8c\u6210","Ignore all":"\u5ffd\u7565\u5168\u90e8",Ignore:"\u5ffd\u7565","Insert row before":"\u63d2\u5165\u884c\u524d",Rows:"\u884c",Height:"\u9ad8","Paste row after":"\u7c98\u8d34\u884c\u540e",Alignment:"\u5bf9\u9f50","Column group":"\u680f\u7ec4",Row:"\u884c","Insert column before":"\u63d2\u5165\u680f\u524d","Split cell":"\u62c6\u5206\u5355\u5143\u683c","Cell padding":"\u5355\u5143\u683c\u586b\u5145","Cell spacing":"\u5355\u5143\u683c\u95f4\u8ddd","Row type":"\u884c\u7c7b\u578b","Insert table":"\u63d2\u5165\u8868\u683c",Body:"\u5185\u5bb9",Caption:"\u8bf4\u660e\u6587\u5b57",Footer:"\u9875\u811a","Delete row":"\u5220\u9664\u884c","Paste row before":"\u7c98\u8d34\u884c\u524d",Scope:"\u89c4\u6a21","Delete table":"\u5220\u9664\u8868\u683c","Header cell":"\u6807\u9898\u5355\u5143\u683c",Column:"\u680f",Cell:"\u5355\u5143\u683c",Header:"\u6807\u9898","Cell type":"\u5355\u5143\u683c\u7c7b\u578b","Copy row":"\u590d\u5236\u884c","Row properties":"\u201c\u884c\u201d\u5c5e\u6027","Table properties":"\u8868\u683c\u5c5e\u6027","Row group":"\u884c\u7ec4",Right:"\u53f3\u952e","Insert column after":"\u63d2\u5165\u680f\u540e",Cols:"\u680f","Insert row after":"\u63d2\u5165\u884c\u540e",Width:"\u5bbd","Cell properties":"\u5355\u5143\u683c\u5c5e\u6027",Left:"\u5de6","Cut row":"\u526a\u5207\u884c","Delete column":"\u5220\u9664\u5217",Center:"\u4e2d\u5fc3","Merge cells":"\u5408\u5e76\u5355\u5143\u683c","Insert template":"\u63d2\u5165\u6a21\u677f",Templates:"\u6a21\u677f","Background color":"\u80cc\u666f\u8272","Text color":"\u6587\u672c\u989c\u8272","Show blocks":"\u663e\u793a\u5757","Show invisible characters":"\u663e\u793a\u4e0d\u53ef\u89c1\u7684\u5b57\u7b26","Words: {0}":"\u5b57\uff1a{0}",Insert:"\u63d2\u5165",File:"\u6587\u4ef6",Edit:"\u7f16\u8f91","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"\u5185\u5bb9\u7f16\u8f91\u5668",Tools:"\u5de5\u5177",View:"\u67e5\u770b",Table:"\u8868\u683c",Format:"\u683c\u5f0f"})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:wrm', location = 'js/editor-wrm.js' */
define("jira/editor/wrm",["exports","wrm/data"],function(a,b){var c=b.claim("com.atlassian.jira.plugins.jira-editor-plugin:wrm.editor-data")||{};a.isAttachmentsAllowed=function(){return c.allowedAttachments};a.getResources=function(){return c.resources};a.getConverters=function(){return c.converters}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter-util', location = '/js/converter/util/color-converter.js' */
define("jira/editor/converter/util/color-converter",["exports"],function(a){a.colorToHex=function b(d){if(d.substr(0,1)==="#"){return d}var g=/(.*?)rgb\((\d+), (\d+), (\d+)\)/.exec(d);var h=g[2];var f=g[3];var c=g[4];var e=[h,f,c].map(function(i){return("00"+parseInt(i).toString(16)).slice(-2)});return g[1]+"#"+e.join("")}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter-util', location = '/js/converter/util/strings.js' */
define("jira/editor/converter/util/strings",["jira/editor/util/strings","exports","jquery","underscore"],function(c,e,g,j){var i="/images/icons/emoticons/";var b="/secure/attachment/";var d="/secure/thumbnail/";var f="/secure/temporaryattachment/";e.asBooleanOrString=function a(m){var l=m?m.toLowerCase():"";var k=m;if(l==="true"){k=true}else{if(l==="false"){k=false}}return k};e.startsWith=function(l,k){if(j.isEmpty(l)){return false}if(j.isEmpty(k)){return false}return l.indexOf(k)===0};e.endsWith=function(l,k){if(j.isEmpty(l)){return false}if(j.isEmpty(k)){return false}return l.indexOf(k)+k.length===l.length};e.substringAfter=function(m,k){if(j.isEmpty(m)){return m}if(j.isEmpty(k)){return""}var l=m.indexOf(k);if(l<0){return""}return m.substring(l+k.length)};e.substringBefore=function(m,k){if(j.isEmpty(m)){return m}if(j.isEmpty(k)){return""}var l=m.indexOf(k);if(l<0){return""}return m.substring(0,l)};e.contains=function(m,k){if(j.isEmpty(m)){return false}var l=m.indexOf(k);return l>=0};e.repeat=function(l,k){if(j.isEmpty(l)){return""}return new Array(k+1).join(l)};e.length=function(k){if(j.isEmpty(k)){return 0}return k.length};e.replace=function(p,m,k){if(j.isEmpty(p)){return""}if(j.isEmpty(m)){return""}if(typeof k==="undefined"){k=""}var q=0;var n=p.indexOf(m);while(n>=0){var l=p.substring(0,n);var o=p.substring(n+m.length);p=l+k+o;q=n+k.length;n=p.indexOf(m,q)}return p.substring(n,p.length)};e.hashCode=function h(m){var k;var n=0;if(!m){return""}for(var l=0;l<m.length;l+=1){k=m.charCodeAt(l);n=(n*31)+k;n|=0}return""+n};e.resolveUrl=function(l,m){if(!l){return}var k=g(document.createElement("base")).prop("href",m).appendTo("head");try{return g(document.createElement("a")).prop("href",l).prop("href")}finally{k.remove()}};e.getBaseUrl=function(){return document.location.origin+document.location.pathname};e.getContextUrl=function(){return document.location.origin+AJS.contextPath()};e.getFilenameFromHref=function(n,o){var l=n.split("/");var m=l[l.length-1];if(!o){var q=l[l.length-2];var p=q+"_";if(m.indexOf(p)===0){m=m.substring(p.length)}}else{var k=m.indexOf("_");if(k>0){m=m.substring(k+1)}}m=m.replace(new RegExp("\\+","g")," ");return decodeURIComponent(m)};e.isAttachmentPath=function(k){return e.contains(k,b)};e.isTemporaryAttachmentPath=function(k){return e.contains(k,f)};e.isThumbnailPath=function(k){return e.contains(k,d)};e.isEmotePath=function(k){return e.contains(k,i)};e.getFilenameFromError=function(k){return c.getFilenameFromError(k)}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter-util', location = '/js/converter/util/uri.js' */
define("jira/editor/converter/util/uri",["exports","atlassian/libs/uri-1.14.1"],function(a,b){a.decodeUri=function(c){return b.decodeQuery(c||"",true)};a.encodeUri=function(c){return b.encodeQuery(c||"",true)}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter-util', location = '/js/converter/util/rte-strings.js' */
define("jira/editor/converter/util/rte-strings",["exports","underscore"],function(a,b){a.isWhitespace=function(e){if(b.isEmpty(e)){return false}var d={"\t":1,"\n":1,"\u0020":1,"\u0009":1,"\u000A":1,"\u000D":1,"\u000C":1,"\u200B":1};var c=true;b.each(e,function(f){c=c&&(f in d)});return c};a.trim=function(c){if(b.isEmpty(c)){return""}return this.trimLeading(this.trimTrailing(c))};a.trimTrailing=function(d){if(b.isEmpty(d)){return""}var c;for(c=d.length-1;c>=0;c--){if(!this.isWhitespace(d[c])){break}}return d.substring(0,c+1)};a.trimLeading=function(d){if(b.isEmpty(d)){return""}var c;for(c=0;c<d.length;c++){if(!this.isWhitespace(d[c])){break}}return d.substring(c,d.length)};a.parseHtml=function(c){var e=new DOMParser();var d=e.parseFromString(c,"text/html").body;return d}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:context-detector', location = 'js/context-detector.js' */
define("jira/editor/context-detector",["jira/editor/converter/util/rte-strings","underscore","jquery"],function(a,h,e){var d="pre,.code.panel,.preformatted.panel,.panelHeader,panel-title";var i="table";var f="a";var g={};g.detectPre=function(j){return c(j,d)};g.detectTable=function(j){return c(j,i)};g.detectA=function(j){return c(j,f)};g.detectPreWithinSelection=function(j){return b(j,d)};return g;function c(k,j){return e(k||{}).closest(j).length>0}function b(k,j){try{return e((h.isString(k)?a.parseHtml(k):k)||{}).find(j).length>0}catch(l){return false}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:context-manager', location = 'js/context-manager.js' */
define("jira/editor/context-manager",["jira/editor/context-detector","backbone","underscore"],function(b,d,a){var c=function(e){this._disableStates={all:null,table:null,a:null};this._tagList=a.difference(Object.keys(this._disableStates),"all");this._editorInstance=e;e.on("selection:update",function(g){var f=g.insidePreformatted||g.preformattedSelected;this._setDisable(f,"all");this._setDisable(f||g.insideTable,"table");this._setDisable(f||g.insideA,"a")}.bind(this));e.on("tabs:changed",function(){this._setDisable(false,"all");this._setDisable(false,"table");this._setDisable(false,"a")}.bind(this));a.extend(this,d.Events)};c.prototype._setDisable=function(f,e){if(this._disableStates[e]===f){return}this._disableStates[e]=f;this.trigger("change:"+e,{disableState:f});if("all"===e){this._tagList.forEach(function(g){this._setDisable(f,g)},this)}};c.prototype.isPreContextActive=function(){return true===this._disableStates.all};c.prototype.isContextActive=function(e){return true===this._disableStates[e]};c.prototype.getPasteSchemaForContext=function(){return b.detectPre(this._editorInstance.editor.selection.getNode())?this._editorInstance.pasteInsidePreSchemaSpec:this._editorInstance.pasteSchemaSpec};return c});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:selection', location = 'js/editor-selection.js' */
define("jira/editor/selection",["jira/editor/constants"],function(b){var a=function(d){this.editor=d};a.prototype.trimSelection=function(){var j=false;var g=this.editor.selection.getRng();if(g.collapsed){return false}if(!g.toString().trim()){g.collapse(true);this.editor.selection.setRng(g);this.editor.focus();return true}var k=g.startContainer;var d=g.startOffset;var e=g.endContainer;var h=g.endOffset;if(k&&k.nodeType===Node.TEXT_NODE){var i=k.nodeValue.substring(d);var f=c(i,b.LEFT_WHITESPACE_MATCHER);if(f){j=true;if(i.length===f.length){g.setStartAfter(k)}else{g.setStart(k,d+f.length)}}}if(e&&e.nodeType===Node.TEXT_NODE){var i=e.nodeValue.substring(0,h);var f=c(i,b.RIGHT_WHITESPACE_MATCHER);if(f){j=true;if(i.length===f.length){g.setEndBefore(e)}else{g.setEnd(e,h-f.length)}}}if(j){this.editor.selection.setRng(g)}return j};a.prototype.hasSelection=function(){return !this.editor.selection.getRng().collapsed};return a;function c(f,e){var d=f.match(e);return(null!==d)?d[0]:""}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:instance', location = 'js/editor-instance.js' */
define("jira/editor/instance",["jira/dialog/dialog","jira/editor/analytics","jira/editor/analytics-shortcuts","jira/util/navigator","jira/editor/tinymce","jira/editor/context-manager","jira/editor/context-detector","jira/editor/schema","jira/editor/selection","wiki-edit/SpeechRecognition","jira/util/logger","jquery","backbone","underscore","wrm/context-path"],function(h,j,a,f,d,q,l,m,i,b,r,g,c,p,k){var n=function(t,s){this.element=t;this.options=p.extend({},s)};p.extend(n.prototype,c.Events);var e=27;n.prototype.init=function(t){this.editor=t;this.operationOverride={};this.analyticsShortcuts=new a(this.editor);this.selection=new i(this.editor);this.editor.on("NodeChange",function(x){var v=j.startMeasure();if(!x){return}if(x.element.nodeName.toLowerCase()==="img"){this.editor.fire("content")}var w=g(x.element);this.trigger("selection:update",{insidePreformatted:l.detectPre(w),preformattedSelected:l.detectPreWithinSelection(this.editor.selection.getContent()),insideTable:l.detectTable(w),insideA:l.detectA(w)});v.measure("nodechange")}.bind(this));this.editor.on("change SetContent blur",this._onChange.bind(this));this.editor.on("keyup",p.debounce(this._onChange.bind(this),1000));this.editor.on("init",function(w){var v=w.target;["tt","del","sup","sub","cite"].forEach(function(y){v.formatter.register(y,{block:y,remove:"all"})});var x=v.formatter.get("removeformat");if(x.length>0&&typeof x[0]==="object"){x[0].selector+=",tt"}});t.on("keydown",function(v){if(v.isDefaultPrevented()){return}const w=g(t.selection.getStart());if(o(v,w)){v.preventDefault();t.execCommand("InsertLineBreak",false,v)}if(h.current&&v.keyCode===e){h.current.hide(true,{reason:h.HIDE_REASON.cancel,})}if(v.keyCode===d.util.VK.ENTER&&(w.is("panel-title, panel-title *")||w.parent().hasClass("panelHeader"))){v.preventDefault()}});var s=function(){if((/(Edge)\/(\d+)\.(\d+)/).test(f._getUserAgent())){return"edge"}else{if(f.isIE()){return"ie"}else{if(f.isChrome()){return"chrome"}else{if(f.isMozilla()){return"firefox"}else{if(f.isSafari()){return"safari"}}}}}return""};j.sendEvent("editor.instance.init");j.sendEvent("bundled.editor.instance.init");var u=s();if(u){j.sendEvent("editor.instance.init."+u)}this.contextManager=new q(this);t.contextManager=this.contextManager};n.prototype.getId=function(){return this.editor.id};n.prototype.relayEvent=function(t,u,s){this.editor.on(t,function(v){u(v)},s)};n.prototype.getAllowedOperations=function(){return["paragraph","h1","h2","h3","h4","h5","h6","monospace","paragraph-quote","block-quote","delete","superscript","subscript","cite","monospace-inline",":)",":(",":P",":D",";)","(y)","(n)","(i)","(/)","(x)","(!)","(+)","(-)","(?)","(on)","(off)","(*)","(*r)","(*g)","(*b)","(*y)","bold","italic","underline","color","bullet-list","numbered-list","mention","table","code","noformat","panel","hr","speech","link","link-mail","link-anchor","link-attachment","image","image-attachment","attachment","editorInsertContent","editorInsertContentInNewLine","editorReplaceContent","editorReplaceContentInNewLine"]};n.prototype._isOperationSupported=function(s){var t=this.getAllowedOperations().filter(function(u){if(u instanceof Object){return u.name===s}else{return u===s}});return t.length>0};n.prototype._assertOperationIsSupported=function(s){if(!this._isOperationSupported(s)){r.error("Operation not supported:",s)}};n.prototype._selectedTextSanitized=function(){return m.sanitizeHtml(this.editor.selection.getContent(),this.editor,this.pasteInsidePreSchemaSpec)};n.prototype.executeOperation=function(B,y){this._assertOperationIsSupported(B);var u=this._mapOperationNameToTinymce(B);var z=this.operationOverride[B];if(z){j.sendEvent("editor.instance.operation."+B);z(y);return true}if("editorReplaceContentInNewLine"===B){this.editor.execCommand("mceReplaceContent",false,"<br />"+y.content)}else{if("editorReplaceContent"===B){this.editor.execCommand("mceReplaceContent",false,y.content)}else{if("editorInsertContentInNewLine"===B){this.editor.insertContent("<br />"+y.content)}else{if("editorInsertContent"===B){this.editor.insertContent(y.content)}else{if("hr"===B){this.editor.insertContent("<hr />")}else{if("color"===B){this.editor.execCommand("ForeColor",true,y)}else{if(["h1","h2","h3","h4","h5","h6","paragraph","paragraph-quote","block-quote","monospace","monospace-inline","cite"].indexOf(B)>-1){this.editor.execCommand("mceToggleFormat",true,u)}else{if(["bold","italic","underline","delete","superscript","subscript"].indexOf(B)>-1){if(this.selection.trimSelection()){if(!this.selection.hasSelection()){j.sendEvent("editor.instance.selection.collapsed",{op:B});return false}else{j.sendEvent("editor.instance.selection.trimmed",{op:B})}}this.editor.execCommand(u,true)}else{if(["bullet-list","numbered-list"].indexOf(B)>-1){this.editor.execCommand(u,true)}else{if(this._emoticonSourceMap(B)){var A;if(AJS&&AJS.contextPath){A=AJS.contextPath()}else{A=""}var x=A+"/images/icons/emoticons/"+this._emoticonSourceMap(B);this.editor.insertContent('<img class="emoticon" src="'+x+'" height="16" width="16" align="absmiddle" alt="" border="0">')}else{if("link"===B){var w=(this._selectedTextSanitized()||"\u94fe\u63a5\u6807\u9898");this.editor.selection.setContent("["+w+"|http://example.com]")}else{if("link-anchor"===B){var w=(this._selectedTextSanitized()||"\u951a\u70b9");this.editor.selection.setContent("[#"+w+"]")}else{if("link-mail"===B){var w=(this._selectedTextSanitized()||"<EMAIL>");this.editor.selection.setContent("[mailto:"+w+"]")}else{if("mention"===B){var w=(this._selectedTextSanitized()||"\u8f93\u5165\u4eba\u540d\u63d0\u53ca\u67d0\u4eba......");this.editor.selection.setContent("@");var t=this.getSelectionStart();this.editor.selection.setContent(w);this.setSelectionStart(t)}else{if("attachment"===B&&y.attachment){if(y.attachment.type==="image"){var s=y.attachment.href||k()+"/images/icons/attach/image.gif";if(y.attachment.thumbnailable){s=s.replace("/attachment/","/thumbnail/")}var v=JIRA.Editor.Tags.Templates.attachedImage({source:s,filename:y.attachment.name})}else{var v=JIRA.Editor.Tags.Templates.attachedFile({title:y.attachment.name,href:y.attachment.href,filename:y.attachment.name})}this.editor.insertContent(v)}else{if("code"===B){var w=(this._selectedTextSanitized()||"//\u4ee3\u7801\u5360\u4f4d\u7b26"+"\n");this.editor.selection.setContent('<pre class="code panel" data-language="code-java">'+w+"</pre>")}else{if("panel"===B){var w=(this.editor.selection.getContent()||"\u6587\u672c\u6807\u9898");this.editor.selection.setContent('<div class="plain panel" style="border-width: 1px;"><panel-title>'+"\u6211\u7684\u6807\u9898"+"</panel-title><p>"+w+"</p></div>")}else{if("noformat"===B){var w=(this._selectedTextSanitized()||"\u8fd9\u91cc\u7684\u6587\u5b57\u6ca1\u6709\u4efb\u4f55\u683c\u5f0f\u4fee\u9970");this.editor.selection.setContent('<pre class="noformat panel">'+w+"</pre>")}else{if("table"===B){var w=(this.editor.selection.getContent()||("\u5217"+" A1"));this.editor.selection.setContent('<div class="table-wrap"><table class="confluenceTable mce-item-table" data-mce-selected="1"><tbody><tr><th class="confluenceTh">'+"\u6807\u9898"+' 1</th><th class="confluenceTh">'+"\u6807\u9898"+' 2</th></tr><tr><td class="confluenceTd">'+w+'</td><td class="confluenceTd">Col A2</td></tr></tbody></table></div>');j.sendEvent("editor.instance.table.toolbar")}else{if("speech"===B){b.start(null,this)}else{if(["link-attachment","image","image-attachment"].indexOf(B)>-1){r.warn("Not supported yet "+B);return false}else{r.warn("Unsupported operation "+B);return false}}}}}}}}}}}}}}}}}}}}}j.sendEvent("editor.instance.operation."+B);this.trigger("content");r.trace("jira.editor.operation.executed");return true};n.prototype._mapOperationNameToTinymce=function(s){var t={bold:"Bold",italic:"Italic",underline:"Underline","bullet-list":"InsertUnorderedList","numbered-list":"InsertOrderedList",blockquote:"mceBlockQuote",paragraph:"p","paragraph-quote":"blockquote","block-quote":"blockquote",monospace:"samp","monospace-inline":"samp-inline","delete":"strikethrough",superscript:"superscript",subscript:"subscript"};if(s in t){return t[s]}return s};n.prototype._emoticonSourceMap=function(t){var s={":)":"smile.png",":(":"sad.png",":P":"tongue.png",":D":"biggrin.png",";)":"wink.png","(y)":"thumbs_up.png","(n)":"thumbs_down.png","(i)":"information.png","(/)":"check.png","(x)":"error.png","(!)":"warning.png","(+)":"add.png","(-)":"forbidden.png","(?)":"help_16.png","(on)":"lightbulb_on.png","(off)":"lightbulb.png","(*)":"star_yellow.png","(*r)":"star_red.png","(*g)":"star_green.png","(*b)":"star_blue.png","(*y)":"star_yellow.png","(flag)":"flag.png","(flagoff)":"flag_grey.png"};if(t in s){return s[t]}};n.prototype.focus=function(){if(!this.editor.initialized||this.editor.removed||this.editor.destroyed){r.warn("bypassing `editor-instance.focus` because the editor instance hasn't initialized yet or already removed");return this}this.editor.focus();this.editor.nodeChanged();return this};n.prototype.destroy=function(){this.editor.contextManager=null;this.editor.remove();j.sendEvent("editor.instance.destroy");j.sendEvent("bundled.editor.instance.destroy")};n.prototype.setContent=function(v,u,s){var t={};if(u){t={format:"raw"}}t.no_events=s;v=v||"";this.editor.setContent(v,t);this.lastContent=this.getContent();if(!s){this.trigger("content")}};n.prototype.replaceSelection=function(s){this.editor.selection.setContent(s);this.trigger("content")};n.prototype.selectAll=function(){this.editor.selection.select(this.editor.getBody(),true)};n.prototype.getSelectionStart=function(){return this.editor.selection.getRng(true)};n.prototype.setSelectionStart=function(t){var s=this.editor.selection.getRng(true);s.setStart(t.startContainer,t.startOffset);this.editor.selection.setRng(s)};n.prototype.getContent=function(t){var s={};if(t){s={format:"raw"}}return this.editor.getContent(s)};n.prototype._onChange=function(){if(this.editor.destroyed){return}var s=this.getContent();var t=s!==this.lastContent;this.lastContent=s;if(!this.hidden&&t){this.trigger("content",this.getContent())}};n.prototype.getSelectedContent=function(s){if(s){return this.editor.selection.getContent({format:"text"})}else{return this.editor.selection.getContent()}};n.prototype.addShortcut=function(s,t){this.editor.addShortcut(s,"",t)};n.prototype.removeShortcut=function(s){this.editor.shortcuts.remove(s)};n.prototype.addOperationOverride=function(s,t){this._assertOperationIsSupported(s);this.operationOverride[s]=t};n.prototype.removeOperationOverride=function(s){this._assertOperationIsSupported(s);delete this.operationOverrideoperationOverride[s]};n.prototype.hide=function(){this.hidden=true;this.editor.hide()};n.prototype.show=function(){this.editor.show();delete this.hidden};n.prototype.isVisible=function(){return !this.editor.isHidden()};n.prototype.switchMode=function(s){this.trigger("switchMode",s)};n.prototype.enable=function(){this.editor.setProgressState(false)};n.prototype.disable=function(){this.editor.setProgressState(true)};function o(u,v){const t="th, td, th *, td *";const s="li";return u.keyCode===d.util.VK.ENTER&&!u.shiftKey&&v.is(t)&&!v.is(s)}return n});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:instance', location = 'js/editor-resources.js' */
define("jira/editor/resources",["jira/editor/tinymce","jira/editor/wrm","wrm/require","jquery"],function(e,g,b,f){var d={};var c={};d.loadPlugins=function(h){var i=h.filter(function(j){return j.resource}).map(function(j){e.PluginManager.urls[j.name]=j.resource;return"wr!"+j.resource});return i.length>0?b(i):new f.Deferred().resolve()};d.require=function(h){return b(h.filter(function(i){return !c[i]}).map(function(i){return"wr!"+i}))};d.loadCssResources=function(){return new f.Deferred().resolve(a(g.getResources())).promise()};return d;function a(h){if(typeof h!=="object"||!h.length){return[]}return h.map(function(i){return i.replace(",","%2C")})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:api', location = 'js/editor-create.js' */
define("jira/editor/create",["jira/editor/instance","jira/editor/resources","jira/editor/schema","jira/editor/customizer","jira/editor/height-helper","jira/util/navigator","jira/editor/tinymce","jquery","underscore"],function(b,a,f,h,e,k,i,d,g){var j=function(l){this.element=l;this.settings={toolbar:false,menubar:false,elementpath:false};this.plugins=[]};j.prototype.build=function(){var m=new d.Deferred();var o=this;var l=new b(o.element);var n=new e(this.element);var q=-2;var p=g.extend(this.settings,{plugins:["autoresize","lists","table"],language:"en",content_style:k.isIE()?"body#tinymce {min-height: 10px}":"",autoresize_on_init:true,autoresize_max_height:n.getMaxHeight()+q,autoresize_min_height:n.getMinHeight()+q,autoresize_bottom_margin:0,allow_conditional_comments:false,object_resizing:"img:not(.emoticon)",end_container_on_empty_block:true,browser_spellcheck:true,table_toolbar:"tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol",hidden_input:false,target:this.element,autoresize_overflow_padding:8,relative_urls:false,convert_urls:false,branding:false,formats:{underline:[{inline:"ins",remove:"all"},{inline:"u",remove:"all"},{inline:"span",styles:{textDecoration:"underline"},exact:true}],strikethrough:[{inline:"del",remove:"all"},{inline:"strike",remove:"all"},{inline:"span",styles:{textDecoration:"line-through"},exact:true}]},setup:function(r){m.notify(l);l.init(r);r.on("init",function(){r.getContainer().classList.add("jira-editor-container");l.schemaSpec.fixChildren(r.schema);m.resolve(l)});r.on("focus",function(){d(o.element).trigger("click").trigger("focusin")})}});d.when(a.loadCssResources(),a.loadPlugins(this.plugins)).then(function(r){this.plugins.forEach(function(t){require(t.name).configure(l,p)},this);var s=f.getSchemaSpecBuilder();h.getCustomizeSettingCallbacks().forEach(function(t){t(p,i,s)});c(s,p,l);p.content_css=r;if(document.readyState==="interactive"){i.dom.EventUtils.Event.domLoaded=true}if(document.body.contains(this.element)){i.init(p)}else{m.reject()}}.bind(this));return m.promise()};j.prototype.withElement=function(l){this.element=l;return this};j.prototype.withPlugin=function(m,l){this.plugins.push({name:m,resource:l});return this};j.prototype.withMeta=function(l){this.meta=l;return this};return{builder:function(l){return new j(l)}};function c(o,m,l){var n=o.build();m.schema=n.getType();m.custom_elements=n.getCustomElements();m.valid_children=n.getValidChildren();m.valid_elements=n.getValidElements();m.extended_valid_elements=n.getExtendedValidElements();l.schemaSpec=n;l.pasteSchemaSpec=f.getPasteSchemaSpecBuilder(o).build();l.pasteInsidePreSchemaSpec=f.getPasteInsidePreSchemaSpecBuilder().build()}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:renderer', location = '/js/converter/html-renderer.js' */
define("jira/editor/html-renderer",["wrm/context-path","jquery","underscore"],function(b,d,c){var a=function(f,e){e=e||{};var g=c.extend({rendererType:"atlassian-wiki-renderer",unrenderedMarkup:f,forWysiwyg:true},e);return d.ajax({url:b()+"/rest/api/1.0/render",contentType:"application/json",type:"POST",data:JSON.stringify(g),dataType:"html"})};return{renderMarkup:a}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:mentions', location = 'js/plugins/mentions/mentions-util.js' */
define("jira/editor/plugins/mentions/mentions-util",["jquery","underscore"],function(e,c){var a=8203;var b=65279;var d=function(g){g.preventDefault();g.stopPropagation();g.stopImmediatePropagation()};var f={isMentionNode:function(g){if(!g){return false}return e(g).is("a.user-hover")},isCaretAfterMention:function(g){return this.getMentionBeforeCaret(g)!=null},getMentionBeforeCaret:function(h){var g=this.getPreviousNode(h).node;if(this.isMentionNode(g)){return g}return null},getPreviousNode:function(m){var i=function(p){if(p.nodeType===Node.TEXT_NODE){return p.nodeValue.length}else{if(p.nodeType===Node.ELEMENT_NODE){return p.childNodes.length}else{return undefined}}};var n=m.getSelection();var j=n.anchorNode;var l=n.anchorOffset;var h={node:j,offset:n.anchorOffset};if(n.isCollapsed&&j){if(j.nodeType===Node.ELEMENT_NODE&&j.childNodes){var o=n.anchorOffset-1;if(o<0){j=j.childNodes[0];l=0;var g=this._getPreviousSiblingSkipEmptyTextNodes(j,l);if(g){return{node:g,offset:i(g)}}}else{var k=o===j.childNodes.length-1;j=j.childNodes[o];l=i(j);var g=this._getPreviousSiblingSkipEmptyTextNodes(j,l,k);if(g){return{node:g,offset:i(g)}}}}else{if(j.nodeType===Node.TEXT_NODE){var g=this._getPreviousSiblingSkipEmptyTextNodes(j,l);if(g){return{node:g,offset:g===j?l:i(g)}}}}}return h},_getPreviousSiblingSkipEmptyTextNodes:function(i,k,h){h=h||false;var g=i;var j;while((j=this._isContentBeforeCursorEmpty(g,k,h)).outcome){g=g.previousSibling;k=undefined;h=j.relaxed}return g},_isContentBeforeCursorEmpty:function(i,l,k){var j={outcome:true,relaxed:false};var g={outcome:false,relaxed:false};var h={outcome:true,relaxed:true};if(i&&i.nodeType===Node.TEXT_NODE){if(c.isUndefined(l)){l=i.nodeValue.length}if(l===0){return j}if(l===1&&this._isEmptyCharacter(i.nodeValue.charCodeAt(0))&&i.nodeValue.length===1){return j}if(k){if(l===1&&i.nodeValue===" "){return h}}}return g},_isEmptyCharacter:function(g){return g===a||g===b},selectMention:function(h,g,i){if(g){d(g)}i.selection.select(h)}};return f});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:mentions', location = 'js/plugins/mentions/mention.js' */
define("jira/editor/plugins/mentions/mention-rte",["jira/mention/mention-matcher","jquery","jira/mention/mention","underscore","jira/editor/plugins/mentions/mentions-util"],function(c,e,b,a,f){var d=b.extend({init:function(h,i,g){b.prototype.init.call(this,h);this.editor=i;this.skipProcessing=g},_replaceCurrentUserName:function(h,g){if(this._selectMentionText()){this.editor.execCommand("mceInsertContent",false,this._renderMentionLink(h,g))}},_selectMentionText:function(){var o=this._getDocument();var p=this._getSelection();if(p&&p.isCollapsed){var l=this._getNodeForUsernameRetrieval();var g=this._getPreviousSiblingsTextNodes(l.node);if(g.length>0){var h=g[0];var n=g[g.length-1];var j=l.offset;var i=this._getStringFormPreviousSiblingsTextNodes(g,j);var k=c.getLastWordBoundaryIndex(i,true);var m=o.createRange();m.setStart(h,k+1);m.setEnd(n,j);this.editor.selection.setRng(m);return true}}else{return false}},_renderMentionLink:function(h,g){return JIRA.Editor.Tags.Templates.mentionlink({username:h,displayname:g})},_getUserNameFromInput:function(){if(a.isFunction(this.skipProcessing)&&this.skipProcessing()){return null}var h=this._getSelection();if(h&&h.isCollapsed){var i=this._getNodeForUsernameRetrieval();var g=this._getPreviousSiblingsTextNodes(i.node);var j=this._getStringFormPreviousSiblingsTextNodes(g,i.offset);this.currentUserName=c.getUserNameFromCurrentWord(j,j.length);return this.currentUserName}else{return null}},_getStringFormPreviousSiblingsTextNodes:function(g,l){var k="";for(var h=0;h<g.length;h=h+1){var j=g[h];if(h===g.length-1){k=k+j.nodeValue.substring(0,l)}else{k=k+j.nodeValue}}return k},_getPreviousSiblingsTextNodes:function(h){var g=[];if(h&&h.nodeType===Node.TEXT_NODE){for(var i=h;i;i=i.previousSibling){if(i.nodeType===Node.TEXT_NODE){g.unshift(i);if(i.nodeValue.indexOf("@")>=0||i.nodeValue.indexOf("[~")>=0){return g}}else{break}}}return[]},_getNodeForUsernameRetrieval:function(){return f.getPreviousNode(this._getDocument())},_getSelection:function(){if(this.$textarea){var g=this.$textarea.get(0);if(g&&g.ownerDocument){return g.ownerDocument.getSelection()}}return null},_getDocument:function(){return this.$textarea.get(0).ownerDocument},_getOffsetTarget:function(){return e(this.editor.getContainer())}});return d});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:mentions-plugin', location = 'js/plugins/mentions/jira-mentions-plugin.js' */
define("jira/editor/plugins/mentions/jira-mentions",["jira/editor/tinymce","jquery","jira/editor/plugins/mentions/mention-rte","jira/editor/plugins/mentions/mentions-util"],function(e,f,b,h){var g=8;var d={9:"Tab",13:"Return",27:"Esc",38:"Up",40:"Down"};var a=function(i){var j=new f.Event("aui:keydown");j.key=d[i.which];j.shiftKey=i.shiftKey;j.ctrlKey=i.ctrlKey;j.altKey=i.altKey;j.target=i.target;arguments[0]=j;f.event.trigger(j,arguments,document,true)};var c=function(j,i){var l=i.$textarea.get(0).ownerDocument;i.mentioningInProgress=false;i.layerController.bind("showLayer",function(){i.mentioningInProgress=true}).bind("hideLayer",function(){i.mentioningInProgress=false});j.on("keydown",function k(n){var o=n.which in d&&i.mentioningInProgress===true;if(o){n.preventDefault();n.stopPropagation();a(n)}else{if(n.keyCode===g){if(h.isCaretAfterMention(l)){var m=h.getMentionBeforeCaret(l);h.selectMention(m,n,this)}}}},true)};e.create("tinymce.plugins.jira.JiraMentions",{init:function(i){var j=i.settings;if(j.jiramentions.mentionable){i.on("init",function(){var m=i.getBody();var l=function(){var o=i.selection.getSel();var p=o?f(o.anchorNode):null;if(p&&(p.parents("pre").length>0||p.parents("div.panelHeader,panel-title").length>0)){return true}};var k=j.jiramentions.issuekey;var n=j.jiramentions.projectkey;f(m).attr("data-issuekey",k);f(m).attr("data-projectkey",n);if(!m._controller){m._controller=new b(k,i,l)}m._controller.textarea(m);c(i,m._controller)})}}});e.PluginManager.add("jira.jiramentions",e.plugins.jira.JiraMentions);e.PluginManager.urls["jira.jiramentions"]=true;return{configure:function(i,j){j.plugins.push("jira.jiramentions");var k=i.element.$textarea.data();j.jiramentions={issuekey:k.issuekey,projectkey:k.projectkey,mentionable:i.element.$textarea.hasClass("mentionable")}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:polyfil-string-ends-with', location = '/js/polyfills/stringEndsWithPolyfill.js' */
if(!String.prototype.endsWith){String.prototype.endsWith=function(b,a){if(a===undefined||a>this.length){a=this.length}return this.substring(a-b.length,a)===b}};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:resources', location = 'js/plugins/paste-plugin.js' */
define("jira/editor/plugins/paste",["jira/editor/tinymce","jira/editor/converter/util/strings","jira/editor/schema","jira/editor/analytics","jquery","wrm/context-path","dndattachment/ctrlv/utility","dndattachment/ctrlv/base64decode","jira/editor/wrm","jira/flag","jira/util/formatter"],function(m,a,f,k,c,d,i,g,e,b,j){var h=e.isAttachmentsAllowed();m.create("tinymce.plugins.jira.Paste",{});m.PluginManager.add("jira.paste",m.plugins.jira.Paste);m.PluginManager.urls["jira.paste"]=true;function l(o){if(o){var p=o.split(",")[0];var n=p.split(":");if(n.length>1){return n[1].split(";")[0]}}}return{configure:function(o,s){function p(u){var v=o.editor;if(v&&v.editorUpload&&v.editorUpload.blobCache){return v.editorUpload.blobCache.getByUri(u)}}var t={URL:{upload:function(){}},BLOB_URL:{upload:function(u,v){k.sendEvent("editor.instance.paste.addimage.nocache.http");n(u,v)}},DATA_URL:{upload:function(u,w){k.sendEvent("editor.instance.paste.addimage.nocache.image");var v=l(u)||"image/png";q(new Blob([g.decodeBase64DataUri(u)],{type:v}),w)}},TINYMCE_CACHED_BLOB:{upload:function(v,w){var u=p(v,o.editor);if(u){k.sendEvent("editor.instance.paste.addimage.cache");q(u.blob(),w)}else{console.warn("no cached blob found in cache")}}}};function r(v){var u=p(v,o.editor);if(u){return t.TINYMCE_CACHED_BLOB}else{if(v.match(/^((data|blob):http)|(blob:)/)){return t.BLOB_URL}else{if(v.match(/^(data|blob):image/)){return t.DATA_URL}}}return t.URL}function q(v,w){var u=i.convertBlobToImage(v,w);if(!i.dropFileToElement(u,o.element.$textarea)){console.warn("Uploading attachment failed. Check 'allow attachment' setting.");b.showWarningMsg("","\u6b64\u9875\u9762\u4e0d\u652f\u6301\u6dfb\u52a0\u9644\u4ef6\u3002",{close:"auto"});o.editor.dom.remove(o.editor.dom.select('img[data-filename="'+w+'"]'))}}function n(v,w){var u=new XMLHttpRequest();u.open("GET",v,true);u.responseType="blob";u.onload=function(x){q(this.response,w)};u.send()}s.plugins.push("paste");s.paste_data_images=true;s.paste_preprocess=function(w,u){var v=o.contextManager.getPasteSchemaForContext();u.content=f.sanitizeHtml(u.content,o.editor,v)};s.paste_postprocess=function(y,w){k.sendEvent("editor.instance.paste");var x=c(w.node).find("img:first-child:last-child");if(x.length===1&&x.is("img")){var B=x[0].src;if(x.hasClass("emoticon")&&a.startsWith(B,document.location.origin+d()+"/images/icons/emoticons/")){k.sendEvent("editor.instance.paste.emoticon");return}k.sendEvent("editor.instance.paste.image");var v=B;if(a.isAttachmentPath(B)||a.isThumbnailPath(B)){v=a.getFilenameFromHref(B)}var A={source:B,filename:v,width:x.attr("width"),height:x.attr("height"),id:x.attr("id"),vspace:x.attr("vspace")};var z=r(B);if(z===t.DATA_URL||z===t.BLOB_URL||z===t.TINYMCE_CACHED_BLOB){if(!!h){v=i.generateFileName()+".png";z.upload(B,v);A.filename=v;var u=JIRA.Editor.Tags.Templates.image(A);x.replaceWith(c(u))}else{b.showWarningMsg("","\u6dfb\u52a0\u9644\u4ef6\u5df2\u88ab\u60a8\u7684Jira\u7ba1\u7406\u5458\u7981\u7528\u3002",{close:"auto"});x.remove()}}else{var u=JIRA.Editor.Tags.Templates.image(A);x.replaceWith(c(u))}}else{k.sendEvent("editor.instance.paste.other")}};return s}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:resources', location = 'js/plugins/wiki-autoformat-plugin.js' */
define("jira/editor/plugins/wiki-autoformat",["jira/editor/tinymce","jira/editor/analytics","jquery","jira/util/version","jira/featureflags/feature-manager","jira/editor/html-renderer","jira/util/logger"],function(m,l,e,b,p,k,n){var c={_REGEXES_EMOTICON:{SMILE:/\B(:-?)$/,SAD:/\B(:-?)$/,CHEEKY:/\B(:-?)$/,CHEEKY_2:/\B(:-?)$/,LAUGH:/\B(:-?)$/,WINK:/\B(;-?)$/,THUMBS_UP:/\B(\(y)$/,THUMBS_DOWN:/\B(\(n)$/,INFORMATION:/\B(\(i)$/,TICK:/\B(\(\/)$/,CROSS:/\B(\(x)$/,WARNING:/\B(\(!)$/,PLUS:/\B(\(\+)$/,MINUS:/\B(\(-)$/,QUESTION:/\B(\(\?)$/,LIGHT_ON:/\B(\(on)$/,LIGHT_OFF:/\B(\(off)$/,YELLOW_STAR:/\B(\(\*)$/,YELLOW_STAR_2:/\B(\(\*y)$/,RED_STAR:/\B(\(\*r)$/,GREEN_STAR:/\B(\(\*g)$/,BLUE_STAR:/\B(\(\*b)$/,FLAG:/\B(\(flag)$/,FLAG_OFF:/\B(\(flagoff)$/,HEART:/\B(<)$/,BROKEN_HEAR:/\B(<\/)$/}};var j;function a(q){return e("<div></div>").append(q.cloneNode(true)).html()}function o(q,r,s){var w;var u;if(!r){throw new Error("text node is null")}if(r.nodeType!==Node.TEXT_NODE){r=r.childNodes[s-1];s=r.length}for(var v=r,t=s;v&&v.nodeType===Node.TEXT_NODE;v=v.previousSibling){if(t===-1){t=v.nodeValue.length}if(t>q){return{container:v,offset:t-q}}else{if(t===q){u=0;w=v.parentNode;while(v=v.previousSibling){u++}return{container:w,offset:u}}else{q-=t;t=-1}}}return null}function g(r){var s;var t;if(!r||!r.collapsed){throw new Error("range is null or not collapsed")}s=r.startContainer;t=r.startOffset;if(s.nodeType===Node.ELEMENT_NODE&&t>0){s=s.childNodes[r.startOffset-1];if(s.nodeType===Node.TEXT_NODE){t=s.nodeValue.length}else{return""}}else{if(s.nodeType!==Node.TEXT_NODE){return""}}var q=s.nodeValue.substring(0,t);for(var u=s.previousSibling;u&&u.nodeType===Node.TEXT_NODE;u=u.previousSibling){q=u.nodeValue+q}return q}function i(s,t,r,q){return{handles:function(y){if(typeof q==="string"){q=[q]}else{if(!q||!q.length){q=[]}}var u=false;var x=y.selection.getRng(true);var A=x.commonAncestorContainer||{};if(!x.collapsed){return false}var w=y.contextManager;if(w.isPreContextActive()){l.sendEvent("editor.instance.autoformat.prevented",{context:"pre"});return false}for(var z=0,v=q.length;z<v;++z){if(w.isContextActive(q[z])){l.sendEvent("editor.instance.autoformat.prevented",{context:q[z]});return false}}u=s.test(g(x));return u},execute:function(z,v){var w;var u;var y;var A=1;var x;var B;var C=d(v);if(C===32){z.execCommand("mceInsertContent",false,"&nbsp;")}else{z.execCommand("mceInsertContent",false,String.fromCharCode(C))}w=z.selection.getRng(true);u=g(w);if(u[u.length-1]==="|"){u+=" ";A=0}y=s.exec(u.substring(0,u.length-1));x=o(y[1].length+A,w.commonAncestorContainer,w.startOffset);w.setStart(x.container,x.offset);B=e(w.commonAncestorContainer);z.selection.setRng(w);t(y,z.selection.getRng(true));j=z.selection.getRng(true);if(r){v.preventDefault();v.stopPropagation();m.dom.Event.cancel(v);return false}}}}function d(q){return e.browser.msie?q.keyCode:q.which}function f(){this.handlers={}}f.prototype={registerHandler:function(r,q){if(!this.handlers[r]){this.handlers[r]=[]}this.handlers[r].push(q)},executeHandlers:function(t,s,r){var q=true;e.each(this.handlers[t]||[],function(u,v){if(v.handles(s)){q=v.execute(s,r);n.trace("jira.editor.wiki.autoformatted");return false}});return q}};function h(v){var w=new f();function x(M,Q,L,N,J,I){var O=M.charCodeAt(0);var K=AJS.contextPath()+"/images/icons/emoticons/"+J;var P;P=i(Q,function(){var R=v.dom.createHTML("img",{src:K,alt:v.getLang(N),title:v.getLang(N),border:0,"class":"emoticon emoticon-"+L,"data-emoticon-name":L,align:"absmiddle"});v.execCommand("mceInsertContent",false,R,{skip_undo:true})},true);this.imagePath=K;w.registerHandler(O,P)}var u=b.isGreaterThanOrEqualTo("7.0")?"png":"gif";var y=[new x(")",c._REGEXES_EMOTICON.SMILE,"smile","emotions_dlg.smile","smile."+u),new x("(",c._REGEXES_EMOTICON.SAD,"sad","emotions_dlg.sad","sad."+u),new x("P",c._REGEXES_EMOTICON.CHEEKY,"tongue","emotions_dlg.tongue","tongue."+u),new x("p",c._REGEXES_EMOTICON.CHEEKY_2,"cheeky","emotions_dlg.tongue","tongue."+u),new x("D",c._REGEXES_EMOTICON.LAUGH,"biggrin","emotions_dlg.biggrin","biggrin."+u),new x(")",c._REGEXES_EMOTICON.WINK,"wink","emotions_dlg.wink","wink."+u),new x(")",c._REGEXES_EMOTICON.THUMBS_UP,"thumbs_up","emotions_dlg.thumbs_up","thumbs_up."+u),new x(")",c._REGEXES_EMOTICON.THUMBS_DOWN,"thumbs_down","emotions_dlg.thumbs_down","thumbs_down."+u),new x(")",c._REGEXES_EMOTICON.INFORMATION,"information","emotions_dlg.information","information."+u),new x(")",c._REGEXES_EMOTICON.TICK,"check","emotions_dlg.check","check."+u),new x(")",c._REGEXES_EMOTICON.CROSS,"error","emotions_dlg.error","error."+u),new x(")",c._REGEXES_EMOTICON.WARNING,"warning","emotions_dlg.warning","warning."+u),new x(")",c._REGEXES_EMOTICON.PLUS,"add","emotions_dlg.add","add."+u),new x(")",c._REGEXES_EMOTICON.MINUS,"forbidden","emotions_dlg.forbidden","forbidden."+u),new x(")",c._REGEXES_EMOTICON.QUESTION,"help_16","emotions_dlg.help_16","help_16."+u),new x(")",c._REGEXES_EMOTICON.LIGHT_ON,"lightbulb_on","emotions_dlg.lightbulb_on","lightbulb_on."+u),new x(")",c._REGEXES_EMOTICON.LIGHT_OFF,"lightbulb","emotions_dlg.lightbulb","lightbulb."+u),new x(")",c._REGEXES_EMOTICON.YELLOW_STAR,"star_yellow","emotions_dlg.star_yellow","star_yellow."+u),new x(")",c._REGEXES_EMOTICON.YELLOW_STAR_2,"star_yellow","emotions_dlg.star_yellow","star_yellow."+u),new x(")",c._REGEXES_EMOTICON.RED_STAR,"star_red","emotions_dlg.star_red","star_red."+u),new x(")",c._REGEXES_EMOTICON.GREEN_STAR,"star_green","emotions_dlg.star_green","star_green."+u),new x(")",c._REGEXES_EMOTICON.BLUE_STAR,"star_blue","emotions_dlg.star_blue","star_blue."+u),new x(")",c._REGEXES_EMOTICON.FLAG,"flag","emotions_dlg.flag","flag."+u),new x(")",c._REGEXES_EMOTICON.FLAG_OFF,"flag_grey","emotions_dlg.flag_grey","flag_grey."+u)];var B=new Array();for(var E=0;E<y.length;E++){B[E]=new Image();B[E].src=y[E].imagePath}function t(L,J){var K=v.formatter.get(L)[0];var I=v.dom.create(K.inline,{style:K.styles});I.appendChild(document.createTextNode(J+"{$caret}"));v.execCommand("mceInsertContent",false,a(I),{skip_undo:true});v.formatter.remove(L)}var H={"*":"bold",_:"italic","~":"subscript","^":"superscript","+":"underline","-":"strikethrough"};e.each(H,function(I,K){var J=new RegExp("(?:[\\s\\xA0\\u200b\\uFEFF]+|^)(\\"+I+"(?=[^\\s"+I+"])([^"+I+"]*?[^\\s]))$");w.registerHandler(I.charCodeAt(0),i(J,function(L){t(K,L[2]);l.sendEvent("editor.instance.autoformat.format")},true))});v.formatter.register("samp-inline",{inline:"samp"});var q=/(?:[\s\xA0\u200b]+|^)({{(?=[^\s])([^}]*?[^\s])})$/;if(p.isFeatureEnabled("jira.renderer.consider.variable.format")){q=/(?:^|[^$])(?:[\s\xA0\u200b]+|^)({{(?=[^\s])([^}]*?[^\s])})$/}w.registerHandler("}".charCodeAt(0),i(q,function(I){t("samp-inline",I[2]);l.sendEvent("editor.instance.autoformat.inlinecode")},true));v.formatter.register("cite",{inline:"cite"});w.registerHandler("?".charCodeAt(0),i(/(?:[\s\xA0\u200b]+|^)(\?\?(?=[^\s])([^\?]*?[^\s])\?)$/,function(I){t("cite",I[2]);l.sendEvent("editor.instance.autoformat.cite")},true));for(var E=1;E<=6;E++){(function(I){w.registerHandler(" ".charCodeAt(0),i(new RegExp("^\\u200b?(h"+I+"\\.)$"),function(){v.execCommand("formatBlock",false,"h"+I,{skip_undo:true});l.sendEvent("editor.instance.autoformat.header")},true))})(E)}w.registerHandler(" ".charCodeAt(0),i(/^\u200b?(bq\.)$/,function(){v.execCommand("formatBlock",false,"blockquote",{skip_undo:true});l.sendEvent("editor.instance.autoformat.bq")},true));w.registerHandler(" ".charCodeAt(0),i(/[^-]*[\s](\-\-\-?)$/,function(I){var J=I[1].length===2?"\u2013":"\u2014";v.execCommand("mceInsertContent",false,J,{skip_undo:true});l.sendEvent("editor.instance.autoformat.dash")},false));var G=i(/(([^\s-]+)(\-\-\-?)([^\s-]+))$/,function(I){var J=I[3].length===2?"\u2013":"\u2014";v.execCommand("mceInsertContent",false,I[2]+J+I[4],{skip_undo:true})},false);w.registerHandler(" ".charCodeAt(0),G);w.registerHandler(m.util.VK.ENTER,G);var s=i(/^\u200b?(\-\-\-\-)$/,function(){v.execCommand("mceInsertContent",false,"<hr />",{skip_undo:true});l.sendEvent("editor.instance.autoformat.hr")},true);w.registerHandler(" ".charCodeAt(0),s);w.registerHandler(m.util.VK.ENTER,s);var A=/(\{code)$/;if(p.isFeatureEnabled("jira.renderer.consider.variable.format")){A=/(?:^|[^$])(\{code)$/}w.registerHandler("}".charCodeAt(0),i(A,function(){v.execCommand("mceInsertContent",false,'<div class="code panel" style="border-width: 1px;"><div class="codeContent panelContent"><pre class="code-java">{$caret} </pre></div></div>');l.sendEvent("editor.instance.autoformat.code")},true));var z=/(\{panel)$/;if(p.isFeatureEnabled("jira.renderer.consider.variable.format")){z=/(?:^|[^$])(\{panel)$/}w.registerHandler("}".charCodeAt(0),i(z,function(){v.execCommand("mceInsertContent",false,'<div class="plain panel"><panel-title>'+"\u6211\u7684\u6807\u9898"+"</panel-title><p>{$caret} </p></div>");l.sendEvent("editor.instance.autoformat.panel")},true));var F=/(\{noformat)$/;if(p.isFeatureEnabled("jira.renderer.consider.variable.format")){F=/(?:^|[^$])(\{noformat)$/}w.registerHandler("}".charCodeAt(0),i(F,function(){v.execCommand("mceInsertContent",false,'<div class="preformatted panel" style="border-width: 1px;"><div class="preformattedContent panelContent"><pre>{$caret} </pre></div></div>');l.sendEvent("editor.instance.autoformat.noformat")},true));w.registerHandler(m.util.VK.ENTER,i(/(^\u200b?\|\|\s*(?:[^|]*\s?\|\|\s?)+$)/,function(K){var N="<table class='confluenceTable'><tr>";var M="";var I=true;var O=e(K[1].slice(2,-2).split("||")).map(function(P){P=e.trim(this);I=I&&P==="";return P});if(I){O[0]="editor.autoformat.sampletext.firstcell"}for(var L=0,J=O.length;L<J;L++){N+="<th class='confluenceTh'>"+O[L]+"</th>";M+="<td class='confluenceTd'>&#x200b;</td>"}N+="</tr><tr>"+M+"</tr></table>";v.execCommand("mceInsertContent",false,N,{skip_undo:true});v.selection.select(e(v.selection.getRng(true).commonAncestorContainer).parents("table").find(I?"th":"td")[0].childNodes[0]);e(v.selection.getRng().startContainer).parent().closest('[contenteditable="true"]').focus();l.sendEvent("editor.instance.table.autoformat.plain")},true,"table"));w.registerHandler(m.util.VK.ENTER,i(/(^\u200b?\|\s?(?:[^|]*\s?\|\s?)+$)/,function(K){var M="<table class='confluenceTable'><tr>";var I=true;var N=e(K[1].slice(1,-1).split("|")).map(function(O){O=e.trim(this);I=I&&O==="";return O});if(I){N[0]="editor.autoformat.sampletext.firstcell"}for(var L=0,J=N.length;L<J;L++){M+="<td class='confluenceTd'>"+N[L]+"</td>"}M+="</tr></table>";v.execCommand("mceInsertContent",false,M,{skip_undo:true});I&&v.selection.select(e(v.selection.getRng(true).commonAncestorContainer).parents("table").find("td")[0].childNodes[0]);e(v.selection.getRng().startContainer).parent().closest('[contenteditable="true"]').focus();l.sendEvent("editor.instance.table.autoformat")},true,"table"));e.each({"]":/(?:\s|^)((\[[^\[^\]]+))$/,"\r":/(?:\s|^)((\[[^\[^\]]+)(?:\]))$/," ":/(?:\s|^)((\[[^\[^\]]+)(?:\]))$/},function(I,J){w.registerHandler(I.charCodeAt(0),i(J,function(K,M){var L=K[1]+(K[1].endsWith("]")?"":"]");k.renderMarkup(L,v.settings.target.getRenderParams()).then(function(N){N=e(new DOMParser().parseFromString(N,"text/html").body).find("p").unwrap().html();v.execCommand("mceInsertContent",false,N,{skip_undo:true});l.sendEvent("editor.instance.autoformat.wikilink")})},true))});var D=i(/\b(((https?|ftp):\/\/|(www\.))[\w\.\$\-_\+!\*'\(\),/\?:@=&%#~;\[\]]+)$/,function(I){var J=I[3]?I[1]:"http://"+I[1];var K=v.dom.create("a",{href:J});K.appendChild(document.createTextNode(I[1]));v.execCommand("mceInsertContent",false,a(K),{skip_undo:true});v.getDoc().execCommand("unlink",false,{});l.sendEvent("editor.instance.autoformat.autolink")},false,"a");w.registerHandler(" ".charCodeAt(0),D);w.registerHandler(m.util.VK.ENTER,D);var r=i(/\b((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)$/i,function(I){var J=v.dom.create("a",{href:"mailto:"+I[1]});J.appendChild(document.createTextNode(I[1]));v.execCommand("mceInsertContent",false,a(J),{skip_undo:true});v.getDoc().execCommand("unlink",false,{})},false,"a");w.registerHandler(" ".charCodeAt(0),r);w.registerHandler(m.util.VK.ENTER,r);var C=function(K,J){var I=i(K,function(){v.execCommand("mceInsertContent",false,J,{skip_undo:true});l.sendEvent("editor.instance.autoformat.quickcorrect")},false);w.registerHandler(" ".charCodeAt(0),I);w.registerHandler(m.util.VK.ENTER,I)};C(/(?:\b|^)(jira)$/i,"Jira");C(/(?:\b|^)(bitbucket|[Bb]itBucket)$/,"Bitbucket");C(/(?:\b|^)(atlassian)$/,"Atlassian");C(/(?:\b|^)([Hh]ipchat)$/,"HipChat");v.on("keydown",function(I){var J=d(I);if(J===m.util.VK.ENTER){return w.executeHandlers(J,v,I)}},true);v.on("keypress",function(I){return w.executeHandlers(d(I),v,I)},true)}m.create("tinymce.plugins.jira.WikiAutoFormat",{init:function(q){q.on("init",function(){h(q)})}});m.PluginManager.add("jira.wikiautoformat",m.plugins.jira.WikiAutoFormat);m.PluginManager.urls["jira.wikiautoformat"]=true;return{configure:function(q,r){r.plugins.push("jira.wikiautoformat")}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:resources', location = 'js/plugins/prevent-default-plugin.js' */
define("jira/editor/plugins/prevent-default",["jira/editor/tinymce","jquery"],function(a,b){a.create("tinymce.plugins.jira.PreventDefaultPlugin",{init:function(c){var d=c.settings;c.on("init",function(){var f=c.getBody()._controller;if(f&&f.$textarea&&f.$textarea.get(0)){var g=f.$textarea.get(0).ownerDocument;if(g){var e=d.preventdefault.selector;b(g).on("click",e,function(h){h.preventDefault()})}}})}});a.PluginManager.add("jira.preventdefault",a.plugins.jira.PreventDefaultPlugin);a.PluginManager.urls["jira.preventdefault"]=true;return{configure:function(c,d){d.plugins.push("jira.preventdefault");d.preventdefault={selector:"a[contenteditable=false]"}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:resources', location = 'js/plugins/editor-content-postprocess-plugin.js' */
define("jira/editor/plugins/editor-content-postprocess",["jira/editor/tinymce","jira/editor/converter/util/strings","jira/editor/util/temporaryAttachment","jira/editor/analytics"],function(i,b,a,h){var c=function(l,k){var j=l&&l.attr("class")||"";return j.indexOf(k)>=0};var f=function(j){j.parser.addAttributeFilter("class",function(k){var l=k.length;var m;while(l--){m=k[l];if(c(m,"user-hover")){m.attr("contenteditable","false");m.attr("tabindex","-1")}}})};var e=function(j){j.parser.addNodeFilter("h1,h2,h3,h4,h5,h6",function(l){var k=function(t,r){var s=t;var u=[];for(t=s.firstChild;t;t=t.next){if(t.name===r&&!t.firstChild){u.push(t)}}return u};var o=l.length;var p;while(o--){p=l[o];var n=k(p,"a");var m=n.filter(function q(r){return typeof r.attr("name")!=="undefined"});m.forEach(function(r){r.remove()})}})};var d=function(j){j.parser.addNodeFilter("div,pre",function(k){k.forEach(function(o){if(o.parent&&o.parent.name==="pre"&&o.name!=="panel-title"){o.unwrap()}if(!o.attr("class")&&o.name==="pre"){o.unwrap()}if(!c(o,"panel")||c(o,"panelHeader")||c(o,"panelContent")){return}if(c(o,"preformatted")){o.attr("class","noformat panel")}if(o.lastChild&&o.lastChild.firstChild&&/^code/.test(o.lastChild.firstChild.attr("class"))){o.attr("data-language",o.lastChild.firstChild.attr("class"));o.attr("class","code panel")}var m=o.firstChild;if(c(m,"panelHeader")){var n=m.attr("style");m.firstChild.unwrap();m.name="panel-title";m.attr("class",n)}var l=o.lastChild;if(c(l,"panel")||c(l,"panelContent")){var n=l.attr("style");if(c(o,"noformat")||c(o,"code")){l.firstChild.unwrap()}l.unwrap()}if(c(o,"noformat")||c(o,"code")){o.name="pre"}else{o.attr("class","plain panel")}})})};var g=function(j){j.parser.addNodeFilter("img",function(k){if(k.length===0){return}var m=a.getFormToken(j.getContainer());if(!m){console.warn("formToken not found, skipping invalid temporary attachment content post processing");return}var l=function(n){var o=n.attr("src");if(b.isTemporaryAttachmentPath(o)){var p="/secure/temporaryattachment/"+m;return !b.contains(o,p)}return false};k.filter(l).forEach(function(n){n.remove();h.sendEvent("editor.plugin.content-postprocess.temporary-attachment.removed")})})};i.create("tinymce.plugins.jira.EditorContentPostprocess",{init:function(j){j.on("PreInit",function(){g(j);f(j);e(j);d(j)})}});i.PluginManager.add("jira.editorcontentpostprocess",i.plugins.jira.EditorContentPostprocess);i.PluginManager.urls["jira.editorcontentpostprocess"]=true;return{configure:function(j,k){k.plugins.push("jira.editorcontentpostprocess");k.editor_content_postprocess={};return k}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/node-handler.js' */
define("jira/editor/converter/handlers/node-handler",["jira/editor/converter/util/strings","jira/editor/converter/util/rte-strings","jira/editor/converter/new-lines/new-lines","jira/lib/class","underscore","jquery"],function(d,c,a,b,i,e){var g={};var j=[];var h=b.extend({init:function h(k){this.node=k},before:function(){return new h.EmptyHandler()},processText:function(){return""},after:function(){return new h.EmptyHandler()},children:function(){var k=[];var m=this.node.childNodes;var l=i.filter(m,function(o,n){var p=this.createChildHandler(o,n,m.length);if(p.processText()===""){return true}if(c.isWhitespace(p.processText())&&d.contains(p.processText(),"\n")&&!p.preformatted){return false}return true},this);i.each(l,function(o,n){k.push(this.createBeforeChild(o,n,l.length));k.push(this.createChildHandler(o,n,l.length));k.push(this.createAfterChild(o,n,l.length))},this);return k},createBeforeChild:function(l,m,k){return new h.EmptyHandler()},createAfterChild:function(l,m,k){return new h.EmptyHandler()},createChildHandler:function(l,m,k){return h.createHandler(l)},newLinesBefore:function(){var k=h._isBlock(this.node.parentNode)||e(this.node).parents().is("th, td");if(h._isBlock(this.node)&&!(e(this.node).is(":first-child")&&k)){return a.single(true)}else{return a.empty()}},newLinesAfter:function(){return a.empty()}});h.EmptyHandler=h.extend({init:function f(k){this.node=k},before:function(){throw undefined},after:function(){throw undefined},processText:function(){return""},children:function(){return[]}});h.addHandler=function(k,l){k=k.toLowerCase();g[k]=l;j.push(k)};h.getHandler=function(k){var l=k.nodeName.toLowerCase();return g[(l+"."+(k.className||"")).toLowerCase()]||g[l]||j.reduce(function(n,m){return n||e(k).is(m)&&g[m]},null)||h};h.createHandler=function(k){return new (h.getHandler(k))(k)};h.isInsideTable=function(k){return e(k).parents("td").length>0||e(k).parents("th").length>0};h.singleNewLineExceptTable=function(l,k){if(h.isInsideTable(l)){return a.empty()}return a.single(k)};h._isBlock=i.memoize(function(l){if(!l||!l.tagName){return false}var m=document.createElement("div");var k=document.createElement(l.tagName);m.appendChild(k);document.body.appendChild(m);try{return window.getComputedStyle(k).display==="block"}finally{document.body.removeChild(m)}},function(k){if(!k||!k.tagName){return""}return k.tagName});h.isEmptyTextNode=function(k){if(!k){return false}return k.nodeType===Node.TEXT_NODE&&c.trim(k.nodeValue)===""};return h});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/empty-handler.js' */
define("jira/editor/converter/handlers/empty-handler",["jira/editor/converter/handlers/node-handler"],function(a){return a.EmptyHandler});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/block-handler.js' */
define("jira/editor/converter/handlers/block-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/new-lines/new-lines","jira/editor/converter/util/strings","jira/editor/converter/util/color-converter","jquery"],function(d,h,c,g,b,f){var a=d.extend({init:function a(i){this.wrapperNode=i;this.node=i.querySelector(".panelContent")||i;this.nodeHeader=i.querySelector("panel-title")||i.querySelector(".panelHeader");this.attrs=this.getAttrs();if(f(this.node).is(".panelContent")){this.workaroundTypingOutsideWrapper()}},workaroundTypingOutsideWrapper:function(){var i=f(this.wrapperNode);i.find("> p").before('<br data-mce-bogus="1">');if(this.node){var j=i.find("> :not(div.preformattedContent.panelContent, div.codeContent.panelContent, div.panelHeader, div.panelContent)");j.detach().appendTo(this.node)}else{this.node=this.wrapperNode}},getAttrs:function(){var i=[];if(this.nodeHeader){if(this.nodeHeader.textContent){i.push("title="+this.nodeHeader.textContent)}if(this.nodeHeader.style.borderBottomStyle){i.push("borderStyle="+this.nodeHeader.style.borderBottomStyle)}if(this.nodeHeader.style.borderBottomWidth&&this.nodeHeader.style.borderBottomWidth!=="1px"){i.push("borderWidth="+this.nodeHeader.style.borderBottomWidth)}if(this.nodeHeader.style.borderBottomColor){i.push("borderColor="+b.colorToHex(this.nodeHeader.style.borderBottomColor))}if(this.nodeHeader.style.backgroundColor){i.push("titleBGColor="+b.colorToHex(this.nodeHeader.style.backgroundColor))}}if(this.wrapperNode){if(this.wrapperNode.style.backgroundColor){i.push("bgColor="+b.colorToHex(this.wrapperNode.style.backgroundColor))}}return i.length>0?i.join("|"):""},createChildHandler:function(k,l,j){if(k.nodeType===Node.TEXT_NODE){return h.createPreformattedTextHandler(k)}else{if(k.tagName==="BR"){return h.newlineHandler()}else{if(k.tagName==="IMG"){if(!g.startsWith(k.src,"data")&&!g.startsWith(k.src,"blob")){return d.createHandler(k)}else{var i=document.createTextNode("(attachment)");return h.createTextHandler(i)}}else{if(k.tagName==="PRE"){return new e(k)}else{if(k.tagName==="PANEL-TITLE"){return new d.EmptyHandler(k)}else{return new e(k)}}}}}},children:function(){var i=d.prototype.children.apply(this,arguments);i.unshift(c.single(true));return i},newLinesBefore:function(){return d.singleNewLineExceptTable(this.wrapperNode,true)},newLinesAfter:function(){return d.singleNewLineExceptTable(this.wrapperNode,true)}});var e=d.extend({createChildHandler:a.prototype.createChildHandler,newLinesBefore:function(){return c.empty()},before:function(){return new d.EmptyHandler()},after:function(){return new d.EmptyHandler()}});return a});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/text-handler.js' */
define("jira/editor/converter/handlers/text-handler",["jira/featureflags/feature-manager","jira/editor/converter/util/strings","jira/editor/converter/util/rte-strings","jira/editor/converter/handlers/node-handler","jira/editor/context-detector"],function(b,f,a,d,c){var e=d.extend({init:function e(g){this.node=g;this.preformatted=this.node.preformatted},processText:function(){var h=this.node.textContent||"";if(this.node.preformatted){return h}if(!a.isWhitespace(h)){h=this.trimNewlines(h)}var g=h;if(b.isFeatureEnabled("jira.renderer.consider.variable.format")){g=g.replace(/([^$])({[^}]+})/g,"$1\\$2").replace(/(\$)({[^}]+)$/g,"$1\\$2")}else{g=g.replace(/{/g,"\\{")}if(c.detectTable(this.node)){g=f.replace(g,"|","\\|")}return g},children:function(){return[]},trimNewlines:function(n){var k;for(k=0;k<n.length;k++){if(!a.isWhitespace(n[k])){break}}var h;for(h=n.length-1;h>=0;h--){if(!a.isWhitespace(n[h])){break}}var l=n.substring(0,k);var m=n.substring(k,h+1);var g=n.substring(h+1);return f.replace(l,"\n","")+m+f.replace(g,"\n","")}});e.createTextHandler=function(g){var h=g;if(g instanceof Node){h=g.textContent}return new e({textContent:h||""})};e.createPreformattedTextHandler=function(g){var h=g;if(g instanceof Node){h=g.textContent}return new e({textContent:h||"",preformatted:true})};e.newlineHandler=function(){return new e({textContent:"\n",preformatted:true})};return e});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/heading-handler.js' */
define("jira/editor/converter/handlers/heading-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler"],function(b,d,a){var e=b.extend({init:function e(f){this.node=f},newLinesBefore:function(){return b.singleNewLineExceptTable(this.node,true)},before:function(){var f=this.node.tagName;if(f in c){return d.createTextHandler(c[f])}return new a()},newLinesAfter:function(){return b.singleNewLineExceptTable(this.node)}});var c={};c.H1="h1. ";c.H2="h2. ";c.H3="h3. ";c.H4="h4. ";c.H5="h5. ";c.H6="h6. ";return e});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/bold-handler.js' */
define("jira/editor/converter/handlers/bold-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/factories/whitespace-extractor"],function(b,d,e){var c=b.extend({init:function a(f){this.node=f},before:function(){return d.createTextHandler("*")},after:function(){return d.createTextHandler("*")}});return e(c)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/emphasis-handler.js' */
define("jira/editor/converter/handlers/emphasis-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/factories/whitespace-extractor"],function(a,c,d){var b=a.extend({init:function e(f){this.node=f},before:function(){return c.createTextHandler("_")},after:function(){return c.createTextHandler("_")}});return d(b)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/monospaced-handler.js' */
define("jira/editor/converter/handlers/monospaced-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/factories/whitespace-extractor"],function(a,c){var b=a.extend({init:function d(e){this.node=e},before:function(){return c.createPreformattedTextHandler("{{")},after:function(){return c.createPreformattedTextHandler("}}")}});return b});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/link-handler.js' */
define("jira/editor/converter/handlers/link-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/image-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/handlers/superscript-handler","jira/editor/converter/util/strings","jira/editor/converter/util/uri","jquery"],function(i,a,h,g,b,e,c,f){var d=i.extend({init:function(m){this.node=m;var l=this.node.getAttribute("href");var k=e.resolveUrl(l,e.getBaseUrl());var j=m.getAttribute("data-filename");if(d.isAnchor(l)){k=l}else{if(d.isAttachment(k)||j){this.attachment=j||e.getFilenameFromHref(k);if(f(m).find(">:first-child:last-child").is("img")){this.imageAttachment=this.attachment}}else{if(d.isIssue(k)){this.issueKey=e.substringAfter(k,e.getContextUrl()+"/browse/")}else{if(d.isProfile(k,m)){this.username=c.decodeUri(e.substringAfter(k,"ViewProfile.jspa?name="))}else{if(d.isWiki(k,m)){this.wikiLink=f(m).text()}}}}}this.href=k},before:function(){var j=f(this.node.previousSibling).is("a")?" ":"";if(this.imageAttachment){return a.createTextHandler(j+"!")}else{if(this.attachment){return a.createTextHandler(j+"[^")}else{if(this.issueKey){return new g()}else{if(this.username){return a.createTextHandler(j+"[~")}else{if(this.href||this.wikiLink){return a.createTextHandler(j+"[")}else{return new g()}}}}}},children:function(){if(this.imageAttachment){return[a.createTextHandler(""+this.imageAttachment)]}else{if(this.attachment){return[a.createTextHandler(""+this.attachment)]}else{if(this.username){return[a.createTextHandler(""+this.username)]}else{if(d.isAnchor(this.href,this.node)){return a.createTextHandler(this.href)}else{if(d.isMailto(this.href,this.node)){return this._filterMailtoChildren(this._super())}else{return this._super()}}}}}},after:function(){if(this.imageAttachment){var j=h.getAttrs(f(this.node).find("> img")[0]);return a.createTextHandler(j+"!")}else{if(this.attachment||this.wikiLink){return a.createTextHandler("]")}else{if(this.issueKey){return new g()}else{if(this.username){return a.createTextHandler("]")}else{if(this.href){if(this.href===f(this.node).text()||d.isAnchor(this.href,this.node)){return a.createTextHandler("]")}else{return a.createTextHandler("|"+this.href+"]")}}else{return new g()}}}}}},_filterMailtoChildren:function(j){if(j.length>=3&&(j[j.length-2] instanceof b)){j=j.splice(0,j.length-3)}return j}});d.isAttachment=function(j){if(/^\//.test(j)){j=window.location.origin+j}return e.startsWith(j,e.getContextUrl()+"/secure/attachment/")||e.startsWith(j,"../secure/attachment/")};d.isIssue=function(j){if(/^\//.test(j)){j=window.location.origin+j}return e.startsWith(j,e.getContextUrl()+"/browse/")};d.isProfile=function(j,k){return e.contains(j,"ViewProfile.jspa?name=")&&f(k).is(".user-hover")};d.isWiki=function(j,k){return e.contains(j,"/wiki/display/")&&e.endsWith(c.decodeUri(j),"/"+f(k).text())};d.isMailto=function(j,k){return e.startsWith(j,"mailto:")};d.isAnchor=function(j,k){return e.startsWith(j,"#")&&(!k||j===("#"+f(k).text()))};return d});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/linebreak-handler.js' */
define("jira/editor/converter/handlers/linebreak-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jquery"],function(a,c,b){return a.extend({init:function d(e){this.node=e},before:function(){return new a.EmptyHandler()},children:function(){return new a.EmptyHandler()},after:function(){if(!this.node.previousSibling&&!this.node.nextSibling&&b(this.node).parent().is("td, th")){return c.createTextHandler(" ")}else{if(this.node.previousSibling&&this.node.previousSibling.tagName==="BR"&&b(this.node).parent().is("td, th")){return new c({textContent:decodeURI("%C2%A0")+"\n",preformatted:true})}else{return c.newlineHandler()}}}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/image-handler.js' */
define("jira/editor/converter/handlers/image-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler","jquery","jira/editor/converter/util/strings","wrm/context-path"],function(j,a,f,c,b,d){var k="/images/icons/";var h="/images/icons/emoticons/";var g={EMOTE:"emote",EXTERNAL:"external-image",ATTACHMENT:"attachment-image",TEMPORARY_ATTACHMENT:"temporary-attachment-image",PLACEHOLDER:"placeholder"};var i=j.extend({init:function i(l){this.node=l;this.imageType=this._getImageType(l)},_getImageType:function(l){if(this.node.getAttribute("imagetext")){return g.PLACEHOLDER}var m=this.node.getAttribute("data-image-type");if(m){return m}var n=this.node.getAttribute("src");if(!n){return}if(b.isEmotePath(n)&&l.classList.contains("emoticon")){if(b.contains(n,k+"mail_small")&&this.$(l).parents("a").is(".external-link")){return}return g.EMOTE}if(b.isAttachmentPath(n)||b.isThumbnailPath(n)||n.match(/^((data|blob):http)|(data:image)/)){return g.ATTACHMENT}if(b.isTemporaryAttachmentPath(n)){return g.TEMPORARY_ATTACHMENT}return g.EXTERNAL},before:function(){var m=this.node.previousSibling;var l=(m&&m.nodeType===Node.TEXT_NODE&&!b.endsWith(m.textContent," "))?" ":"";if(this.imageType&&this.imageType!==g.EMOTE){return a.createTextHandler(l+"!")}return new f()},children:function(){return this.imageType?a.createTextHandler(this._getImageSource()):new f()},after:function(){var m=this.node.nextSibling;var l=(m&&m.nodeType===Node.TEXT_NODE&&!b.startsWith(m.textContent," "))?" ":"";if(this.imageType&&this.imageType!==g.EMOTE){return a.createTextHandler("!"+l)}return new f()},_getImageSource:function(){if(this.imageType===g.PLACEHOLDER){return this.node.getAttribute("imagetext")}else{if(this.imageType===g.EMOTE){var l=this.node.getAttribute("src");var m=b.substringAfter(l,h).replace(/\.(png|gif)$/,"");if(m in e){return e[m]}if(b.startsWith(l,d())){return"!"+document.location.origin+l+"!"}}else{if(this.imageType===g.ATTACHMENT||this.imageType===g.TEMPORARY_ATTACHMENT||this.imageType===g.EXTERNAL){var o=this.node.getAttribute("data-filename")||this.node.getAttribute("href")||this.node.getAttribute("src");if(this.imageType===g.ATTACHMENT){o=b.getFilenameFromHref(o)}else{if(this.imageType===g.TEMPORARY_ATTACHMENT&&!this.node.getAttribute("data-filename")){o=b.getFilenameFromHref(o,true)}}var n=i.getAttrs(this.node);if(n){return o+n}return o}}}}});i.getAttrs=function(m){if(c(m).is("img.emoticon")){return""}var l=[];if(m.width){l.push("width="+m.width)}if(m.height){l.push("height="+m.height)}if(m.id){l.push("id="+m.id)}if(m.vspace){l.push("vspace="+m.vspace)}if(m.align){l.push("align="+m.align)}else{if(m.style.textAlign){l.push("align="+m.style.textAlign)}else{if(m.style["float"]){l.push("align="+m.style["float"])}}}if(b.isThumbnailPath(m.getAttribute("src"))){l.push("thumbnail")}return l.length>0?"|"+l.join(","):""};var e={smile:":)",sad:":(",tongue:":P",biggrin:":D",wink:";)",thumbs_up:"(y)",thumbs_down:"(n)",information:"(i)",check:"(/)",error:"(x)",warning:"(!)",add:"(+)",forbidden:"(-)",help_16:"(?)",lightbulb_on:"(on)",lightbulb:"(off)",star_yellow:"(*)",star_red:"(*r)",star_green:"(*g)",star_blue:"(*b)",flag:"(flag)",flag_grey:"(flagoff)"};return i});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/code-block-handler.js' */
define("jira/editor/converter/handlers/code-block-handler",["jira/editor/converter/handlers/block-handler","jira/editor/converter/handlers/text-handler","jquery"],function(a,d,c){var b=a.extend({init:function b(e){this._super(e);this.language=this.getLanguage()},getLanguage:function(){var e=c(this.node).is("pre")?this.node:this.node.querySelector("pre");var f=c(e).attr("data-language");return f?f.replace("code-",""):""},before:function(){var e=[];if(this.language){e.push(this.language)}if(this.attrs){e.push(this.attrs)}var e=(e.length)?":"+e.join("|"):"";return d.createPreformattedTextHandler("{code"+e+"}")},after:function(){return d.createPreformattedTextHandler("{code}")}});return b});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/noformat-handler.js' */
define("jira/editor/converter/handlers/noformat-handler",["jira/editor/converter/handlers/block-handler","jira/editor/converter/handlers/text-handler"],function(a,c){var b=a.extend({before:function(){var d=this.attrs?":"+this.attrs:"";return c.createPreformattedTextHandler("{noformat"+d+"}")},after:function(){return c.createPreformattedTextHandler("{noformat}")}});return b});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/list-type-matcher.js' */
define("jira/editor/converter/handlers/list-type-matcher",["jquery","underscore"],function(b,a){var c={};c.selectorBulletMap={};c.selectorBulletMap["ul:not(.alternate)"]="*";c.selectorBulletMap["ul.alternate"]="-";c.selectorBulletMap.ol="#";c.DEFAULT_DECORATOR="*";c.matchBullet=function(d){return a.find(c.selectorBulletMap,function(f,e){return b(d).is(e)})};c.getBulletDecorator=function(d){return c.matchBullet(d)||c.DEFAULT_DECORATOR};c.isListNode=function(d){return c.matchBullet(d)!=null};return c});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/list-handler.js' */
define("jira/editor/converter/handlers/list-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/handlers/list-type-matcher","jquery","jira/editor/converter/new-lines/new-lines","jira/editor/context-detector"],function(d,f,a,h,e,c,b){return d.extend({init:function g(i,j){this.node=i;this.type=this.node.getAttribute("type");if(typeof j==="undefined"){this.depth=0}else{this.depth=j}this.bulletDecorator=h.getBulletDecorator(i)},createChildHandler:function(j){var i=d.getHandler(j);if(j.tagName==="LI"){return new i(j,this.depth,this.bulletDecorator)}else{return new i(j)}},before:function(){return new a()},newLinesBefore:function(){if(this.depth===0&&b.detectTable(this.node)&&this.node.previousSibling&&!d.isEmptyTextNode(this.node.previousSibling)){return c.single(true)}return c.empty()},newLinesAfter:function(){if(this.depth===0&&!e(this.node).is(":last-child")){return new c(2,true)}return c.empty()},createAfterChild:function(j,k,i){if(k!==i-1){return f.newlineHandler()}return new a()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/list-item-handler.js' */
define("jira/editor/converter/handlers/list-item-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/handlers/list-type-matcher","jira/editor/converter/util/strings"],function(c,e,a,f,d){return c.extend({init:function b(g,i,h){this.node=g;if(typeof i==="undefined"){this.depth=0}else{this.depth=i}this.bulletDecorator=h},before:function(){var g=d.repeat(this.bulletDecorator,this.depth+1);return e.createTextHandler(" "+g+" ")},after:function(){return new a()},createBeforeChild:function(h,i,g){if(f.isListNode(h)){return e.newlineHandler()}return new a()},createChildHandler:function(i,j,h){var g=c.getHandler(i);if(f.isListNode(i)){return new g(i,this.depth+1)}else{return new g(i)}}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/table-handler.js' */
define("jira/editor/converter/handlers/table-handler",["jira/editor/converter/handlers/node-handler","jira/editor/context-detector","jira/editor/converter/new-lines/new-lines"],function(d,c,b){return d.extend({init:function a(e){this.node=e},newLinesBefore:function(){var e=c.detectTable(this.node.previousElementSibling);return b.single(!e)}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/table-row-handler.js' */
define("jira/editor/converter/handlers/table-row-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/new-lines/new-lines"],function(c,e,a,b){return c.extend({init:function d(f){this.node=f},createBeforeChild:function(g,h,f){if(g.tagName==="TH"){return e.createTextHandler("||")}else{return e.createTextHandler("|")}},newLinesAfter:function(){return b.single()},createAfterChild:function(g,h,f){if(h===f-1){if(g.tagName==="TH"){return e.createTextHandler("||")}else{return e.createTextHandler("|")}}return new a()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/table-data-handler.js' */
define("jira/editor/converter/handlers/table-data-handler",["jira/editor/converter/handlers/node-handler"],function(b){return b.extend({init:function a(c){this.node=c}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/paragraph-handler.js' */
define("jira/editor/converter/handlers/paragraph-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/new-lines/new-lines","jquery"],function(d,b,c,e){return d.extend({init:function a(f){this.node=f},newLinesBefore:function(){if(this.node.previousElementSibling===null&&e(this.node).parents().is("td, th, .panelContent, .panel")){return c.empty()}if(this.node.previousElementSibling!==null){return c.single()}return c.empty()},newLinesAfter:function(){if(e(this.node).is(":last-child")&&e(this.node).parents().is("td, th")){return c.empty()}return c.single(e(this.node).parents().is("td, th"))},before:function(){return new b()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/quote-handler.js' */
define("jira/editor/converter/handlers/quote-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler"],function(a,c){return a.extend({init:function b(d){this.node=d},before:function(){return c.createPreformattedTextHandler("{quote}")},after:function(){return c.createPreformattedTextHandler("{quote}")}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/panel-handler.js' */
define("jira/editor/converter/handlers/panel-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/block-handler"],function(b,c,a){return a.extend({init:function d(e){if(e.firstChild&&e.className===e.firstChild.className){e=e.firstChild}this.node=e;this._super(e)},before:function(){var e=this.attrs?":"+this.attrs:"";return c.createPreformattedTextHandler("{panel"+e+"}")},createChildHandler:function(){return b.prototype.createChildHandler.apply(this,arguments)},after:function(){return c.createPreformattedTextHandler("{panel}")}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/span-handler.js' */
define("jira/editor/converter/handlers/span-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/util/strings","jira/editor/converter/util/color-converter","jquery"],function(d,g,a,f,b,e){var h=d.extend({init:function h(j){this.node=j;this.$node=e(this.node);var i=this.node.getAttribute("href");if(this.getStyle(this.node,"textDecoration")==="underline"){this.decorator="underline"}else{if(this.node.className==="error"){var l=this.$node.text();if(f.getFilenameFromError(l)){this.decorator="attachment-error"}}else{if(f.length(this.getStyle(this.node,"color"))>0){var k=this.getStyle(this.node,"color").toLowerCase();if(f.contains(k,"rgb")){this.color=b.colorToHex(k)}else{if(f.contains(k,"#")){this.color=k}else{if(k in c){this.color=c[k.toLowerCase()]}}}if(this.color!==undefined){this.decorator="color"}}else{if(this.node.className==="jira-issue-macro"&&this.$node.attr("data-jira-key")){this.decorator="jira-issue-macro"}}}}},before:function(){if(this.decorator==="attachment-error"){return g.createTextHandler("!")}else{if(this.decorator==="underline"){return g.createTextHandler("+")}else{if(this.decorator==="color"){return g.createPreformattedTextHandler("{color:"+this.color+"}")}}}return new a()},after:function(){if(this.decorator==="attachment-error"){return g.createTextHandler("!")}else{if(this.decorator==="underline"){return g.createTextHandler("+")}else{if(this.decorator==="color"){return g.createPreformattedTextHandler("{color}")}}}return new a()},children:function(){if(this.decorator==="attachment-error"){var i=f.getFilenameFromError(this.$node.text());return[g.createTextHandler(i)]}else{if(this.decorator==="jira-issue-macro"){return g.createTextHandler(this.$node.attr("data-jira-key"))}else{return this._super()}}},getStyle:function(j,i){if(i in this.node.style){return this.node.style[i]}return undefined}});var c={white:"#FFFFFF",silver:"#C0C0C0",gray:"#808080",black:"#000000",red:"#FF0000",maroon:"#800000",yellow:"#FFFF00",olive:"#808000",lime:"#00FF00",green:"#008000",aqua:"#00FFFF",teal:"#008080",blue:"#0000FF",navy:"#000080",purple:"#800080",orange:"#FFA500",brown:"#AA4B00",mahogany:"#C04000",pink:"#FFC0CB","yellow orche":"#CF912A",harlequin:"#3FFF00",mauvelous:"#EF98AA","dark red":"#8B0000",saffron:"#F4C430",sepia:"#704214",fuchsia:"#FF00FF",magenta:"#FF00FF",marsala:"#955251",indigo:"#000080"};return h});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/strikethrough-handler.js' */
define("jira/editor/converter/handlers/strikethrough-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/link-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/factories/whitespace-extractor","jquery"],function(c,g,e,a,h,f){var d=c.extend({init:function b(i){this.node=i;if(f(i.parentNode).is("a[data-issue-key]")){this.isIssue=e.isIssue(f(i.parentNode).attr("href"))}},before:function(){return this.isIssue?new a():g.createTextHandler("-")},after:function(){return this.isIssue?new a():g.createTextHandler("-")}});return h(d)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/superscript-handler.js' */
define("jira/editor/converter/handlers/superscript-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/util/strings","jira/editor/converter/factories/whitespace-extractor","jquery"],function(b,g,a,f,h,e){var c=b.extend({init:function d(i){this.node=i;if(e(this.node).parents("a").is(".external-link")&&f.endsWith(e(this.node).children().attr("src"),"images/icons/mail_small.gif")){this.dontExpand=true}},before:function(){return this.dontExpand?new a():g.createTextHandler("^")},after:function(){return this.dontExpand?new a():g.createTextHandler("^")}});return h(c)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/subscript-handler.js' */
define("jira/editor/converter/handlers/subscript-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/factories/whitespace-extractor"],function(b,d,e){var c=b.extend({init:function a(f){this.node=f},before:function(){return d.createTextHandler("~")},after:function(){return d.createTextHandler("~")}});return e(c)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/underline-handler.js' */
define("jira/editor/converter/handlers/underline-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/factories/whitespace-extractor"],function(b,d,e){var c=b.extend({init:function a(f){this.node=f},before:function(){return d.createTextHandler("+")},after:function(){return d.createTextHandler("+")}});return e(c)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/hr-handler.js' */
define("jira/editor/converter/handlers/hr-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/empty-handler"],function(b,c,a){return b.extend({init:function d(e){this.node=e},before:function(){return new a()},children:function(){var e=new c({textContent:"----",preformatted:true});return[e]},after:function(){return new a()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/cite-handler.js' */
define("jira/editor/converter/handlers/cite-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler"],function(a,c){return a.extend({init:function b(d){this.node=d},before:function(){return c.createTextHandler("??")},after:function(){return c.createTextHandler("??")}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handlers/external-handler.js' */
define("jira/editor/converter/handlers/external-handler",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/new-lines/new-lines","jira/editor/converter","underscore"],function(d,f,c,e,b){var a=d.extend({init:function(g){this.node=g},children:function(){var g=this.external(this.getParams());return[f.createPreformattedTextHandler(g)]},getParams:function(){return Object.create(Object.prototype,{node:{value:this.node},innerMarkup:{get:function(){return e.handleMarkup(this.node.innerHTML)}}})}});a.forConverter=function(g){var h=AJS.namespace(g["function"]);if(!b.isFunction(h)){console.error(g["function"]," is not a function, this macro won't be supported by converter: ",g.selector," - it may result in data loss.");return}d.addHandler(g.selector,a.extend({external:h}))};a.Legacy=f.extend({init:function(g){this.node=g},processText:function(){var h=this.node.getAttribute("data-original-text");var g=this.node.getAttribute("data-command");return(h||"")+(this.node.innerText||this.node.textContent)+(g&&("{"+g+"}"))},newLinesBefore:function(){return this.node.getAttribute("data-new-line-before")==="true"?c.single(true):c.empty()},newLinesAfter:function(){return this.node.getAttribute("data-new-line-after")==="true"?c.single(true):c.empty()}});return a});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/handler-mapper.js' */
define("jira/editor/converter/handler-mapper",["jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/text-handler","jira/editor/converter/handlers/heading-handler","jira/editor/converter/handlers/bold-handler","jira/editor/converter/handlers/emphasis-handler","jira/editor/converter/handlers/monospaced-handler","jira/editor/converter/handlers/linebreak-handler","jira/editor/converter/handlers/link-handler","jira/editor/converter/handlers/image-handler","jira/editor/converter/handlers/code-block-handler","jira/editor/converter/handlers/noformat-handler","jira/editor/converter/handlers/list-handler","jira/editor/converter/handlers/list-item-handler","jira/editor/converter/handlers/paragraph-handler","jira/editor/converter/handlers/strikethrough-handler","jira/editor/converter/handlers/superscript-handler","jira/editor/converter/handlers/subscript-handler","jira/editor/converter/handlers/underline-handler","jira/editor/converter/handlers/quote-handler","jira/editor/converter/handlers/panel-handler","jira/editor/converter/handlers/span-handler","jira/editor/converter/handlers/table-handler","jira/editor/converter/handlers/table-row-handler","jira/editor/converter/handlers/table-data-handler","jira/editor/converter/handlers/hr-handler","jira/editor/converter/handlers/cite-handler","jira/editor/wrm"],function(n,s,a,b,x,m,t,w,p,l,c,i,o,e,k,g,r,f,u,j,h,y,B,q,A,d,v){return{registerHandlers:function z(){var C=require("jira/editor/converter/handlers/external-handler");v.getConverters().forEach(C.forConverter);n.addHandler("pre.external-macro",C.Legacy);n.addHandler("H1",a);n.addHandler("H2",a);n.addHandler("H3",a);n.addHandler("H4",a);n.addHandler("H5",a);n.addHandler("H6",a);n.addHandler("A",w);n.addHandler("B",b);n.addHandler("STRONG",b);n.addHandler("BR",t);n.addHandler("EM",x);n.addHandler("I",x);n.addHandler("IMG",p);n.addHandler("TT",m);n.addHandler("CODE",m);n.addHandler("SAMP",m);n.addHandler("DIV.preformatted panel",c);n.addHandler("PRE.noformat panel",c);n.addHandler("DIV.code panel",l);n.addHandler("PRE.code panel",l);n.addHandler("PANEL-TITLE",n.EmptyHandler);n.addHandler("UL",i);n.addHandler("OL",i);n.addHandler("LI",o);n.addHandler("P",e);n.addHandler("DEL",k);n.addHandler("SUB",r);n.addHandler("SUP",g);n.addHandler("INS",f);n.addHandler("U",f);n.addHandler("TABLE",y);n.addHandler("TR",B);n.addHandler("CAPTION",B);n.addHandler("TD",q);n.addHandler("TH",q);n.addHandler("SPAN",h);n.addHandler("BLOCKQUOTE",u);n.addHandler("DIV.panel",j);n.addHandler("PRE.panel",j);n.addHandler("HR",A);n.addHandler("CITE",d);n.addHandler("#text",s)}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/factories/whitespace-extractor.js' */
define("jira/editor/converter/factories/whitespace-extractor",["jira/editor/constants","jira/editor/converter/handlers/text-handler","jira/editor/converter/factories/left-trim","jira/editor/converter/factories/right-trim"],function(c,f,b,e){return function a(n){return n.extend({init:function h(o){this._super(o);this.leftWhitespaces=g(o.firstChild,c.LEFT_WHITESPACE_MATCHER);this.rightWhitespaces=g(o.lastChild,c.RIGHT_WHITESPACE_MATCHER);this.leftTrimChildren=!!this.leftWhitespaces;this.rightTrimChildren=!!this.rightWhitespaces},before:function l(){var o=this._super();return this._extractLeftSpaces(o)||o},after:function m(){var o=this._super();return this._extractRightSpaces(o)||o},createChildHandler:function j(p,r,o){var q=this._super(p,r,o);if(this.leftTrimChildren&&0===r){q=b(q)}if(this.rightTrimChildren&&o-1===r){q=e(q)}return q},_extractLeftSpaces:function k(o){if(!this.leftTrimChildren){return null}var p=d(o);return p(this.leftWhitespaces+o.processText())},_extractRightSpaces:function i(o){if(!this.rightTrimChildren){return null}var p=d(o);return p(o.processText()+this.rightWhitespaces)}})};function g(k,j){var i=(k&&k.textContent)||"";var h=i.match(j);return(null!==h)?h[0]:null}function d(h){return(h.preformatted)?f.createPreformattedTextHandler:f.createTextHandler}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/factories/left-trim.js' */
define("jira/editor/converter/factories/left-trim",["jira/editor/constants"],function(b){return function a(f){var d=Object.create(f);d.createChildHandler=function e(h,j,g){var i=Object.getPrototypeOf(d).createChildHandler.call(this,h,j,g);if(0===j){i=a(i)}return i};d.processText=function c(){return Object.getPrototypeOf(d).processText.call(this).replace(b.LEFT_WHITESPACE_MATCHER,"")};d._extractLeftSpaces=function(){};return d}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/factories/right-trim.js' */
define("jira/editor/converter/factories/right-trim",["jira/editor/constants"],function(b){return function a(f){var e=Object.create(f);e.createChildHandler=function d(h,j,g){var i=Object.getPrototypeOf(e).createChildHandler.call(this,h,j,g);if(g-1===j){i=a(i)}return i};e.processText=function c(){return Object.getPrototypeOf(e).processText.call(this).replace(b.RIGHT_WHITESPACE_MATCHER,"")};e._extractRightSpaces=function(){};return e}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/new-lines/new-lines.js' */
define("jira/editor/converter/new-lines/new-lines",["jira/lib/class"],function(a){var b=a.extend({init:function(c,d){this._lines=c;this._optional=d},newLines:function(){return this._lines},optional:function(){return this._optional}});b.empty=function(){return new b(0,true)};b.single=function(c){return new b(1,!!c)};return b});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/new-lines/new-lines-merger.js' */
define("jira/editor/converter/new-lines/new-lines-merger",["underscore","jira/editor/converter/util/strings","jira/editor/converter/new-lines/new-lines"],function(c,d,b){function a(f){var h=c.max(f.filter(function(i){return i.optional()}).map(function(i){return i.newLines()}));var g=f.filter(function(i){return !i.optional()}).reduce(function(i,j){return i+j.newLines()},0);return Math.max(g,h)}function e(f){f=c.filter(f,function(i){return i instanceof b||i!==""});var j=[];var k=[];for(var g=0;g<f.length;g++){var h=f[g];if(h instanceof b){j.push(h);continue}k.push(d.repeat("\n",a(j)));k.push(h);j=[]}return k}return{mergeNewLines:e,_newLinesNumber:a}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:converter', location = '/js/converter/html-converter.js' */
define("jira/editor/converter",["jquery","jira/editor/converter/util/rte-strings","jira/editor/converter/handlers/node-handler","jira/editor/converter/handlers/empty-handler","jira/editor/converter/new-lines/new-lines","jira/editor/converter/new-lines/new-lines-merger","jira/editor/converter/handler-mapper","jira/editor/analytics","exports"],function(d,b,l,e,a,h,j,k,c){j.registerHandlers();var i=function(r){if(r==null){return""}var q=[l.createHandler(r)];var o=[];while(q.length!==0){var p=q.pop();if(p instanceof e){continue}if(p instanceof a){o.push(p);continue}q.push(p.newLinesBefore());q.push(p.before());var n=p.children();q=q.concat(n);q.push(p.after());o.push(p.processText());o.push(p.newLinesAfter())}o=h.mergeNewLines(o);var m=b.trim(o.reverse().join(""));return m};var g=function(m){return i(b.parseHtml(m))};var f=function(o){var n=new d.Deferred();k.mark("rte-convert");try{var r=b.parseHtml(o);var p=(r?r.getElementsByTagName("*").length:0);var m=i(r);n.resolve(m);k.oneShotMeasure("rte-convert",{nodesCount:p})}catch(q){n.reject(q);k.oneShotMeasure("rte-convert",{nodesCount:-1})}return n.promise()};c.handleMarkup=g;c.convert=f});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:plugins', location = 'js/plugins/markup-plugin.js' */
require(["jira/editor/customizer"],function(a){a.customizeSettings(function(b){if(b.plugins.indexOf("textpattern")===-1){b.plugins.push("textpattern")}b.textpattern_patterns=b.textpattern_patterns||[];Array.prototype.push.apply(b.textpattern_patterns,[{start:"1. ",cmd:"InsertOrderedList"},{start:"# ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}])})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-editor-plugin:plugins', location = 'js/plugins/toolbar-sync-plugin.js' */
define("jira/editor/plugins/toolbar-sync",["jira/editor/tinymce",],function(b){var a=["bold","italic","underline"];b.create("tinymce.plugins.jira.ToolbarSyncPlugin",{init:function(c){var d=c.settings;c.on("NodeChange",function(){if(c.isHidden()){return}a.forEach(function(f){var g=c.queryCommandState(f);var h=d.toolbarContainer.find("[data-operation="+f+"]");var e=!!h.attr("aria-pressed");if(e!==g){h.attr("aria-pressed",g?"true":null)}})});c.on("hide",function(){a.forEach(function(e){d.toolbarContainer.find("[data-operation="+e+"]").attr("aria-pressed",null)})});c.contextManager.on("change:all",function(f){d.toolbarContainer.find("[aria-disabled], [data-operation], [aria-controls]").attr("aria-disabled",f.disableState)});c.contextManager.on("change:a",function(f){d.toolbarContainer.find("[data-operation^=link]").attr("aria-disabled",f.disableState)});c.contextManager.on("change:table",function(f){d.toolbarContainer.find("[data-operation=table]").attr("aria-disabled",f.disableState)})}});b.PluginManager.add("jira.toolbarsync",b.plugins.jira.ToolbarSyncPlugin);b.PluginManager.urls["jira.toolbarsync"]=true;return{configure:function(c,d){d.plugins.push("jira.toolbarsync");d.toolbarContainer=c.element.getToolbarContainer()}}});
}catch(e){WRMCB(e)};