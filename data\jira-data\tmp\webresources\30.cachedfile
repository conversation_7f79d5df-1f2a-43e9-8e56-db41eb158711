WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'jira.webresources:aui-extensions-experimental', location = '/includes/ajs/aui-experimental.js' */
AJS.namespace("AJS.asBooleanOrString",null,require("jira/util/strings").asBooleanOrString),AJS.namespace("AJS.overrides",null,require("jira/util/data/meta/store")),AJS.namespace("AJS.Meta",null,require("jira/util/data/meta"));
}catch(e){WRMCB(e)};