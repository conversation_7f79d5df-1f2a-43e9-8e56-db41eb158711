[2025-09-10T14:17:26.309+0800][0.019s][info][gc,heap] Heap region size: 1M
[2025-09-10T14:17:26.320+0800][0.030s][info][gc     ] Using G1
[2025-09-10T14:17:26.322+0800][0.032s][info][gc,heap,coops] Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
[2025-09-10T14:17:26.322+0800][0.033s][info][gc,cds       ] Mark closed archive regions in map: [0x00000000ffe00000, 0x00000000ffe6bff8]
[2025-09-10T14:17:26.324+0800][0.034s][info][gc,cds       ] Mark open archive regions in map: [0x00000000ffc00000, 0x00000000ffc46ff8]
[2025-09-10T14:17:27.257+0800][0.969s][info][gc,start     ] GC(0) Pa<PERSON> Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:27.262+0800][0.974s][info][gc,task      ] GC(0) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:27.272+0800][0.983s][info][gc,phases    ] GC(0)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:27.276+0800][0.987s][info][gc,phases    ] GC(0)   Evacuate Collection Set: 4.2ms
[2025-09-10T14:17:27.276+0800][0.988s][info][gc,phases    ] GC(0)   Post Evacuate Collection Set: 3.6ms
[2025-09-10T14:17:27.277+0800][0.989s][info][gc,phases    ] GC(0)   Other: 6.8ms
[2025-09-10T14:17:27.278+0800][0.989s][info][gc,heap      ] GC(0) Eden regions: 51->0(46)
[2025-09-10T14:17:27.278+0800][0.990s][info][gc,heap      ] GC(0) Survivor regions: 0->7(7)
[2025-09-10T14:17:27.278+0800][0.990s][info][gc,heap      ] GC(0) Old regions: 2->2
[2025-09-10T14:17:27.280+0800][0.991s][info][gc,heap      ] GC(0) Humongous regions: 1->0
[2025-09-10T14:17:27.282+0800][0.993s][info][gc,metaspace ] GC(0) Metaspace: 10800K(11648K)->10800K(11648K) NonClass: 9695K(10240K)->9695K(10240K) Class: 1105K(1408K)->1105K(1408K)
[2025-09-10T14:17:27.282+0800][0.994s][info][gc           ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 52M->7M(1026M) 25.375ms
[2025-09-10T14:17:27.283+0800][0.994s][info][gc,cpu       ] GC(0) User=0.12s Sys=0.00s Real=0.03s
[2025-09-10T14:17:27.500+0800][1.212s][info][gc,start     ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:27.500+0800][1.212s][info][gc,task      ] GC(1) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:27.508+0800][1.220s][info][gc,phases    ] GC(1)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:17:27.509+0800][1.220s][info][gc,phases    ] GC(1)   Evacuate Collection Set: 4.0ms
[2025-09-10T14:17:27.509+0800][1.220s][info][gc,phases    ] GC(1)   Post Evacuate Collection Set: 3.4ms
[2025-09-10T14:17:27.509+0800][1.221s][info][gc,phases    ] GC(1)   Other: 0.7ms
[2025-09-10T14:17:27.509+0800][1.221s][info][gc,heap      ] GC(1) Eden regions: 46->0(59)
[2025-09-10T14:17:27.510+0800][1.221s][info][gc,heap      ] GC(1) Survivor regions: 7->7(7)
[2025-09-10T14:17:27.510+0800][1.221s][info][gc,heap      ] GC(1) Old regions: 2->13
[2025-09-10T14:17:27.510+0800][1.222s][info][gc,heap      ] GC(1) Humongous regions: 1->1
[2025-09-10T14:17:27.510+0800][1.222s][info][gc,metaspace ] GC(1) Metaspace: 11909K(12800K)->11909K(12800K) NonClass: 10679K(11264K)->10679K(11264K) Class: 1230K(1536K)->1230K(1536K)
[2025-09-10T14:17:27.510+0800][1.222s][info][gc           ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 54M->18M(1026M) 10.211ms
[2025-09-10T14:17:27.511+0800][1.222s][info][gc,cpu       ] GC(1) User=0.08s Sys=0.03s Real=0.01s
[2025-09-10T14:17:27.903+0800][1.615s][info][gc,start     ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:27.904+0800][1.615s][info][gc,task      ] GC(2) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:27.910+0800][1.621s][info][gc,phases    ] GC(2)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:27.911+0800][1.622s][info][gc,phases    ] GC(2)   Evacuate Collection Set: 2.1ms
[2025-09-10T14:17:27.911+0800][1.623s][info][gc,phases    ] GC(2)   Post Evacuate Collection Set: 3.2ms
[2025-09-10T14:17:27.911+0800][1.623s][info][gc,phases    ] GC(2)   Other: 1.0ms
[2025-09-10T14:17:27.912+0800][1.623s][info][gc,heap      ] GC(2) Eden regions: 59->0(80)
[2025-09-10T14:17:27.912+0800][1.623s][info][gc,heap      ] GC(2) Survivor regions: 7->3(9)
[2025-09-10T14:17:27.912+0800][1.624s][info][gc,heap      ] GC(2) Old regions: 13->20
[2025-09-10T14:17:27.912+0800][1.624s][info][gc,heap      ] GC(2) Humongous regions: 1->1
[2025-09-10T14:17:27.912+0800][1.624s][info][gc,metaspace ] GC(2) Metaspace: 12997K(13952K)->12997K(13952K) NonClass: 11674K(12288K)->11674K(12288K) Class: 1322K(1664K)->1322K(1664K)
[2025-09-10T14:17:27.913+0800][1.624s][info][gc           ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 77M->21M(1026M) 9.474ms
[2025-09-10T14:17:27.913+0800][1.625s][info][gc,cpu       ] GC(2) User=0.05s Sys=0.02s Real=0.00s
[2025-09-10T14:17:28.241+0800][1.953s][info][gc,start     ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:28.242+0800][1.954s][info][gc,task      ] GC(3) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Evacuate Collection Set: 1.0ms
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Other: 2.7ms
[2025-09-10T14:17:28.248+0800][1.959s][info][gc,heap      ] GC(3) Eden regions: 80->0(112)
[2025-09-10T14:17:28.248+0800][1.959s][info][gc,heap      ] GC(3) Survivor regions: 3->4(11)
[2025-09-10T14:17:28.248+0800][1.959s][info][gc,heap      ] GC(3) Old regions: 20->20
[2025-09-10T14:17:28.248+0800][1.960s][info][gc,heap      ] GC(3) Humongous regions: 1->1
[2025-09-10T14:17:28.248+0800][1.960s][info][gc,metaspace ] GC(3) Metaspace: 13063K(13952K)->13063K(13952K) NonClass: 11735K(12288K)->11735K(12288K) Class: 1327K(1664K)->1327K(1664K)
[2025-09-10T14:17:28.248+0800][1.960s][info][gc           ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 101M->22M(1026M) 6.939ms
[2025-09-10T14:17:28.248+0800][1.960s][info][gc,cpu       ] GC(3) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T14:17:28.747+0800][2.459s][info][gc,start     ] GC(4) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:28.747+0800][2.459s][info][gc,task      ] GC(4) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:28.749+0800][2.461s][info][gc,phases    ] GC(4)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:28.749+0800][2.461s][info][gc,phases    ] GC(4)   Evacuate Collection Set: 1.0ms
[2025-09-10T14:17:28.750+0800][2.461s][info][gc,phases    ] GC(4)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T14:17:28.750+0800][2.461s][info][gc,phases    ] GC(4)   Other: 0.5ms
[2025-09-10T14:17:28.750+0800][2.461s][info][gc,heap      ] GC(4) Eden regions: 112->0(169)
[2025-09-10T14:17:28.750+0800][2.462s][info][gc,heap      ] GC(4) Survivor regions: 4->5(15)
[2025-09-10T14:17:28.750+0800][2.462s][info][gc,heap      ] GC(4) Old regions: 20->20
[2025-09-10T14:17:28.750+0800][2.462s][info][gc,heap      ] GC(4) Humongous regions: 1->1
[2025-09-10T14:17:28.751+0800][2.462s][info][gc,metaspace ] GC(4) Metaspace: 13068K(13952K)->13068K(13952K) NonClass: 11740K(12288K)->11740K(12288K) Class: 1327K(1664K)->1327K(1664K)
[2025-09-10T14:17:28.751+0800][2.462s][info][gc           ] GC(4) Pause Young (Normal) (G1 Evacuation Pause) 134M->23M(1026M) 3.755ms
[2025-09-10T14:17:28.751+0800][2.462s][info][gc,cpu       ] GC(4) User=0.02s Sys=0.00s Real=0.00s
[2025-09-10T14:17:29.347+0800][3.059s][info][gc,start     ] GC(5) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:29.348+0800][3.060s][info][gc,task      ] GC(5) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:29.353+0800][3.065s][info][gc,phases    ] GC(5)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:29.353+0800][3.065s][info][gc,phases    ] GC(5)   Evacuate Collection Set: 1.5ms
[2025-09-10T14:17:29.354+0800][3.065s][info][gc,phases    ] GC(5)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T14:17:29.354+0800][3.066s][info][gc,phases    ] GC(5)   Other: 2.9ms
[2025-09-10T14:17:29.354+0800][3.066s][info][gc,heap      ] GC(5) Eden regions: 169->0(243)
[2025-09-10T14:17:29.355+0800][3.066s][info][gc,heap      ] GC(5) Survivor regions: 5->7(22)
[2025-09-10T14:17:29.355+0800][3.067s][info][gc,heap      ] GC(5) Old regions: 20->20
[2025-09-10T14:17:29.356+0800][3.067s][info][gc,heap      ] GC(5) Humongous regions: 1->1
[2025-09-10T14:17:29.356+0800][3.068s][info][gc,metaspace ] GC(5) Metaspace: 13069K(13952K)->13069K(13952K) NonClass: 11741K(12288K)->11741K(12288K) Class: 1327K(1664K)->1327K(1664K)
[2025-09-10T14:17:29.357+0800][3.068s][info][gc           ] GC(5) Pause Young (Normal) (G1 Evacuation Pause) 192M->25M(1026M) 9.236ms
[2025-09-10T14:17:29.357+0800][3.069s][info][gc,cpu       ] GC(5) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T14:17:30.793+0800][4.505s][info][gc,start     ] GC(6) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:30.796+0800][4.508s][info][gc,task      ] GC(6) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:30.799+0800][4.510s][info][gc,phases    ] GC(6)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:30.799+0800][4.511s][info][gc,phases    ] GC(6)   Evacuate Collection Set: 1.6ms
[2025-09-10T14:17:30.801+0800][4.513s][info][gc,phases    ] GC(6)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T14:17:30.803+0800][4.515s][info][gc,phases    ] GC(6)   Other: 3.5ms
[2025-09-10T14:17:30.805+0800][4.516s][info][gc,heap      ] GC(6) Eden regions: 243->0(606)
[2025-09-10T14:17:30.806+0800][4.518s][info][gc,heap      ] GC(6) Survivor regions: 7->8(32)
[2025-09-10T14:17:30.807+0800][4.518s][info][gc,heap      ] GC(6) Old regions: 20->20
[2025-09-10T14:17:30.808+0800][4.519s][info][gc,heap      ] GC(6) Humongous regions: 1->1
[2025-09-10T14:17:30.808+0800][4.519s][info][gc,metaspace ] GC(6) Metaspace: 13094K(13952K)->13094K(13952K) NonClass: 11767K(12288K)->11767K(12288K) Class: 1327K(1664K)->1327K(1664K)
[2025-09-10T14:17:30.808+0800][4.520s][info][gc           ] GC(6) Pause Young (Normal) (G1 Evacuation Pause) 268M->26M(1026M) 15.039ms
[2025-09-10T14:17:30.808+0800][4.520s][info][gc,cpu       ] GC(6) User=0.02s Sys=0.01s Real=0.02s
[2025-09-10T14:17:31.520+0800][5.232s][info][gc,start     ] GC(7) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T14:17:31.520+0800][5.232s][info][gc,task      ] GC(7) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:31.528+0800][5.239s][info][gc,phases    ] GC(7)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:17:31.528+0800][5.240s][info][gc,phases    ] GC(7)   Evacuate Collection Set: 5.7ms
[2025-09-10T14:17:31.528+0800][5.240s][info][gc,phases    ] GC(7)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T14:17:31.529+0800][5.240s][info][gc,phases    ] GC(7)   Other: 0.9ms
[2025-09-10T14:17:31.529+0800][5.240s][info][gc,heap      ] GC(7) Eden regions: 300->0(605)
[2025-09-10T14:17:31.529+0800][5.241s][info][gc,heap      ] GC(7) Survivor regions: 8->9(77)
[2025-09-10T14:17:31.529+0800][5.241s][info][gc,heap      ] GC(7) Old regions: 20->20
[2025-09-10T14:17:31.529+0800][5.241s][info][gc,heap      ] GC(7) Humongous regions: 1->1
[2025-09-10T14:17:31.529+0800][5.241s][info][gc,metaspace ] GC(7) Metaspace: 20401K(21248K)->20401K(21248K) NonClass: 18134K(18688K)->18134K(18688K) Class: 2267K(2560K)->2267K(2560K)
[2025-09-10T14:17:31.530+0800][5.241s][info][gc           ] GC(7) Pause Young (Concurrent Start) (Metadata GC Threshold) 325M->27M(1026M) 9.678ms
[2025-09-10T14:17:31.530+0800][5.241s][info][gc,cpu       ] GC(7) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T14:17:31.530+0800][5.242s][info][gc           ] GC(8) Concurrent Cycle
[2025-09-10T14:17:31.530+0800][5.242s][info][gc,marking   ] GC(8) Concurrent Clear Claimed Marks
[2025-09-10T14:17:31.530+0800][5.242s][info][gc,marking   ] GC(8) Concurrent Clear Claimed Marks 0.163ms
[2025-09-10T14:17:31.531+0800][5.242s][info][gc,marking   ] GC(8) Concurrent Scan Root Regions
[2025-09-10T14:17:31.532+0800][5.244s][info][gc,marking   ] GC(8) Concurrent Scan Root Regions 1.585ms
[2025-09-10T14:17:31.533+0800][5.244s][info][gc,marking   ] GC(8) Concurrent Mark (5.244s)
[2025-09-10T14:17:31.533+0800][5.245s][info][gc,marking   ] GC(8) Concurrent Mark From Roots
[2025-09-10T14:17:31.533+0800][5.245s][info][gc,task      ] GC(8) Using 4 workers of 4 for marking
[2025-09-10T14:17:31.536+0800][5.248s][info][gc,marking   ] GC(8) Concurrent Mark From Roots 2.953ms
[2025-09-10T14:17:31.536+0800][5.248s][info][gc,marking   ] GC(8) Concurrent Preclean
[2025-09-10T14:17:31.536+0800][5.248s][info][gc,marking   ] GC(8) Concurrent Preclean 0.252ms
[2025-09-10T14:17:31.537+0800][5.248s][info][gc,marking   ] GC(8) Concurrent Mark (5.244s, 5.248s) 3.886ms
[2025-09-10T14:17:31.537+0800][5.249s][info][gc,start     ] GC(8) Pause Remark
[2025-09-10T14:17:31.543+0800][5.254s][info][gc,stringtable] GC(8) Cleaned string and symbol table, strings: 17693 processed, 38 removed, symbols: 58053 processed, 117 removed
[2025-09-10T14:17:31.543+0800][5.255s][info][gc            ] GC(8) Pause Remark 30M->30M(1026M) 5.964ms
[2025-09-10T14:17:31.543+0800][5.255s][info][gc,cpu        ] GC(8) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T14:17:31.543+0800][5.255s][info][gc,marking    ] GC(8) Concurrent Rebuild Remembered Sets
[2025-09-10T14:17:31.545+0800][5.257s][info][gc,marking    ] GC(8) Concurrent Rebuild Remembered Sets 1.759ms
[2025-09-10T14:17:31.546+0800][5.257s][info][gc,start      ] GC(8) Pause Cleanup
[2025-09-10T14:17:31.546+0800][5.258s][info][gc            ] GC(8) Pause Cleanup 30M->30M(1026M) 0.427ms
[2025-09-10T14:17:31.546+0800][5.258s][info][gc,cpu        ] GC(8) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T14:17:31.547+0800][5.258s][info][gc,marking    ] GC(8) Concurrent Cleanup for Next Mark
[2025-09-10T14:17:31.550+0800][5.262s][info][gc,marking    ] GC(8) Concurrent Cleanup for Next Mark 3.588ms
[2025-09-10T14:17:31.550+0800][5.262s][info][gc            ] GC(8) Concurrent Cycle 20.382ms
[2025-09-10T14:17:35.670+0800][9.382s][info][gc,start      ] GC(9) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T14:17:35.672+0800][9.384s][info][gc,task       ] GC(9) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:35.689+0800][9.400s][info][gc,phases     ] GC(9)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:17:35.689+0800][9.401s][info][gc,phases     ] GC(9)   Evacuate Collection Set: 14.5ms
[2025-09-10T14:17:35.689+0800][9.401s][info][gc,phases     ] GC(9)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T14:17:35.689+0800][9.401s][info][gc,phases     ] GC(9)   Other: 2.5ms
[2025-09-10T14:17:35.689+0800][9.401s][info][gc,heap       ] GC(9) Eden regions: 475->0(590)
[2025-09-10T14:17:35.690+0800][9.401s][info][gc,heap       ] GC(9) Survivor regions: 9->24(77)
[2025-09-10T14:17:35.690+0800][9.401s][info][gc,heap       ] GC(9) Old regions: 20->20
[2025-09-10T14:17:35.690+0800][9.401s][info][gc,heap       ] GC(9) Humongous regions: 3->1
[2025-09-10T14:17:35.690+0800][9.402s][info][gc,metaspace  ] GC(9) Metaspace: 34831K(36048K)->34831K(36048K) NonClass: 30640K(31444K)->30640K(31444K) Class: 4190K(4604K)->4190K(4604K)
[2025-09-10T14:17:35.690+0800][9.402s][info][gc            ] GC(9) Pause Young (Concurrent Start) (Metadata GC Threshold) 504M->42M(1026M) 20.362ms
[2025-09-10T14:17:35.691+0800][9.402s][info][gc,cpu        ] GC(9) User=0.09s Sys=0.03s Real=0.02s
[2025-09-10T14:17:35.691+0800][9.403s][info][gc            ] GC(10) Concurrent Cycle
[2025-09-10T14:17:35.691+0800][9.403s][info][gc,marking    ] GC(10) Concurrent Clear Claimed Marks
[2025-09-10T14:17:35.692+0800][9.403s][info][gc,marking    ] GC(10) Concurrent Clear Claimed Marks 0.319ms
[2025-09-10T14:17:35.692+0800][9.403s][info][gc,marking    ] GC(10) Concurrent Scan Root Regions
[2025-09-10T14:17:35.693+0800][9.405s][info][gc,marking    ] GC(10) Concurrent Scan Root Regions 1.616ms
[2025-09-10T14:17:35.694+0800][9.405s][info][gc,marking    ] GC(10) Concurrent Mark (9.405s)
[2025-09-10T14:17:35.694+0800][9.406s][info][gc,marking    ] GC(10) Concurrent Mark From Roots
[2025-09-10T14:17:35.695+0800][9.407s][info][gc,task       ] GC(10) Using 4 workers of 4 for marking
[2025-09-10T14:17:35.699+0800][9.410s][info][gc,marking    ] GC(10) Concurrent Mark From Roots 4.666ms
[2025-09-10T14:17:35.699+0800][9.411s][info][gc,marking    ] GC(10) Concurrent Preclean
[2025-09-10T14:17:35.699+0800][9.411s][info][gc,marking    ] GC(10) Concurrent Preclean 0.270ms
[2025-09-10T14:17:35.699+0800][9.411s][info][gc,marking    ] GC(10) Concurrent Mark (9.405s, 9.411s) 5.754ms
[2025-09-10T14:17:35.700+0800][9.411s][info][gc,start      ] GC(10) Pause Remark
[2025-09-10T14:17:35.702+0800][9.413s][info][gc,stringtable] GC(10) Cleaned string and symbol table, strings: 24119 processed, 0 removed, symbols: 101717 processed, 84 removed
[2025-09-10T14:17:35.702+0800][9.414s][info][gc            ] GC(10) Pause Remark 44M->44M(1026M) 2.654ms
[2025-09-10T14:17:35.703+0800][9.414s][info][gc,cpu        ] GC(10) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T14:17:35.703+0800][9.414s][info][gc,marking    ] GC(10) Concurrent Rebuild Remembered Sets
[2025-09-10T14:17:35.705+0800][9.417s][info][gc,marking    ] GC(10) Concurrent Rebuild Remembered Sets 2.374ms
[2025-09-10T14:17:35.706+0800][9.418s][info][gc,start      ] GC(10) Pause Cleanup
[2025-09-10T14:17:35.707+0800][9.418s][info][gc            ] GC(10) Pause Cleanup 44M->44M(1026M) 0.444ms
[2025-09-10T14:17:35.707+0800][9.418s][info][gc,cpu        ] GC(10) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T14:17:35.707+0800][9.418s][info][gc,marking    ] GC(10) Concurrent Cleanup for Next Mark
[2025-09-10T14:17:35.710+0800][9.422s][info][gc,marking    ] GC(10) Concurrent Cleanup for Next Mark 3.597ms
[2025-09-10T14:17:35.711+0800][9.423s][info][gc            ] GC(10) Concurrent Cycle 19.896ms
[2025-09-10T14:17:49.193+0800][22.904s][info][gc,start      ] GC(11) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:49.193+0800][22.905s][info][gc,task       ] GC(11) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:49.206+0800][22.918s][info][gc,phases     ] GC(11)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:17:49.207+0800][22.918s][info][gc,phases     ] GC(11)   Evacuate Collection Set: 11.0ms
[2025-09-10T14:17:49.207+0800][22.919s][info][gc,phases     ] GC(11)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T14:17:49.207+0800][22.919s][info][gc,phases     ] GC(11)   Other: 0.8ms
[2025-09-10T14:17:49.207+0800][22.919s][info][gc,heap       ] GC(11) Eden regions: 590->0(568)
[2025-09-10T14:17:49.207+0800][22.919s][info][gc,heap       ] GC(11) Survivor regions: 24->46(77)
[2025-09-10T14:17:49.208+0800][22.919s][info][gc,heap       ] GC(11) Old regions: 20->20
[2025-09-10T14:17:49.208+0800][22.919s][info][gc,heap       ] GC(11) Humongous regions: 2->1
[2025-09-10T14:17:49.208+0800][22.919s][info][gc,metaspace  ] GC(11) Metaspace: 52858K(55120K)->52858K(55120K) NonClass: 46628K(48084K)->46628K(48084K) Class: 6229K(7036K)->6229K(7036K)
[2025-09-10T14:17:49.208+0800][22.920s][info][gc            ] GC(11) Pause Young (Normal) (G1 Evacuation Pause) 633M->64M(1026M) 15.234ms
[2025-09-10T14:17:49.208+0800][22.920s][info][gc,cpu        ] GC(11) User=0.14s Sys=0.03s Real=0.02s
[2025-09-10T14:17:50.235+0800][23.947s][info][gc,start      ] GC(12) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T14:17:50.237+0800][23.948s][info][gc,task       ] GC(12) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:50.255+0800][23.967s][info][gc,phases     ] GC(12)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:17:50.256+0800][23.967s][info][gc,phases     ] GC(12)   Evacuate Collection Set: 16.2ms
[2025-09-10T14:17:50.256+0800][23.968s][info][gc,phases     ] GC(12)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T14:17:50.256+0800][23.968s][info][gc,phases     ] GC(12)   Other: 2.9ms
[2025-09-10T14:17:50.256+0800][23.968s][info][gc,heap       ] GC(12) Eden regions: 96->0(571)
[2025-09-10T14:17:50.256+0800][23.968s][info][gc,heap       ] GC(12) Survivor regions: 46->43(77)
[2025-09-10T14:17:50.256+0800][23.968s][info][gc,heap       ] GC(12) Old regions: 20->28
[2025-09-10T14:17:50.257+0800][23.968s][info][gc,heap       ] GC(12) Humongous regions: 1->1
[2025-09-10T14:17:50.257+0800][23.968s][info][gc,metaspace  ] GC(12) Metaspace: 58076K(60240K)->58076K(60240K) NonClass: 51126K(52436K)->51126K(52436K) Class: 6949K(7804K)->6949K(7804K)
[2025-09-10T14:17:50.257+0800][23.968s][info][gc            ] GC(12) Pause Young (Concurrent Start) (Metadata GC Threshold) 159M->69M(1026M) 21.820ms
[2025-09-10T14:17:50.257+0800][23.969s][info][gc,cpu        ] GC(12) User=0.08s Sys=0.02s Real=0.02s
[2025-09-10T14:17:50.257+0800][23.969s][info][gc            ] GC(13) Concurrent Cycle
[2025-09-10T14:17:50.257+0800][23.969s][info][gc,marking    ] GC(13) Concurrent Clear Claimed Marks
[2025-09-10T14:17:50.257+0800][23.969s][info][gc,marking    ] GC(13) Concurrent Clear Claimed Marks 0.262ms
[2025-09-10T14:17:50.258+0800][23.969s][info][gc,marking    ] GC(13) Concurrent Scan Root Regions
[2025-09-10T14:17:50.261+0800][23.972s][info][gc,marking    ] GC(13) Concurrent Scan Root Regions 2.990ms
[2025-09-10T14:17:50.261+0800][23.973s][info][gc,marking    ] GC(13) Concurrent Mark (23.973s)
[2025-09-10T14:17:50.261+0800][23.973s][info][gc,marking    ] GC(13) Concurrent Mark From Roots
[2025-09-10T14:17:50.261+0800][23.973s][info][gc,task       ] GC(13) Using 4 workers of 4 for marking
[2025-09-10T14:17:50.267+0800][23.978s][info][gc,marking    ] GC(13) Concurrent Mark From Roots 5.546ms
[2025-09-10T14:17:50.267+0800][23.978s][info][gc,marking    ] GC(13) Concurrent Preclean
[2025-09-10T14:17:50.267+0800][23.979s][info][gc,marking    ] GC(13) Concurrent Preclean 0.209ms
[2025-09-10T14:17:50.267+0800][23.979s][info][gc,marking    ] GC(13) Concurrent Mark (23.973s, 23.979s) 6.131ms
[2025-09-10T14:17:50.267+0800][23.979s][info][gc,start      ] GC(13) Pause Remark
[2025-09-10T14:17:50.272+0800][23.983s][info][gc,stringtable] GC(13) Cleaned string and symbol table, strings: 57515 processed, 0 removed, symbols: 153368 processed, 131 removed
[2025-09-10T14:17:50.272+0800][23.984s][info][gc            ] GC(13) Pause Remark 70M->70M(1026M) 4.914ms
[2025-09-10T14:17:50.273+0800][23.984s][info][gc,cpu        ] GC(13) User=0.05s Sys=0.00s Real=0.00s
[2025-09-10T14:17:50.273+0800][23.985s][info][gc,marking    ] GC(13) Concurrent Rebuild Remembered Sets
[2025-09-10T14:17:50.276+0800][23.988s][info][gc,marking    ] GC(13) Concurrent Rebuild Remembered Sets 3.252ms
[2025-09-10T14:17:50.277+0800][23.988s][info][gc,start      ] GC(13) Pause Cleanup
[2025-09-10T14:17:50.277+0800][23.989s][info][gc            ] GC(13) Pause Cleanup 70M->70M(1026M) 0.349ms
[2025-09-10T14:17:50.277+0800][23.989s][info][gc,cpu        ] GC(13) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T14:17:50.277+0800][23.989s][info][gc,marking    ] GC(13) Concurrent Cleanup for Next Mark
[2025-09-10T14:17:50.280+0800][23.991s][info][gc,marking    ] GC(13) Concurrent Cleanup for Next Mark 2.292ms
[2025-09-10T14:17:50.280+0800][23.991s][info][gc            ] GC(13) Concurrent Cycle 22.724ms
[2025-09-10T14:20:21.641+0800][175.360s][info][gc,start      ] GC(14) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:20:21.641+0800][175.360s][info][gc,task       ] GC(14) Using 15 workers of 15 for evacuation
[2025-09-10T14:20:21.662+0800][175.381s][info][gc,phases     ] GC(14)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:20:21.662+0800][175.382s][info][gc,phases     ] GC(14)   Evacuate Collection Set: 18.4ms
[2025-09-10T14:20:21.663+0800][175.382s][info][gc,phases     ] GC(14)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T14:20:21.663+0800][175.382s][info][gc,phases     ] GC(14)   Other: 0.7ms
[2025-09-10T14:20:21.663+0800][175.383s][info][gc,heap       ] GC(14) Eden regions: 571->0(547)
[2025-09-10T14:20:21.664+0800][175.383s][info][gc,heap       ] GC(14) Survivor regions: 43->67(77)
[2025-09-10T14:20:21.664+0800][175.383s][info][gc,heap       ] GC(14) Old regions: 28->34
[2025-09-10T14:20:21.664+0800][175.383s][info][gc,heap       ] GC(14) Humongous regions: 5->0
[2025-09-10T14:20:21.665+0800][175.384s][info][gc,metaspace  ] GC(14) Metaspace: 72551K(74960K)->72551K(74960K) NonClass: 64233K(65748K)->64233K(65748K) Class: 8318K(9212K)->8318K(9212K)
[2025-09-10T14:20:21.665+0800][175.384s][info][gc            ] GC(14) Pause Young (Normal) (G1 Evacuation Pause) 644M->99M(1026M) 24.044ms
[2025-09-10T14:20:21.665+0800][175.384s][info][gc,cpu        ] GC(14) User=0.26s Sys=0.02s Real=0.02s
