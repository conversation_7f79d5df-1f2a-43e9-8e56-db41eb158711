WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.component.sidebar', location = 'aui.chunk.a5f03bee152582eb02b4--0ced26dd39ed7a6d6d7a.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.component.sidebar"],{"6wVx":function(t,e,i){},pju7:function(t,e,i){"use strict";i.r(e),i.d(e,"sidebar",function(){return N});i("FStl"),i("Q0fs"),i("rSV2"),i("nqD9"),i("iQXk"),i("IxCr"),i("S3ao"),i("YQ7q"),i("pDZt"),i("eC/R"),i("Rvtc"),i("Nu/Z"),i("6wVx");var o=i("+x/D"),n=(i("XphB"),i("C/C5"),i("HH5i")),a=i("KloK");function s(t){return Object(o.a)(t).clone().removeAttr("id")}Object(a.a)("clone",s);var r=s,l=i("bPPT"),u=window.DocumentTouch,c="ontouchstart"in window||u&&document instanceof u,p=function(t){return"value"in t||t.isContentEditable},d=i("+ay7");var h=i("4dFR"),f=i("OEua"),b=i("6RZY");function v(t,e,i,o){t.find("[".concat(o,"]")).attr(o,(t,o)=>e===o?i:void 0)}function g(t,e){let i=Object(o.a)("<div></div>");i.append(t),i.find("[id]").each((t,o)=>{const n=e("".concat(o.id,"-"));!function(t,e,i){v(t,e,i,"aria-controls"),v(t,e,i,"aria-owns")}(i,o.id,n),o.id=n})}function m(t){return t.offset().top}function w(t){this.$el=Object(o.a)(t),this.$el.length&&(this.$body=Object(o.a)("body"),this.$wrapper=this.$el.children(".aui-sidebar-wrapper"),this.$body.addClass("aui-page-sidebar"),this._previousScrollTop=null,this._previousViewportHeight=null,this._previousViewportWidth=null,this._previousOffsetTop=null,this.submenus=new $,function(t){if(!Object(o.a)(".aui-sidebar").length)return;(function(t){t.$el.on("mouseenter mouseleave click focus",t.collapsedTriggersSelector,function(e){const i=Object(o.a)(e.target);_(t,i)})})(t),c&&function(t){if(window.matchMedia)return window.matchMedia(t).matches;var e=document.createElement("style");e.type="text/css",e.id="testMedia",e.innerText="@media ".concat(t," { #testMedia { width: 1px; } }"),document.head.appendChild(e);var i="1px"===window.getComputedStyle(e,null).width;return e.parentNode.removeChild(e),i}("only screen and (max-device-width:1024px)")&&Object(o.a)("body").addClass("aui-page-sidebar-touch");var e=null,i=function(){null===e&&(e=requestAnimationFrame(function(){t.reflow(),e=null}))};Object(o.a)(window).on("scroll resize",i),i(),t.isAnimated()&&t.$el.on("transitionend webkitTransitionEnd",function(){t.$el.trigger(o.a.Event(y+(t.isCollapsed()?"collapse-end":"expand-end")))});t.$el.on("click",".aui-sidebar-toggle",function(e){e.preventDefault(),t.toggle()}),Object(o.a)(".aui-page-panel").on("click",function(){!t.isCollapsed()&&t.isViewportNarrow()&&t.collapse()});var a=function(e){(function(t){return!(t.which!==d.a.LEFT_SQUARE_BRACKET||t.shiftKey||t.ctrlKey||t.metaKey||p(t.target))})(e)&&t.toggle()};function s(t){return t.keyCode===d.a.TAB&&!t.shiftKey&&!t.altKey}Object(o.a)(document).on("keypress",a),t._remove=function(){this._removeAllTooltips(),Object(o.a)(this.inlineDialogSelector).remove(),this.$el.off(),this.$el.remove(),Object(o.a)(document).off("keypress",a),Object(o.a)(window).off("scroll resize",i)},t.$el.on("touchend",function(e){t.isCollapsed()&&(t.expand(),e.preventDefault())}),t.$el.on("mouseenter focus",t.collapsedTriggersSelector,function(){if(t.isCollapsed()){var e=Object(o.a)(this);O(e)||V(e)}}),t.$el.on("click blur mouseleave",t.collapsedTriggersSelector,function(){t.isCollapsed()&&D(Object(o.a)(this))}),t.$el.on("mouseenter focus",t.toggleSelector,function(){var e=Object(o.a)(this);t.isCollapsed()?e.data("tooltip",n.a.getText("aui.sidebar.expand.tooltip")):e.data("tooltip",n.a.getText("aui.sidebar.collapse.tooltip")),V(e)}),t.$el.on("click blur mouseleave",t.toggleSelector,function(){D(Object(o.a)(this))}),t.$el.on("keydown",t.collapsedTriggersSelector,function(e){if(t.isCollapsed()){var i=e.target,n=x(i);if(!n)return;var a=Object(o.a)(n);s(e)&&n.open&&(e.preventDefault(),function(t){t.attr("persistent",""),t.find(":aui-tabbable").first().focus(),setTimeout(function(){t.removeAttr("persistent")},100)}(a),a.on("keydown",function(t){((function(t){return t.keyCode===d.a.TAB&&t.shiftKey})(t)&&function(t,e){return t===e.find(":aui-tabbable")[0]}(t.target,a)||s(t)&&function(t,e){return t===e.find(":aui-tabbable").last()[0]}(t.target,a))&&(i.focus(),Object(o.a)(this).off("keydown"),T())}))}})}(this),function(t){Object(o.a)(t.collapsedTriggersSelector).each(function(){var e=Object(o.a)(this);_(t,e)})}(this))}var y="_aui-internal-sidebar-";function k(t){return o.a.map(t.split(" "),function(t){return y+t}).join(" ")}function C(){return document.querySelectorAll(w.prototype.inlineDialogSelector)}function $(){this.inlineDialog=null}function S(t){return t.is("a")?t.next(".aui-nav"):t.children(".aui-nav, hr")}function x(t){var e=t.getAttribute("aria-controls");return document.getElementById(e)}function O(t){return 0!==S(t).length}function T(){var t=C();Array.prototype.forEach.call(t,function(t){t.open=!1})}function _(t,e){if(!e.data("_aui-sidebar-submenu-constructed")&&(e.data("_aui-sidebar-submenu-constructed",!0),O(e))){var i=document.createElement("aui-inline-dialog"),n=Object(f.a)("sidebar-submenu");return e.attr("aria-controls",n),e.attr("data-aui-trigger",""),h.a.init(e),i.setAttribute("id",n),i.setAttribute("alignment","right top"),i.setAttribute("hidden",""),i.setAttribute("contained-by","viewport"),t.isCollapsed()&&i.setAttribute("responds-to","hover"),Object(o.a)(i).addClass(w.prototype.inlineDialogClass),document.body.appendChild(i),h.a.init(i),function(t,e,i){i.addEventListener("aui-layer-show",function(n){if(t.isCollapsed()){if(!function(t){return"AUI-INLINE-DIALOG"!==t.target.tagName}(n)&&!function(t){return!t.target.classList.contains("aui-sidebar-submenu-dialog")}(n)){var a=o.a.Event("aui-sidebar-submenu-before-show");e.trigger(a,i),a.isDefaultPrevented()?n.preventDefault():function(t,e){t.addClass("active"),e.innerHTML=j;var i=t.is("a")?t.text():t.children(".aui-nav-heading").text(),n=Object(o.a)(e).find(".aui-navgroup-inner");n.children(".aui-nav-heading").attr("title",i).children("strong").text(i),function(t){const e=r(t);g(e,f.a),e.hasClass("aui-expander-content")&&(e.find(".aui-expander-cutoff").remove(),e.removeClass("aui-expander-content"));return e}(S(t)).appendTo(n)}(e,i)}}else n.preventDefault()}),i.addEventListener("aui-layer-hide",function(){!function(t){t.removeClass("active")}(e)})}(t,e,i),i}}w.prototype.on=function(){var t=arguments[0],e=Array.prototype.slice.call(arguments,1),i=k(t);return this.$el.on.apply(this.$el,[i].concat(e)),this},w.prototype.off=function(){var t=arguments[0],e=Array.prototype.slice.call(arguments,1),i=k(t);return this.$el.off.apply(this.$el,[i].concat(e)),this},w.prototype.setHeight=function(t,e,i){var o=Math.max(0,i-t);return this.$wrapper.height(e-o),this},w.prototype.setTopPosition=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.pageYOffset;return this.$wrapper.toggleClass("aui-is-docked",t>m(this.$el)),this},w.prototype.setPosition=l.b(w.prototype.setTopPosition,"Sidebar.setPosition",{removeInVersion:"10.0.0",sinceVersion:"7.6.1",alternativeName:"Sidebar.setTopPosition"}),w.prototype.setLeftPosition=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.pageXOffset;return this.$wrapper.hasClass("aui-is-docked")&&this.$wrapper.css({left:-t}),this},w.prototype.setCollapsedState=function(t){var e={collapsed:{},expanded:{}};e.collapsed.narrow={narrow:o.a.noop,wide:function(e){e._expand(t,!0)}},e.collapsed.wide={narrow:o.a.noop,wide:o.a.noop},e.expanded.narrow={narrow:o.a.noop,wide:function(t){t.$body.removeClass("aui-sidebar-collapsed"),t.$el.removeClass("aui-sidebar-fly-out")}},e.expanded.wide={narrow:function(t){t._collapse(!0)},wide:o.a.noop};var i=this.isCollapsed()?"collapsed":"expanded",n=this.isViewportNarrow(this._previousViewportWidth)?"narrow":"wide",a=this.isViewportNarrow(t)?"narrow":"wide";return e[i][n][a](this),this},w.prototype._collapse=function(t){if(this.isCollapsed())return this;var e=o.a.Event(y+"collapse-start",{isResponsive:t});return this.$el.trigger(e),e.isDefaultPrevented()?this:(this.$body.addClass("aui-sidebar-collapsed"),this.$el.attr("aria-expanded","false"),this.$el.removeClass("aui-sidebar-fly-out"),this.$el.find(this.submenuTriggersSelector).attr("tabindex",0),Object(o.a)(this.inlineDialogSelector).attr("responds-to","hover"),this.isAnimated()||this.$el.trigger(o.a.Event(y+"collapse-end",{isResponsive:t})),this)},w.prototype.collapse=function(){return this._collapse(!1)},w.prototype._expand=function(t,e){var i=o.a.Event(y+"expand-start",{isResponsive:e});if(this.$el.trigger(i),i.isDefaultPrevented())return this;var n=this.isViewportNarrow(t);return this.$el.attr("aria-expanded","true"),this.$body.toggleClass("aui-sidebar-collapsed",n),this.$el.toggleClass("aui-sidebar-fly-out",n),this.$el.find(this.submenuTriggersSelector).removeAttr("tabindex"),Object(o.a)(this.inlineDialogSelector).removeAttr("responds-to"),this.isAnimated()||this.$el.trigger(o.a.Event(y+"expand-end",{isResponsive:e})),this},w.prototype.expand=function(){return this.isCollapsed()&&this._expand(this._previousViewportWidth,!1),this},w.prototype.isAnimated=function(){return this.$el.hasClass("aui-is-animated")},w.prototype.isCollapsed=function(){return"false"===this.$el.attr("aria-expanded")},w.prototype.isViewportNarrow=function(t){return(t=void 0===t?this._previousViewportWidth:t)<1240},w.prototype._removeAllTooltips=function(){Object(o.a)(this.tooltipSelector).remove()},w.prototype.responsiveReflow=function(t,e){if(t){if(!this.isCollapsed()&&this.isViewportNarrow(e)){var i=this.isAnimated();i&&this.$el.removeClass("aui-is-animated"),this.collapse(),i&&(this.$el[0].offsetHeight,this.$el.addClass("aui-is-animated"))}}else e!==this._previousViewportWidth&&this.setCollapsedState(e)},w.prototype.reflow=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.pageYOffset,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement.clientHeight,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:window.innerWidth,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:document.documentElement.scrollHeight,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:window.pageXOffset;var a=m(this.$el),s=null===this._previousViewportWidth;if(t!==this._previousScrollTop||e!==this._previousViewportHeight||a!==this._previousOffsetTop){this.isCollapsed()&&!s&&t!==this._previousScrollTop&&this._removeAllTooltips();var r=this.$body.hasClass("aui-page-sidebar-touch"),l=t!==this._previousScrollTop&&(t<0||t+e>o);r||!s&&l||(this.setHeight(t,e,a),this.setTopPosition(t))}if(n!==this._previousScrollLeft&&this.setLeftPosition(n),"false"!==this.$el.attr("data-aui-responsive"))this.responsiveReflow(s,i);else{var u=!this.isCollapsed()&&this.isViewportNarrow(i);this.$el.toggleClass("aui-sidebar-fly-out",u)}return this._previousScrollTop=t,this._previousViewportHeight=e,this._previousViewportWidth=i,this._previousOffsetTop=a,this._previousScrollLeft=n,this},w.prototype.toggle=function(){return this.isCollapsed()?(this.expand(),this._removeAllTooltips()):this.collapse(),this},w.prototype.submenuTriggersSelector=".aui-sidebar-group:not(.aui-sidebar-group-tier-one)",w.prototype.collapsedTriggersSelector=[w.prototype.submenuTriggersSelector,".aui-sidebar-group.aui-sidebar-group-tier-one > .aui-nav > li > a",".aui-sidebar-footer > .aui-sidebar-settings-button"].join(", "),w.prototype.toggleSelector=".aui-sidebar-footer > .aui-sidebar-toggle",w.prototype.tooltipSelector=".aui-sidebar-section-tooltip",w.prototype.inlineDialogClass="aui-sidebar-submenu-dialog",w.prototype.inlineDialogSelector="."+w.prototype.inlineDialogClass,$.prototype.submenu=function(t){return H(),S(t)},$.prototype.hasSubmenu=function(t){return H(),O(t)},$.prototype.submenuHeadingHeight=function(){return H(),34},$.prototype.isShowing=function(){return H(),w.prototype.isSubmenuVisible()},$.prototype.show=function(t,e){H(),function(t){x(t).open=!0}(e)},$.prototype.hide=function(){H(),T()},$.prototype.inlineDialogShowHandler=function(){H()},$.prototype.inlineDialogHideHandler=function(){H()},$.prototype.moveSubmenuToInlineDialog=function(){H()},$.prototype.restoreSubmenu=function(){H()},w.prototype.getVisibleSubmenus=function(){return Array.prototype.filter.call(C(),function(t){return t.open})},w.prototype.isSubmenuVisible=function(){return this.getVisibleSubmenus().length>0};const j='<div class="aui-inline-dialog-contents"><div class="aui-sidebar-submenu" ><div class="aui-navgroup aui-navgroup-vertical"><div class="aui-navgroup-inner"><div class="aui-nav-heading"><strong></strong></div></div></div></div></div>';var A={trigger:"manual",gravity:"w",className:"aui-sidebar-section-tooltip",title:function(){var t=Object(o.a)(this);return t.is("a")||t.is("button")?t.attr("title")||t.find(".aui-nav-item-label").text()||t.data("tooltip"):t.children(".aui-nav").attr("title")||t.children(".aui-nav-heading").text()}};function V(t){t.tooltip(A).tooltip("show");var e=t.data("tipsy")&&t.data("tipsy").$tip;e&&e.css({opacity:""}).addClass("tooltip-shown")}function D(t){var e=t.data("tipsy")&&t.data("tipsy").$tip;if(e){var i=e.css("transition-duration");if(i){var o=i.indexOf("ms")>=0?parseInt(i.substring(0,i.length-2),10):1e3*parseInt(i.substring(0,i.length-1),10);setTimeout(function(){e.hasClass("tooltip-shown")&&(t.tooltip("hide"),e.removeClass("tooltip-shown"))},o)}else e.removeClass("tooltip-shown")}}var E=Object(b.a)("sidebar",w);Object(o.a)(function(){E(".aui-sidebar")});var H=l.c("Sidebar.submenus",{removeInVersion:"10.0.0",sinceVersion:"5.8.0"});Object(a.a)("sidebar",E);var N=E}},[["pju7","runtime","aui.splitchunk.vendors--894c8113d9","aui.splitchunk.vendors--084821f40b","aui.splitchunk.vendors--be1eb78c1a","aui.splitchunk.vendors--23f50a6f00","aui.splitchunk.0d131bcbf1","aui.splitchunk.fbbef27525","aui.splitchunk.444efc83be","aui.splitchunk.739b9ec8cc","aui.splitchunk.056561461c","aui.splitchunk.949297951c","aui.splitchunk.dd803a46b4","aui.splitchunk.994e478d48","aui.splitchunk.d7c46c2734","aui.splitchunk.e54c7c7304","aui.splitchunk.fb15cffa72","aui.splitchunk.56dfb54d0c","aui.splitchunk.f1e06f97a4","aui.splitchunk.479fe6ee76","aui.splitchunk.f673ef53ac","aui.splitchunk.8659b532c1","aui.splitchunk.908fe798b4","aui.splitchunk.5f851f97df","aui.splitchunk.d0110a864f","aui.splitchunk.afa5039e04","aui.splitchunk.bff3715233","aui.splitchunk.c750721820","aui.splitchunk.6d6f245ed3","aui.splitchunk.862f26d10a","aui.splitchunk.084821f40b","aui.splitchunk.5b8c290363","aui.splitchunk.baa83dbaf9","aui.splitchunk.b2ecdd4179","aui.splitchunk.36cd9d521c","aui.splitchunk.f154095da3","aui.splitchunk.be1eb78c1a","aui.splitchunk.b652d2668a","aui.splitchunk.d925afe2c0","aui.splitchunk.5b7fdbd666","aui.splitchunk.23f50a6f00","aui.splitchunk.d727dd207f","aui.splitchunk.8a641c03a4","aui.splitchunk.141abf7fb1","aui.splitchunk.543254b237","aui.splitchunk.a2b2c71491","aui.splitchunk.036a05e5ff"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-expansion-manager', location = '/sidebar/expansion-manager.js' */
define("jira/projects/sidebar/expansion-manager",["wrm/data"],function(c){var d=!!c.claim("sidebar-collapsed-by-default");return function(a,b){b||(b=window.localStorage);a.reflow()&&a.isViewportNarrow()||(d&&void 0===b["jira-sidebar-collapsed"]||"true"===b["jira-sidebar-collapsed"]?a.collapse():a.expand());a.on("expand-start",function(a){a.isResponsive&&"true"===b["jira-sidebar-collapsed"]&&a.preventDefault()});a.on("expand-end collapse-end",function(c){c.isResponsive||a.isViewportNarrow()||(b["jira-sidebar-collapsed"]=
a.isCollapsed().toString())})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-api', location = '/sidebar/api.js' */
define("jira/api/projects/sidebar",["jira/jquery/deferred","exports"],function(d,a){var c=new d,b=void 0;a.getSidebar=function(){return c.promise()};a.initAPI=function(a){b=a;AJS.namespace("JIRA.API.Sidebar",null,b);c.resolve(b)}});AJS.namespace("JIRA.API.getSidebar",null,require("jira/api/projects/sidebar").getSidebar);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-init', location = '/sidebar/main.js' */
require("jira/jquery/deferred jira/skate wrm/require jquery jira/api/projects jira/util/logger jira/projects/sidebar/expansion-manager jira/ajs/dark-features".split(" "),function(d,e,k,f,l,m,n,g){var h=!1,p=function(){var a=new d;f(function(){a.resolve();h=!0});return a.promise()}(),q=function(){if(g.isEnabled("com.atlassian.jira.projects.sidebar.DEFER_RESOURCES")&&!g.isEnabled("com.atlassian.jira.projects.sidebar.DEFER_RESOURCES_KILLSWITCH")){var a=l.getCurrentProjectType(),b=["wrc!jira.project.sidebar"];
a&&b.push("wrc!jira.project.sidebar."+a);return k(b)}return(new d).resolve().promise()}(),r=function(){var a=new d;document.addEventListener("sidebar:initialised",function(b){n(b.detail);b.target.classList.contains("projects-sidebar")&&a.resolve(b)});return a.promise()}();f.when(r,q,p).done(function(a){var b=a.target,c=b.parentElement.querySelector(".sidebar-placeholder");c&&(b.classList.add("fade-in"),c.addEventListener("animationend",function(){c.parentNode.removeChild(c)}),c.classList.add("fade-out"));
b.classList.remove("sidebar-pending");require("jira/projects/sidebar/sidebar-initializer").init(a.detail);m.trace("jira.projects.sidebar.init",{dclFired:h})});e("aui-sidebar",{type:e.type.CLASSNAME,attached:function(a){var b=AJS.sidebar(a),c=document.createEvent("CustomEvent");c.initCustomEvent("sidebar:initialised",!0,!1,b);a.dispatchEvent(c)}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-placeholder-init', location = '/sidebar/sidebar-placeholder-init.js' */
define("jira/projects/sidebar/sidebar-placeholder-initializer",["wrm/require","jira/analytics","jira/util/logger"],function(f,g,d){function h(){f(["wr!jira.webresources:jira-formatter","wr!jira.webresources:messages"],function(){var a=require("jira/util/formatter"),c=require("jira/flag"),b=document.createElement("a");b.setAttribute("href",".");b.textContent="\u5237\u65b0\u9875\u9762";c.showWarningMsg("",a.format("\u52a0\u8f7d\u8fb9\u680f\u7684\u65f6\u95f4\u6bd4\u5e73\u65f6\u957f\u3002\u60a8\u53ef\u4ee5{0}\u518d\u6b21\u52a0\u8f7d\u5b83\u3002",b.outerHTML))}).fail(function(){d.error("Failed to display sidebar error message due to error when loading WRM dependencies.")})}
function e(a,c){var b;c&&c.data&&(b={signature:c.data.signature});g.send({name:a,data:b})}return{onSuccess:function(a){e("bigpipe.sidebar.render.success",a)},onError:function(a){h();d.error("Error while rendering sidebar. Message:",a);e("bigpipe.sidebar.render.error",a)}}});
}catch(e){WRMCB(e)};