WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'jira.webresources:init-on-dcl', location = '/static/util/init-on-dcl.js' */
define("jira/util/init-on-dcl",[],function(){"use strict";return AJS.toInit});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/jquery/plugins/avataror/avataror.js' */
jQuery.fn.avataror=function(i){var e=jQuery,t=e(document);this.each(function(){var r=e(this),a=r.find("img").attr("src");r.css({"-moz-border-radius":"10px","-webkit-border-radius":"10px"}),r.html("<p>Loading?</p>");var g={previewSize:48};g.preview=e("<div/>").addClass("avataror-preview").css({border:"solid 1px #000",float:"left",height:g.previewSize+"px",overflow:"hidden",width:g.previewSize+"px",position:"relative",top:"-9999em",left:"-9999em"}),g.preview.prependTo(i.previewElement),g.img=e('<img alt="Avatar Source"/>'),g.img.load(function(){g.image=e("<div/>").css({background:"url('"+a+"') no-repeat",clear:"left",position:"relative"}),g.marker=e("<div/>").css({cursor:"move",position:"relative"}),g.dash=e("<div/>"),g.shadow=e("<div/>"),g.dash.add(g.shadow).css({cursor:"move",opacity:.5,left:0,top:0,position:"absolute"}),g.image.append(g.shadow).append(g.dash).append(g.marker),r.append(g.image),g.marker.html("<div></div><div></div><div></div><div></div>"),e("div",g.marker).each(function(i){var t=e(this);t.css({background:"#000",border:"solid 1px #fff",width:"10px",height:"10px",position:"absolute","font-size":"1px"}),t.css(["left","right","right","left"][i],"-6px"),t.css(["top","top","bottom","bottom"][i],"-6px"),t.css("cursor",["nw-resize","ne-resize","se-resize","sw-resize"][i]),t.mousedown(function(e){e.preventDefault(),e.stopPropagation(),g.dragging={x:e.pageX,y:e.pageY,ax:g.x,ay:g.y,w:g.width,h:g.height,i:i+1},g.shadow.hide()})}),g.marker.add(g.image).mousedown(function(i){i.preventDefault(),g.dragging={x:i.pageX,y:i.pageY,ax:g.x,ay:g.y,w:g.width,h:g.height},g.shadow.hide()}),t.mouseup(function(i){g.handleMouseUp(i)}),t.mousemove(function(i){g.dragging&&(g.handleMouseMove(i.pageX,i.pageY),i.preventDefault())}),g.imgwidth=g.img[0].naturalWidth,g.imgheight=g.img[0].naturalHeight,g.x=parseInt(e("#avatar-offsetX").val()),g.y=parseInt(e("#avatar-offsetY").val()),g.width=parseInt(e("#avatar-width").val()),g.height=g.width,g.image.css({width:g.imgwidth+"px",height:g.imgheight+"px"}),g.setMarker(),r.css({width:g.imgwidth+"px"}),g.preview.css({position:"static"}),e("p",r).remove(),r.trigger("AvatarImageLoaded"),g.adjustPreview()}),g.img.attr("src",a),g.preview.append(g.img),g.setMarker=function(){g.marker.css("border","dashed 1px #fff"),g.dash.css("border","solid 1px #000"),g.shadow.css("border","solid 1px #000"),g.marker.add(this.dash).css("left",this.x-1+"px"),g.marker.add(g.dash).css("top",g.y-1+"px"),g.shadow.css("border-left-width",g.x+"px"),g.shadow.css("border-right-width",g.imgwidth-g.x-g.width+"px"),g.shadow.css("border-top-width",g.y+"px"),g.shadow.css("border-bottom-width",g.imgheight-g.y-g.height+"px"),g.shadow.css("width",g.width+"px"),g.shadow.css("height",g.height+"px"),g.marker.add(g.dash).css("width",g.width+"px"),g.marker.add(g.dash).css("height",g.height+"px")},g.adjustPreview=function(){g.img.attr("width",g.imgwidth*g.previewSize/g.width),g.img.attr("height",g.imgheight*g.previewSize/g.height),g.img.css("margin-left","-"+g.x*g.previewSize/g.width+"px"),g.img.css("margin-top","-"+g.y*g.previewSize/g.height+"px"),g.preview.select()},g.handleMouseMove=function(i,e){if(g.dragging.nextExec=g.dragging.nextExec||0,0!=g.dragging.nextExec)return void g.dragging.nextExec--;g.dragging.nextExec=3;var t=i-g.dragging.x,r=e-g.dragging.y;if(this.dragging.i){(0,g.resizeHandlers[this.dragging.i-1])(t,r)}else g.x=g.dragging.ax+t,g.y=g.dragging.ay+r,g.x+g.width>g.imgwidth&&(g.x=g.imgwidth-g.width),g.y+g.height>g.imgheight&&(g.y=g.imgheight-g.height),g.x<0&&(g.x=0),g.y<0&&(g.y=0);g.setMarker(),g.adjustPreview()},g.handleMouseUp=function(i){e("#avatar-offsetX").val(g.x),e("#avatar-offsetY").val(g.y),e("#avatar-width").val(g.width),g.dragging=null,g.shadow.show()},g.originX=function(){return g.dragging.ax},g.originY=function(){return g.dragging.ay},g.originBottomX=function(){return g.dragging.ax+g.dragging.w},g.originBottomY=function(){return g.dragging.ay+g.dragging.h},g.originNw=function(){return{x:g.originX(),y:g.originY()}},g.originNe=function(){return{x:g.originBottomX(),y:g.originY()}},g.originSe=function(){return{x:g.originBottomX(),y:g.originBottomY()}},g.originSw=function(){return{x:g.originX(),y:g.originBottomY()}},g.nwHandler=function(i,e){var t=g.originSe(),r={x:g.originX()+i,y:g.originY()+e},a=Math.abs(r.x-t.x),o=Math.abs(r.y-t.y),n=Math.min(a,o);n<20&&(n=20),t.x-n<0&&(n=t.x),t.y-n<0&&(n=t.y),g.x=t.x-n,g.y=t.y-n,g.width=g.height=n},g.neHandler=function(i,e){var t=g.originSw(),r={x:g.originBottomX()+i,y:g.originY()+e},a=Math.abs(r.x-t.x),o=Math.abs(r.y-t.y),n=Math.min(a,o);n<20&&(n=20),t.x+n>g.imgwidth&&(n=g.imgwidth-t.x),t.y-n<0&&(n=t.y),g.y=t.y-n,g.width=g.height=n},g.seHandler=function(i,e){var t=g.originNw(),r={x:g.originBottomX()+i,y:g.originBottomY()+e},a=Math.abs(r.x-t.x),o=Math.abs(r.y-t.y),n=Math.min(a,o);n<20&&(n=20),t.x+n>g.imgwidth&&(n=g.imgwidth-t.x),t.y+n>g.imgheight&&(n=g.imgheight-t.y),g.width=g.height=n},g.swHandler=function(i,e){var t=g.originNe(),r={x:g.originX()+i,y:g.originBottomY()+e},a=Math.abs(r.x-t.x),o=Math.abs(r.y-t.y),n=Math.min(a,o);n<20&&(n=20),t.x-n<0&&(n=t.x),t.y+n>g.imgheight&&(n=g.imgheight-t.y),g.x=t.x-n,g.width=g.height=n},g.resizeHandlers=[g.nwHandler,g.neHandler,g.seHandler,g.swHandler]})};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/Avatar.js' */
var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};define("jira/ajs/avatarpicker/avatar",["jira/lib/class"],function(t){return t.extend({init:function(t){this._id=t.id,this._isSystemAvatar=t.isSystemAvatar,this._isSelected=t.isSelected,this._urls=t.urls},setUnSelected:function(){this._isSelected=!1},setSelected:function(){this._isSelected=!0},isSelected:function(){return!!this._isSelected},isSystemAvatar:function(){return this._isSystemAvatar},getId:function(){return this._id},getUrl:function(t){return this._urls[t]},toJSON:function(){return{id:this._id,isSystemAvatar:this._isSystemAvatar,isSelected:this._isSelected,urls:this._urls}}})}),define("jira/ajs/avatarpicker/avatar-factory",["jira/ajs/avatarpicker/avatar","exports"],function(t,e){e.createCustomAvatar=function(e){return e.isSystemAvatar=!1,new t(e)},e.createSystemAvatar=function(e){return e.isSystemAvatar=!0,new t(e)}}),define("jira/ajs/avatarpicker/avatar/sizes",["exports","jquery"],function(t,e){t.getSizeObjectFromName=function(i){if("object"===(void 0===i?"undefined":_typeof(i)))return i;"string"==typeof i&&e.trim(i);return t.LARGE.param===i?t.LARGE:t.MEDIUM.param===i?t.MEDIUM:t.SMALL.param===i?t.SMALL:"xsmall"===i?t.SMALL:t.LARGE},t.LARGE={param:"large",height:48,width:48},t.MEDIUM={param:"medium",width:32,height:32},t.SMALL={param:"small",width:16,height:16}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/AvatarManager.js' */
define("jira/ajs/avatarpicker/avatar-manager",["jira/util/formatter","jira/lib/class","wrm/context-path","jquery"],function(t,a,r,e){"use strict";var s=r();return a.extend({init:function(t){this.store=t.store,this.ownerId=t.ownerId,this.username=t.username,this.anonymousAvatarId=t.anonymousAvatarId,this.avatarSrcBaseUrl=t.avatarSrcBaseUrl},selectAvatar:function(t,a){return this.store.selectAvatar(t,a)},getById:function(t){return this.store.getById(t)},destroy:function(t,a){this.store.destroy(t,a)},update:function(t,a){this.store.update(t,a)},add:function(t,a){this.store._add(t,a)},getAllSystemAvatars:function(){return this.store.getAllSystemAvatars()},getAllCustomAvatars:function(){return this.store.getAllCustomAvatars()},getSelectedAvatar:function(){return this.store.getSelectedAvatar()},getAllAvatars:function(){return this.store.getAllAvatars()},getAllAvatarsRenderData:function(t){var a,r=this,e=this.getAllAvatars(),s={system:[],custom:[]};for(a=0;a<e.system.length;a++)s.system.push(r.getAvatarRenderData(e.system[a],t));for(a=0;a<e.custom.length;a++)s.custom.push(r.getAvatarRenderData(e.custom[a],t));return s},getAvatarRenderData:function(t,a){var r=t.toJSON();return r.src=this.getAvatarSrc(t,a),r.width=a.width,r.height=a.height,r},refreshStore:function(t){this.store.refresh(t)},getAvatarSrc:function(a,r){return this.store.isTempAvatar(a)?s+"/secure/temporaryavatar?"+e.param({cropped:!0,magic:(new Date).getTime(),size:r.param}):a.getUrl(t.format("{0}x{1}",r.height,r.width))},createTemporaryAvatar:function(t,a){this.store.createTemporaryAvatar(t,a)},createAvatarFromTemporary:function(t,a){this.store.createAvatarFromTemporary(t,a)},getAnonymousAvatarId:function(){return this.anonymousAvatarId}})}),define("jira/ajs/avatarpicker/avatar-manager-factory",["jira/ajs/avatarpicker/avatar-store","jira/ajs/avatarpicker/avatar-manager","wrm/context-path","exports"],function(t,a,r,e){"use strict";var s=r();e.createUniversalAvatarManager=function(r){var e,n="",o="",i="",v="";if(r.projectId){var u=s+"/rest/api/latest/universal_avatar/type/"+r.avatarType+"/owner/"+r.projectId;e=u;var c=u+"/avatar";n=null,o=u+"/temp",i=c,v=c}else e=s+"/rest/api/latest/avatar/project/system",o=s+"/rest/api/latest/avatar/project/temporary",i=s+"/rest/api/latest/avatar/project/temporaryCrop";var l=new t({restQueryUrl:e,restUpdateUrl:n,restCreateTempUrl:o,restUpdateTempUrl:i,restSingleAvatarUrl:v,defaultAvatarId:r.defaultAvatarId});return new a({store:l,ownerId:r.projectId,avatarSrcBaseUrl:s+"/secure/projectavatar"})},e.createProjectAvatarManager=function(t){return t.avatarType="project",e.createUniversalAvatarManager(t)},e.createUserAvatarManager=function(r){var e=s+"/rest/api/latest/user",n=new t({restQueryUrl:e+"/avatars",restUpdateUrl:e+"/avatar",restCreateTempUrl:e+"/avatar/temporary",restUpdateTempUrl:e+"/avatar",restSingleAvatarUrl:e+"/avatar",restParams:{username:r.username},defaultAvatarId:r.defaultAvatarId});return new a({store:n,username:r.username,avatarSrcBaseUrl:s+"/secure/useravatar"})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/AvatarStore.js' */
define("jira/ajs/avatarpicker/avatar-store",["jira/util/urls","jira/util/formatter","jira/ajs/avatarpicker/avatar-util","jira/ajs/avatarpicker/avatar-factory","jira/ajs/ajax/smart-ajax","jira/lib/class","jquery"],function(t,e,r,a,s,i,o){return i.extend({TEMP_ID:"TEMP",init:function(e){if(!e.restQueryUrl)throw new Error("JIRA.AvatarStore.init: You must specify [restQueryUrl], The rest url for querying avatars (see class description)");if(!e.restCreateTempUrl)throw new Error("JIRA.AvatarStore.init: You must specify [restCreateTempUrl], The rest url for creating a temporary avatar (see class description)");if(!e.restUpdateTempUrl)throw new Error("JIRA.AvatarStore.init: You must specify [restUpdateTempUrl], The rest url for updating a temporary avatar (see class description)");if(!e.defaultAvatarId)throw new Error("JIRA.AvatarStore.init: You must specify [defaultAvatarId] to the contructor so the store knows what to select if you delete the selected one");this.restQueryUrl=e.restQueryUrl,this.restUpdateUrl=e.restUpdateUrl,this.restCreateTempUrl=e.restCreateTempUrl,this.restUpdateTempUrl=e.restUpdateTempUrl,this.restSingleAvatarUrl=e.restSingleAvatarUrl,this.restParams=e.restParams||{},this.restParams.atl_token=t.atl_token(),this.defaultAvatarId=e.defaultAvatarId,this.avatars={system:[],custom:[]}},_buildCompleteUrl:function(t){var r=t;if(this.restParams){var a="";for(var s in this.restParams)a+=e.format("&{0}={1}",encodeURIComponent(s),encodeURIComponent(this.restParams[s]));r+="?"+a.substr(1)}return r},getById:function(t){var e;return o.each(this.avatars.system,function(r,a){if(this.getId()===t)return e=a,!1}),e||o.each(this.avatars.custom,function(r,a){if(this.getId()===t)return e=a,!1}),e},isTempAvatar:function(t){return t.getId()===this.TEMP_ID},_selectAvatar:function(t){var e=this.getSelectedAvatar();e&&e.setUnSelected(),t.setSelected()},selectAvatar:function(t,e){var r=this;if(!t)throw new Error("JIRA.AvatarStore.selectAvatar: Cannot select Avatar that does not exist");this.restUpdateUrl?s.makeRequest({type:"PUT",contentType:"application/json",dataType:"json",url:this._buildCompleteUrl(this.restUpdateUrl),data:JSON.stringify(t.toJSON()),success:function(){r._selectAvatar(t),e.success&&e.success.call(this,t)},error:e.error}):(r._selectAvatar(t),e.success&&e.success.call(this,t))},_destory:function(t){var e=o.inArray(t,this.avatars.custom);if(-1===e)throw new Error("JIRA.AvatarStore._destroy: Cannot remove avatar ["+t.getId()+"], it might be a system avatar (readonly) or does not exist.");this.avatars.custom.splice(e,1)},destroy:function(t,e){var r=this;if(e=e||{},!t)throw new Error("JIRA.AvatarStore.destroy: Cannot delete Avatar that does not exist");s.makeRequest({type:"DELETE",url:this.getRestUrlForAvatar(t),success:function(){r._destory(t),t.isSelected()?r.selectAvatar(r.getById(r.defaultAvatarId),e):e.success&&e.success.apply(this,arguments)},error:e.error})},getSelectedAvatar:function(){for(var t=0;t<this.avatars.custom.length;t++)if(this.avatars.custom[t].isSelected())return this.avatars.custom[t];for(t=0;t<this.avatars.system.length;t++)if(this.avatars.system[t].isSelected())return this.avatars.system[t]},_update:function(t){var e=this;if(!this.getById(t.getId()))throw new Error("JIRA.AvatarStore._update: Cannot update avatar ["+t.getId()+"], it might be a system avatar (readonly) or does not exist.");o.each(this.avatars.custom,function(r){this.getId()===t.getId()&&(e.avatars.custom[r]=t)})},update:function(t,e){var r=this;e=e||{},s.makeRequest({type:"PUT",url:this.getRestUrlForAvatar(t),error:e.error,success:function(){r._update(t),e.success&&e.success.apply(this,arguments)}})},_add:function(t){t.isSystemAvatar()?this.avatars.system.push(t):this.avatars.custom.push(t)},createAvatarFromTemporary:function(t,e){var r=this;e=e||{},this.restUpdateTempUrl&&s.makeRequest({type:"POST",url:this._buildCompleteUrl(this.restUpdateTempUrl),data:JSON.stringify(t),contentType:"application/json",dataType:"json",success:function(t){t||(t={id:r.TEMP_ID,isSelected:!0});var s=a.createCustomAvatar(t);r._add(s),e.success&&e.success.call(this,t)},error:e.error})},createTemporaryAvatar:function(t,e){e=o.extend(!0,{},e,{params:this.restParams}),r.uploadTemporaryAvatar(this.restCreateTempUrl,t,e)},_refresh:function(t){var e=this;e.avatars.system=[],e.avatars.custom=[],t.system&&o.each(t.system,function(t,r){e.avatars.system.push(a.createSystemAvatar(r))}),t.custom&&o.each(t.custom,function(t,r){e.avatars.custom.push(a.createCustomAvatar(r))})},refresh:function(t){var e=this,r=this.getById(e.TEMP_ID);t=t||{},s.makeRequest({url:this._buildCompleteUrl(this.restQueryUrl),error:t.error,success:function(a){e._refresh(a),r&&e._add(r),t.success&&t.success.apply(this,arguments)}})},getAllAvatars:function(){return this.avatars},getAllSystemAvatars:function(){return this.avatars.system},getAllCustomAvatars:function(){return this.avatars.custom},getRestUrlForAvatar:function(t){return this._buildCompleteUrl(this.restSingleAvatarUrl+"/"+t.getId())}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/AvatarUtil.js' */
define("jira/ajs/avatarpicker/avatar-util",["jira/util/urls","jira/util/logger","jira/attachment/inline-attach","jquery"],function(e,r,a,n){var t={uploadUsingIframeRemoting:function(o,s,i){i=i||{};var l=s.val(),c=new a.Form(new a.FileInput(s,!1)),u=c.addStaticProgress(l),d=c.cloneFileInput();c.fileSelector.clear();var g=new a.Timer(function(){!this.cancelled&&u.show()},this),f=new a.FormUpload({$input:d,url:o,params:n.extend({},i.params,{filename:l,atl_token:e.atl_token()}),scope:this,before:function(){!this.cancelled&&u.start()},success:function(e,r){e.errorMessages&&e.errorMessages.length?c.addErrorWithFileName(e.errorMessages[0],l,t.getErrorTarget(c)):i.success&&i.success(e,r)},error:function(e){r.log(e),this.cancelled||(e.indexOf("SecurityTokenMissing")>=0?c.addError(a.Text.tr("upload.xsrf.timeout",l),t.getErrorTarget(c)):c.addError(a.Text.tr("upload.error.unknown",l),t.getErrorTarget(c)))},after:function(){g.cancel(),u.remove(),this.cancelled||c.enable()}});u.onCancel(function(){f.abort()}),f.upload()},uploadUsingFileApi:function(r,o,s){var i,l,c=o[0].files[0],u=new a.Form(new a.FileInput(o,!1)),d=u.addProgress(c);s=s||{},i=new a.Timer(function(){d.show()}),l=new a.AjaxUpload({file:c,params:n.extend({},s.params,{filename:c.name,size:c.size,atl_token:e.atl_token()}),scope:this,url:r,before:function(){o.hide(),d.start()},progress:function(e){d.progress.$progress.parent().parent().show(),d.update(e)},success:function(e,r){e.errorMessages&&e.errorMessages.length?u.addErrorWithFileName(e.errorMessages[0],c.name,t.getErrorTarget(u)):201===r&&s.success(e,r)},error:function(e,r){r<0?u.addError(e,t.getErrorTarget(u)):u.addError(a.Text.tr("upload.error.unknown",c.name),t.getErrorTarget(u)),s.error&&s.error(e,r)},after:function(){i.cancel(),d.finish().remove(),o.val("").show()}}),l.upload(),d.onCancel(function(){l.abort()})},getErrorTarget:function(e){return{$element:e.$form.find(".error")}},uploadTemporaryAvatar:function(e,r,n){a.AjaxPresenter.isSupported(r)?this.uploadUsingFileApi(e,r,n):this.uploadUsingIframeRemoting(e,r,n)}};return t});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/AvatarPicker.js' */
define("jira/ajs/avatarpicker/avatar-picker",["jira/util/logger","jira/util/formatter","jira/util/strings","jira/ajs/avatarpicker/avatar-picker/image-editor","jira/ajs/ajax/smart-ajax","jira/dialog/dialog","jira/ajs/control","aui/message","jquery"],function(a,e,t,r,i,n,c,o,d){"use strict";return c.extend({init:function(a){this.avatarManager=a.avatarManager,this.avatarRenderer=a.avatarRenderer,this.imageEditor=a.imageEditor,this.size=a.size,this.selectCallback=a.select,this.cropperDialog=null,this.initialSelection=a.initialSelection},render:function(a){var e=this;this.avatarManager.refreshStore({success:function(){e.cropperDialog instanceof n&&(e.cropperDialog.hide(),delete e.cropperDialog),e.element=d('<div id="jira-avatar-picker" />'),e.element.html(JIRA.Templates.AvatarPicker.picker({avatars:e.avatarManager.getAllAvatarsRenderData(e.size)})),e._assignEvents("selectAvatar",e.element.find(".jira-avatar button")),e._assignEvents("deleteAvatar",e.element.find(".jira-delete-avatar")),e._assignEvents("uploader",e.element.find("#jira-avatar-uploader")),void 0!==e.initialSelection&&e.getAvatarElById(e.initialSelection).addClass("jira-selected-avatar"),a(e.element)},error:function(t,r,i,n){e.appendErrorContent(e.element,n),a(e.element)}})},appendErrorContent:function(a,e){try{var r=JSON.parse(e.data);r&&r.errorMessages?d.each(r.errorMessages,function(e,r){o.error(a,{body:t.escapeHtml(r),closeable:!1,shadowed:!1})}):a.append(i.buildDialogErrorContent(e,!0))}catch(t){a.append(i.buildDialogErrorContent(e,!0))}},uploadTempAvatar:function(e){var t=this;this.avatarManager.createTemporaryAvatar(e,{success:function(a){a.id?t.render(function(){t.selectAvatar(a.id)}):(e.val(""),t.cropperDialog=new n({id:"project-avatar-cropper",width:560,content:function(e){function r(){var a=i.find("input[type=submit]"),e=d("<span class='icon throbber loading'></span>");return a.attr("aria-disabled","true").attr("disabled",""),a.before(e),function(){e.remove(),a.removeAttr("aria-disabled").removeAttr("disabled")}}var i=t.imageEditor.render(a);t.imageEditor.edit({confirm:function(a){var e=r();t.avatarManager.createAvatarFromTemporary(a,{success:function(a){t.render(function(){t.selectAvatar(a.id)})},error:e})}}),i.find(".cancel").click(function(){t.cropperDialog.hide()}),e(i)}}),t.cropperDialog.bind("dialogContentReady",function(){d(t).trigger(r.LOADED)}),t.cropperDialog.bind("Dialog.hide",function(){d(t).trigger(r.DISMISSED)}),t.cropperDialog.show())},error:function(){a.log(arguments)}})},getAvatarElById:function(a){return this.element.find(".jira-avatar[data-id='"+a+"']")},selectAvatar:function(a){var e=this.avatarManager.getById(a),t=this;this.avatarManager.selectAvatar(this.avatarManager.getById(a),{error:function(){},success:function(){t.getAvatarElById(a).remove(),t.selectCallback&&t.selectCallback.call(t,e,t.avatarManager.getAvatarSrc(e,t.size))}})},deleteAvatar:function(a){var t=this;confirm("\u786e\u5b9e\u8981\u5220\u9664\u8fd9\u4e2a\u56fe\u50cf\uff1f")&&this.avatarManager.destroy(this.avatarManager.getById(a),{error:function(){},success:function(){var e=t.avatarManager.getSelectedAvatar(),r=t.getAvatarElById(a);r.fadeOut(function(){r.remove()}),e.getId()!==a&&(t.getAvatarElById(e.getId()).addClass("jira-selected-avatar"),t.selectCallback.call(t,e,t.avatarManager.getAvatarSrc(e,t.size),!0))}})},_events:{uploader:{change:function(a,e){this.uploadTempAvatar(e)}},deleteAvatar:{click:function(a,e){this.deleteAvatar(e.attr("data-id"))}},selectAvatar:{click:function(a,e){"button"===e[0].tagName.toLowerCase()&&this.selectAvatar(e.attr("data-id"))}}}})}),define("jira/ajs/avatarpicker/avatar-picker/image-editor",["jira/ajs/control","jquery"],function(a,e){"use strict";var t=a.extend({render:function(a){return this.element=e('<div id="avatar-picker-image-editor"/>').html(JIRA.Templates.AvatarPicker.imageEditor(a)),this.element},edit:function(a){var t=this,r=this.element.find(".avataror");a=a||{},r.unbind(),r.bind("AvatarImageLoaded",function(){a.ready&&a.ready()}),r.find("img").load(function(){r.avataror({previewElement:t.element.find(".jira-avatar-cropper-header"),parent:t.element})}),this.element.find("#avataror").submit(function(t){t.preventDefault(),a.confirm&&a.confirm({cropperOffsetX:e("#avatar-offsetX").val(),cropperOffsetY:e("#avatar-offsetY").val(),cropperWidth:e("#avatar-width").val()})}).find(".cancel").click(function(e){e.preventDefault(),a.cancel&&a.cancel()})}});return t.LOADED="imageEditorLoaded",t.DISMISSED="imageEditorDismissed",t}),define("jira/ajs/avatarpicker/avatar-picker-factory",["jira/util/browser","jira/util/formatter","jira/ajs/avatarpicker/avatar-picker","jira/ajs/avatarpicker/avatar-picker/image-editor","jira/ajs/avatarpicker/avatar-manager-factory","jira/ajs/avatarpicker/avatar/sizes","jira/dialog/form-dialog","wrm/context-path","wrm/data","jquery","exports"],function(a,e,t,r,i,n,c,o,d,l,s){"use strict";s.createUniversalAvatarPicker=function(a){return new t({avatarManager:i.createUniversalAvatarManager({projectKey:a.projectKey,projectId:a.projectId,defaultAvatarId:a.defaultAvatarId,avatarType:a.avatarType}),initialSelection:a.initialSelection,imageEditor:new r,size:a.hasOwnProperty("avatarSize")?a.avatarSize:n.LARGE,select:a.select})},s.createProjectAvatarPicker=function(a){return new t({avatarManager:i.createProjectAvatarManager({projectKey:a.projectKey,projectId:a.projectId,defaultAvatarId:a.defaultAvatarId}),imageEditor:new r,size:n.LARGE,select:a.select})},s.createUserAvatarPicker=function(a){return new t({avatarManager:i.createUserAvatarManager({username:a.username,defaultAvatarId:a.defaultAvatarId}),imageEditor:new r,size:n.LARGE,select:a.select})},s.createUniversalAvatarPickerDialog=function(a){var t=a.initialSelection||a.defaultAvatarId,r=new c({trigger:a.trigger,id:"project-avatar-picker",width:600,stacked:!0,content:function(i){var n,c;c=l('<div id="projectavatar-content-wrapper"/>'),l("<h2 />").text(a.title||"\u9009\u62e9\u4e00\u4e2a\u9879\u76ee\u56fe\u50cf").appendTo(c),n=s.createUniversalAvatarPicker({projectKey:a.projectKey,projectId:a.projectId,defaultAvatarId:a.defaultAvatarId,initialSelection:t,avatarType:a.avatarType,avatarSize:a.avatarSize,select:function(e,i,n){t=String(e.getId()),a.select&&a.select.apply(this,arguments),n||r.hide()}}),n.render(function(a){c.append(a),i(c)})}});r._focusFirstField=function(){}},s.createProjectAvatarPickerDialog=function(a){var t=new c({trigger:a.trigger,id:"project-avatar-picker",width:600,stacked:!0,content:function(r){var i,n;n=l('<div id="projectavatar-content-wrapper"/>'),l("<h2 />").text("\u9009\u62e9\u4e00\u4e2a\u9879\u76ee\u56fe\u50cf").appendTo(n),i=s.createProjectAvatarPicker({projectKey:a.projectKey,projectId:a.projectId,defaultAvatarId:a.defaultAvatarId,select:function(e,r,i){a.select&&a.select.apply(this,arguments),i||t.hide()}}),i.render(function(a){n.append(a),r(n)})}});t._focusFirstField=function(){}};var v=d.claim("jira.core:avatar-picker-data.data");s.createUserAvatarPickerDialog=function(t){if(v&&v.isEnabled)return void l(t.trigger).click(function(e){var t=o()+v.url;t+=(t.indexOf("?")>-1?"&":"?")+"continue="+encodeURIComponent(window.location.href),e.preventDefault(),e.stopPropagation(),a.reloadViaWindowLocation(t)});var r=new c({trigger:t.trigger,id:"user-avatar-picker",width:600,stacked:!0,content:function(a){var i,n;n=l('<div id="useravatar-content-wrapper"/>'),l("<h2 />").text("\u9009\u62e9\u4e00\u4e2a\u7528\u6237\u56fe\u50cf").appendTo(n),i=s.createUserAvatarPicker({username:t.username,defaultAvatarId:t.defaultAvatarId,select:function(a,e,i){t.select&&t.select.apply(this,arguments),l(".avatar-image").attr("src",e),i||r.hide()}}),i.render(function(e){n.append(e),a(n)})}})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/GravatarUtil.js' */
define("jira/ajs/avatarpicker/gravatar-util",["jquery","exports"],function(a,r){r.showGravatarHelp=function(r){void 0!==r&&void 0!==r.entry&&(a(".gravatar-signup-text").addClass("hidden"),a(".gravatar-login-text").removeClass("hidden"))},r.displayGravatarHelp=function(){var t=a("#gravatar_json_url");t.length&&a.ajax(t.val(),{dataType:"jsonp",success:r.showGravatarHelp})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/initAvatarPicker.js' */
!function(){var a=require("jquery"),r=require("jira/ajs/avatarpicker/avatar/sizes"),e=require("jira/ajs/avatarpicker/avatar-factory"),t=require("jira/ajs/avatarpicker/avatar-manager-factory"),c=require("jira/ajs/avatarpicker/avatar-picker-factory"),A=require("jira/ajs/avatarpicker/gravatar-util");AJS.namespace("JIRA.Avatar",null,require("jira/ajs/avatarpicker/avatar")),AJS.namespace("JIRA.Avatar.createCustomAvatar",null,e.createCustomAvatar),AJS.namespace("JIRA.Avatar.createSystemAvatar",null,e.createSystemAvatar),AJS.namespace("JIRA.Avatar.getSizeObjectFromName",null,r.getSizeObjectFromName),AJS.namespace("JIRA.Avatar.LARGE",null,r.LARGE),AJS.namespace("JIRA.Avatar.MEDIUM",null,r.MEDIUM),AJS.namespace("JIRA.Avatar.SMALL",null,r.SMALL),AJS.namespace("JIRA.AvatarManager",null,require("jira/ajs/avatarpicker/avatar-manager")),AJS.namespace("JIRA.AvatarManager.createUniversalAvatarManager",null,t.createUniversalAvatarManager),AJS.namespace("JIRA.AvatarManager.createProjectAvatarManager",null,t.createProjectAvatarManager),AJS.namespace("JIRA.AvatarManager.createUserAvatarManager",null,t.createUserAvatarManager),AJS.namespace("JIRA.AvatarPicker",null,require("jira/ajs/avatarpicker/avatar-picker")),AJS.namespace("JIRA.AvatarPicker.ImageEditor",null,require("jira/ajs/avatarpicker/avatar-picker/image-editor")),AJS.namespace("JIRA.AvatarPicker.createUniversalAvatarPicker",null,c.createUniversalAvatarPicker),AJS.namespace("JIRA.AvatarPicker.createProjectAvatarPicker",null,c.createProjectAvatarPicker),AJS.namespace("JIRA.AvatarPicker.createUserAvatarPicker",null,c.createUserAvatarPicker),AJS.namespace("JIRA.createUniversalAvatarPickerDialog",null,c.createUniversalAvatarPickerDialog),AJS.namespace("JIRA.createProjectAvatarPickerDialog",null,c.createProjectAvatarPickerDialog),AJS.namespace("JIRA.createUserAvatarPickerDialog",null,c.createUserAvatarPickerDialog),AJS.namespace("JIRA.AvatarStore",null,require("jira/ajs/avatarpicker/avatar-store")),AJS.namespace("JIRA.AvatarUtil",null,require("jira/ajs/avatarpicker/avatar-util")),AJS.namespace("JIRA.GravatarUtil.showGravatarHelp",null,A.showGravatarHelp),a(function(){c.createUserAvatarPickerDialog({trigger:"#user_avatar_image",username:a("#avatar-owner-id").text(),defaultAvatarId:a("#default-avatar-id").text()}),a("#gravatar_help_params")&&A.displayGravatarHelp()})}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker', location = '/includes/ajs/avatarpicker/AvatarPicker.soy' */
// This file was automatically generated from AvatarPicker.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.AvatarPicker.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.AvatarPicker == 'undefined') { JIRA.Templates.AvatarPicker = {}; }


JIRA.Templates.AvatarPicker.picker = function(opt_data, opt_ignored) {
  var output = '<form class="jira-avatar-upload-form aui top-label" action="#"><input name="id" value="10000" type="hidden" ><input name="pid" value="10000" type="hidden" ><div class="field-group"><label for="jira-avatar-uploader">' + soy.$$escapeHtml('\u4e0a\u4f20\u4e00\u4e2a\u65b0\u56fe\u50cf') + '</label><input type="file" class="ignore-inline-attach" name="avatar" id="jira-avatar-uploader"/><div class="error"></div></div></form><div class="form-body"><ul class="jira-avatars">';
  var avatarList6 = opt_data.avatars.system;
  var avatarListLen6 = avatarList6.length;
  for (var avatarIndex6 = 0; avatarIndex6 < avatarListLen6; avatarIndex6++) {
    var avatarData6 = avatarList6[avatarIndex6];
    output += '<li class="jira-avatar jira-system-avatar ' + ((avatarData6.isSelected) ? 'jira-selected-avatar' : '') + '" title="Select this Avatar" data-id="' + soy.$$escapeHtml(avatarData6.id) + '"><button data-id="' + soy.$$escapeHtml(avatarData6.id) + '" class="jira-icon-button" title="' + soy.$$escapeHtml('\u9009\u62e9\u8fd9\u4e2a\u56fe\u50cf') + '"><img id="avatar-' + soy.$$escapeHtml(avatarData6.id) + '" src="' + soy.$$escapeHtml(avatarData6.src) + '" width="' + soy.$$escapeHtml(avatarData6.width) + '" height="' + soy.$$escapeHtml(avatarData6.height) + '" alt="' + soy.$$escapeHtml('\u9009\u62e9\u8fd9\u4e2a\u56fe\u50cf') + '"/></button></li>';
  }
  var avatarList29 = opt_data.avatars.custom;
  var avatarListLen29 = avatarList29.length;
  for (var avatarIndex29 = 0; avatarIndex29 < avatarListLen29; avatarIndex29++) {
    var avatarData29 = avatarList29[avatarIndex29];
    output += '<li class="jira-avatar jira-custom-avatar ' + ((avatarData29.isSelected) ? 'jira-selected-avatar' : '') + '" title="Select this avatar" data-id="' + soy.$$escapeHtml(avatarData29.id) + '"><button data-id="' + soy.$$escapeHtml(avatarData29.id) + '" class="jira-icon-button" title="' + soy.$$escapeHtml('\u9009\u62e9\u8fd9\u4e2a\u56fe\u50cf') + '"><img id="avatar-' + soy.$$escapeHtml(avatarData29.id) + '" src="' + soy.$$escapeHtml(avatarData29.src) + '" width="' + soy.$$escapeHtml(avatarData29.width) + '" height="' + soy.$$escapeHtml(avatarData29.height) + '" alt="' + soy.$$escapeHtml('\u9009\u62e9\u8fd9\u4e2a\u56fe\u50cf') + '" /></button><button class="jira-delete-avatar jira-icon-button" data-id="' + soy.$$escapeHtml(avatarData29.id) + '" title="' + soy.$$escapeHtml('\u5220\u9664\u8fd9\u4e2a\u56fe\u50cf') + '">' + soy.$$escapeHtml('\u5220\u9664\u8fd9\u4e2a\u56fe\u50cf') + '</button></li>';
  }
  output += '</ul></div>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.AvatarPicker.picker.soyTemplateName = 'JIRA.Templates.AvatarPicker.picker';
}


JIRA.Templates.AvatarPicker.imageEditor = function(opt_data, opt_ignored) {
  return '<form id="avataror" class="jira-avatar-cropper-form aui" action="/jira/secure/project/AvatarPicker.jspa"><input type="hidden" name="cropperOffsetX" id="avatar-offsetX" value="' + soy.$$escapeHtml(opt_data.cropperOffsetX) + '"><input type="hidden" name="cropperOffsetY" id="avatar-offsetY" value="' + soy.$$escapeHtml(opt_data.cropperOffsetY) + '"><input type="hidden" name="cropperWidth" id="avatar-width"  value="' + soy.$$escapeHtml(opt_data.cropperWidth) + '"><div class="jira-avatar-cropper-header"><p>' + soy.$$escapeHtml('\u8bf7\u622a\u53d6\u9700\u8981\u4e0a\u4f20\u56fe\u50cf\u7684\u533a\u57df\u3002') + '</p></div><div class="form-body"><div class="avataror"><img src="' + soy.$$escapeHtml(opt_data.url) + '" height="300" /></div></div><div class="form-footer buttons-container"><div class="buttons"><input type="submit" class="aui-button aui-button-primary" value="' + soy.$$escapeHtml('\u786e\u8ba4') + '"><a class="aui-button aui-button-link cancel" href="#">' + soy.$$escapeHtml('\u53d6\u6d88') + '</a></div></div></form>';
};
if (goog.DEBUG) {
  JIRA.Templates.AvatarPicker.imageEditor.soyTemplateName = 'JIRA.Templates.AvatarPicker.imageEditor';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:create-user-popup-picker', location = '/includes/jira/field/createUserPickerPopupTrigger.js' */
define("jira/field/create-user-picker-popup-trigger",["exports"],function(e){"use strict";e.createUserPickerPopupTrigger=function(e){var r=e.urlBase,t=e.formName,o=e.fieldName,i=e.actionToOpen,n=e.multiSelect,a=e.fieldConfigId,c=e.triggerEvent,l=e.projectIds;return function(e){var s,f=r;if(e.preventDefault(),f+=i||"/secure/popups/UserPickerBrowser.jspa",f+="?formName="+t,f+="&multiSelect="+Boolean(n),f+="&decorator=popup",f+="&element="+o,a&&(f+="&fieldConfigId="+a),l){l=[].concat(l);var p=!0,u=!1,d=void 0;try{for(var g,m=l[Symbol.iterator]();!(p=(g=m.next()).done);p=!0){f+="&projectId="+g.value}}catch(e){u=!0,d=e}finally{try{!p&&m.return&&m.return()}finally{if(u)throw d}}}c&&(f+="&triggerEvent="+c),s=window.open(f,"UserPicker","status=yes,resizable=yes,top=100,left=100,width=800,height=750,scrollbars=yes"),s.opener=self,s.focus()}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:user-pickers', location = '/includes/jira/field/userPickerUtil.js' */
define("jira/field/user-picker-util",["jira/ajs/list/item-descriptor","jira/ajs/list/group-descriptor","jquery"],function(e,i,r){return{formatResponse:function(t){var a=[];return r(t).each(function(t,l){var s=new i({weight:t,label:l.footer});r(l.users).each(function(){s.addItem(new e({value:this.name,label:this.displayName,html:this.html,icon:this.avatarUrl,iconType:"rounded",allowDuplicate:!1,highlighted:!0,userKey:this.key}))}),a.push(s)}),a}}}),AJS.namespace("JIRA.UserPickerUtil",null,require("jira/field/user-picker-util"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:user-pickers', location = '/includes/jira/field/initSingleUserPickers.js' */
define("jira/field/init-single-user-pickers",["jquery","wrm/context-path","jira/ajs/select/single-select","jira/util/events/reasons","jira/util/events/types","jira/util/events","jira/field/user-picker-util","jira/util/formatter","jira/field/create-user-picker-popup-trigger","jira/ajs/list/item-descriptor"],function(e,r,t,i,a,n,s,o,p,u){"use strict";function c(e){return e&&-1!==e.indexOf("customfield_")}function l(r){e(".js-default-user-picker",r).each(function(){var r=e(this);if(!r.data("aui-ss")){var i=c(r.attr("name")),a=i?"/rest/api/1.0/users/picker":"/rest/api/2/user/picker",n={showAvatar:!0,fieldName:i?r.prop("name"):void 0,fieldConfigId:i?r.data("field-config-id"):void 0,projectId:i?r.data("project-ids").split(",").filter(function(e){return e}):void 0},l=r.data("inputValue");r.data("has-popup-picker")&&r.next(".popup-trigger").on("click",p.createUserPickerPopupTrigger({urlBase:d,formName:r.data("form-name")||r.closest("form").attr("name"),fieldName:r.prop("name"),fieldConfigId:r.data("field-config-id"),projectIds:r.data("project-ids").split(",").filter(function(e){return e}),triggerEvent:"userpicker:onPopUpSelection"})),new t({element:r,submitInputVal:!0,hasAddonIcon:!!r.data("has-popup-picker"),showDropdownButton:!!r.data("show-dropdown-button"),errorMessage:o.format("\u6ca1\u6709\u7528\u6237 \'\'{0}\'\' \u3002","'{0}'"),iconType:"rounded",ajaxOptions:{url:d+a,query:!0,data:n,formatResponse:s.formatResponse},inputText:l,ariaLabel:"\u9009\u62e9\u4e00\u4e2a\u7528\u6237",events:{element:{"userpicker:onPopUpSelection":function(r,t,i,a){e.ajax({url:d+"/rest/api/2/user?username="+a,type:"GET",contentType:"application/json",success:function(e){i.setSelection(new u({value:a,label:e.displayName,icon:e.avatarUrls["24x24"]}),!0)},error:function(e){return console.error(e)}})}}}})}})}var d=r();n.bind(a.NEW_CONTENT_ADDED,function(e,r,t){t!==i.panelRefreshed&&l(r)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:user-pickers', location = '/includes/jira/field/NoBrowseUserNamePicker.js' */
define("jira/field/no-browser-user-name-picker",["require"],function(e){"use strict";var t=e("jquery"),r=e("jira/ajs/list/item-descriptor"),i=e("jira/ajs/select/multi-select"),a=e("jira/util/formatter"),n=e("wrm/context-path"),s=n();return i.extend({_getDefaultOptions:function(){return t.extend(!0,this._super(),{errorMessage:"\u5bfb\u6c42\u7684\u7528\u6237\u4e0d\u5b58\u5728\u3002",showDropdownButton:!1,removeOnUnSelect:!0,itemAttrDisplayed:"label"})},_handleCharacterInput:function(){},_setSuggestions:function(){},_handleServerError:function(e){404===e.status?this.showErrorMessage():this._super()},_deactivate:function(){this.validateAndAdd()},validateAndAdd:function(){var e=this;""===t.trim(this.$field.val())?this.hideErrorMessage():t.ajax({url:s+"/rest/api/2/user",data:{username:t.trim(e.getQueryVal())},success:function(t){e.hideErrorMessage(),e.$field.val(""),e.addItem(new r({label:t.displayName,value:t.name}))},error:function(){e.showErrorMessage()}})},_handleSpace:function(){this.validate()},_handleServerSuggestions:function(){this.hideErrorMessage(),this.handleFreeInput()},handleFreeInput:function(){var e=t.trim(this.$field.val());""!==e&&(this.addItem({value:e,label:e}),this.model.$element.trigger("change")),this.$field.val("")},keys:{Return:function(e){e.preventDefault(),this.validateAndAdd()},Spacebar:function(e){e.preventDefault(),this.validateAndAdd()}}})}),AJS.namespace("AJS.NoBrowseUserNamePicker",null,require("jira/field/no-browser-user-name-picker"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:user-pickers', location = '/includes/jira/field/initMultiUserPickers.js' */
define("jira/field/init-multi-user-pickers",["jquery","wrm/context-path","jira/ajs/select/multi-select","jira/util/events/reasons","jira/util/events/types","jira/util/events","jira/field/no-browser-user-name-picker","jira/field/user-picker-util"],function(e,t,r,i,n,s,a,u){function l(t){t.find(".js-default-multi-user-picker").each(function(){var t=e(this);AJS.params.currentUserCanBrowseUsers?new r({element:this,itemAttrDisplayed:"label",showDropdownButton:!1,removeOnUnSelect:!0,submitInputVal:!0,ajaxOptions:{url:o+"/rest/api/1.0/users/picker",query:!0,data:function(e){return{showAvatar:!0,query:e,exclude:t.val()}},formatResponse:u.formatResponse}}):new a({element:this})})}var o=t();s.bind(n.NEW_CONTENT_ADDED,function(e,t,r){r!==i.panelRefreshed&&l(t)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:user-pickers', location = '/includes/jira/field/init/init-user-pickers-webresource.js' */
require("jira/field/init-multi-user-pickers"),require("jira/field/init-single-user-pickers");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-label-lozenge', location = '/includes/jira/admin/group-browser/group-label-lozenge.js' */
define("jira/admin/group-browser/group-label-lozenge",["jquery","jira/skate"],function(e,t){t("group-label-lozenge",{type:t.type.CLASSNAME,attached:function(t){e(t).tooltip({gravity:"w",html:!0})}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-label-lozenge', location = '/includes/jira/admin/group-browser/group-label-lozenge.soy' */
// This file was automatically generated from group-label-lozenge.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }


JIRA.Templates.groupLabelLozenge = function(opt_data, opt_ignored) {
  return '' + aui.lozenges.lozenge({text: opt_data.label.text, title: opt_data.label.title, isSubtle: true, extraClasses: 'group-label-lozenge'});
};
if (goog.DEBUG) {
  JIRA.Templates.groupLabelLozenge.soyTemplateName = 'JIRA.Templates.groupLabelLozenge';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:groupbrowser', location = '/includes/jira/admin/group-browser/group-browser.js' */
require(["jquery","jira/admin/group-browser/group-label-lozenge"],function(i){i(function(){i(".operations-list .aui-button[disabled]").tooltip({gravity:"e",html:!1})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-pickers', location = '/includes/jira/field/groupPickerUtil.js' */
define("jira/field/group-picker-util",["jira/ajs/list/item-descriptor","jira/ajs/list/group-descriptor","jquery"],function(e,i,r){var t=function(t,a){var n=[],s=a?JIRA.Templates.GroupPickerUtil.formatResponseWithLabels:JIRA.Templates.GroupPickerUtil.formatResponse;return r(t).each(function(t,a){var u=new i({weight:t,label:a.header});r(a.groups).each(function(){u.addItem(new e({value:this.name,label:this.name,title:this.name,html:s(this),highlighted:!0}))}),n.push(u)}),n};return{formatResponseWithLabels:function(e){return t(e,!0)},formatResponse:function(e,i){return t(e,i)}}}),AJS.namespace("JIRA.GroupPickerUtil",null,require("jira/field/group-picker-util"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-pickers', location = '/includes/jira/field/templates/groupPickerUtil.soy' */
// This file was automatically generated from groupPickerUtil.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.GroupPickerUtil.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.GroupPickerUtil == 'undefined') { JIRA.Templates.GroupPickerUtil = {}; }


JIRA.Templates.GroupPickerUtil.formatResponseWithLabels = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.GroupPickerUtil.formatResponse(soy.$$augmentMap(opt_data, {showLabels: true}));
};
if (goog.DEBUG) {
  JIRA.Templates.GroupPickerUtil.formatResponseWithLabels.soyTemplateName = 'JIRA.Templates.GroupPickerUtil.formatResponseWithLabels';
}


JIRA.Templates.GroupPickerUtil.formatResponse = function(opt_data, opt_ignored) {
  var output = '<div class="group-suggestion-item"><span class="group-suggestion-item__name">' + soy.$$filterNoAutoescape(opt_data.html) + '</span>';
  if (opt_data.showLabels) {
    output += '<span class="group-suggestion-item__labels group-labels-lozenges">';
    var labelList13 = opt_data.labels;
    var labelListLen13 = labelList13.length;
    for (var labelIndex13 = 0; labelIndex13 < labelListLen13; labelIndex13++) {
      var labelData13 = labelList13[labelIndex13];
      output += JIRA.Templates.groupLabelLozenge({label: labelData13}) + ' ';
    }
    output += '</span>';
  }
  output += '</div>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.GroupPickerUtil.formatResponse.soyTemplateName = 'JIRA.Templates.GroupPickerUtil.formatResponse';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-pickers', location = '/includes/jira/field/initMultiGroupPickers.js' */
define("jira/field/init-multi-group-pickers",["jquery","wrm/context-path","jira/ajs/select/multi-select","jira/util/events/reasons","jira/util/events/types","jira/util/events","jira/field/group-picker-util"],function(e,t,i,r,a,s,n){"use strict";function u(t){t.find(".js-default-multi-group-picker").each(function(){var t=e(this),r=!0===t.data("show-labels"),a=t.data("user-name");new i({element:this,itemAttrDisplayed:"label",showDropdownButton:!1,ajaxOptions:{data:function(e){return{userName:a,query:e,exclude:t.val()}},url:l+"/rest/api/2/groups/picker",query:!0,formatResponse:r?n.formatResponseWithLabels:n.formatResponse}})})}var l=t();s.bind(a.NEW_CONTENT_ADDED,function(e,t,i){i!==r.panelRefreshed&&u(t)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-pickers', location = '/includes/jira/field/initSingleGroupPickers.js' */
define("jira/field/init-single-group-pickers",["jira/ajs/select/single-select","jquery","wrm/context-path","jira/ajs/list/item-descriptor","jira/util/events/reasons","jira/util/events/types","jira/util/events","jira/field/group-picker-util"],function(e,t,i,a,n,r,s,l){"use strict";function u(i){i.find(".js-default-single-group-picker").each(function(){var i=t(this),n=i.find("option[data-empty]"),r=!0===i.data("show-labels"),s=i.data("user-name");new e({element:this,itemAttrDisplayed:"label",revertOnInvalid:!0,ajaxOptions:{data:function(e){return{userName:s,query:e,exclude:i.val()}},url:o+"/rest/api/2/groups/picker",query:!0,formatResponse:function(e){var t=l.formatResponse(e,r);return n.length&&""!==i.val()&&t.unshift(new a({value:"",label:n.text(),highlighted:!0})),t}}})})}var o=i();s.bind(r.NEW_CONTENT_ADDED,function(e,t,i){i!==n.panelRefreshed&&u(t)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:group-pickers', location = '/includes/jira/field/init/init-group-pickers-webresource.js' */
require("jira/field/init-multi-group-pickers"),require("jira/field/init-single-group-pickers");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-project-issuetype-fields', location = '/includes/jira/field/ProjectIssueTypeSelect.js' */
define("jira/field/project-issue-type-select",["jira/lib/class"],function(e){return e.extend({init:function(e){var s,t=this;this.$project=e.project,this.issueTypeSelect=e.issueTypeSelect,this.$projectIssueTypesSchemes=e.projectIssueTypesSchemes,this.$issueTypeSchemeIssueDefaults=e.issueTypeSchemeIssueDefaults,this.projectIssueTypeSchemes=JSON.parse(this.$projectIssueTypesSchemes.html()),this.issueTypesSchemeDefaults=JSON.parse(this.$issueTypeSchemeIssueDefaults.html()||"[]"),t.$project.length>0&&(s=t.$project.val(),t.setIssueTypeScheme(t.getIssueTypeSchemeForProject(s)),this.$project.change(function(){var e=t.$project.val();t.setIssueTypeScheme(t.getIssueTypeSchemeForProject(e))}))},getIssueTypeSchemeForProject:function(e){return this.projectIssueTypeSchemes[e]},getDefaultIssueTypeForScheme:function(e){return this.issueTypesSchemeDefaults[e]},setIssueTypeScheme:function(e){var s=this.issueTypeSelect.model.getValue();this.issueTypeSelect.model.setFilterGroup(e),this.issueTypeSelect.model.setSelected(s,!1)||this.setDefaultIssueType(e),this.issueTypeSelect.model.$element.data("project",this.$project.val())},setDefaultIssueType:function(e){var s=this.getDefaultIssueTypeForScheme(e),t=this.issueTypeSelect.model.getDescriptor(s);t||(t=this.issueTypeSelect.model.getAllDescriptors()[0]),this.issueTypeSelect.setSelection(t,!1)}})}),AJS.namespace("JIRA.ProjectIssueTypeSelect",null,require("jira/field/project-issue-type-select"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-project-issuetype-fields', location = '/includes/jira/field/initProjectIssueTypeSelect.js' */
define("jira/field/init-project-issue-type-select",["jira/ajs/select/suggestion-collection-model","jquery","jira/ajs/select/single-select","jira/util/events/reasons","jira/util/events/types","jira/util/events","jira/field/project-issue-type-select"],function(e,t,i,s,n,l,r){function c(s){var n=s||t("body");n.find(".issuetype-field").each(function(s){var l=n.find(".project-field, .project-field-readonly"),c=t(this),d=n.find("#"+c.attr("id")+"-projects"),o=n.find("#"+c.attr("id")+"-defaults"),a=new i({element:c,revertOnInvalid:!0,model:e});a.model.remove(""),l.length>0&&new r({project:l.eq(s),issueTypeSelect:a,projectIssueTypesSchemes:d,issueTypeSchemeIssueDefaults:o})})}l.bind(n.NEW_CONTENT_ADDED,function(e,t,i){i!==s.panelRefreshed&&c(t)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-project-issuetype-fields', location = '/includes/jira/field/initProjectPickers.js' */
define("jira/field/init-project-pickers",["jira/ajs/select/scrollable-single-select","jira/ajs/select/suggestion-collection-model","jira/util/events/reasons","jira/util/events/types","jira/util/events"],function(e,i,n,t,l){function s(n){n.find(".project-field").each(function(){new e({element:this,revertOnInvalid:!0,pageSize:50,pagingThreshold:100,model:i})})}l.bind(t.NEW_CONTENT_ADDED,function(e,i,t){t!==n.panelRefreshed&&s(i)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-project-issuetype-fields', location = '/includes/jira/field/init/init-jira-project-issuetype-fields-webresource.js' */
require("jira/field/init-project-issue-type-select"),require("jira/field/init-project-pickers");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:status-category-picker', location = '/includes/jira/field/statusCategoryPicker.js' */
define("jira/field/status-category-single-select",["jira/ajs/select/single-select","jquery"],function(e,t){function s(e){var s,i=t();return e&&e.model&&(s=t.extend({},e.model().data()),delete s.descriptor,i=t(JIRA.Template.Util.Issue.Status.issueStatus({issueStatus:{name:e.label(),statusCategory:s},isCompact:!0})).removeClass("jira-issue-status-lozenge-tooltip").removeAttr("title").removeAttr("data-tooltip")),i}return e.extend({_hasIcon:function(){return this.$field.val()&&this.$field.val()!==this.model.placeholder},setSelection:function(e){this._super(e),this.$container.find(".fake-ss-icon").remove(),this.$container.append(s(e).addClass("fake-ss-icon aui-ss-entity-icon"))},init:function(e){this._super(e);var t=this.listController._renders.suggestion;this.listController._renders.suggestion=function(e){var i=t.apply(this,arguments);return i.find("a").prepend("&nbsp;").prepend(s(e)),i}},_renders:{entityIcon:function(e){return t()}}})}),AJS.namespace("JIRA.StatusCategorySingleSelect",null,require("jira/field/status-category-single-select"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:status-category-picker', location = '/includes/jira/field/initStatusPickers.js' */
define("jira/field/init-status-pickers",["jira/util/events/reasons","jira/util/events/types","jira/util/events","jira/field/status-category-single-select"],function(e,t,i,n){function s(e){e.find("select#statusCategory").each(function(e,t){new n({element:t,revertOnInvalid:!0})})}i.bind(t.NEW_CONTENT_ADDED,function(t,i,n){n!==e.panelRefreshed&&s(i)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:status-category-picker', location = '/includes/jira/field/init/init-status-category-picker-webresource.js' */
require("jira/field/init-status-pickers");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-project-data', location = 'includes/jira/project/project-data.js' */
define("jira/project/projectdata",["wrm/data"],function(t){"use strict";var r=t.claim("jira.core:jira-project-data-data.data");return{getProjectType:function(){return r.projectType}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-admin-data', location = 'includes/jira/admin/admin-data.js' */
define("jira/admin/admindata",["wrm/data"],function(i){"use strict";var a=i.claim("jira.core:jira-admin-data-data.data");return{isUserAdmin:function(){return a.isAdmin},isUserSysAdmin:function(){return a.isSysAdmin}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-admin-analytics', location = '/includes/jira/admin/analytics.js' */
var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};define("jira/admin/analytics",["jira/project/projectdata","jira/util/events","jira/admin/admindata","jira/common/page","wrm/context-path","jira/util/data/meta","jquery","jira/analytics","jira/libs/parse-uri"],function(t,e,o,n,i,c,r,a,f){function l(t,e,o){return o="object"!==(void 0===o?"undefined":_typeof(o))?{}:o,e=e||"unknown",{name:"administration."+t+"."+e,properties:r.extend({type:e,tab:c.get("admin.active.tab"),section:c.get("admin.active.section")},o)}}function d(t,e){a.send(l("projectconfig.workflowscheme",t,e))}function s(t,e){a.send(l("workflowscheme",t,e))}function w(){return!!r(".status-draft").length}function u(t,e){return l("workflow",t,e)}function p(t,e){a.send(l("navigate",t,e))}function k(t,e){a.send(l("projectconfig.workflow",t,e))}function m(t,e){S.on("click",t,function(){var t=r(this).data("mode");a.send(u("selectmode",{mode:t,edit:e}))})}function g(t,e,o){a.send(u("edit",{mode:t,action:e,object:o}))}function j(){k("loadworkflowstab",{referrer:y(document.referrer)})}function h(t,e,o){S.on("click",t,function(){g("text",e,o)})}function v(t,e){"true"!==r(e.currentTarget).attr("aria-pressed")&&a.send({name:"projectconfig.toggle."+t})}function y(t){if("string"!=typeof t)return null;var e,o=f(t),n=c.get("projectKey");return e=o.path.slice(i().length),e=e.replace(/project-config\/(.*?)(\/|$)/,"project-config/PROJECTKEY$2"),n&&n.length&&(e=e.split(n).join("PROJECTKEY")),e}function b(){e.bind("addworkflow",function(){w()&&s("addworkflowtodraft")}),e.bind("assignissuetypes",function(){w()&&s("assignissuetypestodraft")}),S.on("click",".remove-all-issue-types",function(){w()&&s("deleteworkflowfromdraft")}),S.on("click","#discard-draft-dialog input[type=submit]",function(){x?d("discarddraft"):s("discarddraft")}),S.on("click","#view-original",function(){s("vieworiginalofdraft")}),S.on("click","#view-draft",function(){s("viewdraft")}),S.on("click","#publish-draft",function(){s("publishclicked")}),x||"draftMigrationSuccess"!==f(location).anchor||s("migrationcompleted"),e.bind("draftcreated",function(){x?d("draftcreated"):s("draftcreated")}),S.on("click","#workflowscheme-editor .workflow-text-view",function(){s("viewworkflow",{mode:"text"})}),S.on("click","#workflowscheme-editor .workflow-diagram-view",function(){s("viewworkflow",{mode:"diagram"})}),S.on("click","#project-config-panel-screens a.project-config-screen",function(){var t=r(this).data("operationId");1===t?a.send({name:"administration.screenschemes.editlink.clicked.operation.edit"}):2===t?a.send({name:"administration.screenschemes.editlink.clicked.operation.view"}):a.send({name:"administration.screenschemes.editlink.clicked.operation.create"})}),S.on("click",".project-config-more-link",function(){var t=r(this);p("morelink",{href:y(t.attr("href")),title:t.attr("title")})}),n.relatedContentElement().on("click","a",function(){var e=r(this),n=e.attr("id");n=/^view_project_issuetype_\d+$/.test(n)?"view_project_issuetype":n,p("tabs",{id:n,href:y(e.attr("href")),title:e.text(),isAdmin:o.isUserAdmin(),isSysAdmin:o.isUserSysAdmin(),projectType:t.getProjectType()})}),S.on("click","#project-config-header-name",function(){p("projectheader")}),S.on("click","#project-config-header-avatar",function(){p("projectavatar")}),S.on("click",".back-to-proj-config",function(){p("backtoproject",{href:y(r(this).attr("href"))})}),S.on("click",".project-config-summary-scheme a",function(){p("selectedscheme",{href:y(r(this).attr("href"))})}),S.on("click",".project-config-workflow-text-link",function(){k("viewastext")}),S.on("click",".project-config-screen",function(){k("gotoscreen")}),S.on("click",".project-config-workflow-edit",function(){k(r(this).closest(".project-config-workflow-default").length?"copyworkflow":"editworkflow")}),S.on("click",".issuetypeconfig-edit-workflow",function(){k("editworkflowissuetype")}),S.on("click","#toggleWorkflowTab",function(t){v("workflow",t)}),S.on("click","#toggleViewTab",function(t){v("view",t)}),S.on("click",".project-config-workflow-diagram-container",function(){k("enlargeworkflow")}),S.on("click","#project-config-workflows-view-original",function(){d("vieworiginalofdraft")}),S.on("click","#project-config-workflows-view-draft",function(){d("viewdraft")}),S.on("click","#project-config-workflows-scheme-change",function(){d("switchscheme")}),S.on("click","#project_import_link_lnk",function(){p("topnav.jim")}),m(".workflow-view-toggle",!1),m(".workflow-edit-toggle",!0),h("#workflow-step-add-submit","add","step"),h("#workflow-step-update","update","step"),h("#workflow-step-delete","remove","step"),h("#workflow-transition-add","add","transition"),h("#workflow-transition-update","update","transition"),h("#workflow-transition-delete","remove","transition"),h("#workflow-global-transition-update","update","globaltransition"),e.bind("wfd-edit-action",function(t,e){g("diagram",e.action,e.object)})}var S=r(document),_=c.get("admin.active.section"),x="atl.jira.proj.config"===_;return{bindEvents:b,event:l,sendProjectWorkflowSchemeEvent:d,sendLoadWorkflowsTabEvent:j}}),AJS.namespace("JIRA.Analytics",null,require("jira/admin/analytics"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-admin-analytics', location = '/includes/jira/admin/analytics-init.js' */
require(["underscore","jira/util/data/meta","jira/util/init-on-dcl","jira/admin/analytics"],function(i,n,a,t){a(function(){var a=n.get("admin.active.tab");i.defer(function(){t.bindEvents(),"view_project_workflows"===a&&t.sendLoadWorkflowsTabEvent()})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.splitchunk.16f099a0da', location = 'aui.chunk.c1eec30b869275a5639b--cfb8145d49a8309c362c.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.splitchunk.16f099a0da"],{jJ4L:function(i,n,o){"use strict";o.r(n);o("FStl"),o("Q0fs"),o("oTK+"),o("S3ao"),o("HNID"),o("YQ7q"),o("xjlV")},xjlV:function(i,n,o){}}]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.pattern.table', location = 'aui.chunk.34ea24d53f7fdc2e1bf5--bb25ccc50e0b825f5bd3.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.pattern.table"],[],[["jJ4L","runtime","aui.splitchunk.fbbef27525","aui.splitchunk.056561461c","aui.splitchunk.949297951c","aui.splitchunk.d7c46c2734","aui.splitchunk.fb15cffa72","aui.splitchunk.862f26d10a","aui.splitchunk.b2ecdd4179","aui.splitchunk.16f099a0da"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-admin', location = '/includes/jira/admin/initAdmin.js' */
define("jira/admin/init/admin",["jira/util/events","jira/util/events/types","jira/util/events/reasons","jquery"],function(e,t,i,n){"use strict";e.bind(t.NEW_CONTENT_ADDED,function(e,t,r){r!==i.panelRefreshed&&t.find(".jira-iconpicker-trigger").click(function(e){e.preventDefault();var t=n(this),i=t.attr("href");t.prev("input.text").length&&window.open(i,"IconPicker","status=no,resizable=yes,top=100,left=200,width=580,height=650,scrollbars=yes").focus()})}),n(function(){var e=function(e,t){n('<input type="hidden" />').attr({name:t,value:t}).appendTo(e),e.trigger("submit",{operationNameSet:!0})},t=function(t){var i=n(t.target),r=i.closest("form"),o="";o=i.attr("name").toLowerCase().indexOf("join")>-1?"join":"leave",e(r,o)};n(document.body).on("keypress","#userGroupPicker select",function(e){13===e.which&&t.apply(this,arguments)}).on("click","#userGroupPicker .aui-button",function(e){e.preventDefault(),t.apply(this,arguments)}).on("submit","#user-edit-groups",function(t,i){if(!i||!i.operationNameSet){var r=n(t.target);t.preventDefault(),e(r,"join")}})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-admin', location = '/includes/jira/admin/edit-application-properties.js' */
require(["jquery","backbone","jira/util/init-on-dcl"],function(e,i,t){"use strict";var a=i.View.extend({events:{"click input[name=useGravatar]:checked":"_onUseGravatarClicked"},initialize:function(e){i.View.prototype.initialize.call(this,arguments),this._gravatarServer=this.$el.find(".gravatar-server")},_onUseGravatarClicked:function(e){var i="true"===this.$(e.target).val();this._gravatarServer.toggleClass("hidden",!i)}});t(function(){e("#edit-application-properties").each(function(){new a({el:this})})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-admin', location = '/includes/jira/admin/init/init-jira-admin-webresource.js' */
require("jira/admin/init/admin");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.pattern.help', location = 'aui.chunk.a4ef880b74481afdfd7f--2417244617acd3db014b.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.pattern.help"],{EoRc:function(i,u,n){},H4sa:function(i,u,n){"use strict";n.r(u);n("FStl"),n("Q0fs"),n("nqD9"),n("0FWE"),n("VtiI"),n("EoRc")}},[["H4sa","runtime","aui.splitchunk.fbbef27525","aui.splitchunk.056561461c","aui.splitchunk.949297951c","aui.splitchunk.d925afe2c0","aui.splitchunk.d727dd207f","aui.splitchunk.7da3927366"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.helptips.jira-help-tips:analytics', location = 'util/analytics.js' */
define("jira-help-tips/util/analytics",["jira/analytics"],function(t){"use strict";function e(t){return t=""+(t||""),t.replace(/\./g,"-")}function i(i,n){if(n&&n.attributes.id){var r={},a=e(n.attributes.id),s="";n.attributes.eventPrefix&&(s=n.attributes.eventPrefix,"."!==s.charAt(s.length-1)&&(s+=".")),r.name=s+"helptips."+a+"."+i,r.properties={},t.send(r)}}return{clean:e,send:i}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager', location = 'js/HelpTipManager.js' */
define("jira-help-tips/feature/help-tip-manager",["require"],function(s){"use strict";var e=s("underscore"),i=s("jquery"),t=s("jira/jquery/deferred"),n=s("wrm/context-path"),r=s("wrm/data"),d=n()+"/rest/helptips/1.0/tips",u=r.claim("com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager.JiraHelpTipData");return{dismissedTipIds:[],sequences:[],loaded:new t,url:function(){return d},sync:function(s,e){var n=new t;return s||(s="get"),e||(e=null),"get"===s&&u&&u.dismissed?n.resolve(u.dismissed):i.ajax(this.url(),{type:s,dataType:"json",contentType:"application/json",data:e&&JSON.stringify(e),processData:!1}).done(function(s){n.resolve(s)}).fail(function(){n.reject()}),n.promise()},fetch:function(){var s=this.sync();return s.done(i.proxy(function(s){i.merge(this.dismissedTipIds,s),this.loaded.resolve()},this)),s.promise()},show:function(s){this.loaded.done(s)},dismiss:function(s){var e=s.attributes.id;e?(this.dismissedTipIds.push(e),this.sync("post",{id:e})):s._dismissed=!0},undismiss:function(s){var e=s.attributes.id;e?(this.dismissedTipIds.splice(i.inArray(e,this.dismissedTipIds),1),this.sync("delete",{id:e})):s._dismissed=!1},isDismissed:function(s){var e=s.attributes.id;return e?i.inArray(e,this.dismissedTipIds)>=0:s._dismissed},clearSequences:function(){this.sequences=[]},hideSequences:function(){e.each(this.sequences,function(s){s.view.dismiss()})},showSequences:function(){if(!this._showStarted){var s=this,t=0;this._showStarted=!0,i.when(this.loaded).done(function(){s.sequences.sort(function(s,e){return s.attributes.weight-e.attributes.weight}),s.sequences=e.filter(s.sequences,function(i){var n=-1===e.indexOf(s.dismissedTipIds,i.attributes.id);return n&&(i.attributes.position=t++),n}),s.sequences.length>0&&(e.last(s.sequences).attributes.showCloseButton=!0,s.sequences[0].show({force:!0})),s._showStarted=!1})}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.helptips.jira-help-tips:help-tip', location = 'templates/HelpTip.soy' */
// This file was automatically generated from HelpTip.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace AJS.Templates.HelpTip.
 */

if (typeof AJS == 'undefined') { var AJS = {}; }
if (typeof AJS.Templates == 'undefined') { AJS.Templates = {}; }
if (typeof AJS.Templates.HelpTip == 'undefined') { AJS.Templates.HelpTip = {}; }


AJS.Templates.HelpTip.tipContent = function(opt_data, opt_ignored) {
  return ((opt_data.title) ? '<h2 class="helptip-title">' + soy.$$escapeHtml(opt_data.title) + '</h2>' : '') + '<p class="helptip-body">' + soy.$$filterNoAutoescape(opt_data.bodyHtml) + '</p>' + ((opt_data.url) ? '<p><a class="helptip-link" href="' + soy.$$escapeHtml(opt_data.url) + '" target="_blank">' + ((opt_data.linkText) ? soy.$$escapeHtml(opt_data.linkText) : soy.$$escapeHtml('\u4e86\u89e3\u66f4\u591a')) + '</a></p>' : '') + AJS.Templates.HelpTip.tipFooter(opt_data);
};
if (goog.DEBUG) {
  AJS.Templates.HelpTip.tipContent.soyTemplateName = 'AJS.Templates.HelpTip.tipContent';
}


AJS.Templates.HelpTip.tipFooter = function(opt_data, opt_ignored) {
  return '<form class="tip-footer">' + AJS.Templates.HelpTip.nextButton(opt_data) + AJS.Templates.HelpTip.closeButton(opt_data) + AJS.Templates.HelpTip.sequencePaging(opt_data) + '</form>';
};
if (goog.DEBUG) {
  AJS.Templates.HelpTip.tipFooter.soyTemplateName = 'AJS.Templates.HelpTip.tipFooter';
}


AJS.Templates.HelpTip.nextButton = function(opt_data, opt_ignored) {
  return '' + ((opt_data.showNextButton) ? '<button class="aui-button helptip-next" type="button">' + ((opt_data.nextButtonText) ? soy.$$escapeHtml(opt_data.nextButtonText) : soy.$$escapeHtml('\u4e0b\u4e00\u6b65')) + '</button>' : '');
};
if (goog.DEBUG) {
  AJS.Templates.HelpTip.nextButton.soyTemplateName = 'AJS.Templates.HelpTip.nextButton';
}


AJS.Templates.HelpTip.closeButton = function(opt_data, opt_ignored) {
  return '' + ((opt_data.showCloseButton) ? '<button class="aui-button ' + ((opt_data.showNextButton) ? ' aui-button-link ' : '') + ' helptip-close" type="button">' + ((opt_data.closeButtonText) ? soy.$$escapeHtml(opt_data.closeButtonText) : soy.$$escapeHtml('\u5173\u95ed')) + '</button>' : '');
};
if (goog.DEBUG) {
  AJS.Templates.HelpTip.closeButton.soyTemplateName = 'AJS.Templates.HelpTip.closeButton';
}


AJS.Templates.HelpTip.sequencePaging = function(opt_data, opt_ignored) {
  return '' + ((opt_data.isSequence && opt_data.length > 1) ? '<span class="helptip-sequence-paging">' + soy.$$escapeHtml(opt_data.position + 1) + '/' + soy.$$escapeHtml(opt_data.length) + '</span>' : '');
};
if (goog.DEBUG) {
  AJS.Templates.HelpTip.sequencePaging.soyTemplateName = 'AJS.Templates.HelpTip.sequencePaging';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.helptips.jira-help-tips:help-tip', location = 'js/HelpTip.js' */
define("jira-help-tips/feature/help-tip",["require"],function(i){"use strict";function t(){return!1}function e(){return!0}function s(){return"jira-help-tip-"+p+d++}var n=i("jquery"),o=i("underscore"),h=i("jira-help-tips/feature/help-tip-manager"),l=i("jira-help-tips/util/analytics"),r=i("aui/inline-dialog"),a=i("jira/jquery/deferred"),u=AJS.Popups,c=AJS.Templates.HelpTip,d=0,p=(new Date).getTime(),f=function(i,t){this.initialize(i,t)},b=function(i){this.initialize(i)},m=function(i){var t;this.attributes=n.extend({},i),this.attributes.id||(this.attributes.id=!1),this.attributes.callbacks||(this.attributes.callbacks={}),this.attributes.isSequence&&(this.attributes.weight||(this.attributes.weight=Number.MAX_VALUE),h.sequences.push(this)),this.attributes.body&&(this.attributes.bodyHtml=this.attributes.body,delete this.attributes.body),this.cid=s(),t=this.attributes.anchor,delete this.attributes.anchor,this.view=t?new f(this,t):new b(this)};return n.extend(m.prototype,{show:function(i){i=i||{};var t=this,e=new a;if(this.attributes.callbacks.beforeShow){var s=this.attributes.callbacks.beforeShow();s&&o.isFunction(s.done)?s.done(e.resolve):e.resolve()}else e.resolve();e.done(function(){h.show(function(){t.isDismissed()||(!i.force&&u&&u.DisplayController?u.DisplayController.request({name:t.id,weight:1e3,show:function(){t.view.show()}}):t.view.show(),l.send("shown",t))})})},dismiss:function(){var i=l.clean(arguments[0]||"programmatically");this.view.dismiss(),"close-button"===i&&this.attributes.isSequence&&h.clearSequences(),this.isDismissed()||(h.dismiss(this),l.send("dismissed."+i,this))},isVisible:function(){return this.view.$el.is(":visible")},isDismissed:function(){return h.isDismissed(this)},refresh:function(){this.isDismissed()||this.view.refresh()},hide:function(){this.isDismissed()||this.view.dismiss()},showNextHelpTipInSequence:function(){this.view.clickNext()}}),n.extend(f.prototype,{initialize:function(i,e){this.model=i,this.anchorSelector=e,this.anchor=n(e),this._initDialog(e),n(document).bind("showLayer",function(e,s,o){"inlineDialog"===s&&o.id===i.cid&&(r.current=null,n(document.body).unbind("click."+i.cid+".inline-dialog-check"),o._validateClickToClose=t,o.hide=t)})},show:function(){this.beforeHide=t,this.popup.show()},refresh:function(){var i=n(this.anchorSelector);i.is(":visible")?i.get(0)!==this.anchor.get(0)?this.changeAnchor(i):this.isVisible()?this.popup.refresh():this.show():this.dismiss()},changeAnchor:function(i){var t=this.isVisible();this.dismiss(),this.$el.remove(),this.anchor=i,this._initDialog(i),t&&this.show()},dismiss:function(){this.beforeHide=e,this._popupHide()},clickNext:function(){var i=n(this.$el).find(".helptip-next");i.length>0&&i.click()},isVisible:function(){return this.$el.is(":visible")},_initDialog:function(i){var e=this,s=this.model;this.popup=new r(n(i),s.cid,o.bind(this._createDialog,this),o.extend({container:"#content",noBind:!0,preHideCallback:function(){return e.beforeHide()},calculatePositions:function(i,t,e,s){var o=r.opts.calculatePositions(i,t,e,s),h=n(this.container),l=h.offset();return"auto"!==o.popupCss.left&&(o.popupCss.left-=l.left,o.popupCss.right="auto"),o.popupCss.top-=l.top,o},addActiveClass:!1,initCallback:s.attributes.callbacks.init,hideCallback:s.attributes.callbacks.hide,persistent:!0},s.attributes.inlineDialogOpts)),this._popupHide=this.popup.hide,this.popup.hide=t,this.$el=n(this.popup[0]),this.$el.addClass("jira-help-tip aui-help")},_createDialog:function(i,t,e){var s=this,r=h.sequences,a=this.model.attributes.position,u=this.model.attributes.isSequence;i.removeClass("contents"),i.html(n(c.tipContent(o.extend({showNextButton:u&&r.length>1&&a+1<r.length,length:r.length,position:a,showCloseButton:!0},this.model.attributes)))),i.unbind("mouseover mouseout"),i.find(".helptip-link").click(function(){l.send("learn-more.clicked",s.model)}),i.find(".helptip-close").click(function(i){i.preventDefault(),s.model.dismiss("close-button")}),i.find(".helptip-next").click(function(i){i.preventDefault(),s.model.dismiss("next-button");var t=a+1;r[t]&&r[t].show({force:!0})}),e()}}),n.extend(b.prototype,{initialize:function(){this.$el=n("<div></div>"),this.$el.addClass("jira-help-tip aui-help")},show:function(){},dismiss:function(){}}),m});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements-lib', location = 'onboarding/postsetup/postsetup-alert-lib.soy' */
// This file was automatically generated from postsetup-alert-lib.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.PostSetupLib.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.PostSetupLib == 'undefined') { JIRA.Templates.PostSetupLib = {}; }


JIRA.Templates.PostSetupLib.discoveryContentContainer = function(opt_data, opt_ignored) {
  return '<div class="postsetup-discovery-content">' + soy.$$escapeHtml(opt_data.content) + '</div>';
};
if (goog.DEBUG) {
  JIRA.Templates.PostSetupLib.discoveryContentContainer.soyTemplateName = 'JIRA.Templates.PostSetupLib.discoveryContentContainer';
}


JIRA.Templates.PostSetupLib.closeButton = function(opt_data, opt_ignored) {
  return '<a class="postsetup-close-link ' + soy.$$escapeHtml(opt_data.closeElementClass) + '" href="#">' + soy.$$escapeHtml('\u4e0d\u518d\u63d0\u793a') + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.PostSetupLib.closeButton.soyTemplateName = 'JIRA.Templates.PostSetupLib.closeButton';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements-lib', location = 'onboarding/postsetup/postsetup-alert-lib.js' */
define("jira/postsetup/announcements-view-lib",["jira/analytics","jira/util/logger","jira/ajs/ajax/smart-ajax","wrm/context-path","jira/jquery/deferred","jquery","underscore","aui/inline-dialog"],function(n,e,t,i,o,r,u,s){function a(n){return function(e,t){var i=n({popupId:e[0].id,closeElementClass:v,actionElementClass:y});e.on("click","."+v,function(n){n.preventDefault(),e.hide()});var o=JIRA.Templates.PostSetupLib.discoveryContentContainer({content:soydata.VERY_UNSAFE.ordainSanitizedHtml(i)});t.html(o)}}function c(n,e){return function(){return location.pathname.indexOf(n)>=0?e():(new o).reject().promise()}}function l(n){return function(){var e=r(n),t=new o;return e.length>0?t.resolve(e):t.reject(),t.promise()}}function d(n){return function(){return n().then(function(n){function e(){return!!n.isInView({margin:10})&&(t.resolve(n),!0)}var t=new o;return e()||r(window).scroll(u.throttle(function(n){e()&&r(this).unbind(n)},100)),t.promise()})}}function f(n){return function(){return n().then(function(n){var e=new o;return setTimeout(function(){function t(){return 0===r(".aui-flag[open]").length&&(e.resolve(n),!0)}t()||r(document).on("aui-flag-close",function(n){t()&&r(this).unbind(n)})},0),e.promise()})}}function p(n){return function(){return n().then(function(n){function e(){return!r(".aui-inline-dialog").is(":visible")&&(t.resolve(n),!0)}var t=new o,i=setInterval(function(){e()&&clearTimeout(i)},750);return t.promise()})}}function m(e,t){var i=g+"."+e;t=t||{},n.send({name:i,data:t})}function h(n,e){m(n.getId()+".show"),e.on("click","."+v,function(){m(n.getId()+".dismiss")}),e.on("click","."+y,function(){m(n.getId()+".action")})}var g="post.setup.announcement",v="post-setup-discovery-close-js",y="post-setup-action-js",w=function(n,e,t,i,o,r){return{getId:function(){return n},getAnchor:t,buildAnnouncementBody:e,anchorGravity:i||"n",weight:o||0,blockOther:r}},j=function(n,e){var t={weight:0};this.viewsDefinitions=n,this.announcements=u.sortBy(e,function(e){return(n[e]||t).weight})};return j.prototype.createFlags=function(){u.find(this.announcements,function(n){var e=this.viewsDefinitions[n];if(void 0!==e){return this.announce(e)}return!1},this)},j.prototype.announce=function(n){var e=this.addFlag(n);return e.then(u.bind(function(){this.dismiss([n.getId()])},this)),"rejected"!==e.state()&&(n.blockOther||"resolved"===e.state())},j.prototype.dismiss=function(n){t.makeRequest({url:i()+"/rest/onboarding/1/postsetup_announcement/announced",async:!0,contentType:"application/json",type:"POST",dataType:"json",data:JSON.stringify(n)}).done(function(){e.log("Dismissed: "+n)}).fail(function(){e.log("Dismissal failed: "+n)})},j.prototype.addFlag=function(n){var e=n.getId().replace(/[. #>+]/g,"-"),t="post-setup-announcement-"+e;return n.getAnchor().then(function(e){var i=new s(e,t,function(e,t,o){n.buildAnnouncementBody(i,e),o()},{noBind:!0,hideDelay:null,persistent:!0,width:350,gravity:n.anchorGravity});return i.show(null,e),i.addClass("aui-help"),setTimeout(function(){i.scrollIntoView(),e.scrollIntoView()},0),h(n,i),!0}).promise()},{ifOnPage:c,findElement:l,inViewPort:d,holdOnTillFlagsAreGone:f,holdOnTillDialogsAreGone:p,soyTemplateAnnouncementBody:a,closeElementClass:v,actionElementClass:y,GRAVITY_BELOW:"n",GRAVITY_LEFT:"w",AnnouncementsController:j,Announcement:w}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements-lib', location = 'onboarding/postsetup/postsetup-alert-initializer.js' */
define("jira/postsetup/announcements-initializer",["wrm/data","jira/postsetup/announcements-view-lib"],function(n,e){var t=n.claim("com.atlassian.jira.jira-postsetup-announcements-plugin:post-setup-announcements.announcements")||[];return{createFlagsFromDataProvider:function(n){new e.AnnouncementsController(n,t).createFlags()}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:userbrowser-lib', location = '/includes/jira/admin/user-browser/user-browser.soy' */
// This file was automatically generated from user-browser.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.Admin.UserBrowser.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.Admin == 'undefined') { JIRA.Templates.Admin = {}; }
if (typeof JIRA.Templates.Admin.UserBrowser == 'undefined') { JIRA.Templates.Admin.UserBrowser = {}; }


JIRA.Templates.Admin.UserBrowser.userCreatedFlag = function(opt_data, opt_ignored) {
  var output = '';
  var namesSize__soy3 = opt_data.names.length;
  if (namesSize__soy3 > 0) {
    output += '<span class="' + soy.$$escapeHtml(namesSize__soy3 == 1 ? 'user-created-flag-single' : 'user-created-flag-multiple') + '" ' + ((namesSize__soy3 == 1) ? 'data-user-created="' + soy.$$escapeHtml(opt_data.names[0]) + '"' : '') + '>';
    if (namesSize__soy3 == 1) {
      var safeName__soy17 = '' + soy.$$escapeHtml(opt_data.names[0]);
      output += soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} \u521b\u5efa\u6210\u529f','<strong>',safeName__soy17,'</strong>'));
    } else {
      output += soy.$$escapeHtml(AJS.format('{0} \u7528\u6237\u88ab\u6210\u529f\u521b\u5efa',opt_data.names.length));
    }
    output += '</span>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.Admin.UserBrowser.userCreatedFlag.soyTemplateName = 'JIRA.Templates.Admin.UserBrowser.userCreatedFlag';
}


JIRA.Templates.Admin.UserBrowser.userBrowserEmailDiscovery = function(opt_data, opt_ignored) {
  return '<p>' + soy.$$escapeHtml('\u901a\u8fc7\u7535\u90ae\u9080\u8bf7\u7528\u6237\u6ce8\u518c\uff0c\u9700\u8981\u5148\u914d\u7f6eJira\u7535\u90ae\u670d\u52a1\u3002') + '</p><p><a class="aui-button post-setup-action ' + soy.$$escapeHtml(opt_data.actionElementClass) + '" href="' + soy.$$escapeHtml("") + '/secure/admin/OutgoingMailServers.jspa">' + soy.$$escapeHtml('\u914d\u7f6e\u7535\u90ae\u670d\u52a1\u5668') + '</a> ' + JIRA.Templates.PostSetupLib.closeButton(opt_data) + '</p>';
};
if (goog.DEBUG) {
  JIRA.Templates.Admin.UserBrowser.userBrowserEmailDiscovery.soyTemplateName = 'JIRA.Templates.Admin.UserBrowser.userBrowserEmailDiscovery';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:userbrowser-lib', location = '/includes/jira/admin/user-browser/user-browser.js' */
define("jira/admin/user-browser",["jira/util/formatter","jira-help-tips/feature/help-tip","jira/togglelist/toggle-list","jquery","jira/postsetup/announcements-view-lib","jira/postsetup/announcements-initializer"],function(e,i,t,s,n,r){function o(e){var i={"create.user.mail.properties.setup":new n.Announcement("create.user.mail.properties.setup",n.soyTemplateAnnouncementBody(l.userBrowserEmailDiscovery),n.inViewPort(n.findElement(e)),n.GRAVITY_BELOW,30,!0)};r.createFlagsFromDataProvider(i)}var a={},l=JIRA.Templates.Admin.UserBrowser;return a.initToggleLists=function(){s(".toggle-list").each(function(){var e=s(this),i=e.parent();new t({more:e.find("li:gt(4)"),showMoreLink:i.find(".toggle-list__show-more"),showLessLink:i.find(".toggle-list__show-less")})})},a.initNewUsersTip=function(t,n){if(i){var r,a,l=s(t),u=s(n),d=u.parent();l.length&&(a=s("<div></div>").css({position:"fixed","z-index":" -1",height:"1px",width:"1px",top:d.offset().top+d.height()+12,left:d.offset().left+d.width()/2}).insertAfter(d),r=new i({id:"add.new.users",title:"\u5728Jira\u4e2d\u6dfb\u52a0\u65b0\u7528\u6237!",bodyHtml:"\u60a8\u53ef\u4ee5\u901a\u8fc7\u53d1\u9001\u7535\u90ae\u7684\u65b9\u5f0f\u6765\u9080\u8bf7\u65b0\u7528\u6237\u6ce8\u518c\uff0c\u6216\u8005\u901a\u8fc7\u624b\u5de5\u65b9\u5f0f\u76f4\u63a5\u521b\u5efa\u3002",url:u.data("url"),anchor:a,callbacks:{hide:function(){o(t)}}}),l.click(function(){r.dismiss("inviteuser")}),u.click(function(){r.dismiss("createuser")}),r.show(),r.isDismissed()&&o(t))}},a});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:userbrowser', location = '/includes/jira/admin/user-browser/user-browser-flags.js' */
define("jira/admin/user-browser/user-browser-flags",[],function(){return{whenFlagSet:function(e,n){if(window.location.hash==="#"+e){n();var a=location.pathname+location.search;window.history.replaceState&&window.history.replaceState(null,null,a)}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:userbrowser', location = '/includes/jira/admin/user-browser/user-browser-user-created.js' */
define("jira/admin/user-browser/user-created",["jira/util/logger","jquery","underscore","jira/flag","jira/admin/user-browser/user-browser-flags","wrm/data"],function(e,r,a,s,n,u){function i(r){s.showSuccessMsg(null,JIRA.Templates.Admin.UserBrowser.userCreatedFlag({names:r})),e.trace("user-created-flag")}r(function(){n.whenFlagSet("userCreatedFlag",function(){var e=u.claim("UserBrowser:createdUsersDisplayNames");e&&e.length>0&&i(e)})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:userbrowser', location = '/includes/jira/admin/user-browser/user-browser-init.js' */
require(["jira/admin/user-browser","jquery","jira/admin/user-browser/user-created"],function(e,r){r(function(){e.initToggleLists(),e.initNewUsersTip("#invite_user","#create_user")})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-invite-user-plugin:inviteuser-userbrowser-resources', location = 'js/inviteuser-userbrowser.js' */
require(["jira/flag","jira/util/formatter","jquery","jira/admin/user-browser/user-browser-flags"],function(d,b,c,a){c(function(){a.whenFlagSet("userInvitedFlag",function(){d.showSuccessMsg(null,"\u60a8\u5df2\u6210\u529f\u53d1\u9001\u9080\u8bf7\uff01")})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.analytics.analytics-client:js-events', location = 'js/store-1.3.1.js' */
(function(){var b={},d=window,h=d.document;b.disabled=!1;b.set=function(a,b){};b.get=function(a){};b.remove=function(a){};b.clear=function(){};b.transact=function(a,e){var c=b.get(a);"undefined"==typeof c&&(c={});e(c);b.set(a,c)};b.serialize=function(a){return JSON.stringify(a)};b.deserialize=function(a){if("string"==typeof a)return JSON.parse(a)};try{var k="localStorage"in d&&d.localStorage}catch(a){k=!1}if(k){var c=d.localStorage;b.set=function(a,e){if(void 0===e)return b.remove(a);c.setItem(a,
b.serialize(e))};b.get=function(a){return b.deserialize(c.getItem(a))};b.remove=function(a){c.removeItem(a)};b.clear=function(){c.clear()}}else{try{var l="globalStorage"in d&&d.globalStorage&&d.globalStorage[d.location.hostname]}catch(a){l=!1}if(l)c=d.globalStorage[d.location.hostname],b.set=function(a,e){if(void 0===e)return b.remove(a);c[a]=b.serialize(e)},b.get=function(a){return b.deserialize(c[a]&&c[a].value)},b.remove=function(a){delete c[a]},b.clear=function(){for(var a in c)delete c[a]};else if(h.documentElement.addBehavior){d=
function(a){return function(){var e=Array.prototype.slice.call(arguments,0);e.unshift(c);f.appendChild(c);c.addBehavior("#default#userData");c.load("localStorage");e=a.apply(b,e);f.removeChild(c);return e}};try{var g=new ActiveXObject("htmlfile");g.open();g.write('\x3cscript\x3edocument.w\x3dwindow\x3c/script\x3e\x3ciframe src\x3d"/favicon.ico"\x3e\x3c/frame\x3e');g.close();var f=g.w.frames[0].document;c=f.createElement("div")}catch(a){c=h.createElement("div"),f=h.body}b.set=d(function(a,c,d){if(void 0===
d)return b.remove(c);a.setAttribute(c,b.serialize(d));a.save("localStorage")});b.get=d(function(a,c){return b.deserialize(a.getAttribute(c))});b.remove=d(function(a,b){a.removeAttribute(b);a.save("localStorage")});b.clear=d(function(a){var b=a.XMLDocument.documentElement.attributes;a.load("localStorage");for(var c=0,d;d=b[c];c++)a.removeAttribute(d.name);a.save("localStorage")})}}try{b.set("__storejs__","__storejs__"),"__storejs__"!=b.get("__storejs__")&&(b.disabled=!0),b.remove("__storejs__")}catch(a){b.disabled=
!0}"undefined"!=typeof module?module.exports=b:"function"===typeof define&&define.amd?define(b):this.store=b})();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.analytics.analytics-client:js-events', location = 'js/page-visibility.js' */
define("atlassian/analytics/page-visibility",function(){var a=void 0!==document.hidden;return{supported:a,isHidden:function(){return a?document.hidden:!1}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.analytics.analytics-client:js-events', location = 'js/user-activity-xhr-header.js' */
define("atlassian/analytics/user-activity-xhr-header",["atlassian/analytics/page-visibility"],function(c){var d=XMLHttpRequest.prototype.send,e=window.fetch,a=!1;return{install:function(){!a&&c.supported&&(XMLHttpRequest.prototype.send=function(){c.isHidden()&&this.setRequestHeader("x-atlassian-mau-ignore",c.isHidden());d.apply(this,arguments)},e&&(window.fetch=function(a,b){b=b||{};var d=b.headers;c.isHidden()&&(b.headers=d?new Headers(d):new Headers,b.headers.set("x-atlassian-mau-ignore",c.isHidden()));
return e.call(this,a,b)}),a=!0)},uninstall:function(){a&&(XMLHttpRequest.prototype.send=d,e&&(window.fetch=e));a=!1}}});require("atlassian/analytics/user-activity-xhr-header").install();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.analytics.analytics-client:js-events', location = 'js/atlassian-analytics.js' */
(function(r){var t=AJS.$.ajax,u="function"===typeof AJS.contextPath?AJS.contextPath():"",d=null,h=null,k=null,p="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=16*Math.random()|0;return("x"==a?b:b&3|8).toString(16)}),l=function(){var a=[],b;if(0!=AJS.EventQueue.length){a=store.get(h)||a;var c=0;for(b=AJS.EventQueue.length;c<b;++c){var e=AJS.EventQueue[c];e.name&&(e={name:e.name,properties:e.properties,time:e.time||0},a.push(e))}AJS.EventQueue.length=0;store.set(h,a)}},q=0,
m=0,n=function(){function a(){setTimeout(n,Math.min(5E3*Math.pow(2,q=0),3E5))}if(store.get(k))var b=!1;else store.set(k,p),b=store.get(k)===p;if(b)m=0;else if(++m,20>m)return a();try{l();var c=store.get(h);if(!c||!c.length)return a();store.remove(h);if(!v(c))return a();var e=(new Date).getTime();for(b=0;b<c.length;b++)c[b].timeDelta=0<c[b].time?c[b].time-e:b-c.length,delete c[b].time;d=t({type:"POST",url:u+"/rest/analytics/1.0/publish/bulk",data:JSON.stringify(c),contentType:"application/json",dataType:"json"});
d.fail(function(){AJS.EventQueue.concat(c);l();setTimeout(n,Math.min(5E3*Math.pow(2,++q),3E5))});d.done(function(){a()})}finally{store.set(k,null)}},v=function(a){for(var b=a.length-1;0<=b;b--){var c="",e=a[b],g=e.properties;if("undefined"===typeof e.name)c="you must provide a name for the event.";else if("undefined"!==typeof g&&null!==g)if(g.constructor!==Object)c="properties must be an object with key value pairs.";else{var d=void 0;for(d in g)if(g.hasOwnProperty(d)){var f=g[d];"undefined"!==typeof f&&
null!==f&&("number"===typeof f||"string"===typeof f||"boolean"===typeof f)||("undefined"!==typeof f&&null!==f&&f.toString?g[d]=f.toString():g[d]="")}}""!==c&&(AJS.log("WARN: Invalid analytics event detected and ignored, "+c+"\nEvent: "+JSON.stringify(e)),a.splice(b,1))}return a.length};AJS.EventQueue=AJS.EventQueue||[];var w=Array.prototype.push;AJS.EventQueue.push=function(a){a.time=(new Date).getTime();w.call(AJS.EventQueue,a)};AJS.toInit(function(){var a="unknown";"jira"==document.body.id?a="jira":
"com-atlassian-confluence"==document.body.id&&(a="confluence");h="atlassian-analytics."+a;k=h+".lock";setTimeout(n,500);-1<window.location.pathname.indexOf("/secure/admin/ViewApplicationProperties")?AJS.$("[data-property-id\x3d'analytics-enabled']").remove():-1<window.location.pathname.indexOf("/secure/admin/EditApplicationProperties")&&(a=AJS.$(":contains(Enable Atlassian analytics)"),0<a.size()&&(a=a[a.size()-2])&&a.remove())});r(window).on("beforeunload",function(){d&&"resolved"!==d.state()&&"rejected"!==
d.state()&&d.abort();l()});AJS.Analytics={triggerPrivacyPolicySafeEvent:function(a,b){AJS.log("WARN: 'triggerPrivacyPolicySafeEvent' has been deprecated");AJS.EventQueue.push({name:a,properties:b})}};AJS.bind("analytics",function(a,b){AJS.EventQueue.push({name:b.name,properties:b.data})});AJS.bind("analyticsEvent",function(a,b){AJS.EventQueue.push({name:b.name,properties:b.data})})})(AJS.$);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.analytics.analytics-client:policy-update-init', location = 'js/policy-update-init.js' */
AJS.toInit(function(){WRM.data.claim("com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider")&&WRM.require("wrc!com.atlassian.analytics.analytics-client:policy-update")});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.analytics.analytics-client:programmatic-analytics-init', location = 'js/programmatic-analytics-init.js' */
(function(){WRM.data.claim("com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider")&&WRM.require("wrc!com.atlassian.analytics.analytics-client:programmatic-analytics")})();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:application-header-administration-cog-resource', location = 'header/cog.js' */
var NavLinks=(function(a){a.ApplicationHeader=function(b){b.Cog=(function(){var c=function(){var d=AJS.$("#system-admin-menu-content");if(d.length>0){return d}var e=AJS.$("#admin-menu-link-content");if(e.length>0){return e}return AJS.$("#bamboo\\.global\\.header-admin\\.menu")};return{getDropdown:c}}());return b}(a.ApplicationHeader||{});return a}(NavLinks||{}));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:************************-resources', location = 'adminshortcuts/adminshortcuts.soy' */
// This file was automatically generated from adminshortcuts.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace navlinks.templates.adminshortcuts.
 */

if (typeof navlinks == 'undefined') { var navlinks = {}; }
if (typeof navlinks.templates == 'undefined') { navlinks.templates = {}; }
if (typeof navlinks.templates.adminshortcuts == 'undefined') { navlinks.templates.adminshortcuts = {}; }


navlinks.templates.adminshortcuts.section = function(opt_data, opt_ignored) {
  var param5 = '<ul class="aui-list-truncate">';
  var linkList7 = opt_data.links;
  var linkListLen7 = linkList7.length;
  for (var linkIndex7 = 0; linkIndex7 < linkListLen7; linkIndex7++) {
    var linkData7 = linkList7[linkIndex7];
    param5 += '<li><a href="' + soy.$$escapeHtml(linkData7.link) + '">' + soy.$$escapeHtml(linkData7.label) + '</a></li>';
  }
  param5 += '</ul>';
  var output = '' + aui.dropdown2.section({id: 'nl-remote-admin-section', label: '\u5176\u5b83\u5e94\u7528\u7a0b\u5e8f', content: param5});
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.adminshortcuts.section.soyTemplateName = 'navlinks.templates.adminshortcuts.section';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:************************-resources', location = 'adminshortcuts/adminnavlinks.js' */
var NavLinks=(function(a){a.AdminShortcuts=(function(){var c=function(){return AJS.$.ajax({url:AJS.contextPath()+"/rest/menu/latest/admin",cache:false,dataType:"json"})};var b=function(){AJS.$("#nl-remote-admin-section").on("click","a",function(){NL.trackEvent("remoteAdminItemSelected",NL.getCurrentApplication(),$(this).attr("href"))})};return{render:function(){c().done(function(e){e=_.reject(e,function(f){return f.local===true});if(e.length){var d=navlinks.templates.adminshortcuts.section({links:e});a.ApplicationHeader.Cog.getDropdown().append(d);b()}})}}}());return a}(NavLinks||{}));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:************************', location = 'adminshortcuts/adminshortcuts-cdn.js' */
AJS.toInit(function(){if(AJS.DarkFeatures&&AJS.DarkFeatures.isEnabled("rotp.admin.shortcuts")){NavLinks.AdminShortcuts.render()}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:atlassian-ui-popup-display-controller', location = 'popups/DisplayController.js' */
AJS.Popups=AJS.Popups||{};AJS.Popups.DisplayController=function(){var c=[];var a=false;var b=false;AJS.toInit(function(){setTimeout(function(){AJS.Popups.DisplayController.render()},0)});return{request:function(d){c.push(d);if(a&&b===false){this.render()}},render:function(){c.sort(function(e,d){return e.weight-d.weight});a=true;if(c.length!==0){b=true;c[0].show()}}}}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-project-config-plugin:project-config-used-by-lozenge', location = 'global/templates/used-by-lozenge.soy' */
// This file was automatically generated from used-by-lozenge.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.ProjectAdmin.UsedBy.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.ProjectAdmin == 'undefined') { JIRA.Templates.ProjectAdmin = {}; }
if (typeof JIRA.Templates.ProjectAdmin.UsedBy == 'undefined') { JIRA.Templates.ProjectAdmin.UsedBy = {}; }


JIRA.Templates.ProjectAdmin.UsedBy.usedBy = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  return '' + ((opt_data.sharedBy) ? JIRA.Templates.ProjectAdmin.UsedBy.generateUsedBy(soy.$$augmentMap(opt_data, {projects: opt_data.sharedBy.sharedWithProjects ? opt_data.sharedBy.sharedWithProjects : opt_data.sharedBy.projects, hiddenProjectsCount: opt_data.sharedBy.hiddenProjectsCount, projectsTitle: opt_data.projectsTitle ? opt_data.projectsTitle : opt_data.title})) : JIRA.Templates.ProjectAdmin.UsedBy.generateUsedBy(soy.$$augmentMap(opt_data, {projectsTitle: opt_data.projectsTitle ? opt_data.projectsTitle : opt_data.title})));
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.usedBy.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.usedBy';
}


JIRA.Templates.ProjectAdmin.UsedBy.generateUsedBy = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  var output = '';
  var totalProjectsCount__soy13 = (opt_data.projects ? opt_data.projects.length : 0) + (opt_data.hiddenProjectsCount ? opt_data.hiddenProjectsCount : 0);
  var issueTypesCount__soy14 = opt_data.issueTypes ? opt_data.issueTypes.length : 0;
  var minRequiredProjects__soy15 = opt_data.minProjects ? opt_data.minProjects : 1;
  var minRequiredIssueTypes__soy16 = opt_data.minIssueTypes ? opt_data.minIssueTypes : 1;
  var anyProjects__soy17 = totalProjectsCount__soy13 >= minRequiredProjects__soy15;
  var anyIssueTypes__soy18 = issueTypesCount__soy14 >= minRequiredIssueTypes__soy16;
  var localkey__soy19 = '' + ((opt_data.key) ? soy.$$escapeHtml(opt_data.key) : (opt_data.currentIssueTypeId) ? soy.$$escapeHtml(opt_data.currentIssueTypeId) : (opt_data.currentProjectId) ? soy.$$escapeHtml(opt_data.currentProjectId) : soy.$$escapeHtml(Math.floor(Math.random() * 10000)));
  if (anyProjects__soy17 || anyIssueTypes__soy18) {
    var issueTypeId__soy31 = 'project-config-' + localkey__soy19 + '-issuetypes-shared';
    var projectId__soy32 = 'project-config-' + localkey__soy19 + '-projects-shared';
    var issueTypesPopupLink__soy33 = '' + JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopupLink({id: issueTypeId__soy31, issueTypesCount: issueTypesCount__soy14});
    var projectsPopupLink__soy37 = '' + JIRA.Templates.ProjectAdmin.UsedBy.projectPopupLink({id: projectId__soy32, totalProjectsCount: totalProjectsCount__soy13});
    output += '<span class="aui-lozenge used-by"><em>' + ((anyIssueTypes__soy18 && anyProjects__soy17) ? soy.$$filterNoAutoescape(AJS.format('\u5728{0}\u4e2d{1}\u4f7f\u7528',issueTypesPopupLink__soy33,projectsPopupLink__soy37)) : (anyIssueTypes__soy18) ? soy.$$filterNoAutoescape(AJS.format('\u7531{0}\u4f7f\u7528',issueTypesPopupLink__soy33)) : soy.$$filterNoAutoescape(AJS.format('\u7531{0}\u4f7f\u7528',projectsPopupLink__soy37))) + '<used-by-analytics /></em></span>' + ((anyIssueTypes__soy18) ? JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopup(soy.$$augmentMap(opt_data, {id: issueTypeId__soy31, title: opt_data.issueTypesTitle})) : '') + ((anyProjects__soy17) ? JIRA.Templates.ProjectAdmin.UsedBy.projectPopup(soy.$$augmentMap(opt_data, {id: projectId__soy32, title: opt_data.projectsTitle})) : '');
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.generateUsedBy.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.generateUsedBy';
}


JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopupLink = function(opt_data, opt_ignored) {
  return '<a href="#' + soy.$$escapeHtml(opt_data.id) + '" data-aui-trigger aria-controls="' + soy.$$escapeHtml(opt_data.id) + '" class="shared-issuetypes-trigger shared-items-trigger">' + soy.$$escapeHtml(AJS.format('{0,choice,1#{0} issue type|1\x3c{0} issue types}',opt_data.issueTypesCount)) + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopupLink.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopupLink';
}


JIRA.Templates.ProjectAdmin.UsedBy.projectPopupLink = function(opt_data, opt_ignored) {
  return '<a href="#' + soy.$$escapeHtml(opt_data.id) + '" data-aui-trigger aria-controls="' + soy.$$escapeHtml(opt_data.id) + '" class="shared-projects-trigger shared-items-trigger">' + soy.$$escapeHtml(AJS.format('{0,choice,1#{0} project|1\x3c{0} projects}',opt_data.totalProjectsCount)) + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.projectPopupLink.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.projectPopupLink';
}


JIRA.Templates.ProjectAdmin.UsedBy.projectPopup = function(opt_data, opt_ignored) {
  var output = '';
  var projectListCap__soy80 = 15;
  var shouldTrimProjectsList__soy81 = opt_data.projects.length > projectListCap__soy80;
  output += '<aui-inline-dialog ' + ((opt_data.id) ? 'id="' + soy.$$escapeHtml(opt_data.id) + '"' : '') + ' class="shared-items-target shared-projects-target" alignment="bottom center" aria-label="' + soy.$$escapeHtml('View associated items') + '"><div class="shared-items-content">';
  if (opt_data.projects) {
    output += '<h3>' + soy.$$escapeHtml(opt_data.title ? opt_data.title : AJS.format('{0,choice,1#Project|1\x3cProjects}:',opt_data.projects.length)) + '</h3><ul class="shared-items-list shared-projects-list">';
    var iLimit96 = shouldTrimProjectsList__soy81 ? projectListCap__soy80 : opt_data.projects.length;
    for (var i96 = 0; i96 < iLimit96; i96++) {
      var project__soy97 = opt_data.projects[i96];
      output += JIRA.Templates.ProjectAdmin.UsedBy.projectEntry(soy.$$augmentMap(project__soy97, {isCurrent: opt_data.currentProjectId && project__soy97.id == opt_data.currentProjectId}));
    }
    output += '</ul>';
  }
  var notDisplayedProjects__soy101 = (opt_data.hiddenProjectsCount ? opt_data.hiddenProjectsCount : 0) + (shouldTrimProjectsList__soy81 ? opt_data.projects.length - projectListCap__soy80 : 0);
  output += ((notDisplayedProjects__soy101 > 0) ? '<p>' + ((opt_data.projects.length > 0) ? soy.$$escapeHtml(AJS.format('\u9690\u85cf\u7684{0,choice,1#Project|1\x3cProjects}{0}',notDisplayedProjects__soy101)) : soy.$$escapeHtml(AJS.format('\u9690\u85cf\u7684{0,choice,1#Project|1\x3cProjects}{0}',notDisplayedProjects__soy101))) + '</p>' : '') + '</div></aui-inline-dialog>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.projectPopup.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.projectPopup';
}


JIRA.Templates.ProjectAdmin.UsedBy.projectEntry = function(opt_data, opt_ignored) {
  return '<li>' + ((! opt_data.archived) ? '<a class="shared-item-name shared-project-name' + ((opt_data.isCurrent) ? ' current' : '') + '" href="' + soy.$$escapeHtml("") + '/plugins/servlet/project-config/' + soy.$$escapeHtml(opt_data.key) + '" data-project-id="' + soy.$$escapeHtml(opt_data.id) + '" data-project-key="' + soy.$$escapeHtml(opt_data.key) + '" data-project-name="' + soy.$$escapeHtml(opt_data.name) + '" >' : '<span class="shared-item-name shared-project-name' + ((opt_data.isCurrent) ? ' current' : '') + '" data-project-id="' + soy.$$escapeHtml(opt_data.id) + '" data-project-key="' + soy.$$escapeHtml(opt_data.key) + '" data-project-name="' + soy.$$escapeHtml(opt_data.name) + '" >') + '<img class="shared-project-icon" width="16" height="16" alt="" src="' + soy.$$escapeHtml("") + '/secure/projectavatar?size=small&amp;pid=' + soy.$$escapeHtml(opt_data.id) + ((opt_data.avatar) ? '&amp;avatarId=' + soy.$$escapeHtml(opt_data.avatar.id) : '') + '" />' + soy.$$escapeHtml(opt_data.name) + ((opt_data.archived) ? ' (' + soy.$$escapeHtml('\u5f52\u6863') + ') </span>' : '</a>') + '</li>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.projectEntry.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.projectEntry';
}


JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopup = function(opt_data, opt_ignored) {
  var output = '<aui-inline-dialog ' + ((opt_data.id) ? 'id="' + soy.$$escapeHtml(opt_data.id) + '"' : '') + ' class="shared-items-target shared-issuetypes-target" alignment="bottom center" aria-label="' + soy.$$escapeHtml('View associated items') + '"><div class="shared-items-content"><h3>' + soy.$$escapeHtml(opt_data.title ? opt_data.title : AJS.format('{0,choice,1#issue type|1\x3cissue types}:',opt_data.issueTypes.length)) + '</h3><ul class="shared-items-list shared-issuetypes-list">';
  var issueTypeList173 = opt_data.issueTypes;
  var issueTypeListLen173 = issueTypeList173.length;
  for (var issueTypeIndex173 = 0; issueTypeIndex173 < issueTypeListLen173; issueTypeIndex173++) {
    var issueTypeData173 = issueTypeList173[issueTypeIndex173];
    var current__soy174 = opt_data.currentIssueTypeId && issueTypeData173.id == opt_data.currentIssueTypeId;
    output += '<li><a class="shared-item-name shared-issuetype-name' + ((current__soy174) ? ' current' : '') + '" href="' + soy.$$escapeHtml(issueTypeData173.url ? issueTypeData173.url : '#') + '" title="' + soy.$$escapeHtml(issueTypeData173.name) + '" data-issue-type-id="' + soy.$$escapeHtml(issueTypeData173.id) + '" data-issue-type-name="' + soy.$$escapeHtml(issueTypeData173.name) + '" >' + soy.$$escapeHtml(issueTypeData173.name) + '</a></li>';
  }
  output += '</ul></div></aui-inline-dialog>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopup.soyTemplateName = 'JIRA.Templates.ProjectAdmin.UsedBy.issueTypePopup';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-project-config-plugin:project-config-used-by-lozenge', location = 'global/js/used-by-lozenge.js' */
require(["jira/skate","jira/analytics","jquery"],function(e,t,i){"use strict";function n(e,i){e&&e.on("click.usedByAnalytics",function(){t.send({name:"jira.projectconfig.usedby.used",data:{linkType:i}})})}e("used-by-analytics",{type:e.type.ELEMENT,attached:function(e){var t=e.getLozengeRoot();n(t.find(".shared-issuetypes-trigger"),"issuetypes"),n(t.find(".shared-projects-trigger"),"project")},detached:function(e){e.getLozengeRoot().find(".shared-items-trigger").off("click.usedByAnalytics")},prototype:{getLozengeRoot:function(){return i(this).parent()}}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-project-config-plugin:project-config-used-by-lozenge', location = 'global/templates/used-by-lozenge-template-wrapper.js' */
define("jira-project-config/templates/usedby",[],function(){"use strict";return window.JIRA.Templates.ProjectAdmin.UsedBy}),AJS.namespace("JIRA.Templates.ProjectAdmin.UsedBy",null,require("jira-project-config/templates/usedby"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-project-config-plugin:custom-fields-admin', location = 'customfields/js/util/IssuesApi.js' */
define("jira-project-config/issues/api",function(){return window.JIRA&&window.JIRA.Issues&&window.JIRA.Issues.Api});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-project-config-plugin:custom-fields-admin', location = 'customfields/js/init-admin-dialog.js' */
require(["wrm/require","jquery"],function(i,e){var n=function(e){i(["wrc!com.atlassian.jira.jira-project-config-plugin.custom-fields-impl"],function(){require(["jira-project-config/custom-fields/api"],e)})};e(document).on("click","#add_custom_fields",function(i){i.preventDefault(),n(function(i){i.addCustomFieldViaAdminPage()})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.workflow.sharing.jira-workflow-sharing-plugin:jwsp-analytics', location = '/js/jwsp-analytics.js' */
AJS.namespace("JIRA.WSP.Analytics");JIRA.WSP.Analytics.sendEvent=function(a,b){if(AJS.EventQueue){AJS.EventQueue.push({name:"jwsp."+a,properties:b||{}})}};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:jira-analytics', location = '/includes/jira/analytics/analytics-init.js' */
require(["jquery","jira/analytics","jira/util/data/meta"],function(a,t,c){"use strict";function n(a){var t=c.get("analytics-prefix");return t&&(a=t+a),a}function i(){a("[data-track-pageview]").each(function(c,i){t.send({name:n(a(i).data("track-pageview"))})})}a(document).on("click","[data-track-click]",function(){t.send({name:n(a(this).data("track-click"))})}),a(document).on("auxclick","[data-track-auxclick]",function(){t.send({name:n(a(this).data("track-auxclick"))})}),a(i)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker-trigger', location = '/includes/jira/admin/initAvatarPickerTrigger.js' */
define("jira/admin/init/avatar-picker-trigger",["jira/ajs/layer/layer-constants","jira/util/events","jira/util/events/types","jira/util/events/reasons","jira/ajs/layer/inline-layer","jira/ajs/contentretriever/content-retriever","jira/ajs/avatarpicker/avatar","jira/ajs/avatarpicker/avatar-picker-factory","jira/ajs/avatarpicker/avatar-picker","jira/ajs/avatarpicker/avatar-picker/image-editor","jquery"],function(t,a,e,r,i,n,c,s,v,o,d){"use strict";function f(t){var a=d(".jira-avatar-picker-trigger"),e=d(".jira-avatar-picker-trigger img, img.jira-avatar-picker-trigger",t),r=d(t).find("#avatar-picker-avatar-id"),i=d(t).find("#avatar-picker-iconurl"),n=d(t).find("#avatar-type");""!==n.text()&&s.createUniversalAvatarPickerDialog({trigger:a,title:d(t).find("#avatar-dialog-title").text(),projectId:d(t).find("#avatar-owner-id").text(),projectKey:d(t).find("#avatar-owner-key").text(),defaultAvatarId:d(t).find("#default-avatar-id").text(),initialSelection:r.val(),avatarSize:c.getSizeObjectFromName(d(t).find("#avatar-size").text()),avatarType:n.text(),select:function(t,a){e.attr("src",a),i.val(a),r.val(t.getId())}})}function l(a){var e=d(".jira-inline-avatar-picker-trigger",a);e.length&&new j({offsetTarget:e,projectId:d(a).find("#avatar-owner-id").text(),projectKey:d(a).find("#avatar-owner-key").text(),defaultAvatarId:d(a).find("#default-avatar-id").text(),alignment:t.LEFT,width:420,allowDownsize:!0})}var g=n.extend({init:function(t){this.avatarPicker=t},content:function(t){this.avatarPicker.render(function(a){t(d("<div />").html(a))})},cache:function(){return!1},isLocked:function(){},startingRequest:function(){},finishedRequest:function(){}}),j=i.extend({init:function(t){var a=this;this.avatarPicker=v.createProjectAvatarPicker({projectId:t.projectId,projectKey:t.projectKey,defaultAvatarId:t.defaultAvatarId,select:function(e,r,i){t.select&&t.select.apply(this,arguments),i||a.hide(),a.offsetTarget().attr("src",r),a.offsetTarget().trigger("AvatarSelected")}}),t.contentRetriever=new g(this.avatarPicker),d(this.avatarPicker.imageEditor).bind(o.LOADED,function(){a.setWidth(a.layer().attr("scrollWidth"))}),this._super(t);var e=this.offsetTarget(),r=d("<span class='jira-avatar-picker-trigger'></span>");r.insertBefore(e).append(e),this._assignEvents("offsetTarget",r)},_events:{offsetTarget:{click:function(t){this.show()}}}});a.bind(e.NEW_CONTENT_ADDED,function(t,a,e){e!==r.panelRefreshed&&(f(a),l(a))})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:avatar-picker-trigger', location = '/includes/jira/admin/init/init-avatar-picker.js' */
require("jira/admin/init/avatar-picker-trigger");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.feedback.jira-feedback-plugin:button-resources-init', location = '/js/feedback-dialog-init.js' */
require(["jquery","jira/loading/loading","wrm/require"],function(n,i,e){"use strict";function o(){t=!0,AJS.dim(),i.showLoadingIndicator()}function r(n){t=!1,n&&AJS.dim.$dim.css("transition","none"),AJS.undim(),n&&AJS.dim.$dim&&AJS.dim.$dim.css("transition",""),i.hideLoadingIndicator()}var t=!1;n(document).on("click",".jira-feedback-plugin",function(n){n.preventDefault(),t||(o(),e(["wr!com.atlassian.feedback.jira-feedback-plugin:button-resources"]).done(function(){require("jira/feedback/feedback-dialog").show().then(function(){return r(!0)},function(n){console.error("Something went wrong in an attempt to show the issue collector dialog",n),r(!1)})}).fail(function(n){console.error("Something went wrong when loading feedback plugin resources",n),r(!1)}))})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-header-plugin:admin-quicksearch-link', location = 'js/init-admin-quicksearch-link.js' */
require(["jquery","jira/shifter"],function(e,n){e(document).on("click","#admin-search-link",function(e){n.show(),e.preventDefault()})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:dialog-resources', location = 'js/dialog.js' */
define("jira/admin-helper/dialog",["jquery","jira/dialog/dialog","jira/focus/set-focus"],function(c,a,b){return a.extend({_getDefaultOptions:function(){return c.extend(this._super(),{cached:false,widthClass:"large",stacked:true})},defineResources:function(){this._super();this.requireResource("com.atlassian.jira.plugins.************************:whereismycf-resources")},decorateContent:function(){var d=this;this.get$popupContent().find(".cancel").click(function(f){d.hide();f.preventDefault()})},_onShowContent:function(){this._super();if(a.current===this){var d=new b.FocusConfiguration();d.context=this.get$popup()[0];d.parentElementSelectors=[".form-body"];b.pushConfiguration(d);b.triggerFocus()}},hide:function(d){if(this._super(d)===false){return false}b.popConfiguration()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:dialog-resources', location = 'js/notification-helper-init.js' */
require(["jquery","jira/admin-helper/dialog","jira/util/data/meta"],function(c,a,b){if(b.get("is-admin")){c(document).delegate(".notificationhelper-trigger","click",function(e){e.preventDefault();new a({id:"notification-helper-dialog",content:function d(g){var f=this;require(["jira/admin-helper/notification-helper/content-loader"],function(h){h.loadContent(f,g)})}}).show()})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:dialog-resources', location = 'js/permission-helper-init.js' */
require(["jquery","jira/admin-helper/dialog","jira/util/data/meta"],function(c,a,b){if(b.get("is-admin")){c(document).delegate(".permissionhelper-trigger","click",function(e){e.preventDefault();new a({id:"permission-helper-dialog",content:function d(g){var f=this;require(["jira/admin-helper/permission-helper/content-loader"],function(h){h.loadContent(f,g)})}}).show()})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:dialog-resources', location = 'js/whereismycf-init.js' */
require(["jquery","jira/admin-helper/dialog","jira/util/data/meta","jira/dialog/dialog"],function(e,a,d,b){if(d.get("is-admin")){var c="\u67e5\u627e\u57df\u540d";e(document).delegate(".whereismycf-trigger","click",function(g){g.preventDefault();new a({id:"whereismycf-dialog",content:function f(i){var h=this;require(["jira/admin-helper/whereismycf/content-loader"],function(j){j.loadContentForView(h,i)})}}).show()});e(document).bind("showLayer",function(g,i,h){if(i=="inlineDialog"&&h.popup.attr("id")=="inline-dialog-field_picker_popup"){if(h.popup.find(".whereismycf-qfpicker-link").length==0){var f=e("<a href='#' class='whereismycf-qfpicker-link'>"+c+"</a>").appendTo(h.popup.find(".qf-picker-header dl"));f.click(function(l){l.preventDefault();h.hide();var j=b.current;new a({id:"whereismycf-dialog",content:function k(n){var m=this;require(["jira/admin-helper/whereismycf/content-loader"],function(o){o.loadContentForEditAndCreate(m,j,n)})}}).show()})}}})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.helptips.jira-help-tips:initialiser', location = 'js/initialiser.js' */
define("jira-help-tips/page/initialiser",["jira-help-tips/feature/help-tip-manager","jira/util/users/logged-in-user","underscore"],function(e,i,n){"use strict";function r(){i.isAnonymous()||e.fetch()}return{init:n.once(r)}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.helptips.jira-help-tips:common', location = 'js/legacy.js' */
!function(){"use strict";AJS.namespace("AJS.HelpTip",null,require("jira-help-tips/feature/help-tip")),AJS.namespace("AJS.HelpTip.Manager",null,require("jira-help-tips/feature/help-tip-manager")),require("jira-help-tips/page/initialiser").init()}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:admin-helper-nav-resources', location = 'templates/permission-helper-nav.soy' */
// This file was automatically generated from permission-helper-nav.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.Nav.PermissionHelper.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.Nav == 'undefined') { JIRA.Templates.Nav = {}; }
if (typeof JIRA.Templates.Nav.PermissionHelper == 'undefined') { JIRA.Templates.Nav.PermissionHelper = {}; }


JIRA.Templates.Nav.PermissionHelper.editPermissionsNavButton = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.Nav.PermissionHelper.viewPermissionsNavButton(null);
};
if (goog.DEBUG) {
  JIRA.Templates.Nav.PermissionHelper.editPermissionsNavButton.soyTemplateName = 'JIRA.Templates.Nav.PermissionHelper.editPermissionsNavButton';
}


JIRA.Templates.Nav.PermissionHelper.viewPermissionsNavButton = function(opt_data, opt_ignored) {
  return '<a class="aui-button permissionhelper-trigger" id="permission-helper-button" href="' + soy.$$escapeHtml("") + '/secure/admin/PermissionHelperAdmin.jspa" title="' + soy.$$escapeHtml('\u4e86\u89e3\u67d0\u7528\u6237\u4e3a\u4f55\u6709\u6216\u8005\u6ca1\u6709\u67d0\u4e9b\u6743\u9650') + '">' + soy.$$escapeHtml('\u6743\u9650\u52a9\u624b') + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.Nav.PermissionHelper.viewPermissionsNavButton.soyTemplateName = 'JIRA.Templates.Nav.PermissionHelper.viewPermissionsNavButton';
}


JIRA.Templates.Nav.PermissionHelper.permissionHelperTriggerContainer = function(opt_data, opt_ignored) {
  return '<header class=\'aui-page-header\'><div class=\'aui-page-header-inner\'><div class=\'aui-page-header-actions\'><div class=\'aui-buttons\'></div></div></div></header>';
};
if (goog.DEBUG) {
  JIRA.Templates.Nav.PermissionHelper.permissionHelperTriggerContainer.soyTemplateName = 'JIRA.Templates.Nav.PermissionHelper.permissionHelperTriggerContainer';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:admin-helper-nav-resources', location = 'js/permission-helper-nav.js' */
require(["jquery","jira/ajs/dark-features","jira/util/data/meta","jira/util/events","jira/util/events/types","jira/util/events/reasons"],function(g,m,e,c,f,d){if(e.get("is-admin")){g(function(){c.bind(f.NEW_CONTENT_ADDED,function(r,q,s){if(j(q,s)){var p=b();p.find(".aui-buttons").append(g(JIRA.Templates.Nav.PermissionHelper.editPermissionsNavButton()));h(g("#permission-helper-button"))}});if(l()){var o=i();o.find(".aui-buttons").append(g(JIRA.Templates.Nav.PermissionHelper.editPermissionsNavButton()));h(g("#permission-helper-button"))}else{if(k()){var n=a();if(n.length==1){n.prepend(g(JIRA.Templates.Nav.PermissionHelper.viewPermissionsNavButton()));h(g("#permission-helper-button"))}}}});function j(n,o){return typeof n!=="undefined"&&typeof n.is==="function"&&n.is(".project-permissions-container")&&o===d.pageLoad}function l(){var n=g("#edit_project_permissions");return(n.length>=1)}function k(){var n=g("#project-config-panel-permissions");return(n.length>=1)}function i(){var n=g(".aui-page-panel-content > header");if(n.length==0){n=g(JIRA.Templates.Nav.PermissionHelper.permissionHelperTriggerContainer());g(".aui-page-panel-content").prepend(n)}return n}function b(){var n=g(".admin-header-actions-plugin-point");var o=g(JIRA.Templates.Nav.PermissionHelper.permissionHelperTriggerContainer());n.prepend(o);return o}function a(){if(m.isEnabled("com.atlassian.jira.config.PDL")){var n=g("#project-config-panel-permissions > .aui-page-header .aui-page-header-actions");var o=n.find(".aui-buttons");return(o.size()>0)?o:n}return g(".project-config-panel-header > .operation-menu")}function h(o){if(AJS.HelpTip){var n=new AJS.HelpTip({id:"permission-helper-helptip",title:"\u6709\u7591\u95ee\uff1f",body:"\u4f7f\u7528\u6743\u9650\u52a9\u624b\u6765\u786e\u8ba4\u4e3a\u4ec0\u4e48\u4e00\u4e2a\u7528\u6237\u6709\u6216\u8005\u6ca1\u6709\u67d0\u9879\u6743\u9650\u3002",anchor:o});n.show();g("#permission-helper-helptip").click(function(){AJS.HelpTip.dismiss()});o.click(function(){n.dismiss()})}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:admin-helper-nav-resources', location = 'templates/notification-helper-nav.soy' */
// This file was automatically generated from notification-helper-nav.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.Nav.NotificationHelper.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.Nav == 'undefined') { JIRA.Templates.Nav = {}; }
if (typeof JIRA.Templates.Nav.NotificationHelper == 'undefined') { JIRA.Templates.Nav.NotificationHelper = {}; }


JIRA.Templates.Nav.NotificationHelper.editNotificationsNavButton = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.Nav.NotificationHelper.viewNotificationsNavButton(null);
};
if (goog.DEBUG) {
  JIRA.Templates.Nav.NotificationHelper.editNotificationsNavButton.soyTemplateName = 'JIRA.Templates.Nav.NotificationHelper.editNotificationsNavButton';
}


JIRA.Templates.Nav.NotificationHelper.viewNotificationsNavButton = function(opt_data, opt_ignored) {
  return '<a class="aui-button notificationhelper-trigger" id="notification-helper-button" href="' + soy.$$escapeHtml("") + '/secure/admin/NotificationHelperAdmin.jspa" title="' + soy.$$escapeHtml('\u67e5\u627e\u4e3a\u4ec0\u4e48\u7528\u6237\u63a5\u6536\u6216\u4e0d\u63a5\u6536\u901a\u77e5\u7684\u8fd9\u4e00\u95ee\u9898\u3002') + '">' + soy.$$escapeHtml('\u901a\u77e5\u65b9\u6848\u52a9\u624b') + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.Nav.NotificationHelper.viewNotificationsNavButton.soyTemplateName = 'JIRA.Templates.Nav.NotificationHelper.viewNotificationsNavButton';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.************************:admin-helper-nav-resources', location = 'js/notification-helper-nav.js' */
require(["jquery","jira/ajs/dark-features","jira/util/data/meta"],function(g,b,c){if(c.get("is-admin")){g(function(){if(a()){var j=d();j.find(".aui-buttons").append(g(JIRA.Templates.Nav.NotificationHelper.editNotificationsNavButton()));e(AJS.$("#notification-helper-button"))}else{if(f()){var i=h();if(i.length==1){i.prepend(g(JIRA.Templates.Nav.NotificationHelper.viewNotificationsNavButton()));e(g("#notification-helper-button"))}}}});function a(){var i=g("#notificationSchemeTable");return(i.length>=1)}function f(){var i=g("#project-config-notifications-table");return(i.length>=1)}function d(){var i=g(".aui-page-panel-content > header");if(i.length==0){i=g("<header class='aui-page-header'><div class='aui-page-header-inner'><div class='aui-page-header-actions'><div class='aui-buttons'></div></div></div></header>");g(".aui-page-panel-content").prepend(i)}return i}function h(){if(b.isEnabled("com.atlassian.jira.config.PDL")){var i=g("#project-config-panel-notifications > .aui-page-header .aui-page-header-actions");var j=i.find(".aui-buttons");return(j.size()>0)?j:i}return g(".project-config-panel-header > .operation-menu")}function e(j){if(AJS.HelpTip){var i=new AJS.HelpTip({id:"notification-helper-helptip",title:"\u6709\u7591\u95ee\uff1f",body:"\u4f7f\u7528\u901a\u77e5\u65b9\u6848\u52a9\u624b\u6765\u627e\u51fa\u4e3a\u4ec0\u4e48\u7528\u6237\u6536\u5230\u6216\u8005\u6ca1\u6709\u6536\u5230\u901a\u77e5\u3002",anchor:j});i.show();g("#notification-helper-helptip").click(function(){i.dismiss()});j.click(function(){i.dismiss()})}}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.troubleshooting.plugin-jira:atst-common', location = 'js/ajs-amd.js' */
define('troubleshooting/ajs', [], function () {
    'use strict';

    return AJS;
});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.troubleshooting.plugin-jira:atst-healthchecks-notification', location = 'js/notification.js' */
/* global AJS */
require([
    "jquery",
    "aui/flag",
    "troubleshooting/ajs",
    "troubleshooting/location"
], function ($, flag, AJS, location) {
    'use strict';

    if (location.isOnSupportToolsPage()) {
        // Exit if we are on the ATST screen, we don't want to display healthcheck notification if user browsing on ATST
        return;
    }

    var pluginPageUrl = AJS.contextPath() + '/plugins/servlet/troubleshooting/view';

    AJS.toInit(function () {
        var restBase = AJS.contextPath() + '/rest/troubleshooting/1.0';
        var user = $('meta[name=ajs-remote-user]').attr('content');

        // Initialise the healthcheck notification. Make a GET call to /check/{username} endpoint to get the list of failed check by user
        var initHealthcheckNotification = function () {
            $.getJSON(restBase + '/check/' + user).done(function (statusReport) {
                statusReport.statuses.forEach(function (status) {
                    showNotification(status);
                });

                // Listen to click event to dismiss or snooze notification
                $(document).find('.dismiss-notification').on('click', function () {
                    var $notification = $(this).parents().eq(3);
                    dismissNotification($notification);
                });

                $(document).find('.healthcheck-detail').on('click', function () {
                    var $notification = $(this).parents().eq(3);
                    var application = $notification.attr('application');
                    var severity = $notification.attr('severity');
                    var completeKey = $notification.attr('completeKey');

                    analyticsModule.run("Viewed", completeKey, application, severity);
                    snoozeNotification($notification);
                });
            });
        };

        // Renders the flat notification here, and set the necessary attribute to the flag
        var showNotification = function (status) {

            var healthCheckName = status.name;
            var severity = status.severity;
            var notificationId = status.id;
            var application = status.application;
            var completeKey = status.completeKey;
            var key = completeKey.split(':').pop();

            var flagType = (severity === 'warning' || severity === 'minor') ? 'warning' : 'error';
            var healthCheckUrl = pluginPageUrl + '?source=notification&healthCheck=' + key;

            var healthcheckNotification = flag({
                type: flagType,
                body: '<p>' + AJS.format("{0}\u5065\u5eb7\u68c0\u67e5\u5728\u60a8\u7684\u7cfb\u7edf\u4e2d\u5931\u8d25\u3002", '<strong>' + healthCheckName + '</strong>') + '</p>' +
                    '<ul class="aui-nav-actions-list">' +
                    '<li><a href="' + healthCheckUrl + '" class="healthcheck-detail" target="_self">' + "\u67e5\u627e\u66f4\u591a" + '</a></li>' +
                    '<li><a href="#" class="dismiss-notification">' + "\u4e0d\u7528\u518d\u63d0\u9192\u6211" + '</a></li>' +
                    '</ul>',
                close: 'manual'
            });

            healthcheckNotification.setAttribute('id', 'healthcheck-notification');
            healthcheckNotification.setAttribute('healthcheck-name', healthCheckName);
            healthcheckNotification.setAttribute('notification-id', notificationId);
            healthcheckNotification.setAttribute('application', application);
            healthcheckNotification.setAttribute('severity', severity);
            healthcheckNotification.setAttribute('completeKey', completeKey);

            healthcheckNotification.addEventListener('aui-flag-close', function () {
                var $notification = $(this);
                snoozeNotification($notification);
            });


            analyticsModule.run('Display', completeKey, application, severity);
        };

        // Called when the "Don’t remind me again" link is clicked. Notifications that are dismissed / marked as read won't reappear to the user anymore
        var dismissNotification = function ($notification) {
            $notification[0].close();

            var id = $notification.attr('notification-id');
            var name = $notification.attr('healthcheck-name');
            var application = $notification.attr('application');
            var severity = $notification.attr('severity');
            var completeKey = $notification.attr('completeKey');

            var data = {};
            data["username"] = user;
            data["notificationId"] = id;
            data["snooze"] = false;

            var jsonData = JSON.stringify(data);

            AJS.$.ajax({
                async: false,
                type: 'POST',
                url: restBase + '/dismissNotification',
                data: jsonData,
                contentType: "application/json",
                dataType: "json",
                success: function () {

                    analyticsModule.run("Dismissed", completeKey, application, severity);
                }
            })
        };

        // Called when the notification is closed via the cross button or if user open the link to the healthcheck
        // A notification is dismissed / marked as read automatically if it snoozes 3 times
        var snoozeNotification = function ($notification) {
            var id = $notification.attr('notification-id');
            var name = $notification.attr('healthcheck-name');
            var application = $notification.attr('application');
            var severity = $notification.attr('severity');
            var completeKey = $notification.attr('completeKey');

            var data = {};
            data["username"] = user;
            data["notificationId"] = id;
            data["snooze"] = true;

            var jsonData = JSON.stringify(data);

            AJS.$.ajax({
                async: false,
                type: 'POST',
                url: restBase + '/dismissNotification',
                data: jsonData,
                contentType: "application/json",
                dataType: "json",
                success: function (data, textStatus, jqXHR) {

                    isAutoDismissed(id, completeKey, name, application, severity);
                    analyticsModule.run("Snoozed", completeKey, application, severity);

                }
            })
        };

        var isAutoDismissed = function (notificationId, completeKey, name, application, severity) {
            var url = restBase + '/dismissNotification/' + user + '/' + notificationId;

            AJS.$.ajax({
                async: false,
                type: 'GET',
                url: url,
                contentType: "text/xml",
                success: function (data, textStatus, jqXHR) {
                    if (jqXHR.status == 200) {
                        notificationAutoDismissed(name);
                        analyticsModule.run("Autodismissed", completeKey, application, severity);
                    }


                }
            });
        };

        // This module allows for us to capture the correct Analytics events per display as per SHC-344.
        var analyticsModule = (function () {
            var lastEvent;
            var event = {};

            event.run = function (action, completeKey, application, severity) {
                if (lastEvent == "Viewed") {
                    // when user views info, we snooze the notification. We only want to record the Viewed AA event.
                    lastEvent = action;
                } else {
                    lastEvent = action;
                    if (action == "Display") {
                        AJS.trigger('analyticsEvent', {
                            name: "healthcheck.notification.display",
                            data: {action: action, name: completeKey, application: application, severity: severity}
                        });
                    } else {
                        AJS.trigger('analyticsEvent', {
                            name: "healthcheck.notification.action",
                            data: {action: action, name: completeKey, application: application, severity: severity}
                        });
                    }
                }
            };
            return event;
        }());

        // This function is called when a notification is automatically marked as read (normally is called when a notification is snoozed 3 times)
        var notificationAutoDismissed = function (healthCheckName) {
            // We set a quick timeout here so the animation will be displayed properly
            setTimeout(function () {
                flag({
                    type: "info",
                    body: "<p>The <strong>" + healthCheckName + "</strong> notification is now dismissed. </p>" +
                        "<p>Please visit <a href='" + AJS.contextPath() + "/plugins/servlet/troubleshooting/view/?source=notification' class='healthcheck-detail' target='_self'>this link</a> for more on the check.</p>",
                    close: "auto"
                });
            }, 1500);
        };

        initHealthcheckNotification();
    });
});

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.troubleshooting.plugin-jira:atst-healthchecks-notification', location = 'js/shc-location.js' */
define('troubleshooting/location', [], function () {
    return {
        // Get the path name of the current page URL, and returns true if user is on ATST page or false if user is on other admin screens
        isOnSupportToolsPage: function () {
            return window.location.pathname.toLowerCase().indexOf("/troubleshooting/view") >= 0;
        }
    }
});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.troubleshooting.plugin-jira:atst-healthcheck-sensors', location = 'js/sensors.js' */
require([
    'troubleshooting/sensors/page-protocols',
    'troubleshooting/ajs'
], function(
    protocolSensor,
    AJS
) {
    var sensors = [protocolSensor];

    AJS.toInit(function() {

        function hash(str) {
            var hash = 0;
            for (var i = 0; i < str.length; ++i) {
                hash = 31 * hash + str.charCodeAt(i);
                hash |= 0; // this reduces the number to 32bits and prevents Infinity
            }
            return hash;
        }

        //this handles cases when the user disabled access to local storage
        var localStorageWrapper = {
            getWithDefaultOnError: function(sensorName, defaultValue) {
                try {
                    return window.localStorage.getItem(sensorName) || "0";
                } catch (e) {
                    return defaultValue;
                }
            },
            setItemQuietly: function (key, value) {
                try {
                    window.localStorage.setItem(key, value);
                } catch (ignored) {
                }
            }
        };

        // Iterate through each sensor and ingest any data it has for us...
        // but only if it's actually working properly in this client.
        sensors.forEach(function(sensor) {
            if (!sensor.isWorking()) {
                return
            }

            var sensorData = {};
            var newData = sensor.getData();
            for (var key in newData) {
                if (newData.hasOwnProperty(key)) {
                    sensorData[key] = newData[key];
                }
            }

            var sensorName = 'atst.healthcheck.sensors.' + sensor.getName();
            var currentHash = hash(JSON.stringify(sensorData)).toString(36);
            var previousHash = localStorageWrapper.getWithDefaultOnError(sensorName, currentHash);
            if (previousHash===currentHash && Math.random()>0.01) {
                return;
            }
            // What's one more analytics event to the world? A drop in the data ocean.
            AJS.trigger('analytics', {
                name: sensorName,
                data: sensorData
            });
            localStorageWrapper.setItemQuietly(sensorName, currentHash);
        });

    });
});

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.troubleshooting.plugin-jira:atst-healthcheck-sensors', location = 'js/sensors/page-protocols.js' */
define('troubleshooting/sensors/page-protocols', ['troubleshooting/ajs'], function(AJS) {
    var baseUrl = AJS.contextPath();
    var hasPerfData = window.performance && typeof window.performance.getEntriesByType === 'function';

    // WARNING: This is a rough assumption based on how the WRM works.
    // Consuming this from the WRM would be a better option, but meh.
    // See: https://bitbucket.org/atlassian/atlassian-plugins-webresource/src/master/atlassian-plugins-webresource/src/main/java/com/atlassian/plugin/webresource/WebResourceUrlProviderImpl.java
    // See also: https://stash.atlassian.com/projects/CP/repos/static-assets-url/browse/src/main/resources/ui/health-checks/health-checks.js
    var WRM_STATIC_ASSET_REGEX = new RegExp(baseUrl + '/s/.+?/_/');

    function isStaticResource(resource) {
        return WRM_STATIC_ASSET_REGEX.test(resource.name);
    }

    function getNextHopData(resource) {
        return (resource && resource.nextHopProtocol) || 'unknown';
    }

    function unique(elem, pos, arr) {
        return arr.indexOf(elem) === pos;
    }

    return {
        isWorking: function() {
            return hasPerfData;
        },
        getName: function() {
          return 'page-protocols';
        },
        getData: function() {
            var resources = window.performance.getEntriesByType('resource');
            var navigation = window.performance.getEntriesByType('navigation');
            var resourceProtocols = resources
                .filter(isStaticResource)
                .map(getNextHopData)
                .filter(unique)
                .sort();
            if (!resourceProtocols.length) {
                resourceProtocols.push('unknown');
            }
            return {
                resourceProtocols: resourceProtocols,
                navigationProtocol: getNextHopData(navigation[0]),
                userAgent: navigator.getUserAgent && "use-js-client-hints" || navigator.userAgent
            };
        }
    }
});

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:field-templates', location = '/includes/jira/field/templates/singleUserPicker.soy' */
// This file was automatically generated from singleUserPicker.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.Fields.Pickers.User.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.Fields == 'undefined') { JIRA.Templates.Fields = {}; }
if (typeof JIRA.Templates.Fields.Pickers == 'undefined') { JIRA.Templates.Fields.Pickers = {}; }
if (typeof JIRA.Templates.Fields.Pickers.User == 'undefined') { JIRA.Templates.Fields.Pickers.User = {}; }


JIRA.Templates.Fields.Pickers.User.single = function(opt_data, opt_ignored) {
  var output = '<select id="' + soy.$$escapeHtml(opt_data.field.id) + '" name="' + soy.$$escapeHtml(opt_data.field.name) + '" class="single-user-picker js-' + ((opt_data.type) ? soy.$$escapeHtml(opt_data.type) : 'default-user-picker') + '"' + ((opt_data.inputText) ? ' data-input-text="' + soy.$$escapeHtml(opt_data.inputText) + '"' : '') + ((opt_data.editValue) ? ' data-edit-value="' + soy.$$escapeHtml(opt_data.editValue) + '"' : '') + ((opt_data.showDropdownButton) ? ' data-show-dropdown-button="true"' : '') + ((opt_data.userType) ? ' data-user-type="' + soy.$$escapeHtml(opt_data.userType) + '"' : '') + ((opt_data.containerClass) ? ' data-container-class="' + soy.$$escapeHtml(opt_data.containerClass) + '"' : '') + '>';
  var optionList38 = opt_data.options;
  var optionListLen38 = optionList38.length;
  for (var optionIndex38 = 0; optionIndex38 < optionListLen38; optionIndex38++) {
    var optionData38 = optionList38[optionIndex38];
    if (optionData38.optionGroup) {
      output += '<optgroup id="' + soy.$$escapeHtml(opt_data.field.id) + '-group-' + soy.$$escapeHtml(optionData38.id) + '" label="' + soy.$$escapeHtml(optionData38.display) + '"' + ((optionData38.footer) ? ' data-footer-text="' + soy.$$escapeHtml(optionData38.footer) + '"' : '') + ((optionData38.weight != -1) ? ' data-weight="' + soy.$$escapeHtml(optionData38.weight) + '"' : '') + '>';
      var groupOptionList59 = optionData38.groupOptions;
      var groupOptionListLen59 = groupOptionList59.length;
      for (var groupOptionIndex59 = 0; groupOptionIndex59 < groupOptionListLen59; groupOptionIndex59++) {
        var groupOptionData59 = groupOptionList59[groupOptionIndex59];
        output += JIRA.Templates.Fields.Pickers.User.option(groupOptionData59);
      }
      output += '</optgroup>';
    } else {
      output += JIRA.Templates.Fields.Pickers.User.option(optionData38);
    }
  }
  output += '</select>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.Fields.Pickers.User.single.soyTemplateName = 'JIRA.Templates.Fields.Pickers.User.single';
}


JIRA.Templates.Fields.Pickers.User.option = function(opt_data, opt_ignored) {
  return '<option ' + ((opt_data.loggedInUser) ? 'class="current-user" ' : '') + ((opt_data.selected) ? 'selected="selected" ' : '') + 'value="' + soy.$$escapeHtml(opt_data.optionName) + '" data-field-text="' + soy.$$escapeHtml(opt_data.displayName) + '" data-field-label="' + soy.$$escapeHtml(opt_data.displayName) + ((opt_data.emailAddress) ? ' - ' + soy.$$escapeHtml(opt_data.emailAddress) : '') + ((opt_data.optionName && opt_data.optionName != '-1') ? ' (' + soy.$$escapeHtml(opt_data.optionName) + ')' : '') + '" data-icon="' + soy.$$escapeHtml(opt_data.avatarURL) + '" data-icon-type="rounded" >' + soy.$$escapeHtml(opt_data.displayName) + '</option>';
};
if (goog.DEBUG) {
  JIRA.Templates.Fields.Pickers.User.option.soyTemplateName = 'JIRA.Templates.Fields.Pickers.User.option';
}


JIRA.Templates.Fields.Pickers.User.popupTrigger = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  return '' + ((opt_data.hasPermission) ? '<button type="button" class="popup-trigger addon-icon"><span class="icon-default aui-icon aui-icon-small aui-iconfont-admin-roles">' + soy.$$escapeHtml(opt_data.imgTitle) + '</span></button>' : '<button type="button" class="addon-icon"><span class="aui-icon aui-icon-small aui-iconfont-locked">' + soy.$$escapeHtml(opt_data.noPermissionTitle) + '</span></button>');
};
if (goog.DEBUG) {
  JIRA.Templates.Fields.Pickers.User.popupTrigger.soyTemplateName = 'JIRA.Templates.Fields.Pickers.User.popupTrigger';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:field-templates', location = '/includes/jira/field/templates/assigneeField.soy' */
// This file was automatically generated from assigneeField.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.Fields.Pickers.User.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.Fields == 'undefined') { JIRA.Templates.Fields = {}; }
if (typeof JIRA.Templates.Fields.Pickers == 'undefined') { JIRA.Templates.Fields.Pickers = {}; }
if (typeof JIRA.Templates.Fields.Pickers.User == 'undefined') { JIRA.Templates.Fields.Pickers.User = {}; }


JIRA.Templates.Fields.Pickers.User.assignee = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.Fields.Pickers.User.single(soy.$$augmentMap(opt_data, {options: opt_data.assigneeOptions.options, showDropdownButton: true, userType: 'assignee', type: 'assignee-picker', containerClass: 'long-field'})) + ((opt_data.isLoggedInUserAssignable) ? '<a href="#' + soy.$$escapeHtml(opt_data.field.id) + '" id="assign-to-me-trigger">' + soy.$$escapeHtml('\u5206\u914d\u7ed9\u6211') + '</a>' : '') + '<fieldset class="hidden parameters"><input type="hidden" title="projectKeys" value="' + soy.$$escapeHtml(opt_data.projectKeys) + '"/>' + ((opt_data.issueKey) ? '<input type="hidden" title="assigneeEditIssueKey" value="' + soy.$$escapeHtml(opt_data.issueKey) + '"/>' : '') + ((opt_data.actionDescriptorId) ? '<input type="hidden" title="actionDescriptorId" value="' + soy.$$escapeHtml(opt_data.actionDescriptorId) + '"/>' : '') + '</fieldset>';
};
if (goog.DEBUG) {
  JIRA.Templates.Fields.Pickers.User.assignee.soyTemplateName = 'JIRA.Templates.Fields.Pickers.User.assignee';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/lib/jquery.dotdotdot-1.5.7.js' */
(function(i){if(i.fn.dotdotdot){return}i.fn.dotdotdot=function(v){if(this.length==0){if(!v||v.debug!==false){b(true,'No element found for "'+this.selector+'".')}return this}if(this.length>1){return this.each(function(){i(this).dotdotdot(v)})}var r=this;if(r.data("dotdotdot")){r.trigger("destroy.dot")}r.bind_events=function(){r.bind("update.dot",function(y,A){y.preventDefault();y.stopPropagation();t.maxHeight=(typeof t.height=="number")?t.height:o(r);t.maxHeight+=t.tolerance;if(typeof A!="undefined"){if(typeof A=="string"||A instanceof HTMLElement){A=i("<div />").append(A).contents()}if(A instanceof i){w=A}}s=r.wrapInner('<div class="dotdotdot" />').children();s.empty().append(w.clone(true)).css({height:"auto",width:"auto",border:"none",padding:0,margin:0});var z=false,x=false;if(q.afterElement){z=q.afterElement.clone(true);q.afterElement.remove()}if(k(s,t)){if(t.wrap=="children"){x=d(s,t,z)}else{x=m(s,r,s,t,z)}}s.replaceWith(s.contents());s=null;if(i.isFunction(t.callback)){t.callback.call(r[0],x,w)}q.isTruncated=x;return x}).bind("isTruncated.dot",function(y,x){y.preventDefault();y.stopPropagation();if(typeof x=="function"){x.call(r[0],q.isTruncated)}return q.isTruncated}).bind("originalContent.dot",function(y,x){y.preventDefault();y.stopPropagation();if(typeof x=="function"){x.call(r[0],w)}return w}).bind("destroy.dot",function(x){x.preventDefault();x.stopPropagation();r.unwatch().unbind_events().empty().append(w).data("dotdotdot",false)});return r};r.unbind_events=function(){r.unbind(".dot");return r};r.watch=function(){r.unwatch();if(t.watch=="window"){var z=i(window),y=z.width(),x=z.height();z.bind("resize.dot"+q.dotId,function(){if(y!=z.width()||x!=z.height()||!t.windowResizeFix){y=z.width();x=z.height();if(p){clearInterval(p)}p=setTimeout(function(){r.trigger("update.dot")},10)}})}else{u=j(r);p=setInterval(function(){var A=j(r);if(u.width!=A.width||u.height!=A.height){r.trigger("update.dot");u=j(r)}},100)}return r};r.unwatch=function(){i(window).unbind("resize.dot"+q.dotId);if(p){clearInterval(p)}return r};var w=r.contents(),t=i.extend(true,{},i.fn.dotdotdot.defaults,v),q={},u={},p=null,s=null;q.afterElement=c(t.after,r);q.isTruncated=false;q.dotId=l++;r.data("dotdotdot",true).bind_events().trigger("update.dot");if(t.watch){r.watch()}return r};i.fn.dotdotdot.defaults={ellipsis:"... ",wrap:"word",lastCharacter:{remove:[" ",",",";",".","!","?"],noEllipsis:[]},tolerance:0,callback:null,after:null,height:null,watch:false,windowResizeFix:true,debug:false};var l=1;function d(s,w,v){var u=s.children(),p=false;s.empty();for(var r=0,q=u.length;r<q;r++){var t=u.eq(r);s.append(t);if(v){s.append(v)}if(k(s,w)){t.remove();p=true;break}else{if(v){v.remove()}}}return p}function m(s,r,A,t,q){var w=s.contents(),x=false;s.empty();var z="table, thead, tbody, tfoot, tr, col, colgroup, object, embed, param, ol, ul, dl, select, optgroup, option, textarea, script, style";for(var y=0,u=w.length;y<u;y++){if(x){break}var v=w[y],p=i(v);if(typeof v=="undefined"){continue}s.append(p);if(q){s[(s.is(z))?"after":"append"](q)}if(v.nodeType==3){if(k(A,t)){x=e(p,r,A,t,q)}}else{x=m(p,r,A,t,q)}if(!x){if(q){q.remove()}}}return x}function e(q,r,p,w,s){var D=false,A=q[0];if(typeof A=="undefined"){return false}var E=(w.wrap=="letter")?"":" ",t=h(A).split(E),F=-1,v=-1,G=0,y=t.length-1;while(G<=y){var x=Math.floor((G+y)/2);if(x==v){break}v=x;a(A,t.slice(0,v+1).join(E)+w.ellipsis);if(!k(p,w)){F=v;G=v}else{y=v}}if(F!=-1&&!(t.length==1&&t[0].length==0)){var u=f(t.slice(0,F+1).join(E),w);D=true;a(A,u)}else{var z=q.parent();q.remove();var C=(s)?s.length:0;if(z.contents().size()>C){var B=z.contents().eq(-1-C);D=e(B,r,p,w,s)}else{var A=z.prev().contents().eq(-1)[0];if(typeof A!="undefined"){var u=f(h(A),w);a(A,u);z.remove();D=true}}}return D}function k(q,p){return q.innerHeight()>p.maxHeight}function f(p,q){while(i.inArray(p.slice(-1),q.lastCharacter.remove)>-1){p=p.slice(0,-1)}if(i.inArray(p.slice(-1),q.lastCharacter.noEllipsis)<0){p+=q.ellipsis}return p}function j(p){return{width:p.innerWidth(),height:p.innerHeight()}}function a(q,p){if(q.innerText){q.innerText=p}else{if(q.nodeValue){q.nodeValue=p}else{if(q.textContent){q.textContent=p}}}}function h(p){if(p.innerText){return p.innerText}else{if(p.nodeValue){return p.nodeValue}else{if(p.textContent){return p.textContent}else{return""}}}}function c(p,q){if(typeof p=="undefined"){return false}if(!p){return false}if(typeof p=="string"){p=i(p,q);return(p.length)?p:false}if(typeof p=="object"){return(typeof p.jquery=="undefined")?false:p}return false}function o(s){var t=s.innerHeight(),r=["paddingTop","paddingBottom"];for(var u=0,q=r.length;u<q;u++){var p=parseInt(s.css(r[u]),10);if(isNaN(p)){p=0}t-=p}return t}function b(q,p){if(!q){return false}if(typeof p=="string"){p="dotdotdot: "+p}else{p=["dotdotdot:",p]}if(typeof window.console!="undefined"){if(typeof window.console.log!="undefined"){window.console.log(p)}}return false}var n=i.fn.html;i.fn.html=function(p){if(typeof p!="undefined"){if(this.data("dotdotdot")){if(typeof p!="function"){return this.trigger("update",[p])}}return n.call(this,p)}return n.call(this)};var g=i.fn.text;i.fn.text=function(q){if(typeof q!="undefined"){if(this.data("dotdotdot")){var p=i("<div />");p.text(q);q=p.html();p.remove();return this.trigger("update",[q])}return g.call(this,q)}return g.call(this)}})(require("jquery"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/soy/ProjectTemplatesList.soy' */
// This file was automatically generated from ProjectTemplatesList.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.ProjectTemplates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.ProjectTemplates == 'undefined') { JIRA.Templates.ProjectTemplates = {}; }


JIRA.Templates.ProjectTemplates.renderProjectTemplates = function(opt_data, opt_ignored) {
  return '' + ((soy.$$getMapKeys(opt_data.projectTemplates).length == 0) ? JIRA.Templates.errorMsg({closeable: false, msg: '\u6ca1\u6709\u627e\u5230\u9879\u76ee\u6a21\u677f\u3002'}) : JIRA.Templates.ProjectTemplates.renderTemplates(opt_data));
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.renderProjectTemplates.soyTemplateName = 'JIRA.Templates.ProjectTemplates.renderProjectTemplates';
}


JIRA.Templates.ProjectTemplates.renderProjectTemplatesGroupedByType = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.projectTemplatesByType.length == 0) {
    output += JIRA.Templates.errorMsg({closeable: false, msg: '\u6ca1\u6709\u627e\u5230\u9879\u76ee\u6a21\u677f\u3002'});
  } else {
    var projectTypeTemplatesGroupList18 = opt_data.projectTemplatesByType;
    var projectTypeTemplatesGroupListLen18 = projectTypeTemplatesGroupList18.length;
    for (var projectTypeTemplatesGroupIndex18 = 0; projectTypeTemplatesGroupIndex18 < projectTypeTemplatesGroupListLen18; projectTypeTemplatesGroupIndex18++) {
      var projectTypeTemplatesGroupData18 = projectTypeTemplatesGroupList18[projectTypeTemplatesGroupIndex18];
      output += '<div class="template-group" id="project-template-group-' + soy.$$escapeHtml(projectTypeTemplatesGroupData18.projectTypeBean.projectTypeKey) + '"><div class="template-group-header"><h6><img class="project-type-icon" src="data:image/svg+xml;base64, ' + soy.$$escapeHtml(projectTypeTemplatesGroupData18.projectTypeBean.icon) + ' "/><span>' + soy.$$escapeHtml(projectTypeTemplatesGroupData18.projectTypeBean.projectTypeDisplayKey) + '</span></h6></div>' + JIRA.Templates.ProjectTemplates.renderTemplates({projectTemplates: projectTypeTemplatesGroupData18.projectTemplates}) + '</div>';
    }
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.renderProjectTemplatesGroupedByType.soyTemplateName = 'JIRA.Templates.ProjectTemplates.renderProjectTemplatesGroupedByType';
}


JIRA.Templates.ProjectTemplates.renderTemplates = function(opt_data, opt_ignored) {
  return '<div class="pt-templates-list">' + JIRA.Templates.ProjectTemplates.renderItems(opt_data) + '</div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.renderTemplates.soyTemplateName = 'JIRA.Templates.ProjectTemplates.renderTemplates';
}


JIRA.Templates.ProjectTemplates.renderItems = function(opt_data, opt_ignored) {
  var output = '<ol class="templates" tabindex="100">';
  var templateList37 = opt_data.projectTemplates;
  var templateListLen37 = templateList37.length;
  for (var templateIndex37 = 0; templateIndex37 < templateListLen37; templateIndex37++) {
    var templateData37 = templateList37[templateIndex37];
    output += '<li class="template"' + ((templateData37.itemModuleCompleteKey) ? 'data-item-module-complete-key="' + soy.$$escapeHtml(templateData37.itemModuleCompleteKey) + '"' : '') + ((templateData37.projectTemplateModuleCompleteKey) ? 'data-project-template-module-complete-key="' + soy.$$escapeHtml(templateData37.projectTemplateModuleCompleteKey) + '"' : '') + ((templateData37.createProject) ? 'data-create-project="' + soy.$$escapeHtml(templateData37.createProject) + '"' : '') + ((templateData37.demoProject) ? 'data-demo-project="' + soy.$$escapeHtml(templateData37.demoProject) + '"' : '') + ((templateData37.backgroundIconUrl) ? 'data-background-icon-url="' + soy.$$escapeHtml(templateData37.backgroundIconUrl) + '"' : '') + ((templateData37.name) ? 'data-name="' + soy.$$escapeHtml(templateData37.name) + '"' : '') + ((templateData37.description) ? 'data-description="' + soy.$$escapeHtml(templateData37.description) + '"' : '') + ((templateData37.longDescriptionContent) ? 'data-long-description-content="' + soy.$$escapeHtml(templateData37.longDescriptionContent) + '"' : '') + ((templateData37.infoSoyPath) ? 'data-info-soy-path="' + soy.$$escapeHtml(templateData37.infoSoyPath) + '"' : '') + '><img class="template-preview" src="' + soy.$$escapeHtml(templateData37.iconUrl) + '" /><div class="template-meta"><div class="template-name" title="' + soy.$$escapeHtml(templateData37.name) + '">' + soy.$$escapeHtml(templateData37.name) + '</div><div class="template-description" title="' + soy.$$escapeHtml(templateData37.description) + '">' + soy.$$escapeHtml(templateData37.description) + '</div></div></li>';
  }
  output += '</ol>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.renderItems.soyTemplateName = 'JIRA.Templates.ProjectTemplates.renderItems';
}


JIRA.Templates.ProjectTemplates.loadingTemplatesList = function(opt_data, opt_ignored) {
  return '<div class="wait-container"><img class="wait-icon" src="' + soy.$$escapeHtml("") + '/images/icons/wait.gif"><span class="wait-text">' + soy.$$escapeHtml('\u4e0a\u4f20\u9879\u76ee\u6a21\u677f') + '&hellip;</span></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.loadingTemplatesList.soyTemplateName = 'JIRA.Templates.ProjectTemplates.loadingTemplatesList';
}


JIRA.Templates.ProjectTemplates.footerLinks = function(opt_data, opt_ignored) {
  return '<div class="footer-links"><a class="import-project-trigger button-panel-link" href="' + soy.$$escapeHtml("") + '/secure/admin/views/ExternalImport1.jspa">' + soy.$$escapeHtml('\u5bfc\u5165\u4e00\u4e2a\u9879\u76ee') + '</a>|<a class="create-with-shared-config button-panel-link" href="#"><span class="aui-icon aui-icon-create-shared">' + soy.$$escapeHtml('\u521b\u5efa\u4e0e\u5171\u4eab\u914d\u7f6e') + '</span> ' + soy.$$escapeHtml('\u521b\u5efa\u4e0e\u5171\u4eab\u914d\u7f6e') + '</a>' + ((opt_data.showDemoLink) ? '| <a class="add-demo-project-trigger button-panel-link" href="#">' + soy.$$escapeHtml('\u521b\u5efa\u793a\u4f8b\u6570\u636e') + '</a>' : '') + '</div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.footerLinks.soyTemplateName = 'JIRA.Templates.ProjectTemplates.footerLinks';
}


JIRA.Templates.ProjectTemplates.addWorkflowsLink = function(opt_data, opt_ignored) {
  return '<a class="add-workflow-link" href="' + soy.$$escapeHtml(opt_data.baseUrl) + '/plugins/servlet/wfshare-import?src=projecttemplates" tabindex="-1">' + soy.$$escapeHtml('\u67e5\u770b\u5e02\u573a\u5de5\u4f5c\u6d41') + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.addWorkflowsLink.soyTemplateName = 'JIRA.Templates.ProjectTemplates.addWorkflowsLink';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/soy/AddProject.soy' */
// This file was automatically generated from AddProject.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.ProjectTemplates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.ProjectTemplates == 'undefined') { JIRA.Templates.ProjectTemplates = {}; }


JIRA.Templates.ProjectTemplates.addProjectForm = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.errors.errorMessages) {
    var errorList5 = opt_data.errors.errorMessages;
    var errorListLen5 = errorList5.length;
    for (var errorIndex5 = 0; errorIndex5 < errorListLen5; errorIndex5++) {
      var errorData5 = errorList5[errorIndex5];
      output += JIRA.Templates.errorMsg({closeable: false, msg: errorData5});
    }
  }
  output += '<div class="add-project-wrapper"><div class="add-project-form-wrapper">' + aui.form.form({content: '' + aui.form.fieldset({legendContent: '', content: '' + JIRA.Templates.ProjectTemplates.longTextField({id: 'name', name: 'name', isRequired: false, maxLength: opt_data.maxNameLength, labelContent: '\u540d\u79f0', descriptionText: AJS.format('\u6700\u591a{0}\u4e2a\u5b57\u7b26',opt_data.maxNameLength), value: opt_data.currentName ? opt_data.currentName : '', errorTexts: opt_data.errors.errors.projectName ? [opt_data.errors.errors.projectName] : []}) + JIRA.Templates.ProjectTemplates.textFieldWithHelpIcon({id: 'key', name: 'key', isRequired: false, maxLength: opt_data.maxKeyLength, labelContent: '\u5173\u952e\u5b57', descriptionText: AJS.format('\u6700\u591a{0}\u4e2a\u5b57\u7b26',opt_data.maxKeyLength), helpTitle: '\u4ec0\u4e48\u662f\u9879\u76ee\u952e\uff1f', value: opt_data.currentKey ? opt_data.currentKey : '', errorTexts: opt_data.errors.errors.projectKey ? [opt_data.errors.errors.projectKey] : []}) + ((opt_data.shouldShowLead) ? JIRA.Templates.ProjectTemplates.projectLeadField({field: opt_data.projectLeadPickerField, isRequired: false, errorTexts: opt_data.errors.errors.projectLead ? [opt_data.errors.errors.projectLead] : [], options: opt_data.leadOptions}) : '') + ((opt_data.addUserToLicense.displayCheckbox) ? JIRA.Templates.ProjectTemplates.addUserToLicense({fieldId: opt_data.addUserToLicense.fieldId, fieldName: opt_data.addUserToLicense.fieldName, applicationName: opt_data.addUserToLicense.applicationName, usedSeats: opt_data.addUserToLicense.usedSeats, totalSeats: opt_data.addUserToLicense.totalSeats, disableCheckbox: opt_data.addUserToLicense.disableCheckbox, licensingUrl: opt_data.addUserToLicense.licensingUrl}) : '') + '<input type="hidden" name="keyEdited" id="keyEdited" value="false"><input type="hidden" name="projectTemplateWebItemKey" value="' + soy.$$escapeHtml(opt_data.projectTemplateWebItemKey) + '"><input type="hidden" name="projectTemplateModuleKey" value="' + soy.$$escapeHtml(opt_data.projectTemplateModuleKey) + '"><input type="submit" class="pt-hidden-submit offscreen-left">'}), id: 'add-project-form'}) + '</div>' + ((opt_data.projectTemplateDescriptionContent) ? '<div class="add-project-description-wrapper"><div class="project-template-title"><h3>' + soy.$$escapeHtml(opt_data.projectTemplateTitle) + '</h3></div><div class="project-template-description">' + soy.$$filterNoAutoescape(opt_data.projectTemplateDescriptionContent) + '</div></div>' : '') + '</div>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.addProjectForm.soyTemplateName = 'JIRA.Templates.ProjectTemplates.addProjectForm';
}


JIRA.Templates.ProjectTemplates.keyHelp = function(opt_data, opt_ignored) {
  return '<div class="project-key-help"><p><b>' + soy.$$escapeHtml('\u4ec0\u4e48\u662f\u9879\u76ee\u952e\uff1f') + '</b></p><ul><li>' + soy.$$escapeHtml('\u5b83\u4f1a\u6210\u4e3a\u9879\u76ee\u4e2d\u6bcf\u4e2a\u95ee\u9898\u7684\u524d\u7f00') + '</li><li>' + soy.$$escapeHtml('\u5b83\u53ef\u4ee5\u88ab\u6539\u53d8, \u4f46\u8fd9\u5e76\u4e0d\u662f\u4e00\u4ef6\u7b80\u5355\u7684\u4efb\u52a1') + '</li></ul></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.keyHelp.soyTemplateName = 'JIRA.Templates.ProjectTemplates.keyHelp';
}


JIRA.Templates.ProjectTemplates.projectLeadField = function(opt_data, opt_ignored) {
  var param77 = '' + aui.form.label({forField: opt_data.field.name + '-field', isRequired: opt_data.isRequired, content: '\u9879\u76ee\u8d1f\u8d23\u4eba'}) + JIRA.Templates.Fields.Pickers.User.single({field: opt_data.field, options: opt_data.options, editValue: opt_data.value}) + aui.form.fieldDescription({message: '\u8f93\u5165\u9879\u76ee\u8d1f\u8d23\u4eba\u7684\u7528\u6237\u540d'});
  if (opt_data.errorTexts) {
    var errorList90 = opt_data.errorTexts;
    var errorListLen90 = errorList90.length;
    for (var errorIndex90 = 0; errorIndex90 < errorListLen90; errorIndex90++) {
      var errorData90 = errorList90[errorIndex90];
      param77 += aui.form.fieldError({message: errorData90});
    }
  }
  var output = '' + aui.form.fieldGroup({content: param77});
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.projectLeadField.soyTemplateName = 'JIRA.Templates.ProjectTemplates.projectLeadField';
}


JIRA.Templates.ProjectTemplates.addUserToLicense = function(opt_data, opt_ignored) {
  var output = '';
  var labelMessage__soy96 = '' + soy.$$escapeHtml(AJS.format('\u7ed9\u6211\u4e00\u4e2a{0}\u8bb8\u53ef\u8bc1\uff0c\u8fd9\u6837\uff0c\u6211\u5c31\u53ef\u4ee5\u8fdb\u5165\u6b64\u9879\u76ee\u3002',opt_data.applicationName)) + ((opt_data.totalSeats > 0) ? '<br/>' + soy.$$escapeHtml(AJS.format('({0}\u7684{1}\u7684\u8bb8\u53ef\u8bc1\u73b0\u5728\u7528)',opt_data.usedSeats,opt_data.totalSeats)) : '');
  output += aui.form.fieldGroup({content: '' + aui.form.label({forField: opt_data.fieldName, content: 'License'}) + ((opt_data.disableCheckbox) ? aui.form.field({id: opt_data.fieldId, name: opt_data.fieldName, value: 'true', type: 'checkbox', isChecked: false, labelContent: labelMessage__soy96, isDisabled: true, descriptionContent: AJS.format('\u6211\u4eec\u4e0d\u80fd\u8ba9\u60a8\u8bbf\u95ee{0}\u81ea\u52a8\u7684\u3002\u60a8\u53ef\u4ee5\u5728{1}\u4e2d\u7ba1\u7406\u5e94\u7528\u7a0b\u5e8f\u8bbf\u95ee{2}\u7684\u5de5\u4f5c{0}\u9879\u76ee\u3002',opt_data.applicationName,'<a href="' + "" + opt_data.licensingUrl + '">','</a>')}) : aui.form.field({id: opt_data.fieldId, name: opt_data.fieldName, value: 'true', type: 'checkbox', isChecked: false, labelContent: labelMessage__soy96, isDisabled: false}))});
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.addUserToLicense.soyTemplateName = 'JIRA.Templates.ProjectTemplates.addUserToLicense';
}


JIRA.Templates.ProjectTemplates.textFieldWithHelpIcon = function(opt_data, opt_ignored) {
  var param128 = '' + aui.form.label({forField: opt_data.name, isRequired: opt_data.isRequired, content: opt_data.labelContent}) + aui.form.input({id: opt_data.id, name: opt_data.name, type: 'text', maxLength: opt_data.maxLength, value: opt_data.value}) + '<a class="help-lnk" title="' + soy.$$escapeHtml(opt_data.helpTitle) + '" id="' + soy.$$escapeHtml(opt_data.id) + '-help-icon"><span class="aui-icon aui-icon-help">' + soy.$$escapeHtml(opt_data.helpTitle) + '</span></a>' + ((opt_data.descriptionText) ? aui.form.fieldDescription({message: opt_data.descriptionText}) : '');
  if (opt_data.errorTexts) {
    var errorList152 = opt_data.errorTexts;
    var errorListLen152 = errorList152.length;
    for (var errorIndex152 = 0; errorIndex152 < errorListLen152; errorIndex152++) {
      var errorData152 = errorList152[errorIndex152];
      param128 += aui.form.fieldError({message: errorData152});
    }
  }
  var output = '' + aui.form.fieldGroup({content: param128});
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.textFieldWithHelpIcon.soyTemplateName = 'JIRA.Templates.ProjectTemplates.textFieldWithHelpIcon';
}


JIRA.Templates.ProjectTemplates.longTextField = function(opt_data, opt_ignored) {
  var param158 = '' + aui.form.label({forField: opt_data.name, isRequired: opt_data.isRequired, content: opt_data.labelContent}) + aui.form.input({id: opt_data.id, name: opt_data.name, type: 'text', maxLength: opt_data.maxLength, value: opt_data.value, extraClasses: 'long-field'}) + ((opt_data.descriptionText) ? aui.form.fieldDescription({message: opt_data.descriptionText}) : '');
  if (opt_data.errorTexts) {
    var errorList176 = opt_data.errorTexts;
    var errorListLen176 = errorList176.length;
    for (var errorIndex176 = 0; errorIndex176 < errorListLen176; errorIndex176++) {
      var errorData176 = errorList176[errorIndex176];
      param158 += aui.form.fieldError({message: errorData176});
    }
  }
  var output = '' + aui.form.fieldGroup({content: param158});
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.longTextField.soyTemplateName = 'JIRA.Templates.ProjectTemplates.longTextField';
}


JIRA.Templates.ProjectTemplates.spinner = function(opt_data, opt_ignored) {
  return '<span id=\'' + soy.$$escapeHtml(opt_data.id) + '\' class=\'icon throbber loading\'/>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.spinner.soyTemplateName = 'JIRA.Templates.ProjectTemplates.spinner';
}


JIRA.Templates.ProjectTemplates.formatAnchor = function(opt_data, opt_ignored) {
  return '<a href="' + soy.$$escapeHtml(opt_data.href) + '"' + ((opt_data.title) ? ' title="' + soy.$$escapeHtml(opt_data.title) + '"' : '') + '>' + soy.$$escapeHtml(opt_data.body) + '</a>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.formatAnchor.soyTemplateName = 'JIRA.Templates.ProjectTemplates.formatAnchor';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/soy/CreateSharedProject.soy' */
// This file was automatically generated from CreateSharedProject.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.ProjectTemplates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.ProjectTemplates == 'undefined') { JIRA.Templates.ProjectTemplates = {}; }


JIRA.Templates.ProjectTemplates.createSharedProjectForm = function(opt_data, opt_ignored) {
  return '<div class="create-shared-project-wrapper"><div class="create-shared-project-form-wrapper">' + aui.form.form({content: '' + aui.form.fieldset({content: '<p>' + soy.$$escapeHtml('\u9009\u62e9\u60a8\u5e0c\u671b\u5171\u4eab\u914d\u7f6e\u7684\u73b0\u6709\u9879\u76ee\u3002') + '<a class="help-lnk" title="' + soy.$$escapeHtml('\u4ec0\u4e48\u4f1a\u88ab\u5171\u4eab\uff1f') + '" id="shared-help-icon"><span class="aui-icon aui-icon-help">' + soy.$$escapeHtml('\u4ec0\u4e48\u4f1a\u88ab\u5171\u4eab\uff1f') + '</span></a></p>' + aui.form.fieldGroup({extraClasses: 'project-picker-group', content: '' + aui.form.label({forField: 'project-picker', isRequired: false, content: '\u9009\u62e9\u4e00\u4e2a\u9879\u76ee'}) + aui.form.input({id: 'project-picker', name: 'project', type: 'text'}) + '<div id="project-picker-options" data-suggestions="' + soy.$$escapeHtml(opt_data.projectSuggestions) + '"></div>'}) + '<p class="create-shared-info">' + soy.$$escapeHtml('\u5f53\u4e00\u4e2a\u914d\u7f6e\u88ab\u591a\u4e2a\u9879\u76ee\u5171\u4eab\u65f6\uff0c\u8fd9\u610f\u5473\u7740\u5bf9\u914d\u7f6e\u7684\u4efb\u4f55\u6539\u52a8\u90fd\u4f1a\u5f71\u54cd\u6240\u6709\u9879\u76ee\u3002') + '</p>'}), id: 'create-shared-project-form'}) + '</div></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.createSharedProjectForm.soyTemplateName = 'JIRA.Templates.ProjectTemplates.createSharedProjectForm';
}


JIRA.Templates.ProjectTemplates.loading = function(opt_data, opt_ignored) {
  return '<div><div class="dialog-spinner"></div></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.loading.soyTemplateName = 'JIRA.Templates.ProjectTemplates.loading';
}


JIRA.Templates.ProjectTemplates.noProjects = function(opt_data, opt_ignored) {
  return '<p>' + soy.$$escapeHtml('\u770b\u8d77\u6765\u6ca1\u6709\u5176\u4ed6\u9879\u76ee\u7684\u5b58\u5728\u3002\u8bf7\u521b\u5efa\u5168\u65b0\u7684\u9879\u76ee\u7b2c\u4e00\u6b21\u5c1d\u8bd5\u4e4b\u524d\u8981\u5171\u4eab\u73b0\u6709\u9879\u76ee\u7684\u914d\u7f6e\u3002') + '</p>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.noProjects.soyTemplateName = 'JIRA.Templates.ProjectTemplates.noProjects';
}


JIRA.Templates.ProjectTemplates.sharedHelp = function(opt_data, opt_ignored) {
  return '<div class="shared-help"><p><strong>' + soy.$$escapeHtml('\u4ec0\u4e48\u4f1a\u88ab\u5171\u4eab\uff1f') + '</strong></p><p>' + soy.$$escapeHtml('\u914d\u7f6e\u5305\u542b\u9879\u76ee\u4f7f\u7528\u7684\u65b9\u6848\u7ec4\u5408') + '</p><ul><li>' + soy.$$escapeHtml('\u6743\u9650\u65b9\u6848') + '</li><li>' + soy.$$escapeHtml('\u901a\u77e5\u65b9\u6848') + '</li><li>' + soy.$$escapeHtml('\u95ee\u9898\u5b89\u5168\u65b9\u6848') + '</li><li>' + soy.$$escapeHtml('\u5de5\u4f5c\u6d41\u65b9\u6848') + '</li><li>' + soy.$$escapeHtml('\u95ee\u9898\u7c7b\u578b\u65b9\u6848') + '</li><li>' + soy.$$escapeHtml('\u95ee\u9898\u7c7b\u578b\u9875\u9762\u65b9\u6848') + '</li><li>' + soy.$$escapeHtml('\u57df\u914d\u7f6e\u65b9\u6848') + '</li></ul></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.sharedHelp.soyTemplateName = 'JIRA.Templates.ProjectTemplates.sharedHelp';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/Config.js' */
define("jira/project-templates/config",{model:{}});AJS.namespace("JPT.ConfigModel",null,require("jira/project-templates/config"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/ProjectKeyGenerator.js' */
define("jira/project-templates/project-key-generator",["jquery","underscore"],function(d,h){var c={};c.IGNORED_WORDS=["THE","A","AN","AS","AND","OF","OR"];c.CHARACTER_MAP={};c.CHARACTER_MAP[199]="C";c.CHARACTER_MAP[231]="c";c.CHARACTER_MAP[252]="u";c.CHARACTER_MAP[251]="u";c.CHARACTER_MAP[250]="u";c.CHARACTER_MAP[249]="u";c.CHARACTER_MAP[233]="e";c.CHARACTER_MAP[234]="e";c.CHARACTER_MAP[235]="e";c.CHARACTER_MAP[232]="e";c.CHARACTER_MAP[226]="a";c.CHARACTER_MAP[228]="a";c.CHARACTER_MAP[224]="a";c.CHARACTER_MAP[229]="a";c.CHARACTER_MAP[225]="a";c.CHARACTER_MAP[239]="i";c.CHARACTER_MAP[238]="i";c.CHARACTER_MAP[236]="i";c.CHARACTER_MAP[237]="i";c.CHARACTER_MAP[196]="A";c.CHARACTER_MAP[197]="A";c.CHARACTER_MAP[201]="E";c.CHARACTER_MAP[230]="ae";c.CHARACTER_MAP[198]="Ae";c.CHARACTER_MAP[244]="o";c.CHARACTER_MAP[246]="o";c.CHARACTER_MAP[242]="o";c.CHARACTER_MAP[243]="o";c.CHARACTER_MAP[220]="U";c.CHARACTER_MAP[255]="Y";c.CHARACTER_MAP[214]="O";c.CHARACTER_MAP[241]="n";c.CHARACTER_MAP[209]="N";c.desiredKeyLength=4;c.maxKeyLength=10;c.getTotalLength=function a(k){return k.join("").length};c.removeIgnoredWords=function f(k){return h.reject(k,function(l){return d.inArray(l,c.IGNORED_WORDS)!==-1})};c.createAcronym=function b(l){var k="";d.each(l,function(m,n){k+=n.charAt(0)});return k};c.getFirstSyllable=function i(m){var l=false;var k;for(k=0;k<m.length;k++){if(c.isVowelOrY(m[k])){l=true}else{if(l){return m.substring(0,k+1)}}}return m};c.isVowelOrY=function g(k){return k&&k.length===1&&k.search("[AEIOUY]")!==-1};c.init=function j(l,k){c.desiredKeyLength=l;c.maxKeyLength=k};c.generate=function e(l){l=d.trim(l);if(!l){return""}var k=[];for(var n=0,p=l.length;n<p;n++){var o=c.CHARACTER_MAP[l.charCodeAt(n)];k.push(o?o:l[n])}l=k.join("");var r=[];d.each(l.split(/\s+/),function(s,t){if(t){t=t.replace(/[^a-zA-Z]/g,"");t=t.toUpperCase();t.length&&r.push(t)}});if(c.desiredKeyLength&&c.getTotalLength(r)>c.desiredKeyLength){r=c.removeIgnoredWords(r)}var m;if(r.length==0){m=""}else{if(r.length==1){var q=r[0];if(c.desiredKeyLength&&q.length>c.desiredKeyLength){m=c.getFirstSyllable(q)}else{m=q}}else{m=c.createAcronym(r)}}if(c.maxKeyLength&&m.length>c.maxKeyLength){m=m.substr(0,c.maxKeyLength)}return m};return c});AJS.namespace("JPT.ProjectKeyGenerator",null,require("jira/project-templates/project-key-generator"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/DialogView.js' */
define("jira/project-templates/dialog-view-impl",["require","backbone","underscore"],function(e,f,d){var b=AJS.Dialog;var a=AJS.trigger;return f.View.extend({events:{"click   .add-project-trigger":"_onClickAddProject","click   .add-demo-project-trigger":"_onClickAddDemoProject","click   .add-workflow-link":"_onClickViewMarketplaceWorkflows","click   #add-project-dialog .add-demo-project-trigger":"_onCreateProjectAddDemoProjectClick","click   #add-project-dialog .import-project-trigger":"_onClickImportProject","click   #add-project-dialog .create-with-shared-config":"_onClickCreateShared","keydown #add-project-dialog .pt-templates-list":"_onKeydownInTemplatesList"},draw:function(){var g=new b({width:840,height:400,id:"add-project-dialog",closeOnOutsideClick:false,keypressListener:d.bind(this._onKeyPressed,this)});var h=g.popup.element;g.addPage("project-templates-page");g.addHeader("\u521b\u5efa\u9879\u76ee","add-project-dialog-header");g.addPanel("ProjectTemplatesListPanel",JIRA.Templates.ProjectTemplates.loadingTemplatesList(),"pt-content");this._dialog=g;this._$dialogElement=h;g.show();return g},remove:function(){this._dialog&&this._dialog.remove()},showErrorMessage:function(g){this._dialog.getPanel(1,0).html(JIRA.Templates.errorMsg({closable:false,msg:g}))},get$PTContent:function c(){return this.getDialogController().$dialogElement.find(".pt-content")},_onKeyPressed:function(h){var g=27;if(this._dialog){if(h.keyCode===g){this.getDialogController().hideDialogFromNewUser("dismissed");this._dialog.remove();return false}}return true},_onKeydownInTemplatesList:function(h){var g=13;if(this._dialog){if(h.keyCode===g){this._$dialogElement.find(".pt-submit-button:visible").click();return false}}return true},_onClickAddProject:function(g){g.preventDefault();this.getDialogController().handleProjectTemplateTriggered()},_onCreateProjectAddDemoProjectClick:function(){a("analyticsEvent",{name:"jira.project.templates.dialog.create.demo.create.project.clicked"})},_onClickViewMarketplaceWorkflows:function(){a("analyticsEvent",{name:"jira.project.templates.dialog.create.viewmarketplaceworkflows.clicked"})},_onClickAddDemoProject:function(h){h.preventDefault();var g=this.getDialogController().dialog;if(g&&g.popup&&g.popup.element){g.remove()}this.getDialogController().handleDemoProjectTemplateTriggered()},_onClickImportProject:function(g){this.getDialogController().hideDialogFromNewUser("importproject")},_onClickCreateShared:function(g){this.getDialogController().handleCreateShared()},getDialogController:function(){return e("jira/project-templates/dialog-controller")}})});define("jira/project-templates/dialog-view",["jira/project-templates/dialog-view-impl","jquery"],function(b,a){return new b({el:a(document)})});AJS.namespace("JPT.DialogView",null,require("jira/project-templates/dialog-view"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/DialogController.js' */
define("jira/project-templates/dialog-controller",["jira/project-templates/dialog-view","jira/project-templates/config","jira/ajs/ajax/smart-ajax","jira/util/data/meta","jira/lib/class","jquery","underscore","wrm/data"],function(n,e,a,g,b,k,o,m){var d=AJS.contextPath();var f=AJS.isDevMode;var i=AJS.log;function h(p,q){AJS.trigger("analyticsEvent",{name:p,data:q})}var l;var c=function(){if(!l){l=m.claim("com.atlassian.jira.************************:************************-resources.ptAnalyticsData")||{}}return l};var j=b.extend({WELCOME_REST_URL:d+"/rest/welcome/1.0/show/welcome",initialize:function(){o.bindAll(this,"handleProjectTemplateTriggered","hideDialogFromNewUser");if(g.getBoolean("show-welcome-screen")){this.handleProjectTemplateTriggered()}},handleProjectTemplateTriggered:function(){this.demoProject=false;h("jira.project.templates.dialog.create.show");this.openWithFirstProjectTemplateOfTypePreSelected()},handleDemoProjectTemplateTriggered:function(){this.demoProject=true;var p=this.getTemplateController().loadDemoProjectTemplatesData();this.openWithFirstProjectTemplateOfTypePreSelected(null,p);this._addAnalyticsToCreateDemoShow(p)},_addAnalyticsToCreateDemoShow:function(p){p.done(function(r){var s={};var q=function(t){return t.projectTemplatesGroupedByType.map(function(u){return u.projectTemplates.map(function(v){return v.itemModuleCompleteKey}).join()}).join()};s.demoSets=q(r);s.instanceCreatedDate=c().instanceCreatedDate;h("jira.project.templates.dialog.create.demo.show",s)}.bind(this))},openWithFirstProjectTemplateOfTypePreSelected:function(p,q){q=q||this.getTemplateController().loadProjectTemplatesData();this.dialog=n.draw();q.fail(o.bind(function(r){this._handleUnknownErrorOfPTRetrieval()},this));q.done(o.bind(function(r){this.$dialogElement=this.dialog.popup.element;var s;if(!o.isUndefined(e.model.selectedTemplate)){s=e.model.selectedTemplate}e.model=r;e.model.selectedTemplate=s;this.getTemplateController().init(r,p)},this))},handleCreateShared:function(){h("jira.project.templates.dialog.create.shared.clicked");this.demoProject=false;this.openWithCreateShared()},openWithCreateShared:function(){var p=require("jira/project-templates/create-shared-controller");p.initCreateShared()},callbackWithResize:function(p){return o.bind(o.wrap(p,function(q){q.call(this);this.dialog.updateHeight()}),this)},addPage:function(r){var q=this.dialog.addPage(r.name).page[this.dialog.curpage];this.dialog.addHeader(r.title,"add-project-dialog-header");this.dialog.addPanel(r.panelName,"","pt-content");if(r.backButton){this._addBackButton(q)}this.dialog.addButton(r.submitButtonText,this.callbackWithResize(r.submitButtonCallback),r.submitButtonClass+" pt-submit-button");var p=this.$dialogElement.find("."+r.submitButtonClass);p.removeClass("button-panel-button").addClass("aui-button aui-button-primary");p.focus();this.dialog.addCancel("\u53d6\u6d88",o.bind(function(s){this.hideDialogFromNewUser("dismissed");this.dialog.remove()},this));return q},_backButtonOnClickCallback:function(p){return o.bind(function(){if(this.demoProject){h("jira.project.templates.dialog.demo.back")}else{h("jira.project.templates.dialog.create.back")}var q=n.get$PTContent();q.css("background-image","none");this.dialog.prevPage();p.remove();this.dialog.page.pop()},this)},_addBackButton:function(q){this.dialog.addButton("\u56de\u9000",this.callbackWithResize(this._backButtonOnClickCallback(q)),"add-project-back-button");var p=this.$dialogElement.find(".add-project-back-button");p.removeClass("button-panel-button").addClass("aui-button")},_handleUnknownErrorOfPTRetrieval:function(){n.showErrorMessage("\u8bd5\u56fe\u8fde\u63a5Jira\u7684\u65f6\u5019\u53d1\u751f\u4e86\u4e00\u4e2a\u9519\u8bef\u3002")},hideDialogFromNewUser:function(p){if(p==="dismissed"){if(this.demoProject){h("jira.project.templates.dialog.demo.dismissed")}else{h("jira.project.templates.dialog.create.dismissed")}}else{if(p==="importproject"){h("jira.project.templates.dialog.import.clicked")}else{if(p==="templateselected"){if(this.demoProject){h("jira.project.templates.dialog.demo.templateselected")}else{h("jira.project.templates.dialog.create.templateselected")}}}}if(g.getBoolean("show-welcome-screen")){k.ajax({url:this.WELCOME_REST_URL+"/"+p,type:"DELETE",success:function(){if(f&&f()){i("don't show project template dialog anymore")}}})}},getTemplateController:function(){return require("jira/project-templates/select-project-template-controller")}});return new j()});AJS.namespace("JPT.DialogController",null,require("jira/project-templates/dialog-controller"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/AddProjectView.js' */
define("jira/project-templates/add-project-view-impl",["jira/project-templates/dialog-controller","jira/project-templates/config","jira/util/events","jira/util/events/types","require","backbone","jquery","underscore"],function(q,n,t,y,m,e,g,D){var f=AJS.InlineDialog;return e.View.extend({TIMEOUT_MS:100,postDrawCallbacks:[],events:{"submit #add-project-form":"onSubmitForm"},page:undefined,isSubmitting:false,initialize:function(F){D.bindAll(this,"bindHook")},addPostDrawCallback:function(F){this.postDrawCallbacks.push(F)},prepareDialog:function(F){this.page=q.addPage({name:"add-project",title:F,panelName:"add-project",backButton:true,submitButtonText:"\u63d0\u4ea4",submitButtonCallback:this.onSubmitForm.bind(this),submitButtonClass:"add-project-dialog-create-button"})},draw:function(U){function V(X){if(!X.errors){X.errors={}}return X}this.isSubmitting=false;q.dialog.gotoPage(this.page.id);var G=U.webItemData.projectTemplateModuleCompleteKey;var F=D.find(n.model.projectTemplatesGroupedByType,function(X){return D.any(X.projectTemplates,function(Y){return Y.itemModuleCompleteKey==G})});var L=(F&&F.applicationInfo&&F.applicationInfo.applicationName)||"";var K=(F&&F.applicationInfo&&F.applicationInfo.licenseUsedSeats)||0;var H=(F&&F.applicationInfo&&F.applicationInfo.licenseTotalSeats)||0;var T=(F&&F.applicationInfo&&F.applicationInfo.canUserBeAddedToApplication)||false;var J=(F&&F.applicationInfo&&F.applicationInfo.canUserUseApplication)||false;var P=(F&&F.applicationInfo&&F.applicationInfo.ondemand)||false;var I;if(!F||!F.applicationInfo){I=false}else{I=!J}var R={maxNameLength:n.model.maxNameLength,maxKeyLength:n.model.maxKeyLength,shouldShowLead:n.model.shouldShowProjectLead,projectTemplateWebItemKey:n.model.selectedTemplate||U.webItemData.itemModuleCompleteKey,projectTemplateModuleKey:G,projectTemplateTitle:U.webItemData.name,projectTemplateDescriptionContent:U.webItemData.longDescriptionContent,currentKey:U.currentKey||"",currentName:U.currentName||"",errors:V(U.errors),projectLeadPickerField:{id:"lead",name:"lead"},addUserToLicense:{displayCheckbox:I,disableCheckbox:!T,applicationName:L,usedSeats:K,totalSeats:H,fieldId:"licenseUser",fieldName:"licenseUser",licensingUrl:P?"/admin/accessconfig":"/plugins/servlet/applications/versions-licenses"},leadOptions:[{selected:true,displayName:U.currentUserDisplayName,optionName:U.currentUserName,avatarURL:U.currentUserAvatarUrl}]};var N=JIRA.Templates.ProjectTemplates.addProjectForm(R);q.dialog.getPanel(this.page.id,0).html(N);if(U.webItemData&&U.webItemData.backgroundIconUrl){var Q=q.$dialogElement.find(".pt-content");Q.css("background-image",'url("'+U.webItemData.backgroundIconUrl+'")')}var O=g("#add-project-form");t.trigger(y.NEW_CONTENT_ADDED,[O]);this.nameElement=q.$dialogElement.find("#name");this.keyElement=q.$dialogElement.find("#key");this.keyEditedElement=q.$dialogElement.find("#keyEdited");this.leadDisplayElement=q.$dialogElement.find("#lead-field");this.leadValueElement=q.$dialogElement.find("#lead");var W=this.keyElement.parent().find(".aui-icon-help");if(W.length){new f(W,"project-key-help-popup",function(Z,Y,X){Z.html(JIRA.Templates.ProjectTemplates.keyHelp());X()},{width:330,offsetX:-30})}this.keyElement.attr("style","text-transform: uppercase");var S=m("jira/project-templates/add-project-controller");this.nameElement.focus(D.bind(function(X){this.bindHook(X,S.nameTimeout)},this));var M=this;this.nameElement.change(function(X){S.validateName();M.unbindHook(X)});this.nameElement.focus();this.keyElement.focus(D.bind(function(Y){var X=g(Y.target);X.data("lastValue",X.val());this.bindHook(Y,S.keyTimeout)},this));this.keyElement.blur(D.bind(function(X){this.unbindHook(X)},this));this.keyElement.change(function(){S.validateKey();S.autofillKeyIfNeeded()});if(!D.isEmpty(this.postDrawCallbacks)){D.each(this.postDrawCallbacks,function(X){X()})}q.dialog.updateHeight()},onSubmitForm:function(G){var F=m("jira/project-templates/add-project-controller");F.submit();return false},get$SubmitButton:function(){return q.$dialogElement.find(".add-project-dialog-create-button")},get$BackButton:function(){return q.$dialogElement.find(".add-project-back-button")},bindHook:function p(I,G){var F=g(I.target),H;H=D.bind(function(){this.unbindHook(I);G.apply();if(F.is(":visible")){F.data("checkHook",setTimeout(H,this.TIMEOUT_MS))}},this);if(!F.data("checkHook")){F.data("checkHook",setTimeout(H,0))}},unbindHook:function(G){var F=g(G.target);clearTimeout(F.data("checkHook"));F.removeData("checkHook")},showInlineError:function(G,H){if(this.isSubmitting){return}var F=G.parent().find(".error");if(!F.length){F=g("<div class='error'></div>");G.parent().append(F)}F.text(H);F.show()},showInlineErrorForName:function E(F){this.showInlineError(this.nameElement,F)},showInlineErrorForKey:function A(F){this.showInlineError(this.keyElement,F)},hideInlineError:function z(F){F.parent().find(".error").hide()},hideInlineErrorForName:function c(){this.hideInlineError(this.nameElement)},hideInlineErrorForKey:function u(){this.hideInlineError(this.keyElement)},setName:function i(F){this.nameElement.val(F)},getName:function s(){return this.nameElement.val()},setKey:function b(F){this.keyElement.val(F)},getKey:function o(){return this.keyElement.val().toUpperCase()},getLeadDisplayName:function v(){return this.leadDisplayElement.val()},getLeadUserName:function w(){return this.leadValueElement.val()},getAvatarUrlOfSelectedLead:function r(){var F=q.$dialogElement.find("#lead-single-select .aui-ss-entity-icon").css("background-image");if(!D.isUndefined(F)){var G=F.match(/^url\((.+)\)$/);return(G&&G[1])?G[1]:""}else{return""}},setKeyEdited:function l(F){this.keyEditedElement.val(F)},getKeyEdited:function B(){return this.keyEditedElement.val()},setKeyEdited:function l(){var F=this.getKey();if(this.keyElement.data("lastValue")!==F){this.keyEditedElement.val((F)?"true":"false")}this.keyElement.data("lastValue",F)},hasNameErrors:function d(){return this.nameElement.parent().find(".error").size()>0},getAddProjectForm:function h(){return g("#add-project-form")},get$FormFields:function k(){return q.$dialogElement.find(":input")},enterLoadingState:function j(){$submitButton=this.get$SubmitButton();if(!$submitButton.attr("disabled")){$backButton=this.get$BackButton();$submitButton.attr("disabled","disabled");$backButton.attr("disabled","disabled");$backButton.before(JIRA.Templates.ProjectTemplates.spinner({id:"addproject-loading"}));this.get$FormFields().attr("disabled","disabled");this.isSubmitting=true;return true}else{return false}},hideLoadingState:function x(){q.$dialogElement.find("#addproject-loading").remove();this.get$SubmitButton().removeAttr("disabled");this.get$BackButton().removeAttr("disabled");this.get$FormFields().removeAttr("disabled")},avoidDirtyFormWarning:function a(){if(g.fn.removeDirtyWarning){this.getAddProjectForm().removeDirtyWarning()}},hasInlineErrors:function C(){return q.$dialogElement.find(".field-group .error:visible").length!=0}})});define("jira/project-templates/add-project-view",["jira/project-templates/add-project-view-impl","jira/project-templates/dialog-view","jquery"],function(b,a,c){return new b({el:c(document),dialogView:a})});AJS.namespace("JPT.AddProjectView",null,require("jira/project-templates/add-project-view"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/AddProjectController.js' */
define("jira/project-templates/add-project-controller-impl",["jira/project-templates/add-project-view","jira/project-templates/project-key-generator","jira/project-templates/dialog-controller","jira/project-templates/config","jira/lib/class","jquery","underscore"],function(m,i,k,e,b,l,o){var d=AJS.contextPath();var c=AJS.trigger;return b.extend({postProjectCreationCallbacks:[],projectKeyValidationCallbacks:[],projectNamesUpperCased:[],init:function(p){this._window=p.window||window;o.bindAll(this,"projectCreatedHandler","projectValidationFailedHandler","nameTimeout","keyTimeout","submit")},initCreateShared:function(p){this.existingProjectData=p;this._initAddProject("\u521b\u5efa\u4e0e\u5171\u4eab\u914d\u7f6e")},initProjectTemplate:function(p){this.existingProjectData=undefined;this.webItemData=p;this._initAddProject(p.name)},_initAddProject:function(p){this.desiredKeyLength=4;this.lastKeyValidated="";this._getExistingProjects();i.init(this.desiredKeyLength,e.model.maxKeyLength);m.prepareDialog(p);m.draw({webItemData:this.webItemData||{},maxKeyLength:e.model.maxKeyLength,maxNameLength:e.model.maxNameLength,currentUserName:e.model.currentUserName,currentUserDisplayName:e.model.currentUserDisplayName,currentUserAvatarUrl:e.model.currentUserAvatarUrl,errors:{}})},_resetProjectLeadValues:function(){e.model.currentUserDisplayName="";e.model.currentUserName="";e.model.currentUserAvatarUrl=""},_hasFullyConfiguredProjectLead:function(){return m.getLeadDisplayName()&&m.getAvatarUrlOfSelectedLead()!=""},submit:function(p){var q;if(this.existingProjectData){q={key:m.getKey(),name:m.getName(),lead:m.getLeadUserName()&&m.getLeadUserName()[0]}}else{q=jQuery.param(m.getAddProjectForm().serializeArray().map(function(r){if(r.name==="key"){r.value=r.value.toUpperCase()}return r}))}if(m.hasInlineErrors()){return}if(m.enterLoadingState()){if(this._hasFullyConfiguredProjectLead()){e.model.currentUserDisplayName=m.getLeadDisplayName();e.model.currentUserName=e.model.currentUserDisplayName?m.getLeadUserName():"";e.model.currentUserAvatarUrl=m.getAvatarUrlOfSelectedLead()}else{this._resetProjectLeadValues()}if(this.existingProjectData){l.ajax({url:d+"/rest/project-templates/1.0/createshared/"+this.existingProjectData.existingProjectId,type:"POST",contentType:"application/json",data:JSON.stringify(q)}).done(this.projectCreatedHandler).fail(this.projectValidationFailedHandler)}else{l.ajax({url:d+(this.webItemData.demoProject?"/rest/jira-importers-plugin/1.0/demo/create":"/rest/project-templates/1.0/templates"),type:"POST",data:q,headers:{"X-Atlassian-Token":"no-check"}}).done(this.projectCreatedHandler).fail(this.projectValidationFailedHandler)}}},registerPostProjectCreationCallback:function(p){this.postProjectCreationCallbacks.push(p)},registerProjectKeyValidationCallback:function(p){this.projectKeyValidationCallbacks.push(p)},localStoragePrefix:"jira.projecttemplates.",projectCreatedHandler:function(q){if(this.existingProjectData){c("analyticsEvent",{name:"jira.project.templates.dialog.create.shared.project.create.success"})}else{if(this.webItemData.demoProject){c("analyticsEvent",{name:"jira.project.templates.dialog.create.demo.success"})}else{c("analyticsEvent",{name:"jira.project.templates.dialog.create.project.success"})}}m.avoidDirtyFormWarning();var p=this.localStoragePrefix;var r=["confluenceProject","fisheyeProject","crucibleProject","bambooProject"];o.map(r,function(s){if(q.remoteProjectLinks&&q.remoteProjectLinks[s]){localStorage.setItem(p+s,q.remoteProjectLinks[s])}});if(!o.isEmpty(this.postProjectCreationCallbacks)){k.dialog.addPage("post-project-created-page");o.each(this.postProjectCreationCallbacks,function(s){s(k.dialog,q.returnUrl,q.projectId,q.projectKey,q.projectName)})}else{this._window.location=d+q.returnUrl}},projectValidationFailedHandler:function(q,s){if(this.existingProjectData){c("analyticsEvent",{name:"jira.project.templates.dialog.create.shared.project.create.failure"})}else{if(this.webItemData.demoProject){c("analyticsEvent",{name:"jira.project.templates.dialog.create.demo.failure"})}else{c("analyticsEvent",{name:"jira.project.templates.dialog.create.project.failure"})}}var r={};if(this.isBadRequest(q)){r=JSON.parse(q.responseText)}else{if(this.isUnDefinedServerSideError(q)){r={errorMessages:["\u55ef...\u6211\u4eec\u65e0\u6cd5\u521b\u5efa\u60a8\u7684\u9879\u76ee\u7531\u4e8e\u672a\u77e5\u7684\u9519\u8bef\u3002\u8bf7\u5c1d\u8bd5\u5237\u65b0\u9875\u9762\u5e76\u91cd\u65b0\u5f00\u59cb\u3002"]}}else{if(this.isDefinedServerSideError(q)){var p=JSON.parse(q.responseText);JIRA.Messages.showReloadErrorMsg(p.message);m.avoidDirtyFormWarning();this._window.location=d+p.returnUrl;return}else{if(this.isTimeoutError(s)){r={errorMessages:["\u521b\u5efa\u9879\u76ee\u7684\u65f6\u5019\u8bf7\u6c42\u8d85\u65f6"]}}else{r={errorMessages:[AJS.format("\u521b\u5efa\u9879\u76ee {0} \u65f6\u53d1\u751f\u9519\u8bef",q.responseText)]}}}}}m.draw({webItemData:this.webItemData||{},errors:r,currentName:m.getName(),currentKey:m.getKey(),currentUserDisplayName:e.model.currentUserDisplayName,currentUserName:e.model.currentUserName,currentUserAvatarUrl:e.model.currentUserAvatarUrl});m.hideLoadingState()},isBadRequest:function(p){return(p.status===400)},isUnDefinedServerSideError:function(p){if(p.status===500){try{JSON.parse(p.responseText)}catch(q){return true}}return false},isDefinedServerSideError:function(p){return p.status===500&&!o.isUndefined(JSON.parse(p.responseText).message)},isTimeoutError:function(p){return p==="timeout"},_updateAndValidateKey:function a(p){m.setKey(p);this.validateKey()},_shouldUpdateKey:function h(){return(m.getKeyEdited()!="true")},autofillKeyIfNeeded:function n(){if(this._shouldUpdateKey()){var p=i.generate(m.getName());if(p.length>1){this._updateAndValidateKey(p)}else{m.setKey("")}}},_doesProjectNameExists:function(q){var p;for(p in this.projectNamesUpperCased){if(q.toUpperCase()==this.projectNamesUpperCased[p]){return true}}return false},validateName:function(){var p=l.trim(m.getName());if(!p){return}if(p.length<e.model.minNameLength){m.showInlineErrorForName(AJS.format("\u9879\u76ee\u540d\u79f0\u81f3\u5c11\u5e94\u8be5 {0} \u4e2a\u5b57\u7b26\u3002",e.model.minNameLength));return}if(p.length>e.model.maxNameLength){m.showInlineErrorForName(AJS.format("\u9879\u76ee\u540d\u79f0\u957f\u5ea6\u4e0d\u80fd\u8d85\u8fc7 {0} \u4e2a\u5b57\u7b26\u3002",e.model.maxNameLength));return}if(this._doesProjectNameExists(p)){m.showInlineErrorForName("\u5df2\u7ecf\u5b58\u5728\u540c\u540d\u9879\u76ee");return}m.hideInlineErrorForName()},_performKeyValidationChecks:function(p){var q=l.ajax({url:d+"/rest/api/latest/projectvalidate/key?key="+p.toUpperCase()});q.done(o.bind(function(s){if(s.errors&&s.errors.projectKey){m.showInlineErrorForKey(s.errors.projectKey)}else{var r=false;o.each(this.projectKeyValidationCallbacks,function(u){var t=u(p.toUpperCase());if(t.errors&&t.errors.projectKey){r=true;m.showInlineErrorForKey(t.errors.projectKey)}});if(!r){m.hideInlineErrorForKey()}}},this))},validateKey:function g(){var p=m.getKey();if(this.lastKeyValidated===p){return}if(p){this.lastKeyValidated=p;this._performKeyValidationChecks(p)}else{m.hideInlineErrorForKey()}},nameTimeout:function j(){this.autofillKeyIfNeeded()},keyTimeout:function f(){m.setKeyEdited()},_getExistingProjects:function(){if(this.projectNamesUpperCased.length>0){return this.projectNamesUpperCased}var p=l.ajax({url:d+"/rest/api/latest/project"});p.done(o.bind(function(q){this.projectNamesUpperCased=o.map(q,function(r){return r.name.toUpperCase()})},this))}})});define("jira/project-templates/add-project-controller",["jira/project-templates/add-project-controller-impl","jquery"],function(a,b){return new a({el:b(document)})});AJS.namespace("JPT.AddProjectController",null,require("jira/project-templates/add-project-controller"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/TemplateInfoView.js' */
define("jira/project-templates/template-info-view",["jira/project-templates/dialog-controller","require","backbone","jquery","underscore"],function(e,c,f,d,b){var a=AJS.trigger;return f.View.extend({initialize:function(g){b.bindAll(this,"draw","onNext")},draw:function(j,h){var k=e.addPage({name:"template-info",title:h,panelName:"template-info",backButton:true,submitButtonText:"\u9009\u62e9",submitButtonCallback:this.onNext,submitButtonClass:"template-info-dialog-create-button"});var g=this._nameToObj(j)();e.dialog.getPanel(k.id,0).html(g);e.dialog.gotoPage(k.id);var i=b.find(k.button,function(l){return l.item&&l.item.hasClass("pt-submit-button")});if(i){setTimeout(function(){i.item.focus()},0)}},_nameToObj:function(g){return b.reduce(g.split("."),function(i,h){if(i){return i[h]}},window)},onNext:function(h){a("analyticsEvent",{name:"jira.project.templates.dialog.create.templateinfo.next"});var g=c("jira/project-templates/template-info-controller");g.next();return false}})});AJS.namespace("JPT.TemplateInfoView",null,require("jira/project-templates/template-info-view"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/TemplateInfoController.js' */
define("jira/project-templates/template-info-controller",["jira/project-templates/template-info-view","jira/lib/class","require","underscore"],function(e,a,c,b){var d=a.extend({init:function(f){b.bindAll(this,"initTemplateInfo","next")},initTemplateInfo:function(g){this.projectTemplateData=g;var f=new e();f.draw(g.infoSoyPath,g.name)},next:function(){var f=c("jira/project-templates/select-project-template-controller");f.openAddProjectPage(this.projectTemplateData)}});return new d()});AJS.namespace("JPT.TemplateInfoControllerImpl",null,require("jira/project-templates/template-info-controller"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/SelectProjectTemplateView.js' */
define("jira/project-templates/select-project-template-view",["jira/project-templates/dialog-controller","jira/project-templates/config","jira/featureflags/feature-manager","require","backbone","jquery","underscore"],function(d,b,i,c,g,e,h){var a=AJS.contextPath();var f=AJS.preventDefault;return{ROW_LENGTH:2,DIALOG_BODY_CLASS:"select-project-templates-page",DIALOG_WIDTH:800,draw:function(n,m){var j=c("jira/project-templates/select-project-template-controller");this.page=d.addPage({name:this.DIALOG_BODY_CLASS,title:n.demoProjects?"\u4f7f\u7528\u793a\u4f8b\u6570\u636e\u521b\u5efa\u65b0\u9879\u76ee":"\u521b\u5efa\u9879\u76ee",panelName:"ProjectTemplatesListPanel",backButton:false,submitButtonText:"\u4e0b\u4e00\u6b65",submitButtonCallback:j.dialogSubmitted,submitButtonClass:"create-project-dialog-create-button"});function p(){if(j.isProjectTypesEnabled(n)){return JIRA.Templates.ProjectTemplates.renderProjectTemplatesGroupedByType({projectTemplatesByType:n.projectTemplatesGroupedByType})}return JIRA.Templates.ProjectTemplates.renderProjectTemplates({projectTemplates:n.projectTemplates})}d.dialog.getPanel(this.page.id,0).html(p());if(b.model.projectCount>=2&&!n.demoProjects){e(JIRA.Templates.ProjectTemplates.addWorkflowsLink({baseUrl:a})).appendTo(d.$dialogElement.find(".dialog-title"))}var o=d.$dialogElement.find(".create-project-dialog-create-button");var l=this;this.getTemplateItems().click(function(){var q=e(this);q.addClass("selected");l.getTemplateItems().not(this).removeClass("selected")}).dblclick(function(){o.click()}).focus(function(){e(this).click()});var k=this.get$TemplatesContainer();if(!n.demoProjects){e(JIRA.Templates.ProjectTemplates.footerLinks({showDemoLink:i.isFeatureEnabled("jira.onboarding.cyoa")})).prependTo(d.$dialogElement.find(".dialog-button-panel"))}this.bindKeyboardEvents(k);if(j.isProjectTypesEnabled(n)){if((n.projectTemplatesGroupedByType).length==0){o.attr("disabled","disabled")}}else{if((n.projectTemplates).length==0){o.attr("disabled","disabled")}}this.focusOnFirstTemplate(k,m);d.dialog.updateHeight();this.truncateTemplateDescriptions()},get$TemplatesContainer:function(){return d.$dialogElement.find(".templates")},focusOnFirstTemplate:function(j,k){if(k){this.getFirstTemplateItemOfProjectType(k).click()}else{this.getFirstTemplateItem().click()}setTimeout(function(){j.focus()},0)},getSelectedTemplateData:function(){var j=d.$dialogElement.find(".template.selected");return j.data()},getMoveDeltaForKey:function(j){switch(j){case 37:return -1;case 39:return +1;case 38:return -this.ROW_LENGTH;case 40:return +this.ROW_LENGTH}return 0},bindKeyboardEvents:function(j){j.bind("keydown",h.bind(function(k){var l=this.getMoveDeltaForKey(k.which);if(l){this.moveSelection(j,l);return f(k)}},this))},moveSelection:function(l,o){var k=l.find(".template");var n=k.filter(".selected");var m=k.index(n)+o;if(m<k.length&&m>=0){var j=k.eq(m);j.click().focus();this.scrollToSelectedElement(j)}},scrollToSelectedElement:function(l){var o=e(".dialog-panel-body.pt-content");var j=o.offset().top;var k=l.offset().top;var n=k+l.height();var m=j+o.height();if(k<j){o.scrollTop(o.scrollTop()-(j-k))}else{if(n>(j+o.height())){o.scrollTop(o.scrollTop()+n-m)}}},get$NextButton:function(){return d.$dialogElement.find(".create-project-dialog-create-button")},disableNextButton:function(){this.get$NextButton().attr("disabled","disabled")},truncateTemplateDescriptions:function(){var j=AJS.Meta.get("user-locale");var k="word";if(j==="ja_JP"){k="letter"}d.$dialogElement.find(".template-description").each(function(){e(this).dotdotdot({wrap:k,lastCharacter:{remove:[" ",",",";",".","!","?","。"],noEllipsis:[]}})})},getFirstTemplateItem:function(){return this.getTemplateItems().first()},getFirstTemplateItemOfProjectType:function(j){return this.getTemplateItems().filter("#project-template-group-"+j+" *").first()},getTemplateItems:function(){return d.$dialogElement.find(".template")}}});AJS.namespace("JPT.SelectProjectTemplateView",null,require("jira/project-templates/select-project-template-view"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/SelectProjectTemplateController.js' */
define("jira/project-templates/select-project-template-controller",["jira/project-templates/select-project-template-view","jira/project-templates/template-info-controller","jira/project-templates/add-project-controller","jira/project-templates/dialog-controller","jira/project-templates/dialog-view","jira/project-templates/config","wrm/context-path","jquery","underscore"],function(f,h,k,l,o,g,n,m,p){var e=n();var d={init:function q(s,r){f.draw(s,r)},isProjectTypesEnabled:function c(r){return r.projectTemplatesGroupedByType!=null},loadProjectTemplatesData:function j(){return m.ajax({url:e+"/rest/project-templates/1.0/templates",type:"GET"})},loadDemoProjectTemplatesData:function j(){return m.ajax({url:e+"/rest/project-templates/1.0/templates/demo-projects",type:"GET"}).done(function(r){r.demoProjects=true;return r})},dialogSubmitted:function b(){var r=f.getSelectedTemplateData();l.hideDialogFromNewUser("templateselected");d.raiseAtlassianEvent(r.itemModuleCompleteKey);if(!p.isUndefined(r.infoSoyPath)){h.initTemplateInfo(r)}else{d.openAddProjectPage(r)}},openAddProjectPage:function i(r){if(!r){o.showErrorMessage("\u8bd5\u56fe\u8fde\u63a5Jira\u7684\u65f6\u5019\u53d1\u751f\u4e86\u4e00\u4e2a\u9519\u8bef\u3002");return}if(r.createProject){k.initProjectTemplate(r)}else{l.dialog.addPage("blank-template-page");m("body").trigger(r.itemModuleCompleteKey,l.dialog)}},raiseAtlassianEvent:function a(r){g.model.selectedTemplate=r;if(AJS.EventQueue){AJS.EventQueue.push({name:"projecttemplates.templateselected",properties:{selectedTemplate:r}})}}};return d});AJS.namespace("JPT.SelectProjectTemplateController",null,require("jira/project-templates/select-project-template-controller"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/CreateSharedView.js' */
define("jira/project-templates/create-shared-view",["jira/project-templates/dialog-controller","jira/project-templates/dialog-view","jira/ajs/select/scrollable-single-select","jira/ajs/select/suggestion-collection-model","require","backbone","jquery"],function(d,h,g,i,c,j,e){var f=AJS.InlineDialog;var b=AJS.trigger;var a=j.View.extend({events:{"submit #create-shared-project-form":"onSubmitForm"},page:undefined,prepareDialog:function(l){var k=c("jira/project-templates/create-shared-controller");this.page=d.addPage({name:"create-shared-project",title:l,panelName:"create-shared-project",backButton:true,submitButtonText:"\u4e0b\u4e00\u6b65",submitButtonCallback:k.dialogSubmitted,submitButtonClass:"create-shared-dialog-button"})},draw:function(l){d.dialog.gotoPage(this.page.id);var k=JIRA.Templates.ProjectTemplates.createSharedProjectForm({projectSuggestions:JSON.stringify(l.projectSuggestions)});d.dialog.getPanel(this.page.id,0).html(k);this._createProjectPicker();var m=d.$dialogElement.find("#shared-help-icon");if(m.length){new f(m,"shared-project-help-popup",function(p,o,n){p.html(JIRA.Templates.ProjectTemplates.sharedHelp());n()},{width:330,offsetX:-30})}d.$dialogElement.find(".dialog-button-panel button").removeAttr("disabled")},showProjectMissingError:function(){this._clearFormErrors();this._getProjectPickerInput().after(aui.form.fieldError({extraClasses:"project-picker-missing-error",message:"\u8bf7\u5148\u9009\u62e9\u4e00\u4e2a\u73b0\u6709\u7684\u9879\u76ee"}))},drawEmptyInfo:function(){var k=JIRA.Templates.ProjectTemplates.noProjects();d.dialog.getPanel(this.page.id,0).html(k);d.$dialogElement.find(".dialog-button-panel button").hide()},drawError:function(k){d.dialog.getPanel(this.page.id,0).html(JIRA.Templates.errorMsg({closable:false,msg:k}))},drawLoading:function(){d.dialog.gotoPage(this.page.id);var k=JIRA.Templates.ProjectTemplates.loading();d.dialog.getPanel(this.page.id,0).html(k);d.$dialogElement.find(".dialog-spinner").spin();d.$dialogElement.find(".dialog-button-panel button").attr("disabled","disabled")},_clearFormErrors:function(){d.$dialogElement.find(".project-picker-missing-error").remove()},_getProjectPickerInput:function(){return d.$dialogElement.find("#project-picker")},onSubmitForm:function(l){this._clearFormErrors();var k=c("jira/project-templates/create-shared-controller");k.dialogSubmitted();return false},_getExtraInfoMessage:function(){return d.$dialogElement.find(".create-shared-info")},_createProjectPicker:function(){this._getExtraInfoMessage().hide();this.projectSelect=new g({element:this._getProjectPickerInput(),revertOnInvalid:true,pageSize:50,pagingThreshold:100,model:i});this.projectSelect.$field.focus();var k=this;this._getProjectPickerInput().on("selected",function(m,l){if(l.value()){b("analyticsEvent",{name:"jira.project.templates.dialog.create.shared.project.selected"});k._getExtraInfoMessage().show()}})},getSelectedProject:function(){return this.projectSelect.getSelectedDescriptor()&&this.projectSelect.getSelectedDescriptor().value()}});return new a({el:e(document),dialogView:h})});AJS.namespace("JPT.CreateSharedView",null,require("jira/project-templates/create-shared-view"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/CreateSharedController.js' */
define("jira/project-templates/create-shared-controller-impl",["jira/project-templates/add-project-controller","jira/project-templates/create-shared-view","jira/lib/class","jquery","underscore"],function(e,f,a,d,c){var b=AJS.contextPath();return a.extend({init:function(g){this._window=g.window||window;c.bindAll(this,"dialogSubmitted")},initCreateShared:function(){f.prepareDialog("\u521b\u5efa\u4e0e\u5171\u4eab\u914d\u7f6e");var g=this;this._getProjectSuggestions().done(function(h){if(g._hasSuggestions(h)){f.draw({projectSuggestions:h})}else{f.drawEmptyInfo()}}).fail(function(){f.drawError("\u8bd5\u56fe\u8fde\u63a5Jira\u7684\u65f6\u5019\u53d1\u751f\u4e86\u4e00\u4e2a\u9519\u8bef\u3002")});f.drawLoading()},_hasSuggestions:function(h){var g=false;d.each(h,function(j,k){if(k&&k.items&&k.items.length>0){g=true}});return g},_getProjectSuggestions:function(){return d.ajax({url:b+"/rest/project-templates/1.0/createshared"})},dialogSubmitted:function(){var g=f.getSelectedProject();if(g){e.initCreateShared({existingProjectId:g})}else{f.showProjectMissingError()}}})});define("jira/project-templates/create-shared-controller",["jira/project-templates/create-shared-controller-impl","jquery"],function(a,b){return new a({el:b(document)})});AJS.namespace("JPT.CreateSharedController",null,require("jira/project-templates/create-shared-controller"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.************************:************************-resources', location = '/js/RemoteProjectsCreatedMessage.js' */
var $=require("jquery");$(function(){var i=AJS.format;var e=require("jira/project-templates/add-project-controller");var b=localStorage.getItem(e.localStoragePrefix+"confluenceProject");var j=localStorage.getItem(e.localStoragePrefix+"fisheyeProject");var g=localStorage.getItem(e.localStoragePrefix+"crucibleProject");var d=localStorage.getItem(e.localStoragePrefix+"bambooProject");localStorage.removeItem(e.localStoragePrefix+"confluenceProject");localStorage.removeItem(e.localStoragePrefix+"fisheyeProject");localStorage.removeItem(e.localStoragePrefix+"crucibleProject");localStorage.removeItem(e.localStoragePrefix+"bambooProject");var k;function c(m,l){return JIRA.Templates.ProjectTemplates.formatAnchor({href:m,body:l,title:l})}function a(n,o,m,p){var l=[];l.push("Jira \u9879\u76ee");if(n){l.push(c(n,"Confluence \u7a7a\u95f4"))}if(o){l.push(c(o,"FishEye\u5e93"))}if(m){l.push(c(m,"Crucible\u9879\u76ee"))}if(p){l.push(c(p,"Bamboo\u9879\u76ee"))}return l}function h(m,o,l,r){var q="\u4ee5\u4e0b\u5b9e\u4f53\u7684\u521b\u5efa: {0}\u548c{1}\u3002";var n=a(m,o,l,r);var p=n.pop();return(n.length>0)?i(q,n.join(", "),p):null}function f(l){var m=$(l).offset();if(m){window.scrollTo(m.left,m.top)}}if(b||j||g||d){k=h(b,j,g,d);if(k){JIRA.Messages.showSuccessMsg(k,{closeable:true})}f("#project-config-webpanel-summary-settings")}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira-core-project-templates:jira************************resources', location = '/soy/CoreProjectTemplates.soy' */
// This file was automatically generated from CoreProjectTemplates.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.ProjectTemplates.CoreTemplates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.ProjectTemplates == 'undefined') { JIRA.Templates.ProjectTemplates = {}; }
if (typeof JIRA.Templates.ProjectTemplates.CoreTemplates == 'undefined') { JIRA.Templates.ProjectTemplates.CoreTemplates = {}; }


JIRA.Templates.ProjectTemplates.CoreTemplates.taskManagementInfoDialog = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog({description: '\u521b\u5efa\u7b80\u5355\u7684\u4efb\u52a1, \u7ec4\u7ec7\u4ed6\u4eec, \u8ba9\u4ed6\u4eec\u53bb\u505a\u3002\u60a8\u53ef\u4ee5\u4f7f\u7528\u6b64\u9879\u76ee\u6765\u7ba1\u7406\u60a8\u7684\u4efb\u52a1\u6216\u5c06\u5b83\u4eec\u5206\u914d\u7ed9\u5176\u4ed6\u4eba\u3002', projectTemplate: 'taskManagement'});
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.taskManagementInfoDialog.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.taskManagementInfoDialog';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.projectManagementInfoDialog = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog({description: '\u521b\u5efa\u60a8\u7684\u4efb\u52a1\u3001\u7ec4\u7ec7\u548c\u8ddf\u8e2a\u4ed6\u4eec\u7684\u8fdb\u5c55\u3001\u63d0\u4f9b\u60a8\u7684\u5de5\u4f5c\u65f6\u95f4\u3002\u4f30\u8ba1\u548c\u65f6\u95f4\u8ddf\u8e2a\u8ba9\u60a8\u62a5\u544a\u5728\u54ea\u91cc\u60a8\u7684\u9879\u76ee\u6240\u5904\u7684\u9636\u6bb5\u3002', projectTemplate: 'projectManagement'});
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.projectManagementInfoDialog.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.projectManagementInfoDialog';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.processManagementInfoDialog = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog({description: '\u521b\u5efa\u60a8\u7684\u4efb\u52a1\u548c\u8ddf\u8e2a\u4ed6\u4eec\u7684\u6bcf\u4e2a\u6b65\u9aa4, \u4ece\u5f00\u59cb\u5230\u7ed3\u675f\u3002\u60a8\u53ef\u4ee5\u4f7f\u7528\u6b64\u9879\u76ee, \u5ba1\u67e5\u6587\u6863\u3001\u5ba1\u6279\u8d39\u7528\u3001\u6216\u5176\u4ed6\u8fdb\u7a0b\u3002', projectTemplate: 'processManagement'});
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.processManagementInfoDialog.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.processManagementInfoDialog';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.boardBetaInfoDialog = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog({description: '\u8de8\u56e2\u961f\u677f\u9762\u53ef\u89c6\u5316\u5de5\u4f5c\u3002', projectTemplate: 'projectManagement', issueTypesHtml: '' + JIRA.Templates.ProjectTemplates.CoreTemplates.taskIssueType(null)});
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.boardBetaInfoDialog.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.boardBetaInfoDialog';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog = function(opt_data, opt_ignored) {
  return '<div><div class="top-panel"><p>' + soy.$$escapeHtml(opt_data.description) + '</p></div><div class="left-panel"><h6>' + soy.$$escapeHtml('\u95ee\u9898\u7c7b\u578b') + '</h6><ul class="project-template-issuetype-list">' + ((opt_data.issueTypesHtml != null) ? soy.$$filterNoAutoescape(opt_data.issueTypesHtml) : JIRA.Templates.ProjectTemplates.CoreTemplates.taskIssueType(null) + JIRA.Templates.ProjectTemplates.CoreTemplates.subtaskIssueType(null)) + '</ul></div><div class="right-panel"><h6>' + soy.$$escapeHtml('\u5de5\u4f5c\u6d41') + '</h6><div class="workflow ' + soy.$$escapeHtml(opt_data.projectTemplate) + '"></div></div></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.templateInfoDialog';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.issueType = function(opt_data, opt_ignored) {
  return '<li><span class="issuetype-list-label"><span class="issuetype-icon ' + soy.$$escapeHtml(opt_data.iconKey) + '"></span><span class="issuetype-name">' + soy.$$escapeHtml(opt_data.label) + '</span></span></li>';
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.issueType.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.issueType';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.taskIssueType = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.ProjectTemplates.CoreTemplates.issueType({iconKey: 'task', label: '\u4efb\u52a1'});
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.taskIssueType.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.taskIssueType';
}


JIRA.Templates.ProjectTemplates.CoreTemplates.subtaskIssueType = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.ProjectTemplates.CoreTemplates.issueType({iconKey: 'subtask', label: '\u5b50\u4efb\u52a1'});
};
if (goog.DEBUG) {
  JIRA.Templates.ProjectTemplates.CoreTemplates.subtaskIssueType.soyTemplateName = 'JIRA.Templates.ProjectTemplates.CoreTemplates.subtaskIssueType';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-analytics-tracker', location = 'includes/js/rapid/AnalyticsTracker.js' */
define("jira-agile/rapid/analytics-tracker",["jira/util/logger","underscore","jquery"],function(e,a,i){function t(e,i,t){if(a.isUndefined(e))throw new Error("You must specify a category");this.params={},this.params.category=e;var r=0;a.isUndefined(i)||(this.params.action=i,r++,a.isUndefined(t)||(this.params.label=t,r++)),this.requiredArgumentNames=["action","label","value"].slice(r),this.useAsync=!0}return t.prototype.setAsync=function(e){return this.useAsync=e,this},t.prototype.trigger=function(){this.requiredArgumentNames.length<arguments.length&&e.log("Warning: too many arguments passed in. Needed at most "+this.requiredArgumentNames.length+" but got "+arguments.length);for(var t=a.clone(this.params),r=0;r<arguments.length&&!a.isUndefined(this.requiredArgumentNames[r]);r++)t[this.requiredArgumentNames[r]]=arguments[r];this._validateParams(t);var n="gh.analytics.async";i(document).trigger(n,t)},t.prototype._validateParams=function(e){var i=["category","action","label"];a.each(i,function(i){a.isUndefined(e[i])||a.isString(e[i])?a.isUndefined(e[i])&&(e[i]=""):e[i]=e[i].toString()}),a.each(["category","action"],function(a){e[a]=e[a].replace(/\s+/g,"")}),a.isUndefined(e.value)||(e.value=parseInt(e.value,10),isNaN(e.value)&&(e.value=void 0))},t}),AJS.namespace("GH.AnalyticsTracker",null,require("jira-agile/rapid/analytics-tracker"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-help-analytics', location = 'includes/js/rapid/ui/help/HelpAnalytics.js' */
AJS.$(function(){var e=require("jira-agile/rapid/analytics-tracker"),r=function(r){return new e(r).setAsync(!1)};AJS.$("#gh_view_help").click(function(e){return r("gh.gethelp").trigger("usermenu","docs"),n(AJS.$(this))}),AJS.$("#greenhopper-my-jira-home-enablement").click(function(e){return r("gh.myjirahome").trigger("usermenu","enabled"),n(AJS.$(this))}),AJS.$("#greenhopper-my-jira-home-enablement-ondemand").click(function(e){return r("gh.myjirahome").trigger("usermenu","set"),n(AJS.$(this))}),AJS.$("#greenhopper-my-jira-home-disablement-ondemand").click(function(e){return r("gh.myjirahome").trigger("usermenu","disable"),n(AJS.$(this))}),AJS.$("#greenhopper-my-jira-home-set").click(function(e){return r("gh.myjirahome").trigger("usermenu","set"),n(AJS.$(this))}),AJS.$("#beta_gh\\.configuration\\.rapid\\.removal").click(function(e){var n=AJS.$(this),t=n.attr("checked")?"on":"off";return r("gh.labs.feature").trigger("rapid_removal",t),!0}),AJS.$(document).delegate("#js-classic-link_lnk","click",function(e){return r("gh.agile.menu").trigger("classic"),!0});var n=function(e){return"_blank"==e.attr("target")||(setTimeout('document.location = "'+e.attr("href")+'"',100),!1)}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-help-analytics', location = 'includes/js/rapid/Analytics.js' */
define("jira-agile/rapid/analytics",["jquery","underscore"],function(n,a){"use strict";function e(){n(document).on(l,function(n,a){i(a)})}function i(n){var e={},i=n.category;a.isUndefined(n.action)||(i=i+"."+n.action),a.isUndefined(n.label)||(e.label=n.label),a.isUndefined(n.value)||(e.value=n.value),AJS.trigger("analytics",{name:i,data:e})}GH.AnalyticsEnabled=!0;var l="gh.analytics.async";e();var t={};return t.isEnabled=function(){return GH.AnalyticsEnabled},t.setEnabled=function(n){return GH.AnalyticsEnabled=n},t}),AJS.namespace("GH.Analytics",null,require("jira-agile/rapid/analytics"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:project-template-resources', location = '/projecttemplates/soy/ProjectTemplates.soy' */
// This file was automatically generated from ProjectTemplates.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace GH.tpl.projecttemplates.
 */

if (typeof GH == 'undefined') { var GH = {}; }
if (typeof GH.tpl == 'undefined') { GH.tpl = {}; }
if (typeof GH.tpl.projecttemplates == 'undefined') { GH.tpl.projecttemplates = {}; }


GH.tpl.projecttemplates.kanbanInfoPageJira7 = function(opt_data, opt_ignored) {
  return '' + GH.tpl.projecttemplates.templateInfoDialog({description: '\u4f7f\u7528\u6b64\u9879\u76ee\u53ef\u4ee5\u4f18\u5316\u7684\u5de5\u4f5c\u6d41\u7a0b\u5728\u60a8\u7684\u5f00\u53d1\u9879\u76ee\u3002\u5c06\u7ea6\u675f\u6dfb\u52a0\u5230\u5de5\u4f5c\u4e2d--\u8fdb\u5ea6\u3001\u5206\u6790\u9700\u8981\u591a\u957f\u65f6\u95f4\u624d\u80fd\u5b8c\u6210\u7684\u95ee\u9898\u3001\u627e\u5230\u7684\u74f6\u9888\u60a8\u7684\u8fdb\u7a0b, \u548c\u66f4\u591a\u3002\u8fd9\u4e00\u9879\u76ee\u5305\u62ec\u5728\u770b\u677f\u677f\u3001\u57fa\u672c\u7684\u7075\u6d3b\u7684\u5de5\u4f5c\u6d41\u7a0b\u548c\u95ee\u9898\u7c7b\u578b\u7684\u914d\u7f6e, \u60a8\u53ef\u4ee5\u5728\u4ee5\u540e\u66f4\u6539\u3002', extraIssueTypes: '<li><span class="issuetype-list-label"><span class="issuetype-icon story"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u6545\u4e8b') + '</span></span></li><li><span class="issuetype-list-label"><span class="issuetype-icon epic"></span><span class="issuetype-name">' + soy.$$escapeHtml('Epic') + '</span></span></li>', workflowScreenshotClass: 'kanban-workflow-screenshot'});
};
if (goog.DEBUG) {
  GH.tpl.projecttemplates.kanbanInfoPageJira7.soyTemplateName = 'GH.tpl.projecttemplates.kanbanInfoPageJira7';
}


GH.tpl.projecttemplates.scrumInfoPageJira7 = function(opt_data, opt_ignored) {
  return '' + GH.tpl.projecttemplates.templateInfoDialog({description: '\u4f7f\u7528\u6b64\u9879\u76ee\u53ef\u7ba1\u7406\u60a8\u7684\u654f\u6377\u5f00\u53d1\u7684\u5de5\u4f5c\u3002\u521b\u5efa\u79ef\u538b\u3001\u7b79\u529e\u5de5\u4f5c\u8fdb\u5165\u51b2\u523a\u3001\u68c0\u67e5\u4f7f\u7528\u7684\u8fdb\u5c55\u60c5\u51b5\u7684\u62a5\u544a, \u4ee5\u53ca\u66f4\u591a\u3002\u8fd9\u4e00\u9879\u76ee\u5305\u62ec Scrum \u677f\u3001\u57fa\u672c\u7684\u7075\u6d3b\u5de5\u4f5c\u6d41\u7a0b\u548c\u95ee\u9898\u7c7b\u578b\u7684\u914d\u7f6e, \u60a8\u53ef\u4ee5\u5728\u4ee5\u540e\u66f4\u6539\u3002', extraIssueTypes: '<li><span class="issuetype-list-label"><span class="issuetype-icon story"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u6545\u4e8b') + '</span></span></li><li><span class="issuetype-list-label"><span class="issuetype-icon epic"></span><span class="issuetype-name">' + soy.$$escapeHtml('Epic') + '</span></span></li>', workflowScreenshotClass: 'scrum-workflow-screenshot'});
};
if (goog.DEBUG) {
  GH.tpl.projecttemplates.scrumInfoPageJira7.soyTemplateName = 'GH.tpl.projecttemplates.scrumInfoPageJira7';
}


GH.tpl.projecttemplates.softwareDevelopmentInfoPageJira7 = function(opt_data, opt_ignored) {
  return '' + GH.tpl.projecttemplates.templateInfoDialog({description: '\u4f7f\u7528\u6b64\u9879\u76ee\u5de5\u4f5c\u7684\u65b0\u529f\u80fd\u4e3a\u60a8\u7684\u4ea7\u54c1\u4ee5\u53ca\u8ddf\u8e2a\u7684\u4efb\u4f55 bug\u3002\u672c\u9879\u76ee\u4e3a\u60a8\u63d0\u4f9b\u4e86\u57fa\u672c\u7684\u5de5\u4f5c\u6d41\u7a0b\u548c\u95ee\u9898\u7c7b\u578b\u7684\u914d\u7f6e, \u60a8\u53ef\u4ee5\u5728\u4ee5\u540e\u66f4\u6539\u3002', extraIssueTypes: '<li><span class="issuetype-list-label"><span class="issuetype-icon improvement"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u6539\u8fdb') + '</span></span></li><li><span class="issuetype-list-label"><span class="issuetype-icon newfeature"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u65b0\u529f\u80fd') + '</span></span></li><li><span class="issuetype-list-label"><span class="issuetype-icon epic"></span><span class="issuetype-name">' + soy.$$escapeHtml('Epic') + '</span></span></li>', workflowScreenshotClass: 'basic-development-workflow-screenshot'});
};
if (goog.DEBUG) {
  GH.tpl.projecttemplates.softwareDevelopmentInfoPageJira7.soyTemplateName = 'GH.tpl.projecttemplates.softwareDevelopmentInfoPageJira7';
}


GH.tpl.projecttemplates.templateInfoDialog = function(opt_data, opt_ignored) {
  return '<div><div class="top-panel"><p>' + soy.$$escapeHtml(opt_data.description) + '</p></div><div class="left-panel"><h6>' + soy.$$escapeHtml('\u95ee\u9898\u7c7b\u578b') + '</h6><ul class="project-template-issuetype-list" ><li><span class="issuetype-list-label"><span class="issuetype-icon bug"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u6545\u969c') + '</span></span></li><li><span class="issuetype-list-label"><span class="issuetype-icon task"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u4efb\u52a1') + '</span></span></li><li><span class="issuetype-list-label"><span class="issuetype-icon subtask"></span><span class="issuetype-name">' + soy.$$escapeHtml('\u5b50\u4efb\u52a1') + '</span></span></li>' + soy.$$filterNoAutoescape(opt_data.extraIssueTypes) + '</ul></div><div class="right-panel"><h6>' + soy.$$escapeHtml('\u5de5\u4f5c\u6d41') + '</h6><div class="workflow ' + soy.$$escapeHtml(opt_data.workflowScreenshotClass) + '"></div></div></div>';
};
if (goog.DEBUG) {
  GH.tpl.projecttemplates.templateInfoDialog.soyTemplateName = 'GH.tpl.projecttemplates.templateInfoDialog';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-notification', location = 'includes/js/rapid/ui/notification/Notification.soy' */
// This file was automatically generated from Notification.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace GH.tpl.rapid.notification.
 */

if (typeof GH == 'undefined') { var GH = {}; }
if (typeof GH.tpl == 'undefined') { GH.tpl = {}; }
if (typeof GH.tpl.rapid == 'undefined') { GH.tpl.rapid = {}; }
if (typeof GH.tpl.rapid.notification == 'undefined') { GH.tpl.rapid.notification = {}; }


GH.tpl.rapid.notification.renderErrorMessages = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.errors.length > 1) {
    output += '<ul>';
    var errorList6 = opt_data.errors;
    var errorListLen6 = errorList6.length;
    for (var errorIndex6 = 0; errorIndex6 < errorListLen6; errorIndex6++) {
      var errorData6 = errorList6[errorIndex6];
      output += '<li>' + ((errorData6.noAutoescape) ? soy.$$filterNoAutoescape(errorData6.message) : soy.$$escapeHtml(errorData6.message)) + '</li>';
    }
    output += '</ul>';
  } else {
    var errorList18 = opt_data.errors;
    var errorListLen18 = errorList18.length;
    for (var errorIndex18 = 0; errorIndex18 < errorListLen18; errorIndex18++) {
      var errorData18 = errorList18[errorIndex18];
      output += '<p>' + ((errorData18.noAutoescape) ? soy.$$filterNoAutoescape(errorData18.message) : soy.$$escapeHtml(errorData18.message)) + '</p>';
    }
  }
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.notification.renderErrorMessages.soyTemplateName = 'GH.tpl.rapid.notification.renderErrorMessages';
}


GH.tpl.rapid.notification.renderContextualErrors = function(opt_data, opt_ignored) {
  var output = '';
  var errorList29 = opt_data.errors;
  var errorListLen29 = errorList29.length;
  for (var errorIndex29 = 0; errorIndex29 < errorListLen29; errorIndex29++) {
    var errorData29 = errorList29[errorIndex29];
    output += '<div class="ghx-error">' + soy.$$escapeHtml(errorData29.message) + '</div>';
  }
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.notification.renderContextualErrors.soyTemplateName = 'GH.tpl.rapid.notification.renderContextualErrors';
}


GH.tpl.rapid.notification.renderMessageHolder = function(opt_data, opt_ignored) {
  return '<div class="ghx-body-warning">' + soy.$$escapeHtml(AJS.format('\u6b64\u7248\u672c\u7684{0}\u4e0d\u662f\u7cfb\u7edf\u652f\u6301\u7684\u6d4f\u89c8\u5668\u3002',opt_data.browser)) + ' <a href="' + soy.$$escapeHtml(opt_data.docsUrl) + '">' + soy.$$escapeHtml('\u67e5\u770b\u6587\u6863\u4e86\u89e3\u652f\u6301\u7684\u6d4f\u89c8\u5668\u3002') + '</a></div>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.notification.renderMessageHolder.soyTemplateName = 'GH.tpl.rapid.notification.renderMessageHolder';
}


GH.tpl.rapid.notification.renderAuiMessage = function(opt_data, opt_ignored) {
  return '<div class="aui-message ' + soy.$$escapeHtml(opt_data.type) + ((opt_data.className) ? ' aui-message-' + soy.$$escapeHtml(opt_data.className) : '') + '">' + ((opt_data.title) ? '<p class="title">' + ((opt_data.icon) ? '<span class="aui-icon icon-' + soy.$$escapeHtml(opt_data.type) + '"></span>' : '') + '<strong>' + soy.$$escapeHtml(opt_data.title) + '</strong></p>' : '') + ((opt_data.message) ? '<p>' + ((! opt_data.title && opt_data.icon) ? '<span class="aui-icon icon-' + soy.$$escapeHtml(opt_data.type) + '"></span>' : '') + ((opt_data.messageHtml) ? soy.$$filterNoAutoescape(opt_data.message) : soy.$$escapeHtml(opt_data.message)) + '</p>' : '') + '</div>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.notification.renderAuiMessage.soyTemplateName = 'GH.tpl.rapid.notification.renderAuiMessage';
}


GH.tpl.rapid.notification.renderGHtvMessage = function(opt_data, opt_ignored) {
  return '<div id="ghx-update-message" class="ghx-tv-message"><p>' + soy.$$escapeHtml('\u6b64\u677f\u5df2\u7ecf\u66f4\u65b0') + ': <a href="" class="js-refresh-now" data-track-click="gh.rapidboard.updated.message.refresh.click">' + soy.$$escapeHtml('\u5237\u65b0') + '</a><span class="ghx-divider">&bull;</span><a href="" class="js-ignore-refresh" data-track-click="gh.rapidboard.updated.message.cancel.click">' + soy.$$escapeHtml('\u5ffd\u7565') + '</a></p></div>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.notification.renderGHtvMessage.soyTemplateName = 'GH.tpl.rapid.notification.renderGHtvMessage';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-notification', location = 'includes/js/rapid/ui/notification/Notification.js' */
define("jira-agile/rapid/ui/notification",["require"],function(e){"use strict";function a(e,a,r){return s(i({body:e,title:r.showTitle?a:void 0,close:r.autoHide?"auto":r.closeable?"manual":"never",type:r.type}))}function s(e){var a=r(e).find(".title");return""===r.trim(a.text())&&a.remove(),e}var r=e("jquery"),t=e("underscore"),i=e("aui/flag"),n=e("jira/util/logger"),o=e("jira/analytics"),d={showErrors:function(e){var a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s=arguments[2],r="\u9519\u8bef",t=GH.tpl.rapid.notification.renderErrorMessages(e);d.showError(r,t,a,s)},showError:function(e,s){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=arguments[3],n={autoHide:!1,closeable:r,showTitle:!0,type:"error"};i=t.extend({},n,i),a(s,e,i)},showWarnings:function(e,s){var r={preRendered:!1,autoHide:!0,closeable:!0,showTitle:!1,type:"warning"};s=t.extend({},r,s);var i=void 0;i=s.preRendered?e:GH.tpl.rapid.notification.renderErrorMessages({errors:e}),a(i,"\u8b66\u544a",s)},showWarning:function(e){d.showWarnings(e,{preRendered:!0})},showSuccess:function(e,s){var r={closeable:!0,autoHide:!0,showTitle:!1,type:"success"};s=t.extend({},r,s),a(e,null,s)},clear:function(){r("#aui-flag-container").empty()},handleDateFormatMismatchError:function(e){var a='<a href="'+GH.Ajax.CONTEXT_PATH+'/secure/admin/AdvancedApplicationProperties.jspa">';a+="\u9ad8\u7ea7\u8bbe\u7f6e",a+="</a>";var s=AJS.format("\u60a8\u7684 Jira \u65e5\u671f\u683c\u5f0f\u8bbe\u7f6e\u4e0d\u6b63\u786e\u3002\u8bf7\u68c0\u67e5 {0} \u5e76\u786e\u4fdd \'\'jira.date.time.picker.java.format\'\' \u53ca \'\'jira.date.time.picker.javascript.format\'\' \u5b58\u5728\u5339\u914d\u8f93\u51fa\u3002",a);d.showWarnings(s,{autoHide:!1,preRendered:!0})},auiMessage:function(){n.warn("[deprecated] GH.Notification.auiMessage has no effect")},showIssueMessage:function(e){var a=JIRA.SessionStorage,s=null,r=null;e||(e=a.getItem("selectedIssueId")),e&&(r=a.getItem("selectedIssueKey"),s=a.getItem("selectedIssueMsg"),d.showIssueMessageImpl(e,s,r)),a.removeItem("selectedIssueId"),a.removeItem("selectedIssueKey"),a.removeItem("selectedIssueMsg")},showIssueMessageImpl:function(e,a,s){a||(a="thanks_issue_updated");var r=AJS.params[a];r&&s&&(r=AJS.format(r,s),d.showSuccess(r))},addPageLoadMessage:function(e,a){var s=GH.storage.get("gh.pageloadmessages",!0)||[];s.push({message:e,type:a||"success"}),GH.storage.put("gh.pageloadmessages",s,!0)},_displayPageLoadMessages:function(){var e=GH.storage.get("gh.pageloadmessages",!0);if(e){var a=e[0].type,s="";switch(t.each(e,function(e){s.length>0&&(s+="<br>"),s+=e.message}),a){case"warning":d.showWarning(s);break;case"success":default:d.showSuccess(s)}GH.storage.put("gh.pageloadmessages",null,!0)}},showBoardUpdatedMessage:function(){d.triggerShowMessageAnalytics(),d.clearBoardUpdatedMessage();var e=r.Deferred(),a=r(GH.tpl.rapid.notification.renderGHtvMessage()).appendTo("#gh");return a.data("deferred",e),a.find(".js-refresh-now").click(function(s){s.preventDefault(),a.removeData("deferred"),d.clearBoardUpdatedMessage(),e.resolve()}),a.find(".js-ignore-refresh").click(function(e){e.preventDefault(),d.clearBoardUpdatedMessage()}),e.promise()},clearBoardUpdatedMessage:function(){var e=r("#ghx-update-message");if(e.length){var a=e.data("deferred");a&&a.reject&&a.reject(),e.remove()}},isBoardUpdatedMessageVisible:function(){var e=r("#ghx-update-message");return e.length>0},triggerShowMessageAnalytics:function(){o.send({name:"gh.rapidboard.updated.message.show",properties:{initial:!d.isBoardUpdatedMessageVisible()}})}};return d}),AJS.namespace("GH.Notification",null,require("jira-agile/rapid/ui/notification")),AJS.$(document).ready(function(){GH.Notification.showIssueMessage(),GH.storage&&GH.Notification._displayPageLoadMessages()});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-globalkeyboardshortcuts', location = 'includes/js/gh-globalkeyboardshortcuts.js' */
define("jira-agile/gh-globalkeyboardshortcuts",["jira-agile/rapid/ui/notification","jira/ajs/ajax/smart-ajax","jira/data/session-storage","jira/issuenavigator/issue-navigator/shortcuts","jira/issue","jira/issuenavigator/issue-navigator","jira/dialog/dialog","jquery","jira/util/browser"],function(e,s,o,t,a,i,n,r,u){var c={},d=require("wrm/context-path");return c.findSelectedIssueId=function(){var e;return i&&i.getSelectedIssueId&&(e=i.getSelectedIssueId()),!e&&a&&a.getIssueId&&(e=a.getIssueId()),e},c.goToAgile=function(){var e="/secure/RapidBoard.jspa",s=d();s&&(e=s+e),window.location.href=e},c.followLink=function(e){var s=r(e);s.length>0&&("a"===s[0].nodeName.toLowerCase()||"link"===s[0].nodeName.toLowerCase())&&(s.click(),window.location.href=s.attr("href"))},c.sendToTop=function(e,o){if(window.GH&&GH.RapidBoard&&GH.Shortcut&&GH.Shortcut.sendToTop)return GH.Shortcut.sendToTop(e),void c.closeDialogAndPopMessage();if("Action"==e){var t=o||c.findSelectedIssueId(),a=d()||"";s.makeRequest({type:"post",contentType:"application/json",url:a+"/rest/greenhopper/1.0/rank/global/first",data:JSON.stringify({issueId:t}),success:c.handleRankSuccess}),r(document).trigger("gh.global.rankissues",{action:"rankToTop"+(e?e:""),count:1})}},c.sendToBottom=function(e,o){if(window.GH&&GH.RapidBoard&&GH.Shortcut&&GH.Shortcut.sendToBottom)return GH.Shortcut.sendToBottom(e),void c.closeDialogAndPopMessage();if("Action"==e){var t=o||c.findSelectedIssueId(),a=d()||"";s.makeRequest({type:"post",contentType:"application/json",url:a+"/rest/greenhopper/1.0/rank/global/last",data:JSON.stringify({issueId:t}),success:c.handleRankSuccess}),r(document).trigger("gh.global.rankissues",{action:"rankToBottom"+(e?e:""),count:1})}},c.closeDialogAndPopMessage=function(){i.isNavigator()?u.reloadViaWindowLocation():(n&&n.current&&n.current.hide(),window.GH?(GH.RapidBoard&&e&&e.showIssueMessage&&e.showIssueMessage(),window.Boards&&window.Boards.refreshAll()):i&&t&&t.flashIssueRow&&t.flashIssueRow())},c.handleRankSuccess=function(e,s,o){c.storeSuccessMessage(e.issueId,e.issueKey,"thanks_issue_updated"),c.closeDialogAndPopMessage()},c.storeSuccessMessage=function(e,s,t){var a=o;try{a.setItem("selectedIssueId",e),a.setItem("selectedIssueKey",s),a.setItem("selectedIssueMsg",t)}catch(e){}},c}),require(["jquery","jira-agile/gh-globalkeyboardshortcuts"],function(e,s){e(document).delegate(".issueaction-greenhopper-rank-top-operation","click",function(o){o.preventDefault();var t=e(this).attr("data-issueid");s.sendToTop("Action",t)}),e(document).delegate(".issueaction-greenhopper-rank-bottom-operation","click",function(o){o.preventDefault();var t=e(this).attr("data-issueid");s.sendToBottom("Action",t)})}),AJS.namespace("gh.app.globalkeyboardshortcuts",null,require("jira-agile/gh-globalkeyboardshortcuts"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugin.ext.bamboo:bamboo-soy-resources', location = 'templates/plugins/bamboo/components.soy' */
// This file was automatically generated from components.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace bamboo.components.
 */

if (typeof bamboo == 'undefined') { var bamboo = {}; }
if (typeof bamboo.components == 'undefined') { bamboo.components = {}; }


bamboo.components.pageHeader = function(opt_data, opt_ignored) {
  return '' + aui.page.pageHeader({content: '' + ((opt_data.headerImageContent) ? aui.page.pageHeaderImage({content: opt_data.headerImageContent}) : '') + aui.page.pageHeaderMain({content: opt_data.headerMainContent}) + ((opt_data.headerActionsContent) ? aui.page.pageHeaderActions({content: opt_data.headerActionsContent}) : '')});
};
if (goog.DEBUG) {
  bamboo.components.pageHeader.soyTemplateName = 'bamboo.components.pageHeader';
}


bamboo.components.icon = function(opt_data, opt_ignored) {
  return '<span class="bamboo-icon bamboo-icon-' + soy.$$escapeHtml(opt_data.type) + '"' + ((opt_data.text && opt_data.showTitle) ? ' title="' + soy.$$escapeHtml(opt_data.text) + '"' : '') + '>' + ((opt_data.text) ? '<span>' + soy.$$escapeHtml(opt_data.text) + '</span>' : '') + '</span>';
};
if (goog.DEBUG) {
  bamboo.components.icon.soyTemplateName = 'bamboo.components.icon';
}


bamboo.components.buildDetail = function(opt_data, opt_ignored) {
  return '<dl><dt class="' + soy.$$escapeHtml(opt_data.keyClass) + '">' + soy.$$escapeHtml(opt_data.key) + '</dt><dd>' + soy.$$filterNoAutoescape(opt_data.value) + '</dd></dl>';
};
if (goog.DEBUG) {
  bamboo.components.buildDetail.soyTemplateName = 'bamboo.components.buildDetail';
}


bamboo.components.artifacts = function(opt_data, opt_ignored) {
  var output = '<ul id="shared-artifacts">';
  var artifactList42 = opt_data.artifacts;
  var artifactListLen42 = artifactList42.length;
  for (var artifactIndex42 = 0; artifactIndex42 < artifactListLen42; artifactIndex42++) {
    var artifactData42 = artifactList42[artifactIndex42];
    output += bamboo.components.artifactItem(soy.$$augmentMap(artifactData42, {id: artifactData42.name, url: artifactData42.link.href}));
  }
  output += '</ul>';
  return output;
};
if (goog.DEBUG) {
  bamboo.components.artifacts.soyTemplateName = 'bamboo.components.artifacts';
}


bamboo.components.artifactItem = function(opt_data, opt_ignored) {
  return '<li>' + bamboo.components.icon({type: 'artifact-shared'}) + ' <a class="artifact-link" id="artifact-' + soy.$$escapeHtml(opt_data.id) + '" href="' + soy.$$escapeHtml(opt_data.url) + '" title="' + soy.$$escapeHtml(opt_data.name) + '">' + soy.$$escapeHtml(opt_data.name) + '</a><span class="filesize">(' + soy.$$escapeHtml(opt_data.prettySizeDescription) + ')</span></li>';
};
if (goog.DEBUG) {
  bamboo.components.artifactItem.soyTemplateName = 'bamboo.components.artifactItem';
}


bamboo.components.labels = function(opt_data, opt_ignored) {
  var output = '<ul class="labels">';
  var labelList65 = opt_data.labels;
  var labelListLen65 = labelList65.length;
  for (var labelIndex65 = 0; labelIndex65 < labelListLen65; labelIndex65++) {
    var labelData65 = labelList65[labelIndex65];
    output += bamboo.components.labelItem(soy.$$augmentMap(labelData65, {url: opt_data.baseBambooUrl + '/browse/label/' + labelData65.name}));
  }
  output += '</ul>';
  return output;
};
if (goog.DEBUG) {
  bamboo.components.labels.soyTemplateName = 'bamboo.components.labels';
}


bamboo.components.labelItem = function(opt_data, opt_ignored) {
  return '<li><a class="lozenge" href="' + soy.$$escapeHtml(opt_data.url) + '" title="' + soy.$$escapeHtml(opt_data.name) + '"><span>' + soy.$$escapeHtml(opt_data.name) + '</span></a></li>';
};
if (goog.DEBUG) {
  bamboo.components.labelItem.soyTemplateName = 'bamboo.components.labelItem';
}


bamboo.components.errorResponseMessage = function(opt_data, opt_ignored) {
  return '' + bamboo.components.auiWarning({body: '' + ((opt_data.errorMessage.messageBody) ? '<p>' + soy.$$escapeHtml(opt_data.errorMessage.messageBody) + '</p>' : (opt_data.errorMessage.oauthCallback) ? '<p><a href="' + soy.$$filterNoAutoescape(opt_data.errorMessage.oauthCallback) + '&amp;redirectUrl=' + soy.$$filterNoAutoescape(opt_data.oAuthDanceReturnUrl) + '">' + soy.$$escapeHtml('\u767b\u5f55\u548c\u5ba1\u6279') + '</a></p>' : '<p>' + soy.$$escapeHtml(opt_data.errorMessage.message) + '</p>')});
};
if (goog.DEBUG) {
  bamboo.components.errorResponseMessage.soyTemplateName = 'bamboo.components.errorResponseMessage';
}


bamboo.components.auiWarning = function(opt_data, opt_ignored) {
  return '<div class="aui-message aui-message-warning warning">' + soy.$$filterNoAutoescape(opt_data.body) + '</div>';
};
if (goog.DEBUG) {
  bamboo.components.auiWarning.soyTemplateName = 'bamboo.components.auiWarning';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.administration.atlassian-admin-quicksearch-jira:admin-quicksearch-webresources', location = 'com/atlassian/administration/quicksearch/jira/js/adminQuickNav.js' */
/**
 * Shifter group for admin search
 */
require([
    'jquery',
    'underscore',
    'jira/ajs/ajax/smart-ajax',
    'jira/shifter',
    'wrm/context-path'
], function (jQuery,
             _,
             SmartAjax,
             Shifter,
             contextPath) {
    Shifter.register(function () {
        var suggestionsDeferred = jQuery.Deferred();

        function formatItem(item) {
            return {
                label: item.label,
                value: item.linkUrl,
                keywords: item.aliases
            };
        }

        function getItemsInSection(section) {
            return _.map(section.items, formatItem).concat(_.map(section.sections, getItemsInSection));
        }

        function formatResponse(data) {
            return _.flatten(getItemsInSection(data));
        }

        SmartAjax.makeRequest({
            dataType: 'json',
            url: contextPath() + '/rest/adminquicksearch/latest/links/default'
        })
            .pipe(formatResponse)
            .done(function (suggestions) {
                suggestionsDeferred.resolve(suggestions);
            })
            .fail(function () {
                suggestionsDeferred.reject();
            });

        return {
            id: 'admin',
            name: "\u7ba1\u7406",
            weight: 500,
            getSuggestions: function () {
                return suggestionsDeferred;
            },
            onSelection: function (value) {
                window.location = value;
                return jQuery.Deferred();
            }
        };
    });
});

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api', location = 'probe.js' */
!function(){var n,t,e,r,i,o,u,a,c,s,f,l,h,d,y,p,v,m,w;n=function(){return window}(),t=function(n){return!(!n.performance||!n.performance.now)}(n),e=[],r=function(n){return function(t){n.unshift({addReporter:t})}}(e),i=function(n){return function(t){for(;n.length;)t(n.splice(0,1)[0]);n.unshift=t,n.push=t}}(e),o=function(n,t){return function(e){n.push({end:{key:e.key,timestamp:t.performance.now()}})}}(e,n),u=function(n){return n.document}(n),a=function(n){return n.Promise}(n),c=function(){function n(){this._={}}var t=function(e){var r=e[0],i=e[1];i instanceof n?e.length>=3?Object.keys(i._).forEach(function(n){t([r,i._[n],n].concat(e.slice(2)))}):Object.keys(i._).forEach(function(n){t([r,i._[n],n])}):Array.isArray(i)&&r.apply(null,[i].concat(e.slice(2)))};n.prototype.forEach=function(n){t([n,this])},n.prototype.add=function(){for(var t=this,e=null,r=null,i=0;i<arguments.length;i++){if(r=arguments[i],i===arguments.length-1&&Array.isArray(t)){t.push(r);break}i<arguments.length-2&&!t._.hasOwnProperty(r)?t._[r]=new n:i!==arguments.length-2||t._.hasOwnProperty(r)||(t._[r]=[]),t=t._[r],e=r}};var e=function(n,t){if(0!==n.length){var r=n.pop(),i=r[0],o=r[1];i===t?e(n,i):o._.hasOwnProperty(t)&&delete o._[t],0===Object.keys(o).length&&e(n,i)}};return n.prototype.remove=function(){for(var n,t=!1,r=null,i=this,o=[[r,i]],u=null,a=0;a<arguments.length;a++)if(u=arguments[a],Array.isArray(i))n=i.indexOf(u),n>-1&&(i.splice(n,1),0===i.length&&o.length>1&&e(o,r),t=!0);else{if(!i._.hasOwnProperty(u))break;a===arguments.length-1&&(delete i._[u],0===Object.keys(i).length&&o.length>1&&e(o,r),t=!0),r=u,i=i._[u],o.push([r,i])}return t},n.prototype.get=function(n){return this._.hasOwnProperty(n)?this._[n]:[]},n}(),s=function(n,t,e,r){function i(n){return!n||null==n||"null"===n||"undefined"===n}function o(t,e,r){l||(c.observe(n,{attributes:!0,childList:!0,subtree:!0}),l=!0),s.add(t,e,r)}function u(t,e){var r=n.querySelectorAll(t);return r.length&&(i(e)||Array.prototype.every.call(r,function(n){return!n.querySelector(e)}))}function a(n,e){var r;n.forEach||(n=[n]),!i(e)&&Array.isArray(e)&&(e=e.join(", "));var a=new t(function(i,a){var c=[],f=[];n.forEach(function(n){var r,i;u(n,e)||(r=new t(function(t){o(n,e,t),i=function(){s.remove(n,e,t)},f.push(i)}),c.push(r))});var l=function(){f.forEach(function(n){n()})};t.all(c).then(l).then(i,a),r=function(){l(),a()}});return a.dismiss=r,a}var c,s,f=r.MutationObserver,l=!1;return f&&t?(s=new e,c=new f(function(){s.forEach(function(n,t,e){u(e,t)&&(n.forEach(function(n){n()}),s.remove(e,t))})}),a):void 0}(u,a,c,n),f=function(n){return!!n}(s),l=function(n){function t(){c(),n.body.classList.add(u)}function e(){function e(){n.body.classList.remove(u),n.removeEventListener(i,s),n.removeEventListener(o,c),r=null}if(r)return r;var c,s,f=!1;return r=new Promise(function(e,r){"visible"!==n.visibilityState?r():(s=function(){f=!0},c=function(n){n.animationName===a&&(f?r():e())},n.addEventListener(i,s),n.addEventListener(o,c),t())}),r.then(e,e),r}var r,i="visibilitychange",o="animationend",u="browser-metrics-visibility-test",a="browser-metrics-visibility-animation",c=function(){var t=n.createElement("style"),e=["."+u+" {","-webkit-animation-duration: 0.001s;","animation-duration: 0.001s;","-webkit-animation-name: "+a+";","animation-name: "+a+";","-webkit-animation-iteration-count: 1;","animation-iteration-count: 1;","}","@keyframes "+a+" {}","@-webkit-keyframes "+a+" {","from {}","to {}","}"].join("\n");t.type="text/css",t.styleSheet?t.styleSheet.cssText=e:t.appendChild(n.createTextNode(e)),n.head.appendChild(t),c=function(){}};return e}(u),h=function(n,t,e,r,i){function o(n){return Array.isArray(n)||(n=[n]),n.map(function(n){return"string"==typeof n?{selector:n,hasNone:null}:n})}function u(n){return Array.isArray(n)||"string"==typeof n}function a(n){return u(n)&&(n={rules:n}),n.rules=o(n.rules),n.requirePaint="undefined"==typeof n.requirePaint?!1:n.requirePaint,n}return function(i,o){if(n){i=a(i);var u=function(){},c=new e(function(n,r){var o=[],a=i.rules.map(function(n){var e=new t(n.selector,n.hasNone);return o.push(function(){e.dismiss()}),e});u=function(){o.forEach(function(n){n()}),r()},e.all(a).then(function(n){}).then(n,r)});return c.cancel=u,i.requirePaint&&(c=c.then(r)),"function"==typeof o&&c.then(o),c}}}(f,s,a,l,n),d=function(n,t){function e(){return r}var r=!1;return n.addEventListener("DOMContentLoaded",function(){t.setTimeout(function(){r=!0})}),e}(u,n),y=function(n,t,e,r,i,o,u){function a(){c=null}var c;return function(o){var s="isInitial"in o?o.isInitial:i()===!1,f="threshold"in o?o.threshold:1e3,l="reporters"in o?o.reporters:[];r.push({start:{key:o.key,isInitial:s,threshold:f,timestamp:s?0:u.performance.now(),reporters:Array.isArray(l)?l:[l]}}),c&&(c.cancel(),a()),o.ready&&e&&(c=n(o.ready),c.then(function(){t({key:o.key})}).then(a,a))}}(h,o,f,e,d,a,n),p=function(n){return function(t){n.push({subscribe:t})}}(e),v=function(){return window}(),m=function(n){return n.performance}(v),w=function(n,t,e,r,i,o,u){var a=function(){};return u?{start:n?i:a,end:n?r:a,addReporter:n?t:a,delegateTo:n?e:a,subscribe:n?o:a}:void 0}(t,r,i,o,y,p,m),window["browser-metrics"]=w,window.define&&window.define("internal/browser-metrics",function(){return w})}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api', location = 'internal/browser-metrics-aa-beacon.js' */
!function(){var e={};e=function(e){function n(e,n){Object.keys(n).forEach(function(r){e[r]=n[r]})}Object.defineProperty(e,"__esModule",{value:!0});var r,t=[],o=[];return e.addUrlCleaner=function(e){o.push(e)},e.cleanUrl=function(e){return o.reduce(function(n,r){var t=r(e);return t.length>n.length?t:n},"")},e.addReportMarshaller=function(e){t.push(e)},e.setEventQueue=function(e){r=e},e.beacon=function(e){var o={};t.forEach(function(r){var t=r(e);"object"==typeof t&&n(o,t)});var a={name:"browser.metrics.navigation",properties:o};(r||AJS.EventQueue).push(a)},e}(e),window["browser-metrics-aa-beacon"]=e,window.define&&window.define("internal/browser-metrics-aa-beacon",function(){return e})}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.browser.metrics.browser-metrics-plugin:api', location = 'loader.js' */
!function(){var n={},r={};r=function(n,r,i){function e(){t===u&&o&&(o(),o=null)}Object.defineProperty(n,"__esModule",{value:!0});var t=0,u=0,o=null,c={install:function(n){t+=1,n(function(){u+=1,e()})}};return r["browser-metrics-plugin"]=c,i.require(["wrc!browser-metrics-plugin.contrib"],function(){r.require(["internal/browser-metrics-plugin/collector"],function(n){o=function(){n.install()},e()})}),n}(r,n=window,n.WRM)}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-browser-metrics:sensors', location = 'sensors.js' */
require(["internal/browser-metrics","jira/util/events"],function(e,i){var a,o={},d={},t={};o=function(e,i,a){function o(){function e(e){return o.hasOwnProperty(e)?o[e]:null}var o={"bonfire_top_menu_dropdown-content":"jira.header.menu.capture","browse_link-content":"jira.header.menu.projects","find_link-content":"jira.header.menu.issues","greenhopper_menu-content":"jira.header.menu.agile","home_link-content":"jira.header.menu.dashboards","plugins-jira-webitem-main-content":"jira.header.menu.portfolio","servicedesk-section-content":"jira.header.menu.service-desk","system-admin-menu-content":"jira.header.menu.admin","system-help-menu-content":"jira.header.menu.help","user-options-content":"jira.header.menu.profile"};i.bind("aui-dropdown2-show-before",function(i){var o=i.target,d=e(o&&o.id);d&&a.start({key:d,isInitial:!1,threshold:250})}),i.bind("aui-dropdown2-show-after",function(i){var o=i.target,d=e(o&&o.id);d&&a.end({key:d})})}return e.applicationMenusSensorInit=o,e}(o,i,e),d=function(e){function i(e){var i;if(a.hasOwnProperty(e))return e;for(i=0;i<o.length;i++)if(e.match(o[i].pattern))return o[i].dialogId;return null}e.safeDialogId=i;var a=["about-dialog","add-basicuser-dialog","add-developer-dialog","add-field-configuration-dialog","add-field-configuration-scheme-dialog","add-field-dialog","add-field-screen-dialog","add-field-screen-scheme-dialog","add-incoming-mail-handler-dialog","add-issue-type-dialog","add-issue-type-field-configuration-association-dialog","add-issue-type-screen-scheme-configuration-association-dialog","add-issue-type-screen-scheme-dialog","add-new-issue-type-to-scheme-dialog","add-screen-scheme-item-dialog","add-subtask-type-dialog","add-workflow-dialog","add_workflowscheme-dialog","assign-dialog","assign-issue-types-dialog","assign-to-me-link-handler","attach-file-dialog","attach-screenshot-dialog","auditing-settings-dialog","clone-issue-dialog","comment-add-dialog","configure_wallboard_dialog","confluence-page-search-dialog","copy_classic default workflow-dialog","copy-filter-dialog","copy_jira-dialog","create-issue-dialog","create-issue-dialog.issueType","create-issue-dialog.projectId","create-request-dialog","create-service-desk-dialog","create-status-dialog","create-story-dialog","create-story-dialog.issueType","create-story-dialog.projectId","create-subtask-dialog","create-subtask-dialog.issueType","create-team-dialog","create_user-dialog","credits-dialog","delete-attachment-dialog","delete-comment-dialog","delete-dshboard","delete-filter-dialog","delete-issue-dialog","delete-issue-link-dialog","delete-log-work-dialog","delete-metric","delete-queue","delete-status-dialog","deleteuser_link-dialog","devstatus-branch-detail-dialog","devstatus-build-detail-dialog","devstatus-commit-detail-dialog","devstatus-cta-dialog","devstatus-deployment-detail-dialog","devstatus-pullrequest-detail-dialog","devstatus-review-detail-dialog","discard-draft-dialog","discard_draft_workflow-dialog","edit-attachments-dialog","edit-comment","editgroups_admin-dialog","editgroups_link-dialog","editgroups_sysadmin-dialog","edit-issue-dialog","edit-labels-dialog","edit-log-work-dialog","edit-status-dialog","edituser_link_admin-dialog","edit-workflow-dialog","gh-rapidboard-dialog","inline-issue-create-dialog","invite_user-dialog","issue-actions-dialog","issue-tab-error-dialog","jim-create-project-dialog","keyboard-shortcuts-dialog","link-issue-dialog","log-work-dialog","manage-attachment-dialog","metric-pre-save-dialog","modal-field-view","permission-helper-dialog","project-avatar-cropper","project-avatar-picker","project-config-details-project-category-dialog","project-config-project-edit-dialog","project-config-project-edit-lead-and-default-assignee-dialog","project-email-dialog","publish_draft_workflow-dialog","queue-dirty-edits","QuickCreateIssue.error","QuickCreateIssue.success","QuickCreateSubtask.error","QuickCreateSubtask.success","QuickEdit.error","QuickEdit.success","remote-jira-search-dialog","rename-filter-dialog","report-dirty-edits","save-filter-dialog","sd-add-default-value","sd-add-remove-agent-dialog","sd-remove-field-dialog","server-error-dialog","report-delete-confirm","tempo-add-hours-issue-dialog","tempo-add-internal-activity-form","tempo-core-medium-form","tempo-delete-form-dialog","tempo-grace-form","tempo-large-form","tempo-medium-form","tempo-move-form-dialog","tempo-pdf-form","tempo-small-form","tempo-split-form-dialog","tempo-user-settings-dialog","user-avatar-picker","user-defaults-edit-dialog","versionsMergeDialog","view-workflow-dialog","view-workflow-dialog-project-admin","view-workflow-dialog-workflow-schemes","wait-migrate-dialog","whereismycf-dialog","workflow-text-view"].reduce(function(e,i){return e[i]=!0,e},{}),o=[{dialogId:"component-delete-dialog",pattern:/^component-\d+-delete-dialog$/i},{dialogId:"version-delete-dialog",pattern:/^version-\d+-delete-dialog$/i},{dialogId:"workflow-transition-dialog",pattern:/^workflow-transition-\d+-dialog$/i}];return e}(d),t=function(e,i,a,o){function d(){i.bind("beforeShow",function(e,i){if("string"==typeof i){var d=(0,o.safeDialogId)(i);d&&a.start({key:"jira.dialog.open."+d,isInitial:!1,threshold:1e3})}}),i.bind("dialogContentReady",function(e,i){if("string"==typeof i.options.id){var d=(0,o.safeDialogId)(i.options.id);d&&a.end({key:"jira.dialog.open."+d})}})}return e.dialogsSensorInit=d,e}(t,i,e,d),a=function(e,i){(0,e.applicationMenusSensorInit)(),(0,i.dialogsSensorInit)()}(o,t)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-quicksearch-plugin:commons_common', location = 'frontend/common.js' */
!function(e){function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}var r=window.atlassianWebpackJsonpe5ed4b01bb42b7893d8aef5bda9bf664;window.atlassianWebpackJsonpe5ed4b01bb42b7893d8aef5bda9bf664=function(t,a,i){for(var c,u,s,f=0,l=[];f<t.length;f++)u=t[f],o[u]&&l.push(o[u][0]),o[u]=0;for(c in a)Object.prototype.hasOwnProperty.call(a,c)&&(e[c]=a[c]);for(r&&r(t,a,i);l.length;)l.shift()();if(i)for(f=0;f<i.length;f++)s=n(n.s=i[f]);return s};var t={},o={2:0};n.e=function(e){function r(){c.onerror=c.onload=null,clearTimeout(u);var n=o[e];0!==n&&(n&&n[1](new Error("Loading chunk "+e+" failed.")),o[e]=void 0)}var t=o[e];if(0===t)return new Promise(function(e){e()});if(t)return t[2];var a=new Promise(function(n,r){t=o[e]=[n,r]});t[2]=a;var i=document.getElementsByTagName("head")[0];if({0:!0}[e])return WRM.require("wrc!com.atlassian.jira.plugins.jira-quicksearch-plugin:"+e),a;var c=document.createElement("script");c.type="text/javascript",c.charset="utf-8",c.async=!0,c.timeout=12e4,n.nc&&c.setAttribute("nonce",n.nc),c.src=n.p+"bundle."+e+".js";var u=setTimeout(r,12e4);return c.onerror=c.onload=r,i.appendChild(c),a},n.m=e,n.c=t,n.d=function(e,r,t){n.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:t})},n.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(r,"a",r),r},n.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},n.p="",n.oe=function(e){throw console.error(e),e},"undefined"!=typeof AJS&&(n.p=AJS.contextPath()+"/download/resources/com.atlassian.jira.plugins.jira-quicksearch-plugin:assets-79bdc076-c56d-4c27-b7da-dbbe1ae1f655/")}([]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-quicksearch-plugin:entrypoint-jira-quicksearch-plugin', location = 'frontend/bundle.jira-quicksearch-plugin.js' */
atlassianWebpackJsonpe5ed4b01bb42b7893d8aef5bda9bf664([1],[function(e,t){e.exports=require("jquery")},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.QUICK_SEARCH_RESULTS_EMPTY_STATE=t.QUICK_SEARCH_PARENT_LOADING_CLASS=t.QUICK_SEARCH_SPINNER_LOCATOR_CLASS=t.QUICK_SEARCH_SPINNER_CLASS=t.QUICK_SEARCH_INPUT_LOCATOR=t.QUICK_SEARCH_INPUT_ID=t.QUICK_SEARCH_FEATURE_FLAG=void 0;var r=n(5),u=i(r),a=n(2),o=i(a),s=o.default,c=(t.QUICK_SEARCH_FEATURE_FLAG="jira.quick.search",t.QUICK_SEARCH_INPUT_ID="quickSearchInput"),l=t.QUICK_SEARCH_INPUT_LOCATOR="#"+c,_=t.QUICK_SEARCH_SPINNER_CLASS="quick-search-spinner";t.QUICK_SEARCH_SPINNER_LOCATOR_CLASS=l+" ~ ."+_,t.QUICK_SEARCH_PARENT_LOADING_CLASS="quicksearch-loading",t.QUICK_SEARCH_RESULTS_EMPTY_STATE=Object.freeze([{id:"quick-search-issues",items:[],name:"\u95ee\u9898",url:(0,u.default)()+"/issues/?jql=order+by+lastViewed+DESC",viewAllTitle:"\u663e\u793a\u6240\u6709\u95ee\u9898"},{id:"quick-search-projects",items:[],name:"ultimate.search.group.projects.name",url:(0,u.default)()+"/projects?selectedCategory=all&selectedProjectType=all&contains=",viewAllTitle:"\u67e5\u770b\u6240\u6709\u9879\u76ee"}])},function(e,t){e.exports=require("jira/util/formatter")},function(e,t){e.exports=require("jira/analytics")},function(e,t){e.exports=require("underscore")},function(e,t){e.exports=require("wrm/context-path")},function(e,t,n){"use strict";function i(){return(0,c.default)(_.QUICK_SEARCH_SPINNER_LOCATOR_CLASS)}function r(){return(0,c.default)(_.QUICK_SEARCH_INPUT_LOCATOR)}function u(){return r().parent()}function a(){u().addClass(_.QUICK_SEARCH_PARENT_LOADING_CLASS),i().spin()}function o(){u().removeClass(_.QUICK_SEARCH_PARENT_LOADING_CLASS),i().spinStop()}Object.defineProperty(t,"__esModule",{value:!0}),t.spinStart=a,t.spinStop=o;var s=n(0),c=function(e){return e&&e.__esModule?e:{default:e}}(s),l=n(1),_=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(l);n(9)},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}var r=n(0),u=i(r),a=n(4),o=i(a),s=n(3),c=i(s);n(8);var l=n(6),_=n(1),d=n(10),f=i(d),C=n(11),S=o.default.once(function(){(0,l.spinStart)(),n.e(0).then(n.bind(null,13))});(0,u.default)(function(){if(f.default.isFeatureEnabled(_.QUICK_SEARCH_FEATURE_FLAG)){c.default.send({name:"quicksearch.enabled"});var e=(0,u.default)(_.QUICK_SEARCH_INPUT_LOCATOR);e.after('<div class="'+_.QUICK_SEARCH_SPINNER_CLASS+'"></div>'),e.on("beforeBlurInput",function(e){e.preventDefault()}),e.focus(S),e.is(":focus")&&S()}}),(0,u.default)(window).load(function(){f.default.isFeatureEnabled(_.QUICK_SEARCH_FEATURE_FLAG)&&(0,C.showOnboardingTip)()})},function(e,t){},function(e,t){e.exports=void 0},function(e,t){e.exports=require("jira/featureflags/feature-manager")},function(e,t,n){"use strict";function i(e){return e&&e.__esModule?e:{default:e}}function r(e){return"<div><p>"+e+"</p></div>"}function u(){if(0!==(0,f.default)(S).length){var e={anchor:S,id:"qs-onboarding-tip",isSequence:!1,showCloseButton:!0,closeButtonText:"\u597d\u7684\uff0c\u77e5\u9053\u4e86",callbacks:{hide:function(){return c.default.send({name:"quicksearch.onboarding.tip.dissmised"})}},title:"\u66f4\u5feb\u627e\u5230\u60a8\u7684\u5de5\u4f5c",bodyHtml:r("\u901a\u8fc7\u60a8\u6240\u6709\u7684\u95ee\u9898\u548c\u9879\u76ee\u8fdb\u884c\u641c\u7d22\uff0c\u6700\u91cd\u8981\u7684\u5de5\u4f5c\u553e\u624b\u53ef\u5f97\u3002\u8f93\u5165\u540e\u5f00\u59cb\u641c\u7d22......")},t=new o.default(e);if(!t.isDismissed()){if(t.show(),!t.isVisible())return void c.default.send({name:"quicksearch.onboarding.tip.notshown"});c.default.send({name:"quicksearch.onboarding.tip.shown"})}}}Object.defineProperty(t,"__esModule",{value:!0}),t.showOnboardingTip=u;var a=n(12),o=i(a),s=n(3),c=i(s),l=n(2),_=i(l),d=n(0),f=i(d),C=_.default,S="#quicksearch-menu:visible"},function(e,t){e.exports=require("jira-help-tips/feature/help-tip")}],[7]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.pattern.label', location = 'aui.chunk.a186607d5d8fbfab624b--15a6a253405a93beb52b.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.pattern.label"],{XpI9:function(i,n,u){},Y5aH:function(i,n,u){"use strict";u.r(n);u("FStl"),u("Q0fs"),u("nqD9"),u("XpI9")}},[["Y5aH","runtime","aui.splitchunk.fbbef27525","aui.splitchunk.056561461c","aui.splitchunk.949297951c"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.component.dropdown2', location = 'aui.chunk.a54a0d5070ee5aa366b2--fb603a270a13fc9bf2e0.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.component.dropdown2"],[],[["XHZH","runtime","aui.splitchunk.vendors--894c8113d9","aui.splitchunk.vendors--95c789edf5","aui.splitchunk.vendors--9c48cc20a9","aui.splitchunk.vendors--084821f40b","aui.splitchunk.vendors--d2297af84a","aui.splitchunk.0d131bcbf1","aui.splitchunk.fbbef27525","aui.splitchunk.444efc83be","aui.splitchunk.739b9ec8cc","aui.splitchunk.dd803a46b4","aui.splitchunk.994e478d48","aui.splitchunk.e54c7c7304","aui.splitchunk.fb15cffa72","aui.splitchunk.56dfb54d0c","aui.splitchunk.f1e06f97a4","aui.splitchunk.479fe6ee76","aui.splitchunk.f673ef53ac","aui.splitchunk.9c48cc20a9","aui.splitchunk.8659b532c1","aui.splitchunk.5f851f97df","aui.splitchunk.d0110a864f","aui.splitchunk.afa5039e04","aui.splitchunk.bff3715233","aui.splitchunk.c750721820","aui.splitchunk.6d6f245ed3","aui.splitchunk.ed86a19e01","aui.splitchunk.2e16019fb9","aui.splitchunk.084821f40b","aui.splitchunk.5b8c290363","aui.splitchunk.baa83dbaf9","aui.splitchunk.36cd9d521c","aui.splitchunk.e5af17b48e","aui.splitchunk.f154095da3","aui.splitchunk.4248e12b20","aui.splitchunk.b548966f06"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:rotp-menu', location = 'appswitcher/appswitcher.soy' */
// This file was automatically generated from appswitcher.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace navlinks.templates.appswitcher.
 */

if (typeof navlinks == 'undefined') { var navlinks = {}; }
if (typeof navlinks.templates == 'undefined') { navlinks.templates = {}; }
if (typeof navlinks.templates.appswitcher == 'undefined') { navlinks.templates.appswitcher = {}; }


navlinks.templates.appswitcher.linkSection = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.list.length > 0) {
    output += '<div class="aui-nav-heading sidebar-section-header">' + soy.$$escapeHtml(opt_data.title) + '</div><ul class="aui-nav nav-links">';
    var linkList8 = opt_data.list;
    var linkListLen8 = linkList8.length;
    for (var linkIndex8 = 0; linkIndex8 < linkListLen8; linkIndex8++) {
      var linkData8 = linkList8[linkIndex8];
      output += navlinks.templates.appswitcher.applicationsItem(linkData8);
    }
    output += '</ul>';
  }
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.linkSection.soyTemplateName = 'navlinks.templates.appswitcher.linkSection';
}


navlinks.templates.appswitcher.applicationsItem = function(opt_data, opt_ignored) {
  return '<li class="nav-link"><a href="' + soy.$$escapeHtml(opt_data.link) + '" ' + ((opt_data.self) ? 'class="checked"' : '') + ' title="' + soy.$$escapeHtml(opt_data.link) + '"><span class="nav-link-label">' + soy.$$escapeHtml(opt_data.label) + '</span></a></li>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.applicationsItem.soyTemplateName = 'navlinks.templates.appswitcher.applicationsItem';
}


navlinks.templates.appswitcher.shortcutsItem = function(opt_data, opt_ignored) {
  return '<li class="nav-link"><a href="' + soy.$$escapeHtml(opt_data.link) + '" ' + ((opt_data.self) ? 'class="checked"' : '') + ' title="' + soy.$$escapeHtml(opt_data.link) + '"><span class="nav-link-label">' + soy.$$escapeHtml(opt_data.label) + '</span>' + ((opt_data.showDescription && opt_data.description) ? '<span class="nav-link-description">' + soy.$$escapeHtml(opt_data.description) + '</span>' : '') + '</a></li>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.shortcutsItem.soyTemplateName = 'navlinks.templates.appswitcher.shortcutsItem';
}


navlinks.templates.appswitcher.error = function(opt_data, opt_ignored) {
  return '<div class="app-switcher-error">' + soy.$$filterNoAutoescape('\u51fa\u4e86\u9519, \u8bf7\x3cspan class\x3d\x22app-switcher-retry\x22\x3e\u518d\u8bd5\u4e00\u6b21\x3c/span \x3e\u3002') + '</div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.error.soyTemplateName = 'navlinks.templates.appswitcher.error';
}


navlinks.templates.appswitcher.sidebarContents = function(opt_data, opt_ignored) {
  return '<div class="aui-page-panel-nav"><nav class="aui-navgroup aui-navgroup-vertical"><div class="app-switcher-section app-switcher-applications"><div class="aui-nav-heading">' + soy.$$escapeHtml('\u5e94\u7528\u7a0b\u5e8f\u94fe\u63a5') + '</div><div class="app-switcher-loading">' + soy.$$filterNoAutoescape('\u6b63\u5728\u4e0a\u4f20\u548c\u4e71\u7801\uff1b') + '</div></div><div class="app-switcher-section app-switcher-shortcuts"><div class="aui-nav-heading">' + soy.$$escapeHtml('\u5feb\u6377\u65b9\u5f0f') + '</div><div class="app-switcher-loading">' + soy.$$filterNoAutoescape('\u6b63\u5728\u4e0a\u4f20\u548c\u4e71\u7801\uff1b') + '</div></div></nav></div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.sidebarContents.soyTemplateName = 'navlinks.templates.appswitcher.sidebarContents';
}


navlinks.templates.appswitcher.trigger = function(opt_data, opt_ignored) {
  return '<span class="aui-icon aui-icon-small aui-iconfont-appswitcher">' + soy.$$escapeHtml('\u5df2\u94fe\u63a5\u5e94\u7528\u7a0b\u5e8f') + '</span>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.trigger.soyTemplateName = 'navlinks.templates.appswitcher.trigger';
}


navlinks.templates.appswitcher.projectHeaderSection = function(opt_data, opt_ignored) {
  return '<div class="app-switcher-title">' + aui.avatar.avatar({size: 'large', avatarImageUrl: opt_data.avatarUrl, isProject: true, title: opt_data.name}) + '<div class="sidebar-project-name">' + soy.$$escapeHtml(opt_data.name) + '</div></div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.projectHeaderSection.soyTemplateName = 'navlinks.templates.appswitcher.projectHeaderSection';
}


navlinks.templates.appswitcher.cogDropdown = function(opt_data, opt_ignored) {
  var output = '';
  var dropdownList__soy74 = '' + navlinks.templates.appswitcher.dropdownList({list: opt_data.links});
  output += aui.dropdown2.dropdown2({menu: {id: opt_data.id, content: dropdownList__soy74, extraClasses: 'aui-style-default sidebar-customize-section'}, trigger: {showIcon: false, content: '<span class="aui-icon aui-icon-small aui-iconfont-configure"></span>', container: '#app-switcher'}});
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.cogDropdown.soyTemplateName = 'navlinks.templates.appswitcher.cogDropdown';
}


navlinks.templates.appswitcher.dropdownList = function(opt_data, opt_ignored) {
  var output = '<ul class="sidebar-admin-links">';
  var linkList82 = opt_data.list;
  var linkListLen82 = linkList82.length;
  for (var linkIndex82 = 0; linkIndex82 < linkListLen82; linkIndex82++) {
    var linkData82 = linkList82[linkIndex82];
    output += '<li class="nav-link"><a href="' + soy.$$escapeHtml(linkData82.href) + '" title="' + soy.$$escapeHtml(linkData82.title) + '"><span class="nav-link-label">' + soy.$$escapeHtml(linkData82.label) + '</span></a></li>';
  }
  output += '</ul>';
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.dropdownList.soyTemplateName = 'navlinks.templates.appswitcher.dropdownList';
}


navlinks.templates.appswitcher.switcher = function(opt_data, opt_ignored) {
  var output = '';
  if (true) {
    if (AJS.DarkFeatures.isEnabled('rotp.sidebar')) {
      var sidebarContents__soy97 = '' + navlinks.templates.appswitcher.sidebarContents(null);
      var triggerContent__soy99 = '' + navlinks.templates.appswitcher.trigger(null);
      output += navlinks.templates.appswitcher.sidebar({sidebar: {id: 'app-switcher', content: sidebarContents__soy97}, trigger: {showIcon: false, content: triggerContent__soy99}});
    } else {
      output += navlinks.templates.appswitcher_old.switcher(null);
    }
  }
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.switcher.soyTemplateName = 'navlinks.templates.appswitcher.switcher';
}


navlinks.templates.appswitcher.sidebar = function(opt_data, opt_ignored) {
  return '<button class="sidebar-trigger app-switcher-trigger" aria-owns="' + soy.$$escapeHtml(opt_data.sidebar.id) + '" aria-haspopup="true" >' + soy.$$filterNoAutoescape(opt_data.trigger.content) + '</button><div id=' + soy.$$escapeHtml(opt_data.sidebar.id) + ' class="app-switcher-sidebar aui-style-default sidebar-offscreen" data-is-sidebar="true" data-is-user-admin="' + soy.$$escapeHtml(false) + '">' + soy.$$filterNoAutoescape(opt_data.sidebar.content) + '</div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher.sidebar.soyTemplateName = 'navlinks.templates.appswitcher.sidebar';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:rotp-menu', location = 'appswitcher/appswitcher.js' */
(function(e,a){var d="is-user-admin";var c="#app-switcher";a.SideBar=function(f){var g=this;this.$sidebar=null;f=e.extend({sidebarContents:null},f);this.getLinks=function(){return e.ajax({url:AJS.contextPath()+"/rest/menu/latest/appswitcher",cache:false,dataType:"json"}).done(this.updateAppLinks).fail(this.showAppSwitcherError)};this.populateProjectHeader=function(i,h){g.getSidebar().find(".app-switcher-shortcuts .aui-nav-heading").after(navlinks.templates.appswitcher.projectHeaderSection({avatarUrl:h,name:i}))};this.getProjectData=function(){var h=e(".project-shortcut-dialog-trigger"),i=h.data("key"),j=h.data("entity-type");if(h.size()==0||!i||!j){e(".app-switcher-shortcuts").remove();return}var l,k;k=e.ajax({url:AJS.contextPath()+"/rest/project-shortcuts/1.0/local/"+i,cache:false,data:{entityType:j},dataType:"json"});l=e.ajax({url:AJS.contextPath()+"/rest/project-shortcuts/1.0/remote/"+i,cache:false,data:{entityType:j},dataType:"json"});e.when(k,l).then(function(n,m){g.updateProjectShortcuts(n,m,{key:i,entityType:j,name:h.data("name"),avatarUrl:h.find("img").prop("src")})},g.showProjectShortcutsError)};this.getSidebar=function(){if(!this.$sidebar){this.$sidebar=e(f.sidebarContents)}return this.$sidebar};this.addApplicationsCog=function(){e(".app-switcher-applications .aui-nav-heading").before(navlinks.templates.appswitcher.cogDropdown({id:"sidebar-applications-admin-dropdown",links:[{href:AJS.contextPath()+"/plugins/servlet/customize-application-navigator",label:"\u81ea\u5b9a\u4e49\u5bfc\u822a",title:"\u6dfb\u52a0\u65b0\u6761\u76ee\u3001\u9690\u85cf\u6216\u9650\u5236\u8c01\u770b\u89c1\u3002"},{href:AJS.contextPath()+"/plugins/servlet/applinks/listApplicationLinks",label:"\u7ba1\u7406\u5e94\u7528\u7a0b\u5e8f\u94fe\u63a5",title:"\u94fe\u63a5\u5230\u66f4\u591aAtlassian\u5e94\u7528\u7a0b\u5e8f"}]}))};this.addProjectShortcutsCog=function(h,j){var i=[{href:AJS.contextPath()+"/plugins/servlet/custom-content-links-admin?entityKey="+h,label:"\u81ea\u5b9a\u4e49\u5feb\u6377\u952e",title:""}];if(g.entityMappings[j]){i.push({href:g.generateEntityLinksUrl(h,g.entityMappings[j]),label:"\u7ba1\u7406\u4ea7\u54c1\u94fe\u63a5",title:""})}g.getSidebar().find(".app-switcher-shortcuts .aui-nav-heading").before(navlinks.templates.appswitcher.cogDropdown({id:"sidebar-project-shortcuts-admin-dropdown",links:i}))};this.updateAppLinks=function(h){e(function(){g.getSidebar().find(".app-switcher-applications").html(navlinks.templates.appswitcher.linkSection({title:"\u5e94\u7528\u7a0b\u5e8f\u94fe\u63a5",list:h}));if(g.getSidebar().data(d)){g.addApplicationsCog()}g.bindAnalyticsHandlers(g.getSidebar(),h)})};this.updateProjectShortcuts=function(k,i,j){var l=k[0].shortcuts,h=i[0].shortcuts;g.getSidebar().find(".app-switcher-shortcuts").html(navlinks.templates.appswitcher.linkSection({title:"\u5feb\u6377\u65b9\u5f0f",list:l.concat(h)}));if(g.getSidebar().data(d)){g.addProjectShortcutsCog(j.key,j.entityType)}g.populateProjectHeader(j.name,j.avatarUrl);g.bindAnalyticsHandlers(g.getSidebar(),data)};this.entityMappings={"confluence.space":"com.atlassian.applinks.api.application.confluence.ConfluenceSpaceEntityType","jira.project":"com.atlassian.applinks.api.application.jira.JiraProjectEntityType","bamboo.project":"com.atlassian.applinks.api.application.bamboo.BambooProjectEntityType","stash.project":"com.atlassian.applinks.api.application.stash.StashProjectEntityType"};this.generateEntityLinksUrl=function(h,i){if(i===g.entityMappings["confluence.space"]){return AJS.contextPath()+"/spaces/listentitylinks.action?typeId="+i+"&key="+h}else{return AJS.contextPath()+"/plugins/servlet/applinks/listEntityLinks/"+i+"/"+h}};this.showAppSwitcherError=function(){e(function(){var h=g.getSidebar();h.find(".app-switcher-applications .app-switcher-loading").replaceWith(navlinks.templates.appswitcher.error());h.off(".appswitcher").on("click.appswitcher",".app-switcher-retry",e.proxy(g.retryLoading,g))})};this.showProjectShortcutsError=function(){e(function(){var h=g.getSidebar();h.find(".app-switcher-shortcuts .app-switcher-loading").replaceWith(navlinks.templates.appswitcher.error());h.off(".appswitcher").on("click.appswitcher",".app-switcher-retry",e.proxy(g.retryLoading,g))})};this.retryLoading=function(h){this.getSidebar().html(navlinks.templates.appswitcher.sidebarContents());this.getLinks();this.getProjectData();h&&h.stopPropagation()};this.bindAnalyticsHandlers=function(h,i){};this.getLinks();e(this.getProjectData);this.toggleSidebar=function(j){j.preventDefault();var k=g.getSidebar(),i=e("body"),h=e(window.document);if(!i.hasClass("app-switcher-open")){var m=e("#header");k.css("left",-k.width());k.parent("body").length||k.appendTo("body");b({data:k});k.animate({left:0},300);function l(n){var p=n.target&&e(n.target),o=n.keyCode;if(n.originalEvent===j.originalEvent){return}if(p&&!o&&!(p.closest(k).length||p.closest(m).length)&&j.which==1&&!(n.shiftKey||n.ctrlKey||n.metaKey)){g.toggleSidebar()}else{if(o===27){g.toggleSidebar()}}}h.on("click.appSwitcher",l);h.on("keydown.appSwitcher",l);h.on("scroll.appSwitcher",k,b)}else{h.off(".appSwitcher")}i.toggleClass("app-switcher-open")};e("#header").on("click",".app-switcher-trigger",this.toggleSidebar)};function b(h){var f=e(document).scrollTop(),i=e("#header"),g=(i.height()+i.offset().top)-f;if(g>=0){h.data.css({top:g,position:"fixed"})}else{h.data.css({top:0,left:0,position:"fixed"})}}e(function(){if(e(c).data("is-sidebar")===true){new a.SideBar({sidebarContents:c})}})}(jQuery,window.NL=(window.NL||{})));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:rotp-menu', location = 'appswitcher/appswitcher_old.js' */
var APPSWITCHER_TRIGGER_CLICK="appswitcher.trigger.click";var APPSWITCHER_DROPDOWN_SHOW="appswitcher.dropdown.show";var APPSWITCHER_DROPDOWN_DISPLAY_ERROR="appswitcher.dropdown.display.error";var APPSWITCHER_APP_LINK_CLICK="appswitcher.app.link.click";var APPSWITCHER_CONFIGURE_LINK_CLICK="appswitcher.configure.link.click";(function(c,f){var b="isAppSuggestionAvailable";var d="isSiteAdminUser";var e="isUserAdmin";var a="#app-switcher";f.AppSwitcher=function(j){var l=AJS.contextPath()+"/plugins/servlet/customize-application-navigator";var k="unified.usermanagement";var m=this;this.$dropdown=null;j=c.extend({dropdownContents:null},j);this.getLinks=function(){return c.ajax({url:AJS.contextPath()+"/rest/menu/latest/appswitcher",cache:false,dataType:"json"}).done(this.updateDropdown).fail(this.showError)};this.getDropdown=function(){if(!this.$dropdown){this.$dropdown=c(j.dropdownContents);this.envData=this.$dropdown.data("environment")}return this.$dropdown};this.updateDropdown=function(n){c(function(){m.getDropdown().html(navlinks.templates.appswitcher_old.applications({apps:n,showAdminLink:m.envData[e],adminLink:l}));m.bindAnalyticsHandlers();if(m.envData[b]===true){m.handleSuggestionApps(n)}})};this.bindAnalyticsHandlers=function(){c(".app-switcher-trigger").on("click",function(){AJS.trigger("analyticsEvent",{name:APPSWITCHER_TRIGGER_CLICK})});c("#app-switcher").on("aui-dropdown2-show",function(){AJS.trigger("analyticsEvent",{name:APPSWITCHER_DROPDOWN_SHOW})});c("#app-switcher .nav-link").on("click",function(){var p="custom";var q=c(this).find("a");var o=q.attr("href");var n=window.location.hostname;if(o&&o.indexOf("bitbucket.org")>-1){p="bitbucket-cloud"}else{if(o.indexOf(n+"/wiki")>-1){p="confluence"}else{if(o.indexOf(n+"/build")>-1){p="bamboo"}else{if(o.indexOf(n)>-1){p="jira"}}}}AJS.trigger("analyticsEvent",{name:APPSWITCHER_APP_LINK_CLICK,data:{product:p}})});c(".nav-link-edit-wrapper").on("click",function(){AJS.trigger("analyticsEvent",{name:APPSWITCHER_CONFIGURE_LINK_CLICK})})};this.isBillingSystemEnabled=function(){return(this.envData[d]===true)&&AJS.DarkFeatures.isEnabled(k)};this.handleSuggestionApps=function(q){var r=_.map(q,function(s){return s.applicationType.toLowerCase()});var o=c("<div id='app-switcher-suggestion-apps' class='aui-dropdown2-section'/>");o.html(navlinks.templates.appswitcher_old.suggestionApps);var p=o.find(".suggestion-apps");var n=false;_.each(g,function(s){if(!_.contains(r,s.appName)){n=true;p.append(navlinks.templates.appswitcher_old.suggestionApp({suggestionApp:s,isBillingSystemEnabled:m.isBillingSystemEnabled()}))}});if(!n){return}c("#app-switcher").append(o);c(".app-discovery-suggestion-app").click(function(){var t=c(this).find("a");var s;if(m.envData[d]===true){s="appswitcher.discovery.siteadmin.select.inproduct."}else{s="appswitcher.discovery.user.select."}s=s+t.attr("id").toLowerCase();AJS.trigger("analytics",{name:s})});c(".app-discovery-suggestion-app").hover(function(){c(this).find("a").removeClass("active").removeClass("aui-dropdown2-active")});c(".app-discovery-cancel-button").click(function(){AJS.trigger("analytics",{name:"appswitcher.discovery.nothanks.button.click"});i(h,"true");o.remove()})};this.showError=function(){c(function(){AJS.trigger("analyticsEvent",{name:APPSWITCHER_DROPDOWN_DISPLAY_ERROR});m.getDropdown().html(navlinks.templates.appswitcher_old.error()).off(".appswitcher").on("click.appswitcher",".app-switcher-retry",c.proxy(m.retryLoading,m))})};this.retryLoading=function(n){this.getDropdown().html(navlinks.templates.appswitcher_old.loading());this.getLinks();n&&n.stopPropagation()};this.getLinks()};var h="key-no-thanks";var g=[{appName:"jira",appDesc:"\u95ee\u9898\u548c\u9879\u76ee\u8ddf\u8e2a\u8f6f\u4ef6",discoveryUrl:"https://www.atlassian.com/software/jira",billingSystemDiscoveryUrl:"/admin/billing/addapplication"},{appName:"confluence",appDesc:"\u534f\u4f5c\u548c\u5185\u5bb9\u5171\u4eab",discoveryUrl:"https://www.atlassian.com/software/confluence",billingSystemDiscoveryUrl:"/admin/billing/addapplication?product=confluence.ondemand"},{appName:"bamboo",appDesc:"\u6301\u7eed\u96c6\u6210",discoveryUrl:"https://www.atlassian.com/software/bamboo",billingSystemDiscoveryUrl:"/admin/billing/addapplication?product=bamboo.ondemand"}];var i=function(j,k){c.ajax({url:AJS.contextPath()+"/rest/menu/latest/userdata/",type:"PUT",contentType:"application/json",data:JSON.stringify({key:j,value:k})})};c(function(){if(c(a).data("is-switcher")===true){new f.AppSwitcher({dropdownContents:a})}})}(jQuery,window.NL=(window.NL||{})));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugins.atlassian-nav-links-plugin:rotp-menu', location = 'appswitcher/appswitcher_old.soy' */
// This file was automatically generated from appswitcher_old.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace navlinks.templates.appswitcher_old.
 */

if (typeof navlinks == 'undefined') { var navlinks = {}; }
if (typeof navlinks.templates == 'undefined') { navlinks.templates = {}; }
if (typeof navlinks.templates.appswitcher_old == 'undefined') { navlinks.templates.appswitcher_old = {}; }


navlinks.templates.appswitcher_old.applications = function(opt_data, opt_ignored) {
  return '' + navlinks.templates.appswitcher_old.applicationsSection({list: opt_data.apps, listClass: 'nav-links', showDescription: opt_data.showDescription}) + ((opt_data.custom) ? navlinks.templates.appswitcher_old.applicationsSection({list: opt_data.custom, showDescription: opt_data.showDescription}) : '') + ((opt_data.showAdminLink) ? navlinks.templates.appswitcher_old.adminSection(opt_data) : '');
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.applications.soyTemplateName = 'navlinks.templates.appswitcher_old.applications';
}


navlinks.templates.appswitcher_old.applicationsSection = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.list.length > 0) {
    var param19 = '<ul' + ((opt_data.listClass) ? ' class="' + soy.$$escapeHtml(opt_data.listClass) + '"' : '') + '>';
    var linkList27 = opt_data.list;
    var linkListLen27 = linkList27.length;
    for (var linkIndex27 = 0; linkIndex27 < linkListLen27; linkIndex27++) {
      var linkData27 = linkList27[linkIndex27];
      param19 += navlinks.templates.appswitcher_old.applicationsItem(soy.$$augmentMap(linkData27, {showDescription: opt_data.showDescription}));
    }
    param19 += '</ul>';
    output += aui.dropdown2.section({content: param19});
  }
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.applicationsSection.soyTemplateName = 'navlinks.templates.appswitcher_old.applicationsSection';
}


navlinks.templates.appswitcher_old.applicationsItem = function(opt_data, opt_ignored) {
  return '<li class="nav-link' + ((opt_data.self) ? ' nav-link-local' : '') + '"><a href="' + soy.$$escapeHtml(opt_data.link) + '" class="aui-dropdown2-radio ' + ((opt_data.self) ? 'aui-dropdown2-checked' : '') + '" title="' + soy.$$escapeHtml(opt_data.link) + '"><span class="nav-link-label">' + soy.$$escapeHtml(opt_data.label) + '</span>' + ((opt_data.showDescription && opt_data.description) ? '<span class="nav-link-description">' + soy.$$escapeHtml(opt_data.description) + '</span>' : '') + '</a></li>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.applicationsItem.soyTemplateName = 'navlinks.templates.appswitcher_old.applicationsItem';
}


navlinks.templates.appswitcher_old.adminSection = function(opt_data, opt_ignored) {
  return '' + aui.dropdown2.section({content: '<ul class="nav-links"><li><a class="nav-link-edit-wrapper" href="' + soy.$$escapeHtml(opt_data.adminLink) + '"><span class="nav-link-edit">' + soy.$$filterNoAutoescape('\u914d\u7f6e...') + '</span></a></li></ul>'});
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.adminSection.soyTemplateName = 'navlinks.templates.appswitcher_old.adminSection';
}


navlinks.templates.appswitcher_old.error = function(opt_data, opt_ignored) {
  return '<div class="app-switcher-error">' + soy.$$filterNoAutoescape('\u51fa\u4e86\u9519, \u8bf7\x3cspan class\x3d\x22app-switcher-retry\x22\x3e\u518d\u8bd5\u4e00\u6b21\x3c/span \x3e\u3002') + '</div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.error.soyTemplateName = 'navlinks.templates.appswitcher_old.error';
}


navlinks.templates.appswitcher_old.loading = function(opt_data, opt_ignored) {
  return '<div class="app-switcher-loading">' + soy.$$filterNoAutoescape('\u6b63\u5728\u4e0a\u4f20\u548c\u4e71\u7801\uff1b') + '</div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.loading.soyTemplateName = 'navlinks.templates.appswitcher_old.loading';
}


navlinks.templates.appswitcher_old.trigger = function(opt_data, opt_ignored) {
  return '<span class="aui-icon aui-icon-small aui-iconfont-appswitcher">' + soy.$$escapeHtml('\u5df2\u94fe\u63a5\u5e94\u7528\u7a0b\u5e8f') + '</span>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.trigger.soyTemplateName = 'navlinks.templates.appswitcher_old.trigger';
}


navlinks.templates.appswitcher_old.switcher = function(opt_data, opt_ignored) {
  var output = '';
  if (true) {
    var loadingContent__soy81 = '' + navlinks.templates.appswitcher_old.loading(null);
    var triggerContent__soy83 = '' + navlinks.templates.appswitcher_old.trigger(null);
    output += aui.dropdown2.dropdown2({menu: {id: 'app-switcher', content: loadingContent__soy81, extraClasses: 'aui-style-default', extraAttributes: {'data-environment': {}, 'data-is-switcher': 'true'}}, trigger: {tagName: 'button', showIcon: false, content: triggerContent__soy83, extraClasses: 'app-switcher-trigger aui-dropdown2-trigger-arrowless', extraAttributes: {href: '#app-switcher'}}});
  }
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.switcher.soyTemplateName = 'navlinks.templates.appswitcher_old.switcher';
}


navlinks.templates.appswitcher_old.suggestionApp = function(opt_data, opt_ignored) {
  var output = '';
  var href__soy89 = opt_data.isBillingSystemEnabled == true ? opt_data.suggestionApp.billingSystemDiscoveryUrl : opt_data.suggestionApp.discoveryUrl;
  output += '<li class="app-discovery-suggestion-app"><a id="' + soy.$$escapeHtml(opt_data.suggestionApp.appName) + '" href="' + soy.$$escapeHtml(href__soy89) + '" class="app-discovery-link aui-icon-container app-discovery-' + soy.$$escapeHtml(opt_data.suggestionApp.appName) + '-product-icon" title="' + soy.$$escapeHtml(href__soy89) + '" target="_blank"></a><div class="app-discovery-small">' + soy.$$escapeHtml(opt_data.suggestionApp.appDesc) + '</div></li>';
  return output;
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.suggestionApp.soyTemplateName = 'navlinks.templates.appswitcher_old.suggestionApp';
}


navlinks.templates.appswitcher_old.suggestionApps = function(opt_data, opt_ignored) {
  return '<ul class=\'nav-links suggestion-apps\'><li><span class=\'app-discovery-suggest-title nav-link-label\'><h6>' + soy.$$escapeHtml('\u5c1d\u8bd5\u5176\u5b83Atlassian\u5e94\u7528\u7a0b\u5e8f') + '</h6></span></li></ul><div class=\'buttons-container app-discovery-suggest-apps-buttons\'><div class=\'buttons\'><button class=\'aui-button aui-button-link app-discovery-cancel-button\' name=\'cancel\' accesskey=\'c\' href=\'#\'>' + soy.$$escapeHtml('\u4e0d\u518d\u663e\u793a\u6b64\u9879') + '</button></div></div>';
};
if (goog.DEBUG) {
  navlinks.templates.appswitcher_old.suggestionApps.soyTemplateName = 'navlinks.templates.appswitcher_old.suggestionApps';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-header-plugin:header-templates', location = 'soy/headerDropdown.soy' */
// This file was automatically generated from headerDropdown.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.Menu.Dropdowns.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.Menu == 'undefined') { JIRA.Templates.Menu = {}; }
if (typeof JIRA.Templates.Menu.Dropdowns == 'undefined') { JIRA.Templates.Menu.Dropdowns = {}; }


JIRA.Templates.Menu.Dropdowns.dropdown2Fragment = function(opt_data, opt_ignored) {
  var output = '';
  var sectionList3 = opt_data.sections;
  var sectionListLen3 = sectionList3.length;
  for (var sectionIndex3 = 0; sectionIndex3 < sectionListLen3; sectionIndex3++) {
    var sectionData3 = sectionList3[sectionIndex3];
    output += JIRA.Templates.Menu.Dropdowns.dropdown2Section(sectionData3);
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.Menu.Dropdowns.dropdown2Fragment.soyTemplateName = 'JIRA.Templates.Menu.Dropdowns.dropdown2Fragment';
}


JIRA.Templates.Menu.Dropdowns.dropdown2Section = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  var output = '';
  var hasItems__soy7 = opt_data.items && opt_data.items.length > 0;
  if (hasItems__soy7) {
    output += '<div' + ((opt_data.id) ? ' id="' + soy.$$escapeHtml(opt_data.id) + '"' : '') + ' class="aui-dropdown2-section' + ((opt_data.style) ? soy.$$escapeHtml(opt_data.style) : '') + '" >' + ((opt_data.label) ? '<strong>' + soy.$$escapeHtml(opt_data.label) + '</strong>' : '') + '<ul class="aui-list-truncate">';
    var itemList27 = opt_data.items;
    var itemListLen27 = itemList27.length;
    for (var itemIndex27 = 0; itemIndex27 < itemListLen27; itemIndex27++) {
      var itemData27 = itemList27[itemIndex27];
      output += '<li' + ((itemData27.id) ? ' id="' + soy.$$escapeHtml(itemData27.id) + '"' : '') + ((itemData27.style) ? ' class="' + soy.$$escapeHtml(itemData27.style) + '"' : '') + '>' + JIRA.Templates.Menu.Dropdowns.dropdown2Item(itemData27) + '</li>';
    }
    output += '</ul></div>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.Menu.Dropdowns.dropdown2Section.soyTemplateName = 'JIRA.Templates.Menu.Dropdowns.dropdown2Section';
}


JIRA.Templates.Menu.Dropdowns.dropdown2Item = function(opt_data, opt_ignored) {
  var output = '<a href="' + soy.$$escapeHtml(opt_data.url) + '"' + ((opt_data.parameters && opt_data.parameters.trackClick) ? ' data-track-click="' + soy.$$escapeHtml(opt_data.parameters.trackClick) + '"' : '') + ((opt_data.id) ? ' id="' + soy.$$escapeHtml(opt_data.id) + '_lnk"' : '') + ' class="' + ((opt_data.iconUrl) ? 'aui-icon-container' : '') + ((opt_data.parameters && opt_data.parameters['class']) ? ' ' + soy.$$escapeHtml(opt_data.parameters['class']) : '') + '"';
  if (opt_data.parameters) {
    var keyList69 = soy.$$getMapKeys(opt_data.parameters);
    var keyListLen69 = keyList69.length;
    for (var keyIndex69 = 0; keyIndex69 < keyListLen69; keyIndex69++) {
      var keyData69 = keyList69[keyIndex69];
      output += (keyData69 != 'class' && keyData69 != 'iconurl') ? ' ' + soy.$$escapeHtml(keyData69) + '="' + soy.$$escapeHtml(opt_data.parameters[keyData69]) + '"' : '';
    }
  }
  output += '>' + ((opt_data.iconUrl) ? '<img class="icon" src="' + soy.$$escapeHtml(opt_data.iconUrl) + '" alt="" />' : '') + soy.$$escapeHtml(opt_data.label) + '</a>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.Menu.Dropdowns.dropdown2Item.soyTemplateName = 'JIRA.Templates.Menu.Dropdowns.dropdown2Item';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-header-plugin:analytics-header', location = 'js/header-analytics.js' */
define("jira/header/init-header-analytics",["jira/analytics","jira/util/logger","jquery"],function(n,t,e){"use strict";function i(n,t){var e=void 0;return n.forEach(function(n){n.id===t&&(e=n.analyticEventKey)}),e}function o(o){var m=e(o);m.on("click","#logo",function(){n.send({name:a+"home"}),t.trace(r)}),m.on("aui-dropdown2-show",l,function(){var n=e(this).attr("id");c(u,n)&&d(n,u,".open")}),m.on("aui-dropdown2-hide",l,function(){var n=e(this).attr("id");c(u,n)&&d(n,u,".close")}),m.on("aui-dropdown2-show",s,function(){var n=e(this);n.trigger("aui-dropdown2-show-before"),n.trigger("aui-dropdown2-show-after")}),m.on("click",y,function(){var n=this.getAttribute("aria-controls");c(u,n)&&d(n,u,".click")}),m.on("click",".aui-dropdown2 .aui-dropdown2-section a",function(o){var c=e(o.target).closest(l).first();if(null!==c){var d=c.find("a").index(o.target),s=c.attr("id"),y=i(u,s);n.send({name:a+y+".item.click",itemIndex:d}),t.trace(r)}})}var a="jira.navigation.header.",r="jira.header.analytics.event",c=function(n,t){return n.some(function(n){return n.id===t})},d=function(e,o,c){var d=i(o,e),u=a+d+c;n.send({name:u}),t.trace(r)},u=[{id:"home_link-content",analyticEventKey:"dashboards"},{id:"browse_link-content",analyticEventKey:"projects"},{id:"find_link-content",analyticEventKey:"issues"},{id:"tempo_menu-content",analyticEventKey:"tempo"},{id:"bonfire_top_menu_dropdown-content",analyticEventKey:"capture"},{id:"greenhopper_menu-content",analyticEventKey:"boards"},{id:"plugins-jira-webitem-main-content",analyticEventKey:"portfolio"},{id:"system-help-menu-content",analyticEventKey:"help"},{id:"system-admin-menu-content",analyticEventKey:"admin"}],s="#system-help-menu-content, #user-options-content, #system-admin-menu-content",l=u.map(function(n){return"#"+n.id}).join(", "),y=u.reduce(function(n,t){return n+"a.aui-dropdown2-trigger[aria-owns="+t.id+"]"},"");return{init:o}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-header-plugin:jira-header-assets', location = 'js/init-dropdown2.js' */
define("jira/header/dropdown/element",["jira/ajs/ajax/smart-ajax","jira/jquery/deferred","jira/skate","jira/util/data/meta","wrm/context-path","jquery"],function(e,a,t,n,o,r){"use strict";var i=0;return t("aui-dropdown2-ajax",{type:t.type.CLASSNAME,attached:function(t){function d(){document.activeElement===c.get(0)&&s.get(0).focusItem(0)}function u(){c.data("kbd-interacted",!1)}var s=r(document.getElementById(t.getAttribute("aria-controls"))),c=r(t),m=s.data("aui-dropdown2-ajax-key");i+=1;var w=".aui-dropdown2-ajax"+i;t.jiraInitDropdown2EventNamespace=w;c.on("keydown"+w,function(){c.data("kbd-interacted",!0),setTimeout(u,4)}),s.on("aui-dropdown2-show-after"+w,u),u(),s.on("aui-dropdown2-show"+w,function(){if(s.trigger("aui-dropdown2-show-before"),m){var t=new a,i=new a;setTimeout(function(){t.resolve(c.data("kbd-interacted"))},0),s.empty(),s.addClass("aui-dropdown2-loading"),c.attr("aria-busy","true"),e.makeRequest({url:o()+"/rest/api/1.0/menus/"+m,data:{inAdminMode:n.getBoolean("in-admin-mode")},dataType:"json",cache:!1,success:function(e){i.resolve(e)},error:i.reject}),r.when(t,i).done(function(e,a){s.html(JIRA.Templates.Menu.Dropdowns.dropdown2Fragment(a));var t=c.hasClass("aui-dropdown2-sub-trigger");e&&t?setTimeout(d,0):setTimeout(function(){return s.focus()},0)}).always(function(){c.attr("aria-busy","false"),s.removeClass("aui-dropdown2-loading"),s.trigger("aui-dropdown2-show-after")})}else s.trigger("aui-dropdown2-show-after")})},detached:function(e){var a=r(document.getElementById(e.getAttribute("aria-controls"))),t=r(e),n=e.jiraInitDropdown2EventNamespace;n&&(a.off(n),t.off(n))}})}),function(e){"use strict";e(document).on("aui-responsive-menu-item-created",".aui-header.aui-dropdown2-trigger-group",function(a){var t=a.originalEvent.detail;e(t.originalItem).find("> .aui-dropdown2-ajax").length>0&&e(t.newItem).find("> .aui-dropdown2-trigger").addClass("aui-dropdown2-ajax")})}(require("jquery"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-header-plugin:jira-header-assets', location = 'js/app-switcher-analytics.js' */
define("jira/header/init-app-switcher-analytics",["jira/analytics","jquery"],function(i,n){"use strict";function a(a){var e=n(a);e.on("aui-dropdown2-show-before","#app-switcher",function(){i.send({name:"navigation.header.appswitcher.open"})}),e.on("click","#app-switcher a, #app-switcher button",function(a){var e,t=n(a.target),o=t.closest("li").index();t.is(".nav-link-edit")?e="configure":t.is(".app-discovery-link")?e=this.id?this.id:"unknown.app.discovery":t.is(".app-discovery-cancel-button")?(e="dont.show",o=0):e="application",i.send({name:"navigation.header.appswitcher.click",data:{position:o,linkType:e}})}),e.on("aui-dropdown2-hide","#app-switcher",function(){i.send({name:"navigation.header.appswitcher.close"})})}return{init:a}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-header-plugin:jira-header', location = 'js/init-header.js' */
require(["jira/header/dropdown/element","jira/header/init-header-analytics","jira/header/init-app-switcher-analytics"],function(e,i,t){"use strict";i.init(document),t.init(document)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:underscore', location = 'js/lib/underscore-adapter.js' */
define("workflow-designer/underscore",["underscore"],function(A){return A});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:workflow-designer-loader', location = 'js/require.js' */
define("workflow-designer/application-loader",["wrm/require","require"],function(A,B){return{load:function C(D){return A(["wrc!com.atlassian.jira.plugins.jira-workflow-designer.workflow-designer"],function(){D&&D(B("workflow-designer/application"))})}}});AJS.namespace("JIRA.WorkflowDesigner.require",null,require("workflow-designer/application-loader").load);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:workflow-designer-templates', location = 'js/templates-amd.js' */
define("workflow-designer/templates",[],function(){AJS.namespace("JIRA.WorkflowDesigner.Templates");return JIRA.WorkflowDesigner.Templates});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:dialog', location = 'js/util/DialogSizeCalculator.js' */
define("workflow-designer/dialog-size-calculator",["workflow-designer/underscore","jquery"],function(A,B){return{DISTANCE_FROM_EDGE:100,calculateDialogSize:function(D,C){this.dialog=D;C=A.defaults(C||{},{windowHeight:this._getWindowHeight(),windowWidth:this._getWindowWidth(),dialogHeaderHeight:this._getDialogHeaderHeight(),dialogFooterHeight:this._getDialogFooterHeight()});return{height:this._calculateDialogHeight(C.windowHeight,C.dialogHeaderHeight,C.dialogFooterHeight),width:this._calculateDialogWidth(C.windowWidth)}},_calculateDialogHeight:function(E,C,D){return E-C-D-(this.DISTANCE_FROM_EDGE*2)},_calculateDialogWidth:function(C){return C-(this.DISTANCE_FROM_EDGE*2)},_getWindowHeight:function(){return B(window).height()},_getWindowWidth:function(){return B(window).width()},_getDialogHeaderHeight:function(){return this.dialog.$popupHeading.outerHeight()},_getDialogFooterHeight:function(){return this.dialog.$buttonContainer.outerHeight()}}});AJS.namespace("JIRA.WorkflowDesigner.DialogSizeCalculator",null,require("workflow-designer/dialog-size-calculator"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:dialog', location = 'js/views/dialogs/Dialog.js' */
define("workflow-designer/dialog",["workflow-designer/dialog-size-calculator","jira/dialog/form-dialog","workflow-designer/application-loader","workflow-designer/templates","jira/dialog/dialog","workflow-designer/underscore","jquery"],function(A,F,E,D,B,C,G){return F.extend({init:function(H){C.bindAll(this,"_sizeAndPositionDialog");H=C.defaults({},H,{content:this.createDialogContent,isDraft:false,onContentRefresh:this._showWorkflowDesigner,widthClass:"large"});this.options=H;this.sizeAndPositionDialog=C.throttle(this._sizeAndPositionDialog,100);this._super(H);this.onContentReady(this._addLabelHint)},createDialogContent:function(H){H(D.Dialog.workflowDialog({title:C.result(this.options,"workflowId")}))},show:function(){this._super.apply(this,arguments);G(window).on("resize",this.sizeAndPositionDialog)},hide:function(){this._designer&&this._designer.destroy();this._super.apply(this,arguments);G(window).off("resize",this.sizeAndPositionDialog)},_sizeAndPositionDialog:function(){var H,I;if(B.current===this){I=A.calculateDialogSize(this);this.options.width=I.width;H=this.$popup.find(".form-body");H.css({height:I.height,maxHeight:""});this._positionInCenter()}},_showWorkflowDesigner:function(){var H=this;this._sizeAndPositionDialog();E.load(function(I){if(H.isCurrent()){H._designer=new I({actions:false,element:H.get$popup().find(".form-body"),immutable:true,draft:C.result(H.options,"isDraft"),layoutData:C.result(H.options,"layoutData"),workflowId:C.result(H.options,"workflowId"),currentStepId:C.result(H.options,"currentStepId")})}})},_addLabelHint:function(){this.getButtonsContainer().prepend(D.Dialog.labelMouseOverHint())}})});AJS.namespace("JIRA.WorkflowDesigner.Dialog",null,require("workflow-designer/dialog"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:dialog', location = 'soy/Dialog.soy' */
// This file was automatically generated from Dialog.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.WorkflowDesigner.Templates.Dialog.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.WorkflowDesigner == 'undefined') { JIRA.WorkflowDesigner = {}; }
if (typeof JIRA.WorkflowDesigner.Templates == 'undefined') { JIRA.WorkflowDesigner.Templates = {}; }
if (typeof JIRA.WorkflowDesigner.Templates.Dialog == 'undefined') { JIRA.WorkflowDesigner.Templates.Dialog = {}; }


JIRA.WorkflowDesigner.Templates.Dialog.workflowDialog = function(opt_data, opt_ignored) {
  return '<div class="aui-dialog-content"><h2 class="dialog-title">' + soy.$$escapeHtml(opt_data.title) + '</h2><div class="form-body"></div></div>';
};
if (goog.DEBUG) {
  JIRA.WorkflowDesigner.Templates.Dialog.workflowDialog.soyTemplateName = 'JIRA.WorkflowDesigner.Templates.Dialog.workflowDialog';
}


JIRA.WorkflowDesigner.Templates.Dialog.labelMouseOverHint = function(opt_data, opt_ignored) {
  return '<div class="buttons-container-left"><small>' + soy.$$escapeHtml('\u5c06\u9f20\u6807\u7f6e\u4e8e\u76ee\u6807\u4e0a\u65b9\u4ee5\u67e5\u770b\u8f6c\u6362\u540d\u79f0') + '</small></div>';
};
if (goog.DEBUG) {
  JIRA.WorkflowDesigner.Templates.Dialog.labelMouseOverHint.soyTemplateName = 'JIRA.WorkflowDesigner.Templates.Dialog.labelMouseOverHint';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:workflow-designer-browser-is-supported', location = 'js/browserIsSupported.js' */
define("workflow-designer/browser-support",["jira/util/navigator"],function(A){return{browserIsSupported:function B(){return !(A.isIE()&&parseInt(A.majorVersion(),10)<10)}}});AJS.namespace("JIRA.WorkflowDesigner.browserIsSupported",null,require("workflow-designer/browser-support").browserIsSupported);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-workflow-designer:view-workflow-project-admin', location = 'soy/ProjectAdminWorkflows.soy' */
// This file was automatically generated from ProjectAdminWorkflows.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.WorkflowDesigner.Templates.ProjectAdminWorkflows.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.WorkflowDesigner == 'undefined') { JIRA.WorkflowDesigner = {}; }
if (typeof JIRA.WorkflowDesigner.Templates == 'undefined') { JIRA.WorkflowDesigner.Templates = {}; }
if (typeof JIRA.WorkflowDesigner.Templates.ProjectAdminWorkflows == 'undefined') { JIRA.WorkflowDesigner.Templates.ProjectAdminWorkflows = {}; }


JIRA.WorkflowDesigner.Templates.ProjectAdminWorkflows.link = function(opt_data, opt_ignored) {
  return '<a data-workflowname="' + soy.$$escapeHtml(opt_data.workflowName) + '" class="project-config-workflow-designer-link new-workflow-designer" href="#" data-mode="new-diagram"><span>' + soy.$$escapeHtml('\u56fe\u5f62') + '</span></a>';
};
if (goog.DEBUG) {
  JIRA.WorkflowDesigner.Templates.ProjectAdminWorkflows.link.soyTemplateName = 'JIRA.WorkflowDesigner.Templates.ProjectAdminWorkflows.link';
}

}catch(e){WRMCB(e)};