2025-09-10 16:53:18,417 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin starting...
*********************************************************************************

2025-09-10 16:53:18,916 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

JIRA Agile Run History

	*JIRA Agile v8.13.7 (49aa7d64221da5c8) started at 10 Sep 2025 04:53


2025-09-10 16:53:18,919 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] There does not appear to be any JIRA Agile upgrade history
2025-09-10 16:53:18,920 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] This can be ok as upgrade history was not introduced until JIRA Agile v5.10.3
2025-09-10 16:53:18,921 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK]: com.atlassian.greenhopper.service.lexorank.LexoRankIssueEventListener
2025-09-10 16:53:18,921 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [EPIC_LINK]: com.atlassian.greenhopper.customfield.epiclink.EpicLinkIssueEventListener
2025-09-10 16:53:18,922 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [ISSUE_CLOSED]: com.atlassian.greenhopper.issue.RemoveFutureSprintsFromClosedIssuesListener
2025-09-10 16:53:18,922 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [ISSUE_MOVED]: com.atlassian.greenhopper.service.issue.IssueMovedEventListener
2025-09-10 16:53:18,923 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [CACHE_MANAGER]: com.atlassian.greenhopper.service.GreenHopperCacheManager
2025-09-10 16:53:18,923 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [RANK_INDEX_SERVICE]: com.atlassian.greenhopper.service.rank.RankIndexServiceImpl
2025-09-10 16:53:18,924 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK_FIELD]: com.atlassian.greenhopper.customfield.lexorank.LexoRankCFListener
2025-09-10 16:53:18,925 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK_BALANCING_EVENT]: com.atlassian.greenhopper.service.lexorank.balance.************************
2025-09-10 16:53:18,927 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [INDEX_CHECK_SERVICE]: com.atlassian.greenhopper.service.index.IndexCheckServiceImpl
2025-09-10 16:53:18,928 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [QUERY_MAPPER_CACHE]: com.atlassian.greenhopper.service.query.QueryToProjectMapper
2025-09-10 16:53:18,929 ThreadPoolAsyncTaskExecutor::Thread 14 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [AUDIT_LOG]: com.atlassian.greenhopper.auditing.AuditingEventListener
2025-09-10 16:53:22,401 http-nio-8080-exec-6 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin started.  Get Agile!
*********************************************************************************

2025-09-10 16:53:34,217 active-objects-init-JiraTenantImpl{id='system'}-0 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [atlassian.greenhopper.upgrade.AOUpgradeTask001] Starting upgrade task.
2025-09-10 16:53:34,850 active-objects-init-JiraTenantImpl{id='system'}-0 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [atlassian.greenhopper.upgrade.AOUpgradeTask001] No need to run upgrade task. Lexo rank table not found.
2025-09-10 16:58:22,956 FelixShutdown INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin stopping...
*********************************************************************************

2025-09-10 16:58:22,957 FelixShutdown INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.Launcher] Unscheduling Jira Agile jobs...
2025-09-10 16:58:22,958 FelixShutdown INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.Launcher] Destroying caches
2025-09-10 16:58:22,958 FelixShutdown INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [atlassian.greenhopper.service.GreenHopperCacheManager] ClearCacheEvent triggered but plugin was not yet alive...
2025-09-10 16:58:22,958 FelixShutdown INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.Launcher] Destroying services
2025-09-10 16:59:40,117 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin starting...
*********************************************************************************

2025-09-10 16:59:40,354 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

JIRA Agile Run History

	*JIRA Agile v8.13.7 (49aa7d64221da5c8) started at 10 Sep 2025 04:59


2025-09-10 16:59:40,355 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] There does not appear to be any JIRA Agile upgrade history
2025-09-10 16:59:40,356 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] This can be ok as upgrade history was not introduced until JIRA Agile v5.10.3
2025-09-10 16:59:40,356 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK]: com.atlassian.greenhopper.service.lexorank.LexoRankIssueEventListener
2025-09-10 16:59:40,356 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [EPIC_LINK]: com.atlassian.greenhopper.customfield.epiclink.EpicLinkIssueEventListener
2025-09-10 16:59:40,357 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [ISSUE_CLOSED]: com.atlassian.greenhopper.issue.RemoveFutureSprintsFromClosedIssuesListener
2025-09-10 16:59:40,357 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [ISSUE_MOVED]: com.atlassian.greenhopper.service.issue.IssueMovedEventListener
2025-09-10 16:59:40,357 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [CACHE_MANAGER]: com.atlassian.greenhopper.service.GreenHopperCacheManager
2025-09-10 16:59:40,357 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [RANK_INDEX_SERVICE]: com.atlassian.greenhopper.service.rank.RankIndexServiceImpl
2025-09-10 16:59:40,357 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK_FIELD]: com.atlassian.greenhopper.customfield.lexorank.LexoRankCFListener
2025-09-10 16:59:40,357 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK_BALANCING_EVENT]: com.atlassian.greenhopper.service.lexorank.balance.************************
2025-09-10 16:59:40,358 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [INDEX_CHECK_SERVICE]: com.atlassian.greenhopper.service.index.IndexCheckServiceImpl
2025-09-10 16:59:40,359 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [QUERY_MAPPER_CACHE]: com.atlassian.greenhopper.service.query.QueryToProjectMapper
2025-09-10 16:59:40,359 ThreadPoolAsyncTaskExecutor::Thread 1 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [AUDIT_LOG]: com.atlassian.greenhopper.auditing.AuditingEventListener
2025-09-10 16:59:42,872 http-nio-8080-exec-6 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin started.  Get Agile!
*********************************************************************************

2025-09-10 16:59:51,669 active-objects-init-JiraTenantImpl{id='system'}-0 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [atlassian.greenhopper.upgrade.AOUpgradeTask001] Starting upgrade task.
2025-09-10 16:59:52,101 active-objects-init-JiraTenantImpl{id='system'}-0 INFO anonymous 1009x216x1 10afwhz 172.18.0.1 /secure/SetupDatabase.jspa [atlassian.greenhopper.upgrade.AOUpgradeTask001] No need to run upgrade task. Lexo rank table not found.
2025-09-10 17:05:03,672 FelixShutdown INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin stopping...
*********************************************************************************

2025-09-10 17:05:03,675 FelixShutdown INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.Launcher] Unscheduling Jira Agile jobs...
2025-09-10 17:05:03,677 FelixShutdown INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.Launcher] Destroying caches
2025-09-10 17:05:03,677 FelixShutdown INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [atlassian.greenhopper.service.GreenHopperCacheManager] ClearCacheEvent triggered but plugin was not yet alive...
2025-09-10 17:05:03,677 FelixShutdown INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.Launcher] Destroying services
2025-09-10 17:06:16,854 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin starting...
*********************************************************************************

2025-09-10 17:06:16,934 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [greenhopper.service.logging.LogSupport] 

JIRA Agile Run History

	*JIRA Agile v8.13.7 (49aa7d64221da5c8) started at 10 Sep 2025 04:59


2025-09-10 17:06:16,936 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [greenhopper.service.logging.LogSupport] There does not appear to be any JIRA Agile upgrade history
2025-09-10 17:06:16,936 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [greenhopper.service.logging.LogSupport] This can be ok as upgrade history was not introduced until JIRA Agile v5.10.3
2025-09-10 17:06:16,937 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK]: com.atlassian.greenhopper.service.lexorank.LexoRankIssueEventListener
2025-09-10 17:06:16,937 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [EPIC_LINK]: com.atlassian.greenhopper.customfield.epiclink.EpicLinkIssueEventListener
2025-09-10 17:06:16,937 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [ISSUE_CLOSED]: com.atlassian.greenhopper.issue.RemoveFutureSprintsFromClosedIssuesListener
2025-09-10 17:06:16,938 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [ISSUE_MOVED]: com.atlassian.greenhopper.service.issue.IssueMovedEventListener
2025-09-10 17:06:16,938 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [CACHE_MANAGER]: com.atlassian.greenhopper.service.GreenHopperCacheManager
2025-09-10 17:06:16,938 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [RANK_INDEX_SERVICE]: com.atlassian.greenhopper.service.rank.RankIndexServiceImpl
2025-09-10 17:06:16,938 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK_FIELD]: com.atlassian.greenhopper.customfield.lexorank.LexoRankCFListener
2025-09-10 17:06:16,939 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [LEXO_RANK_BALANCING_EVENT]: com.atlassian.greenhopper.service.lexorank.balance.************************
2025-09-10 17:06:16,940 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [INDEX_CHECK_SERVICE]: com.atlassian.greenhopper.service.index.IndexCheckServiceImpl
2025-09-10 17:06:16,940 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [QUERY_MAPPER_CACHE]: com.atlassian.greenhopper.service.query.QueryToProjectMapper
2025-09-10 17:06:16,940 ThreadPoolAsyncTaskExecutor::Thread 4 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [com.atlassian.greenhopper.EventListenerRegistry] registered event listener [AUDIT_LOG]: com.atlassian.greenhopper.auditing.AuditingEventListener
2025-09-10 17:06:18,763 http-nio-8080-exec-3 INFO anonymous 1025x225x1 10afwhz 172.18.0.1 /secure/SetupLicense.jspa [greenhopper.service.logging.LogSupport] 

*********************************************************************************
Atlassian GreenHopper v8.13.7 #49aa7d64221da5c8 built 2021-05-18T15:47:06.079+08:00 - plugin started.  Get Agile!
*********************************************************************************

2025-09-10 17:08:59,677 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [com.atlassian.greenhopper.Launcher] Scheduling Jira Agile jobs...
2025-09-10 17:09:00,409 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.ManagedFieldRestrictor] Configuration locked for field 史诗链接 with true
2025-09-10 17:09:00,411 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.issuelink.EpicCustomFieldServiceImpl] Created Default Epic Link field with ID '10100'
2025-09-10 17:09:00,784 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.ManagedFieldRestrictor] Configuration locked for field Epic状态 with true
2025-09-10 17:09:00,785 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.issuelink.EpicCustomFieldServiceImpl] Created Default Epic Status field with ID '10101'
2025-09-10 17:09:01,088 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.ManagedFieldRestrictor] Configuration locked for field 史诗名称 with true
2025-09-10 17:09:02,065 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.issuelink.EpicCustomFieldServiceImpl] Created Default Epic Name field with ID '10102'
2025-09-10 17:09:02,593 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.ManagedFieldRestrictor] Configuration locked for field Epic颜色 with true
2025-09-10 17:09:02,593 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.issuelink.EpicCustomFieldServiceImpl] Created Default Epic Color field with ID '10103'
2025-09-10 17:09:03,104 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.ManagedFieldRestrictor] Configuration locked for field Sprint with true
2025-09-10 17:09:03,105 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.sprint.SprintCustomFieldServiceImpl] Created Default Sprint field with ID '10104'
2025-09-10 17:09:03,719 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.customfield.lexorank.LexoRankCFListener] LexoRank field created with id 10105, initing field values
2025-09-10 17:09:04,011 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.lexorank.collation.CollationIntegrityCheckerImpl]  Collation check performed against LexoRank table: OK
2025-09-10 17:09:04,332 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.ManagedFieldRestrictor] Configuration locked for field 等级 with true
2025-09-10 17:09:07,587 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.manager.version.VersionAdapterFactory] Begin JIRA start dates for versions migration
2025-09-10 17:09:09,517 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.manager.version.VersionAdapterFactory] Done migrating JIRA start dates for versions
2025-09-10 17:09:13,544 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [com.atlassian.greenhopper.Launcher] Registering dynamic modules
2025-09-10 17:09:14,526 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [service.lexorank.balance.************************] Initialising LexoRank Balancing Service
2025-09-10 17:09:14,530 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [service.lexorank.balance.************************] Scheduling clustered job, jobKey=LEXO_RANK_SCHEDULER_JOB, JobHandlerKey=com.atlassian.greenhopper.service.lexorank.balance.LexoRankBalancePluginJob
2025-09-10 17:09:16,068 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [service.lexorank.balance.************************] LexoRank Balancing Service is initialised, foregroundIndexRunning=false
2025-09-10 17:09:17,823 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.issuetypes.EpicIssueTypeProvider] Created Issue Type with ID '10000'
2025-09-10 17:09:18,740 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.issuetypes.StoryIssueTypeProvider] Created Issue Type with ID '10001'
2025-09-10 17:09:19,139 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [manager.issue.fields.StoryPointsCustomFieldProvider] Created Default field with ID '10106'
2025-09-10 17:09:19,670 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.issuelink.EpicLinkTypeServiceImpl] Epic-Story Link Type now exists with ID 10200
2025-09-10 17:09:19,910 http-nio-8080-exec-8 INFO anonymous 1028x246x1 10afwhz 172.18.0.1 /secure/SetupMailNotifications.jspa [greenhopper.service.logging.LogSupport] 

Managed Issue Types
	Epic                            id=10000                   name=Epic                            
	Story                           id=10001                   name=故事                              


Managed Custom Fields
	Epic Colour                     id=customfield_10103       name=Epic颜色                          type=com.pyxis.greenhopper.jira:gh-epic-color
	Epic Link                       id=customfield_10100       name=史诗链接                            type=com.pyxis.greenhopper.jira:gh-epic-link
	Epic Name                       id=customfield_10102       name=史诗名称                            type=com.pyxis.greenhopper.jira:gh-epic-label
	Epic Status                     id=customfield_10101       name=Epic状态                          type=com.pyxis.greenhopper.jira:gh-epic-status
	Default Global Rank             id=customfield_10105       name=等级                              type=com.pyxis.greenhopper.jira:gh-lexo-rank
	Sprint                          id=customfield_10104       name=Sprint                          type=com.pyxis.greenhopper.jira:gh-sprint
	Story Points                    id=customfield_10106       name=Story Point                     type=com.atlassian.jira.plugin.system.customfieldtypes:float


Managed Issue Link Types
	Epic Link Issue Link Type       id=10200                   name=Epic-Story Link                 

2025-09-10 17:09:21,377 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,378 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Starting upgrade task (buildNumber=46) : Sets status categories for (what is assumed to be) JIRA Agile's simple workflow's statuses.
2025-09-10 17:09:21,382 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask046] Finding statuses with no assigned status category semantics...
2025-09-10 17:09:21,432 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask046] Found 0 statuses with no status category
2025-09-10 17:09:21,476 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Upgrade task finished (buildNumber=46) : Sets status categories for (what is assumed to be) JIRA Agile's simple workflow's statuses.
2025-09-10 17:09:21,477 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,606 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,606 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Starting upgrade task (buildNumber=47) : Removing classic gadgets from dashboards
2025-09-10 17:09:21,676 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Upgrade task finished (buildNumber=47) : Removing classic gadgets from dashboards
2025-09-10 17:09:21,679 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,812 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,812 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Starting upgrade task (buildNumber=48) : Add reindex required message for Epic Label field searching
2025-09-10 17:09:21,857 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Upgrade task finished (buildNumber=48) : Add reindex required message for Epic Label field searching
2025-09-10 17:09:21,859 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,969 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:21,969 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Starting upgrade task (buildNumber=49) : Migrate existing Project Administer permission holders to also have Manage Sprints permission
2025-09-10 17:09:22,143 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Upgrade task finished (buildNumber=49) : Migrate existing Project Administer permission holders to also have Manage Sprints permission
2025-09-10 17:09:22,143 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:22,263 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:22,263 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Starting upgrade task (buildNumber=50) : Re-performs the migration for Manage Sprints permission due to the rollback as the consequence of HOT-35978
2025-09-10 17:09:22,263 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask050] Finding all schemes which currently have Manage Sprint entities
2025-09-10 17:09:22,267 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask050] Deleting 1 Manage Sprint entity(ies) from Default Permission Scheme
2025-09-10 17:09:22,313 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask050] Start copying all current ADMINISTER_PROJECT entities to MANAGE_SPRINT!
2025-09-10 17:09:22,449 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Upgrade task finished (buildNumber=50) : Re-performs the migration for Manage Sprints permission due to the rollback as the consequence of HOT-35978
2025-09-10 17:09:22,449 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:22,578 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:09:22,579 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Starting upgrade task (buildNumber=51) : Perform manage sprint permission migration for BTF customer only, since the 49th and 50th upgrade tasks, which were empty for BTF in order to allow customers downgrade from Cloud to BTF in the absence of a formal downgrade process
2025-09-10 17:09:22,579 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask051] Start the migration for Manage Sprints permission
2025-09-10 17:09:22,580 http-nio-8080-exec-8 INFO      [atlassian.greenhopper.upgrade.GhUpgradeTask051] Migration has finished!
2025-09-10 17:09:22,623 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] Upgrade task finished (buildNumber=51) : Perform manage sprint permission migration for BTF customer only, since the 49th and 50th upgrade tasks, which were empty for BTF in order to allow customers downgrade from Cloud to BTF in the absence of a formal downgrade process
2025-09-10 17:09:22,624 http-nio-8080-exec-8 INFO      [com.atlassian.greenhopper.upgrade] =========================================
2025-09-10 17:13:24,541 http-nio-8080-exec-2 INFO test 1033x369x1 10afwhz 172.18.0.1 /rest/project-templates/1.0/templates [manager.issue.fields.CustomFieldContextConfiguratorImpl] Options configuration created for field 'Epic状态'
2025-09-10 17:13:24,546 http-nio-8080-exec-2 INFO test 1033x369x1 10afwhz 172.18.0.1 /rest/project-templates/1.0/templates [atlassian.greenhopper.customfield.CustomFieldConfigurationRestorerImpl] Associated CustomField '史诗名称' with ID 'customfield_10102' with the project screens
2025-09-10 17:13:25,529 http-nio-8080-exec-2 INFO test 1033x369x1 10afwhz 172.18.0.1 /rest/project-templates/1.0/templates [atlassian.greenhopper.customfield.CustomFieldConfigurationRestorerImpl] Associated CustomField '史诗链接' with ID 'customfield_10100' with the project screens
