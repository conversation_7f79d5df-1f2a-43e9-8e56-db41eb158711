WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:project-page', location = '/sidebar/navigation/items.js' */
require(["jquery"],function(a){a(document).on("click",'.aui-nav \x3e [aria-expanded] \x3e a.aui-nav-item[href\x3d"#"]',function(b){b.preventDefault();AJS.navigation(a(this).siblings(".aui-nav")).toggle()})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.component.dialog2', location = 'aui.chunk.1c44bbbadb78d6728b28--0e47003a58d9c4b609b4.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.component.dialog2"],{"0+IH":function(t,i,e){"use strict";e.r(i),e.d(i,"dialog2",function(){return b});e("AehQ"),e("rWoL"),e("ZO0i");var a=e("+x/D"),n=e("TmQU"),u=e("KloK"),o=e("jEnx"),c=e("6RZY"),s=e("+ay7"),l=e("Pjwx"),h={"aui-focus":"false","aui-blanketed":"true"};function r(t){this._handlers=new WeakMap,this.$el=t?Object(a.a)(t):Object(a.a)('\n        <section role="dialog" class="aui-layer aui-dialog2 aui-dialog2-medium">\n            <header class="aui-dialog2-header">\n                <h2 class="aui-dialog2-header-main"></h2>\n                '.concat(l.a,'\n                </button>\n            </header>\n            <div class="aui-dialog2-content"></div>\n            <footer class="aui-dialog2-footer"></footer>\n        </section>')),function(t){a.a.each(h,function(i,e){var a="data-"+i;t[0].hasAttribute(a)||t.attr(a,e)})}(this.$el)}r.prototype.on=function(t,i){const e=this.$el;if(!this._handlers.get(i)){const a=function(t){t.target===e.get(0)&&i.apply(this,arguments)};Object(o.b)(e).on(t,a),this._handlers.set(i,a)}return this},r.prototype.off=function(t,i){const e=this.$el,a=this._handlers.get(i);return a&&(Object(o.b)(e).off(t,a),this._handlers.delete(i)),this},r.prototype.show=function(){return Object(o.b)(this.$el).show(),this},r.prototype.hide=function(){return Object(o.b)(this.$el).hide(),this},r.prototype.remove=function(){return Object(o.b)(this.$el).remove(),this},r.prototype.isVisible=function(){return Object(o.b)(this.$el).isVisible()};var d=Object(c.a)("dialog2",r),f=new Set;d.on=function(t,i){return f.has(i)||(o.b.on(t,".aui-dialog2",i),f.add(i)),this},d.off=function(t,i){return f.has(i)&&(o.b.off(t,".aui-dialog2",i),f.delete(i)),this},Object(a.a)(document).on("click keydown",".aui-dialog2-header ".concat(l.b),function(t){("click"===t.type||t.keyCode===s.a.ENTER||t.keyCode===s.a.SPACE)&&(t.preventDefault(),d(Object(a.a)(t.target).closest(".aui-dialog2")).hide())}),d.on("show",function(t,i){i.find(l.b).attr("tabindex",0),i.attr("tabindex",-1);var e=i[0].hasAttribute("data-aui-focus-selector")&&i.attr("data-aui-focus-selector");if(e){var a=i.find(e);if(a.length>0)return a.first().focus()}i.focus()}),d.on("hide",function(t,i){var e=Object(o.b)(i);i.data("aui-remove-on-hide")&&e.remove()}),Object(n.a)("aui/dialog2",d),Object(u.a)("dialog2",d);var b=d},ZO0i:function(t,i,e){}},[["0+IH","runtime","aui.splitchunk.vendors--894c8113d9","aui.splitchunk.0d131bcbf1","aui.splitchunk.fbbef27525","aui.splitchunk.444efc83be","aui.splitchunk.739b9ec8cc","aui.splitchunk.dd803a46b4","aui.splitchunk.994e478d48","aui.splitchunk.d7c46c2734","aui.splitchunk.e54c7c7304","aui.splitchunk.fb15cffa72","aui.splitchunk.f1e06f97a4","aui.splitchunk.479fe6ee76","aui.splitchunk.f673ef53ac","aui.splitchunk.8659b532c1","aui.splitchunk.5f851f97df","aui.splitchunk.d0110a864f","aui.splitchunk.afa5039e04","aui.splitchunk.bff3715233","aui.splitchunk.c750721820","aui.splitchunk.6d6f245ed3","aui.splitchunk.5b8c290363","aui.splitchunk.3d2cb1af14"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:change-project-type-dialog', location = '/includes/jira/dialog/changeProjectTypeDialog.soy' */
// This file was automatically generated from changeProjectTypeDialog.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.project.ChangeType.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.project == 'undefined') { JIRA.Templates.project = {}; }
if (typeof JIRA.Templates.project.ChangeType == 'undefined') { JIRA.Templates.project.ChangeType = {}; }


JIRA.Templates.project.ChangeType.changeProjectTypeDialog = function(opt_data, opt_ignored) {
  return '<section role="dialog" id="change-project-type-dialog-' + soy.$$escapeHtml(opt_data.projectId) + '" class="aui-layer aui-dialog2 aui-dialog2-medium" hidden><header class="aui-dialog2-header"><h2 class="aui-dialog2-header-main">' + soy.$$escapeHtml('\u66f4\u6539\u9879\u76ee\u7c7b\u578b') + '</h2></header><div class="aui-dialog2-content"></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions form-footer"><div class="icon throbber"></div><button class="aui-button aui-button-primary dialog-change-button hidden">' + soy.$$escapeHtml('\u66f4\u6539') + '</button><button class="aui-button aui-button-link dialog-close-button">' + soy.$$escapeHtml('\u53d6\u6d88') + '</button></div></footer></section>';
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.changeProjectTypeDialog.soyTemplateName = 'JIRA.Templates.project.ChangeType.changeProjectTypeDialog';
}


JIRA.Templates.project.ChangeType.changeProjectTypeForm = function(opt_data, opt_ignored) {
  var output = '<form class="aui change-project-type-form"><div class="form-body"><div class="aui-group project-type-change-group"><div class="aui-item">' + JIRA.Templates.project.ChangeType.projectAvatar(opt_data) + '</div><div class="aui-item project-type-select-group">' + JIRA.Templates.project.ChangeType.projectTypeDropdown({projectTypeKey: opt_data.project.projectTypeKey, projectTypes: opt_data.projectTypes}) + '</div></div></div></form><p>';
  var helpLinkAnchor__soy21 = '<a href=' + soy.$$escapeHtml(opt_data.helpLink) + ' target="_blank">';
  output += soy.$$filterNoAutoescape(AJS.format('\u5982\u679c\u60a8\u66f4\u6539\u9879\u76ee\u7c7b\u578b\uff0c\u60a8\u4e5f\u53ef\u66f4\u6539\u7528\u6237\u53ef\u4ee5\u770b\u5230\u4e1c\u897f\u5e76\u5f00\u5c55\u5de5\u7a0b\u3002{0}\u68c0\u67e5\u5206\u6b67\u5728\u8fd9\u91cc{1}',helpLinkAnchor__soy21,'</a>')) + '</p>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.changeProjectTypeForm.soyTemplateName = 'JIRA.Templates.project.ChangeType.changeProjectTypeForm';
}


JIRA.Templates.project.ChangeType.projectTypeDropdown = function(opt_data, opt_ignored) {
  var output = '<select class="project-type-select select" name="project-type">';
  var projectTypeList30 = opt_data.projectTypes;
  var projectTypeListLen30 = projectTypeList30.length;
  for (var projectTypeIndex30 = 0; projectTypeIndex30 < projectTypeListLen30; projectTypeIndex30++) {
    var projectTypeData30 = projectTypeList30[projectTypeIndex30];
    output += '<option class="imagebacked" data-icon="data:image/svg+xml;base64, ' + soy.$$escapeHtml(projectTypeData30.icon) + '" value="' + soy.$$escapeHtml(projectTypeData30.key) + '" ' + ((projectTypeData30.key == opt_data.projectTypeKey) ? ' selected ' : '') + '>' + soy.$$escapeHtml(projectTypeData30.formattedKey) + '</option>';
  }
  output += '</select>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.projectTypeDropdown.soyTemplateName = 'JIRA.Templates.project.ChangeType.projectTypeDropdown';
}


JIRA.Templates.project.ChangeType.updateTargetElement = function(opt_data, opt_ignored) {
  return '<img src="data:image/svg+xml;base64,' + soy.$$escapeHtml(opt_data.icon) + '" class="project-type-icon" /><span>' + soy.$$escapeHtml(opt_data.formattedKey) + '</span>';
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.updateTargetElement.soyTemplateName = 'JIRA.Templates.project.ChangeType.updateTargetElement';
}


JIRA.Templates.project.ChangeType.projectAvatar = function(opt_data, opt_ignored) {
  return '<div class="project-avatar-header"><span class="aui-avatar aui-avatar-large aui-avatar-project"><span class="aui-avatar-inner"><img src="' + soy.$$escapeHtml(opt_data.project.avatarUrls['48x48']) + '" alt="' + soy.$$escapeHtml(opt_data.project.name) + '"></span></span><div class="project-header" title="' + soy.$$escapeHtml(opt_data.project.name) + '">' + soy.$$escapeHtml(opt_data.project.name) + '</div></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.projectAvatar.soyTemplateName = 'JIRA.Templates.project.ChangeType.projectAvatar';
}


JIRA.Templates.project.ChangeType.dialogSpinner = function(opt_data, opt_ignored) {
  return '<div class="dialog-spinner"></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.dialogSpinner.soyTemplateName = 'JIRA.Templates.project.ChangeType.dialogSpinner';
}


JIRA.Templates.project.ChangeType.successMsg = function(opt_data, opt_ignored) {
  return '' + soy.$$escapeHtml(AJS.format('\u60a8\u5df2\u7ecf\u5c06 {0} \u53d8\u6210 {1} \u9879\u76ee\u7c7b\u578b\u3002',opt_data.projectName,opt_data.projectTypeName));
};
if (goog.DEBUG) {
  JIRA.Templates.project.ChangeType.successMsg.soyTemplateName = 'JIRA.Templates.project.ChangeType.successMsg';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:change-project-type-dialog', location = '/includes/jira/dialog/changeProjectTypeDialog.js' */
define("jira/project/admin/change-project-type-dialog",["jira/util/formatter","jquery","underscore","jira/analytics","jira/message","jira/ajs/select/single-select","wrm/context-path"],function(e,t,o,n,a,r,c){"use strict";function p(e){return t.ajax({url:c()+"/rest/internal/2/projects/"+e+"/changetypedata",dataType:"json",contentType:"application/json",type:"GET"})}function i(r){var p=t(".project-type-select",r.dialogBody),i=p.val()[0],s=o.findWhere(r.projectTypes,{key:i});t(".dialog-change-button",r.dialogBody).attr("disabled","disabled"),t(t.ajax({url:c()+"/rest/api/2/project/"+r.projectId+"/type/"+i,dataType:"json",contentType:"application/json",type:"PUT"}).done(function(){r.changeProjectTypeDialog.hide(),r.onProjectTypeChanged&&r.onProjectTypeChanged(r.trigger,s),a.showSuccessMsg(JIRA.Templates.project.ChangeType.successMsg({projectName:r.projectName,projectTypeName:s.formattedKey})),n.send({name:"administration.projecttype.change",properties:{projectId:r.projectId,sourceProjectType:d(r.sourceProjectType),destinationProjectType:d(i)}})}).fail(function(){t(".aui-dialog2-content",r.dialogBody).prepend(aui.message.error({content:e.format("\u6211\u4eec\u4e0d\u80fd\u80fd\u591f\u5b8c\u6210\u8be5\u9879\u76ee\u7684\u8f6c\u6362\u3002\u60a8\u53ef\u4ee5\u5237\u65b0\u8be5\u9875\u9762\u5e76\u91cd\u8bd5\u3002\u5982\u679c\u8fd9\u4e0d\u8d77\u4f5c\u7528, \u8bf7\u8054\u7cfb{0}\u652f\u6301{1}\u3002",'<a href="https://support.atlassian.com/">',"</a>")}))})).throbber({target:t(".throbber",r.dialogBody)})}function d(e){return e&&e.replace("_","")}function s(e,t,o){e==t?o.find(".dialog-change-button").attr("disabled","disabled"):o.find(".dialog-change-button").removeAttr("disabled")}function l(o){var n=t(JIRA.Templates.project.ChangeType.changeProjectTypeDialog({projectId:o.projectId})),a=AJS.dialog2(n);a.on("show",function(){t(".aui-dialog2-content",n).html(JIRA.Templates.project.ChangeType.dialogSpinner()),t(".dialog-spinner",n).spin(),t(".dialog-change-button",n).unbind("click").addClass("hidden")}),t(o.trigger).click(function(c){c.preventDefault(),a.show(),p(o.projectId).done(function(e){n.find(".aui-dialog2-content").html(JIRA.Templates.project.ChangeType.changeProjectTypeForm(e)),new r({element:t(".project-type-select",n),revertOnInvalid:!0,width:165}),n.find(".dialog-change-button").removeClass("hidden"),s(t(".project-type-select",n).val(),e.project.projectTypeKey,n);var c={dialogBody:n,changeProjectTypeDialog:a,projectName:e.project.name,projectTypes:e.projectTypes,trigger:o.trigger,projectId:o.projectId,onProjectTypeChanged:o.onProjectTypeChanged,sourceProjectType:e.project.projectTypeKey};t(".dialog-change-button",n).click(function(e){e.preventDefault(),i(c)}),t(".change-project-type-form",n).on("submit",function(e){e.preventDefault(),i(c)}),t(".project-type-select",n).on("change",function(o){s(t(this).val(),e.project.projectTypeKey,n)})}).fail(function(){t(".aui-dialog2-content",n).html(aui.message.error({content:e.format("\u6211\u4eec\u65e0\u6cd5\u52a0\u8f7d\u6240\u9700\u7684\u6570\u636e\u9879\u76ee\u7c7b\u578b\u7684\u66f4\u6539\u3002\u60a8\u53ef\u4ee5\u5237\u65b0\u8be5\u9875\u9762\u5e76\u91cd\u8bd5\u3002\u5982\u679c\u8fd9\u4e0d\u8d77\u4f5c\u7528, \u8bf7\u8054\u7cfb{0}\u652f\u6301{1}\u3002",'<a href="https://support.atlassian.com/">',"</a>")}))})}),t(".dialog-close-button",n).click(function(e){e.preventDefault(),a.hide()})}return function(e){l(e)}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:project-type-warning', location = '/static/projecttypes/warning/dialog/project-type-warning-dialog.js' */
define("jira/project/types/warning/dialog",["require"],function(e){"use strict";function t(t,a,r){var o=n(".project-type-warning-icon",r);i(o,"uninstalled-warning-dialog",function(i,r,o){return i.html(JIRA.Project.Types.Warning.dialog({title:t.title,firstParagraph:t.firstParagraph,secondParagraph:t.secondParagraph,callToActionText:t.callToActionText})),e("jira/project/admin/change-project-type-dialog")({trigger:n(".warning-dialog-change-project-type"),projectId:t.projectId,onProjectTypeChanged:a}),o(),!1},{width:375,gravity:"w"})}var n=e("jquery"),a=e("wrm/data"),i=e("aui/inline-dialog"),r=a.claim("project.type.warning.dialogs.data");return{init:function(e){e=e||{},e.sectionElement=e.sectionElement||n("body"),t(r,e.onProjectTypeChanged,e.sectionElement)}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:project-type-warning', location = '/static/projecttypes/warning/dialog/templates/templates.soy' */
// This file was automatically generated from templates.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Project.Types.Warning.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Project == 'undefined') { JIRA.Project = {}; }
if (typeof JIRA.Project.Types == 'undefined') { JIRA.Project.Types = {}; }
if (typeof JIRA.Project.Types.Warning == 'undefined') { JIRA.Project.Types.Warning = {}; }


JIRA.Project.Types.Warning.dialog = function(opt_data, opt_ignored) {
  return '<div class="project-type-warning-dialog"><p class="header"><span>' + soy.$$escapeHtml(opt_data.title) + '</span></p><p class="type-not-accessible-message">' + soy.$$escapeHtml(opt_data.firstParagraph) + '</p><p class="available-functionality-message">' + soy.$$escapeHtml(opt_data.secondParagraph) + '</p><div class="actions"><a class="warning-dialog-change-project-type" href="#">' + soy.$$escapeHtml(opt_data.callToActionText) + '</a></div></div>';
};
if (goog.DEBUG) {
  JIRA.Project.Types.Warning.dialog.soyTemplateName = 'JIRA.Project.Types.Warning.dialog';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-component', location = '/sidebar/component/navigation-item.js' */
define("jira/projects/sidebar/component/navigation-item",["jira/util/browser","jira/projects/libs/marionette"],function(b,c){return c.CompositeView.extend({ui:{link:"a.aui-nav-item"},events:{'simpleClick @ui.link:not([target\x3d"_blank"])':function(a){a.preventDefault();this.navigate()}},initialize:function(){this.id=this.$el.find("\x3ea").attr("data-link-id");this.bindUIElements()},onDestroy:function(){this.unbind()},getElement:function(){return this.el},navigate:function(){if(this.select()){if(this.triggerPreventable("before:navigate").isPrevented)return!1;
var a=this.ui.link.attr("href");a&&b.reloadViaWindowLocation(a);return!0}},select:function(){var a=this.triggerPreventable("before:select");if(a.isPrevented)return!1;this.$el.addClass("aui-nav-selected");this.trigger("select",a);return!0},deselect:function(){if(!this.isSelected())return!0;var a=this.triggerPreventable("before:deselect");if(a.isPrevented)return!1;this.$el.removeClass("aui-nav-selected");this.$el.find("a").blur();this.trigger("deselect",a);return!0},isSelected:function(){return this.$el.hasClass("aui-nav-selected")},
removeBadge:function(){this.$el.find(".aui-badge").remove()},getId:function(){return this.id},getSelectedNavigationItem:function(){if(this.isSelected())return this},hasASelectedItem:function(){return this.isSelected()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-component', location = '/sidebar/component/navigation-group.js' */
define("jira/projects/sidebar/component/navigation-group",["jira/projects/sidebar/component/navigation-group-factory","jira/projects/libs/marionette","jira/util/logger"],function(c,d,e){return d.CompositeView.extend({childEvents:{"before:select":function(a,b){this.retriggerPreventable("before:select",b);b.isPrevented||this.deselect()||b.preventDefault()},select:function(a,b){this.trigger("select",b)},"before:deselect":function(a,b){this.retriggerPreventable("before:deselect",b)},deselect:function(a,
b){this.trigger("deselect",b)},"before:navigate":function(a,b){this.retriggerPreventable("before:navigate",b)},"before:navigate:prevented":function(a,b){this.trigger("before:navigate:prevented",b)}},initialize:function(){this.id=this.$el.attr("data-id");this.$("\x3eul\x3eli").each(function(a,b){a=c.build(b);this.getItem(a.id)&&e.warn("Duplicated IDs detected. There are more than one NavigationItem with id data-link-id\x3d'"+a.id+"'");this.proxyChildEvents(a);this.children.add(a,a.id)}.bind(this))},
onDestroy:function(){this.unbind()},getElement:function(){return this.el},deselect:function(){if(this.triggerPreventable("before:deselect").isPrevented)return!1;var a=!0;this.children.each(function(b){a=b.deselect()&&a});return a},getItem:function(a){return this.children.findByCustom(a)},getItemAt:function(a){return this.children.findByIndex(a)},getSelectedNavigationItem:function(){var a=this.children.find(function(a){return a.hasASelectedItem()});if(a)return a.getSelectedNavigationItem()},hasASelectedItem:function(){return this.children.any(function(a){return a.hasASelectedItem()})}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-component', location = '/sidebar/component/navigation-subgroup.js' */
define("jira/projects/sidebar/component/navigation-subgroup",["jira/projects/sidebar/component/navigation-group","underscore"],function(b,c){return b.extend({childEvents:{"before:select":function(a,d){b.prototype.childEvents["before:select"].apply(this,arguments);d.isPrevented||this.expand()}},initialize:function(){this.childEvents=c.extend({},b.prototype.childEvents,this.childEvents);b.prototype.initialize.apply(this,arguments);this.id=this.$el.find("\x3ea[data-link-id]").attr("data-link-id")},expand:function(){this.$el.attr("aria-expanded",
"true")},collapse:function(){this.$el.attr("aria-expanded","false")},isExpanded:function(){return"true"===this.$el.attr("aria-expanded")},isSelected:function(){return this.$el.hasClass("aui-nav-selected")},getId:function(){return this.id},getSelectedNavigationItem:function(){if(this.isSelected())return this;var a=this.children.find(function(a){return a.hasASelectedItem()});if(a)return a.getSelectedNavigationItem()},hasASelectedItem:function(){return this.isSelected()?!0:this.children.any(function(a){return a.hasASelectedItem()})}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-component', location = '/sidebar/component/navigation-group-factory.js' */
define("jira/projects/sidebar/component/navigation-group-factory",["exports","jira/projects/sidebar/component/navigation-item","jira/projects/sidebar/component/navigation-subgroup","jquery"],function(b,c,d,e){b.build=function(a){return e(a).find("ul").length?new d({el:a}):new c({el:a})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-component', location = '/sidebar/component/component.js' */
define("jira/projects/sidebar/component","jira/projects/sidebar/component/navigation-group jira/projects/libs/marionette jira/util/logger wrm/data jquery underscore".split(" "),function(e,f,g,h,k,d){var l=!!h.claim("is-global-sidebar");return f.CompositeView.extend({childEvents:{"before:select":function(a,b){this.hasASelectedItem()||(b.isInitial=!0);this.retriggerPreventable("before:select",b);b.isPrevented||this.deselectAllGroups()},select:function(a,b){this.trigger("select",b)},"before:deselect":function(a,
b){this.retriggerPreventable("before:deselect",b)},deselect:function(a,b){this.trigger("deselect",b)},"before:navigate":function(a,b){this.retriggerPreventable("before:navigate",b)},"before:navigate:prevented":function(a,b){this.trigger("before:navigate:prevented",b)}},initialize:function(){this.render({force:!0})},render:function(a){a=d.defaults({},a,{force:!1});var b="el"in a,c=0<this.children.length;if(!b&&!0!==a.force||this.triggerPreventable("before:render").isPrevented)return this;if(c){if(this.triggerPreventable("before:detach").isPrevented)return this;
this.destroyChildren({checkEmpty:!1});this.trigger("detach")}b&&(a=k(a.el),this.$el.replaceWith(a),this.setElement(a));this.$(".aui-sidebar-group").each(d.bind(function(a,b){a=new e({el:b});this.getGroup(a.id)&&g.warn('Duplicated IDs detected. There are more than one NavigationGroup with id data-id\x3d"'+a.id+'"');this.proxyChildEvents(a);this.children.add(a,a.id)},this));this.trigger("render");return this},deselectAllGroups:function(){this.children.call("deselect")},replaceGroup:function(a,b){a=
this.getGroup(a);a.$el.replaceWith(b.$el);b.cid=a.cid;this.children.remove(a);this.children.add(b,b.id)},getGroup:function(a){return this.children.findByCustom(a)},getGroupAt:function(a){return this.children.findByIndex(a)},getItem:function(a){return this.getDefaultGroup().getItem(a)},getElement:function(){return this.el},getDefaultGroup:function(){return this.getGroup("sidebar-navigation-panel")},isProjectSidebar:function(){return!l},getSelectedScopeFilterId:function(){return this.$(".scope-filter a.scope-filter-trigger").attr("data-scope-filter-id")},
setReportsItemLink:function(a){var b=this.getGroup("sidebar-navigation-panel"),c=b.getItem("com.atlassian.jira.jira-projects-plugin:report-page");"undefined"===typeof c&&(c=b.getItem("com.pyxis.greenhopper.jira:global-sidebar-report"));c&&c.ui.link.attr("href",a)},getAUISidebar:function(){return AJS.sidebar(".aui-sidebar")},getContentContainer:function(){return this.$(".aui-sidebar-body .sidebar-content-container")},getSelectedNavigationItem:function(){return this.getDefaultGroup().getSelectedNavigationItem()},
hasASelectedItem:function(){return this.getDefaultGroup().hasASelectedItem()},dim:function(){this.$el.attr({dimmed:"","aria-hidden":"true"})},undim:function(){this.$el.removeAttr("dimmed");this.$el.removeAttr("aria-hidden")}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-component', location = '/sidebar/component/component-namespaces.js' */
(function(){var a=require("jira/projects/sidebar/component/navigation-group-factory"),b=require("jira/projects/sidebar/component");AJS.namespace("JIRA.Projects.Sidebar.Component",null,b);AJS.namespace("JIRA.Projects.Sidebar.Component.NavigationItem",null,require("jira/projects/sidebar/component/navigation-item"));AJS.namespace("JIRA.Projects.Sidebar.Component.NavigationSubgroup",null,require("jira/projects/sidebar/component/navigation-subgroup"));AJS.namespace("JIRA.Projects.Sidebar.Component.NavigationGroup",
null,require("jira/projects/sidebar/component/navigation-group"));AJS.namespace("JIRA.Projects.Sidebar.Component.NavigationGroup.build",null,a.build)})();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-analytics', location = '/sidebar/analytics/hash.js' */
define("jira/projects/sidebar/analytics/hash",["underscore"],function(e){function g(a,b){return 0===a.indexOf(b)}var d=[];return{prefixedHash:function(a){var b=e.find(d,e.partial(g,a))||"";a=a.substring(g(a,b)?b.length:0);var c=0;if(a){for(var f=0;f<a.length;f+=1){var h=a.charCodeAt(f);c=32*c-c+h;c|=0}a=c}else a="";return{prefix:b,hash:b+a}},addPrefix:function(a){a&&!e.contains(d,a)&&d.push(a)},clearPrefixes:function(){d.length=0}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-analytics', location = '/sidebar/analytics/analytics.js' */
define("jira/projects/sidebar/analytics/analytics",["jira/api/projects/sidebar","jira/projects/sidebar/analytics/hash","jira/util/data/meta","jira/analytics","jquery"],function(g,h,k,l,d){function e(a,c){l.send({name:"jira.project.centric.navigation.sidebar."+a,data:c})}function m(a){a=a.getAUISidebar();var c=d();c=c.add(a.$el);c=c.add(a.submenus.inlineDialog);c.on("click","a",function(){var b=d(this).attr("data-link-id");b=h.prefixedHash(b||"NONE");b={id:b.hash,prefix:b.prefix};var a=k.get("sidebar-source-page");
a?b.sidebarSourcePage=a:0<d(".issue-tools button.expand").length?b.sidebarSourcePage="search-el":0<d(".navigation-tools button.collapse").length?b.sidebarSourcePage="fullscreen-el":"atl.jira.proj.config"===d('meta[name\x3d"admin.active.section"]').attr("content")&&(a=d('meta[name\x3d"admin.active.tab"]').attr("content"),a=a.replace("com.atlassian.servicedesk.project-ui:",""),-1!==f.indexOf(["proj.config",a].join("."))?b.sidebarSourcePage=["proj.config",a].join("."):b.sidebarSourcePage="proj.config");
-1===f.indexOf(b.sidebarSourcePage)&&(b.sidebarSourcePage="unknown");e("click.link",b)})}var f="search-el fullscreen-el search fullscreen rapid-board service-desk proj.config proj.config.view_project_summary proj.config.view_project_issuetypes proj.config.project-issuetypes-expand proj.config.view_project_workflows proj.config.view_project_screens proj.config.view_project_fields proj.config.view_project_versions proj.config.view_project_components proj.config.view_project_roles proj.config.view_project_permissions proj.config.view_project_issuesecurity proj.config.view_project_notifications proj.config.devstatus-admin-page-link proj.config.hipchat-project-admin-page-link proj.config.view_issue_collectors proj.config.sd-project-request-types-item proj.config.sd-project-request-security-item proj.config.sd-project-portal-settings-item proj.config.sd-project-feedback-settings-item proj.config.sd-project-email-settings-item proj.config.sd-project-confluence-kb-item proj.config.sd-project-sidebar-sla proj.config.sd-project-automation-item".split(" ");
return{register:function(){g.getSidebar().done(function(a){a.isProjectSidebar()?e("load.project"):e("load.global");m(a)})}}});AJS.namespace("JIRA.Projects.Sidebar.Analytics",null,require("jira/projects/sidebar/analytics/analytics"));AJS.namespace("JIRA.Projects.Sidebar.Analytics.Hash",null,require("jira/projects/sidebar/analytics/hash"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/templates.soy' */
// This file was automatically generated from templates.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Projects.Sidebar.ScopeFilter.Templates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Projects == 'undefined') { JIRA.Projects = {}; }
if (typeof JIRA.Projects.Sidebar == 'undefined') { JIRA.Projects.Sidebar = {}; }
if (typeof JIRA.Projects.Sidebar.ScopeFilter == 'undefined') { JIRA.Projects.Sidebar.ScopeFilter = {}; }
if (typeof JIRA.Projects.Sidebar.ScopeFilter.Templates == 'undefined') { JIRA.Projects.Sidebar.ScopeFilter.Templates = {}; }


JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedContainer = function(opt_data, opt_ignored) {
  return '<div class="aui-sidebar-group aui-sidebar-group-actions collapsed-scope-filter-container"><!-- AUI sidebar does a if (!hasSubmenu) check while initializing submenu hence we need aui-nav element even if it is empty, so that our sidebar group gets properly initialized --><ul class="aui-nav"><!-- On first hover, while we are waiting for data, this content will be displayed in submenu inline dialog. Once we have data we will replace this with the actual data in DOM. Any further hover will clone the new DOM structure (actual data without progress indicator) and display in the submenu dialog. This is the only way I could find to pass a progress indicator to submenu inside dialog, because AUI does a destructive `submenuInlineDialog.innerHTML=` AFTER `sidebarSubmenuBeforeShow` event handler is executed, hence losing anything injected from it--><li><aui-spinner size="small" style="margin: auto"/></li></ul></div>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedContainer.soyTemplateName = 'JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedContainer';
}


JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedScopeList = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  var output = '';
  if (opt_data.selectableScopes || opt_data.createActions) {
    output += '<div class="aui-nav-heading" title="' + soy.$$escapeHtml('\u672c\u9879\u76ee\u4e2d\u7684\u9762\u677f') + '"><strong class="collapsed-scope-header">' + soy.$$escapeHtml('\u672c\u9879\u76ee\u4e2d\u7684\u9762\u677f') + '</strong></div><ul class="aui-nav collapsed-scope-list" title="' + soy.$$escapeHtml('\u9009\u62e9\u4e00\u4e2a\u9762\u677f') + '">';
    if (opt_data.selectedScope) {
      output += '<li class="scope-filter selected-scope-filter" title="' + soy.$$escapeHtml(opt_data.selectedScope.label) + '">' + soy.$$escapeHtml(opt_data.selectedScope.label) + '</li>';
      var scopeFilterList21 = opt_data.selectableScopes;
      var scopeFilterListLen21 = scopeFilterList21.length;
      for (var scopeFilterIndex21 = 0; scopeFilterIndex21 < scopeFilterListLen21; scopeFilterIndex21++) {
        var scopeFilterData21 = scopeFilterList21[scopeFilterIndex21];
        output += JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedSelectableItem({itemClass: 'scope-filter', anchorClass: scopeFilterData21.styleClass, label: scopeFilterData21.label, link: scopeFilterData21.link});
      }
    }
    if (opt_data.createActions && opt_data.createActions[0]) {
      output += '<hr/>';
      var createActionList31 = opt_data.createActions;
      var createActionListLen31 = createActionList31.length;
      for (var createActionIndex31 = 0; createActionIndex31 < createActionListLen31; createActionIndex31++) {
        var createActionData31 = createActionList31[createActionIndex31];
        output += JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedSelectableItem({itemClass: 'create-scope-action', anchorClass: createActionData31.styleClass, label: createActionData31.label, link: createActionData31.link});
      }
    }
    output += '</ul>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedScopeList.soyTemplateName = 'JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedScopeList';
}


JIRA.Projects.Sidebar.ScopeFilter.Templates.scopeListError = function(opt_data, opt_ignored) {
  return '<div class="scope-filter-error-message-container"><div class="scope-filter-error-message">' + soy.$$escapeHtml('\u6211\u4eec\u65e0\u6cd5\u68c0\u7d22\u516c\u544a\u677f\u5217\u8868') + '</div><div class="aui-button aui-button-compact">' + soy.$$escapeHtml('\u91cd\u8bd5') + '</div></div>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ScopeFilter.Templates.scopeListError.soyTemplateName = 'JIRA.Projects.Sidebar.ScopeFilter.Templates.scopeListError';
}


JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedSelectableItem = function(opt_data, opt_ignored) {
  return '<li class="' + soy.$$escapeHtml(opt_data.itemClass) + '"><a href="' + soy.$$escapeHtml(opt_data.link) + '" title="' + soy.$$escapeHtml(opt_data.label) + '"' + ((opt_data.anchorClass) ? ' class="' + soy.$$escapeHtml(opt_data.anchorClass) + '"' : '') + '>' + soy.$$escapeHtml(opt_data.label) + '</a></li>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedSelectableItem.soyTemplateName = 'JIRA.Projects.Sidebar.ScopeFilter.Templates.collapsedSelectableItem';
}


JIRA.Projects.Sidebar.ScopeFilter.Templates.renderScopefilterItems = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  var output = '';
  if (opt_data.scopeFilterItems.length > 0) {
    output += '<div class="aui-dropdown2-section"><strong>' + soy.$$escapeHtml('\u672c\u9879\u76ee\u4e2d\u7684\u9762\u677f') + '</strong><ul class="aui-list-truncate">';
    var itemList67 = opt_data.scopeFilterItems;
    var itemListLen67 = itemList67.length;
    for (var itemIndex67 = 0; itemIndex67 < itemListLen67; itemIndex67++) {
      var itemData67 = itemList67[itemIndex67];
      output += '<li><a href="' + soy.$$escapeHtml(itemData67.link) + '" title="' + soy.$$escapeHtml(itemData67.label) + '" data-scope-filter-id="' + soy.$$escapeHtml(itemData67.id) + '">' + soy.$$escapeHtml(itemData67.label) + '</a></li>';
    }
    output += '</ul></div>';
  } else if (! opt_data.canCreateScope) {
    output += '<div class="aui-dropdown2-section"><strong>' + soy.$$escapeHtml('\u672c\u9879\u76ee\u4e2d\u7684\u9762\u677f') + '</strong><div class="empty-boards-list">' + soy.$$escapeHtml('\u65e0\u516c\u544a\u677f') + '</div></div>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ScopeFilter.Templates.renderScopefilterItems.soyTemplateName = 'JIRA.Projects.Sidebar.ScopeFilter.Templates.renderScopefilterItems';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/templates.js' */
define("jira/projects/sidebar/scopefilter/templates",function(){return JIRA.Projects.Sidebar.ScopeFilter.Templates});AJS.namespace("JIRA.Projects.Sidebar.ScopeFilter.Templates",null,require("jira/projects/sidebar/scopefilter/templates"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/model.js' */
define("jira/projects/sidebar/scopefilter/model",["underscore","jira/api/projects"],function(c,d){var b=function(a){this.selectedScope=null;this.createScopeActions=[];this.selectableScopes=[];this.setData(a)};b.prototype.setData=function(a){a&&(a.selectedScope&&(this.selectedScope=a.selectedScope),a.createScopeActions.length&&(this.createScopeActions=a.createScopeActions),a.scopes.length&&(this.selectableScopes=a.selectedScope?c.reject(a.scopes,function(a){return a.label===this.selectedScope.label&&
a.link===this.selectedScope.link},this):a.scopes))};b.prototype.getSelectedScope=function(){return this.selectedScope};b.prototype.getSelectableScopes=function(){return this.selectableScopes};b.prototype.getCreateScopeActions=function(){return this.createScopeActions};b.prototype.shouldDisplayWhenSidebarIsCollapsed=function(){const a=d.getCurrentProjectType();return"service_desk"!==a&&"business"!==a};return b});AJS.namespace("JIRA.Projects.Sidebar.ScopeFilter.Model",null,require("jira/projects/sidebar/scopefilter/model"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/scopeFilterDropdownView.js' */
define("jira/projects/sidebar/scopefilter/scopefilter-view",["jquery","jira/projects/libs/marionette","jira/projects/sidebar/scopefilter/templates"],function(c,d,b){return d.ItemView.extend({template:b.renderScopefilterItems,events:{"aui-dropdown2-show":"show"},initialize:function(a){this.controller=a.controller;this.rendered=!1},show:function(){if(!this.rendered){var a=c('\x3caui-spinner size\x3d"small"/\x3e').css("margin","auto");this.$el.prepend(a);this.$el.addClass("data-load-in-progress");this.controller.fetchScopeFilterList().then(this.onFetchSuccess.bind(this)).fail(this.onFetchFail.bind(this)).always(function(){this.$el.addClass("data-load-finished").removeClass("data-load-in-progress");
a.remove()}.bind(this))}},onFetchSuccess:function(){this.$el.addClass("data-load-success");var a=this.model.getSelectableScopes(),b=!!this.model.getCreateScopeActions().length;a&&(a=this.template({scopeFilterItems:a,canCreateScope:b}),this.$el.prepend(a),this.rendered=!0)},onFetchFail:function(){this.$el.addClass("data-load-fail");var a=b.scopeListError();this.$el.prepend(a);this.rendered=!0;this.$el.find(".scope-filter-error-message-container .aui-button").on("click",this.onRetryClick.bind(this))},
onRetryClick:function(a){a.stopPropagation();a=this.$el.find(".scope-filter-error-message-container");a.length&&a.remove();this.rendered=!1;return this.show()}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/collapsed-view.js' */
define("jira/projects/sidebar/scopefilter/collapsed-view",["jquery","jira/projects/libs/marionette","jira/projects/sidebar/scopefilter/templates"],function(d,e,c){return e.ItemView.extend({template:c.collapsedContainer,ui:{trigger:".aui-sidebar-group-actions"},events:{"aui-sidebar-submenu-before-show @ui.trigger":"show"},initialize:function(a){this.controller=a.controller;this.rendered=!1},serializeData:function(){return{selectedScope:this.model.getSelectedScope(),selectableScopes:this.model.getSelectableScopes(),
createActions:this.model.getCreateScopeActions()}},show:function(a,b){this.rendered||(this.$el.addClass("data-load-in-progress"),this.controller.fetchScopeFilterList().then(this.onFetchSuccess.bind(this,b)).fail(this.onFetchFail.bind(this,b)).always(function(){this.$el.addClass("data-load-finished").removeClass("data-load-in-progress")}.bind(this)))},onFetchSuccess:function(a){this.$el.addClass("data-load-success");var b=c.collapsedScopeList(this.serializeData());this.renderPreparedTemplate(a,b);
this.rendered=!0},onFetchFail:function(a){this.$el.addClass("data-load-fail");var b=c.scopeListError();this.renderPreparedTemplate(a,b);d(a).find(".scope-filter-error-message-container .aui-button").click(this.onRetryClick.bind(this,a))},renderPreparedTemplate:function(a,b){this.ui.trigger.html(b);d(a).find(".aui-navgroup-inner").html(b)},onRetryClick:function(a){return this.show(null,a)}})});AJS.namespace("JIRA.Projects.Sidebar.ScopeFilter.CollapsedView",null,require("jira/projects/sidebar/scopefilter/collapsed-view"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/scopeFilterController.js' */
define("jira/projects/sidebar/scopefilter/controller","jquery wrm/require wrm/data jira/api/projects wrm/context-path jira/util/logger jira/projects/libs/marionette jira/projects/sidebar/scopefilter/model jira/projects/sidebar/scopefilter/scopefilter-view jira/projects/sidebar/scopefilter/collapsed-view".split(" "),function(b,m,c,d,e,f,g,h,k,l){return g.Controller.extend({initialize:function(a){this.sidebarAPI=a.sidebarAPI;this.sidebar=this.sidebarAPI.getAUISidebar();this.$sidebarContentContainer=
this.sidebarAPI.getContentContainer();this.sidebarAPI.isProjectSidebar()&&(a=c.claim("scope-filter-data"),this.scopeFilterModel=new h(a),this.dataFetched=!1,this.shouldRenderScopeFilterView=!this.scopeFilterModel.getSelectableScopes().length,this.sidebar.isCollapsed()?this.createCollapsedScopeFilterView():this.createScopeFilterView(),this.listenToSidebarEvents())},createCollapsedScopeFilterView:function(){this.scopeFilterModel.shouldDisplayWhenSidebarIsCollapsed()&&(this.collapsedView=new l({model:this.scopeFilterModel,
controller:this}),this.collapsedView.render(),this.$sidebarContentContainer.prepend(this.collapsedView.el),f.trace("sidebar.scopefilter.collapsed"))},createScopeFilterView:function(){this.shouldRenderScopeFilterView&&(this.scopeFilterDropdownView=new k({el:"#sidebar-scope-filter-list",model:this.scopeFilterModel,controller:this}))},fetchScopeFilterList:function(){var a=b.Deferred();if(this.dataFetched||this.scopeFilterModel.getSelectableScopes().length)return a.resolve();a=e()+"/rest/projects/1.0/project/"+
d.getCurrentProjectKey()+"/rapidviews";return b.ajax({contentType:"application/json",type:"GET",url:a}).done(function(a){this.dataFetched=!0;this.scopeFilterModel.setData(a)}.bind(this))},listenToSidebarEvents:function(){this.sidebar.on("collapse-start",function(){this.collapsedView||this.createCollapsedScopeFilterView()}.bind(this));this.sidebar.on("expand-end",function(){this.collapsedView&&(this.collapsedView.destroy(),this.collapsedView=null);this.scopeFilterDropdownView||this.createScopeFilterView()}.bind(this))}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:scope-filter', location = '/sidebar/scopefilter/scopeFilter-init.js' */
require(["jira/api/projects/sidebar","jira/projects/sidebar/scopefilter/controller"],function(a,b){a.getSidebar().done(function(a){new b({sidebarAPI:a})})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:feature-discovery', location = '/sidebar/feature-discovery/feature-discovery.js' */
define("jira/projects/sidebar/feature-discovery",["jira/api/projects","jira/api/projects/sidebar","wrm/context-path","underscore","jquery"],function(c,d,e,f,g){function h(b,a){f.each(a,function(a){(a=b.getItem(a))&&a.removeBadge()})}function k(b){d.getSidebar().done(function(a){h(a,b)})}return{updateBadges:function(){"undefined"!==typeof c.getCurrentProjectKey()&&g.ajax({url:e()+"/rest/projects/1.0/project/"+encodeURIComponent(c.getCurrentProjectKey())+"/badges",type:"PUT",contentType:"application/json"}).done(function(b){k(b.badgesToRemove||
[])})}}});AJS.namespace("JIRA.Projects.Sidebar.FeatureDiscovery",null,require("jira/projects/sidebar/feature-discovery"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-last-visited', location = '/sidebar/lastvisited/last-visited-updater.js' */
define("jira/projects/sidebar/lastvisited/updater",["jira/api/projects","jira/util/logger","wrm/context-path","jquery"],function(f,g,h,k){function d(a,c,b){a&&(b=b||function(){g.trace("last.visited.item.saved")},k.ajax({url:h()+"/rest/projects/1.0/project/"+encodeURIComponent(a)+"/lastVisited",type:"PUT",contentType:"application/json",data:JSON.stringify({id:c})}).done(b))}return{start:function(a,c){var b=f.getCurrentProjectKey(),e=a.getSelectedNavigationItem();e&&d(b,e.getId(),c);a.on("before:navigate:prevented",
function(a){d(b,a.emitter.id,c)});a.on("before:select",function(a){a.isInitial&&d(b,a.emitter.id,c)})}}});AJS.namespace("JIRA.Projects.Sidebar.LastVisited.Updater",null,require("jira/projects/sidebar/lastvisited/updater"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar', location = '/sidebar/sidebar-init.js' */
define("jira/projects/sidebar/sidebar-initializer",["require"],function(a){function e(a){a.id="sidebar"}function f(a){a.setAttribute("aria-label","Sidebar")}var h=a("jira/util/logger"),k=a("jira/api/projects"),g=a("jira/api/projects/sidebar"),l=a("jira/project/types/warning/dialog"),m=a("jira/projects/sidebar/lastvisited/updater"),n=a("jira/projects/sidebar/component"),p=a("jira/projects/sidebar/analytics/analytics"),q=a("jira/projects/sidebar/analytics/hash"),r=a("wrm/context-path"),
d=a("jquery"),t=a("underscore"),c=a("jira/util/formatter"),u="NONE com.atlassian.jira.jira-projects-plugin:project-issue-search-link com.atlassian.jira.jira-projects-plugin:components-page com.atlassian.jira.jira-projects-plugin:reports-panel com.atlassian.jira.jira-projects-plugin:summary-panel com.atlassian.jira.jira-projects-plugin:summary-page com.atlassian.jira.jira-projects-plugin:components-page com.atlassian.jira.jira-projects-plugin:release-page com.atlassian.jira.jira-projects-plugin:release-sidebar-version- com.atlassian.jira.jira-projects-plugin:report-page com.pyxis.greenhopper.jira:global-sidebar-report com.pyxis.greenhopper.jira:global-sidebar-plan-scrum com.pyxis.greenhopper.jira:global-sidebar-work-scrum com.pyxis.greenhopper.jira:global-sidebar-work-kanban com.pyxis.greenhopper.jira:project-sidebar-plan-scrum com.pyxis.greenhopper.jira:project-sidebar-work-scrum com.pyxis.greenhopper.jira:project-sidebar-work-kanban com.atlassian.jira.jira-************************:embedded-issue-navigator-link com.atlassian.jira.jira-************************:sidebar-issue-navigator".split(" ");
return{init:function(a){function c(a){function b(){a.getAUISidebar().reflow()}setTimeout(b,200);setInterval(b,3E5)}g.initAPI(new n({el:a.$el[0]}));t(u).each(q.addPrefix);p.register();(function(a){a.on("collapse-start",function(){var b=a.$el.find(".scope-filter-trigger");b.hasClass("aui-dropdown2-active")&&b.trigger("aui-button-invoke")})})(a);(function(a){var b=d(".aui-sidebar .aui-badge");b.tooltip({gravity:"w"});a.isCollapsed()&&b.tooltip("disable");a.on("collapse-end expand-end",function(){d(".tipsy").remove();
d(".aui-sidebar .aui-badge").tooltip("toggleEnabled")})})(a);(function(){l.init({onProjectTypeChanged:function(){var a=k.getCurrentProjectKey();window.location.replace(r()+"/projects/"+a+"/summary")}});h.trace("project.types.warning.messages.init")})();g.getSidebar().done(function(a){e(a.el);f(a.el);c(a);m.start(a)})},setSidebarId:e,setSidebarAriaLabel:f}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/templates.soy' */
// This file was automatically generated from templates.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Projects.Sidebar.ProjectShortcuts.Templates.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Projects == 'undefined') { JIRA.Projects = {}; }
if (typeof JIRA.Projects.Sidebar == 'undefined') { JIRA.Projects.Sidebar = {}; }
if (typeof JIRA.Projects.Sidebar.ProjectShortcuts == 'undefined') { JIRA.Projects.Sidebar.ProjectShortcuts = {}; }
if (typeof JIRA.Projects.Sidebar.ProjectShortcuts.Templates == 'undefined') { JIRA.Projects.Sidebar.ProjectShortcuts.Templates = {}; }


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.content = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.canManage || opt_data.numberOfShortcuts > 0) {
    output += '<div id="project-shortcuts-list-align-container" class="aui-sidebar-group jira-sidebar-group-with-divider project-shortcuts-group' + ((opt_data.numberOfShortcuts == 0) ? ' project-shortcuts-group_empty' : '') + '" data-id="project-shortcuts-group"><span class="aui-icon aui-icon-small aui-iconfont-link">' + soy.$$escapeHtml('\u9879\u76ee\u94fe\u63a5') + '</span><div class="aui-nav-heading">' + soy.$$escapeHtml('\u9879\u76ee\u94fe\u63a5') + '</div>' + ((opt_data.canManage) ? '<p class="project-shortcuts-group__description' + ((opt_data.numberOfShortcuts > 0) ? ' hidden' : '') + '">' + soy.$$escapeHtml('\u6dfb\u52a0\u4e00\u4e2a\u6709\u7528\u7684\u94fe\u63a5\u4fe1\u606f\u7ed9\u60a8\u7684\u56e2\u961f\u3002') + '</p>' : '') + '<ul class="aui-nav project-shortcuts-list">';
    if (opt_data.shortcuts) {
      var shortcutList26 = opt_data.shortcuts;
      var shortcutListLen26 = shortcutList26.length;
      for (var shortcutIndex26 = 0; shortcutIndex26 < shortcutListLen26; shortcutIndex26++) {
        var shortcutData26 = shortcutList26[shortcutIndex26];
        output += JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcut({id: shortcutData26.id, name: shortcutData26.name, url: shortcutData26.url, icon: shortcutData26.icon, canManage: opt_data.canManage, iconsMap: opt_data.iconsMap, isWithIcon: opt_data.isWithIcons});
      }
    }
    output += ((opt_data.canManage) ? '<li><a class="aui-nav-item project-shortcuts-group__add" href="#" data-link-id="project-shortcut-add"><span class="aui-icon aui-icon-large aui-iconfont-add-small"></span><span class="aui-nav-item-label">' + soy.$$escapeHtml('\u65b0\u589e\u94fe\u63a5') + '</span></a></li>' : '') + '</ul></div>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.content.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.content';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcut = function(opt_data, opt_ignored) {
  return '<li class="project-shortcut"><a class="aui-nav-item project-shortcuts-group__link" href="' + soy.$$escapeHtml(opt_data.url) + '" title="' + soy.$$escapeHtml(opt_data.name) + '" target="_blank" rel="nofollow noopener noreferrer" data-shortcut-id="' + soy.$$escapeHtml(opt_data.id) + '" data-link-id="project-shortcut-' + soy.$$escapeHtml(opt_data.id) + '">' + ((opt_data.isWithIcon) ? '<span class="aui-icon aui-icon-large ' + JIRA.Projects.Sidebar.ProjectShortcuts.Templates.icon({iconId: opt_data.icon, iconsMap: opt_data.iconsMap}) + '" data-project-shortcuts-icon-id="' + soy.$$escapeHtml(opt_data.icon) + '">' + soy.$$escapeHtml('\u9879\u76ee\u7684\u5feb\u6377\u65b9\u5f0f\u56fe\u6807\u3002') + '</span>' : '') + '<span class="aui-nav-item-label">' + soy.$$escapeHtml(opt_data.name) + '</span></a>' + ((opt_data.canManage) ? '<button class="aui-button aui-button-subtle aui-dropdown2-trigger aui-dropdown2-trigger-arrowless project-shortcuts-group__actions" aria-owns="project-shortcuts-dropdown_' + soy.$$escapeHtml(opt_data.id) + '" aria-controls="project-shortcuts-dropdown_' + soy.$$escapeHtml(opt_data.id) + '" aria-haspopup="true" data-aui-alignment-container="#project-shortcuts-list-align-container"><span class="aui-icon aui-icon-small aui-iconfont-more">' + soy.$$escapeHtml('\u7b5b\u9009\u5668\u64cd\u4f5c') + '</span></button><div id="project-shortcuts-dropdown_' + soy.$$escapeHtml(opt_data.id) + '" class="aui-dropdown2 aui-style-default project-shortcuts-group__dropdown"><ul class="aui-list-truncate"><li><a class="project-shortcuts-group__actions__edit" href="#">' + soy.$$escapeHtml('\u7f16\u8f91') + '</a></li><li><a class="project-shortcuts-group__actions__delete  " href="#">' + soy.$$escapeHtml('\u5220\u9664') + '</a></li></ul></div>' : '') + '</li>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcut.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcut';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.icon = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  var output = '';
  if (opt_data.iconsMap) {
    var id__soy85 = opt_data.iconId && opt_data.iconsMap[opt_data.iconId] ? opt_data.iconId : '1';
    output += (opt_data.iconsMap[id__soy85]) ? soy.$$escapeHtml(opt_data.iconsMap[id__soy85].className) : '';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.icon.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.icon';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcutFormFields = function(opt_data, opt_ignored) {
  return '<div class="project-shortcuts-field-group">' + aui.form.textField({name: 'project-shortcuts-url-' + opt_data.action, isRequired: true, id: 'project-shortcuts-url-' + opt_data.action, labelContent: '\u7f51\u9875\u5730\u5740', placeholderText: '\u4f8b\u5982 http://www.atlassian.com', extraClasses: 'project-shortcuts-url', value: opt_data.url ? opt_data.url : '', errorTexts: opt_data.errors.urlError ? [opt_data.errors.urlError] : []}) + '<div class="field-group project-shortcuts-name">' + aui.form.label({isRequired: true, forField: 'project-shortcuts-name-' + opt_data.action, content: '\u6807\u7b7e'}) + '<div class="project-shortcuts-name-icon-block"><div class="project-shortcuts-icon-picker-block"></div>' + aui.form.input({name: 'project-shortcuts-name-' + opt_data.action, id: 'project-shortcuts-name-' + opt_data.action, placeholderText: '\u4f8b\u5982 Atlassian \u7684\u7f51\u7ad9', extraClasses: 'project-shortcuts-name-input' + (opt_data.isWithIcon ? ' project-shortcuts-name-input-with-icon' : ''), value: opt_data.name ? opt_data.name : '', type: 'text'}) + '</div>' + ((opt_data.errors.iconError) ? aui.form.fieldError({message: opt_data.errors.iconError}) : '') + ((opt_data.errors.nameError) ? aui.form.fieldError({message: opt_data.errors.nameError}) : '') + '</div></div>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcutFormFields.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcutFormFields';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsPicker = function(opt_data, opt_ignored) {
  return '<span>' + JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsList(opt_data) + '</span>' + aui.dropdown2.trigger({menu: {id: 'project-shortcuts-icons-list-' + opt_data.cid}, extraClasses: 'aui-button project-shortcuts-icons-picker', iconClasses: 'aui-icon aui-icon-large  ' + soy.$$escapeHtml(opt_data.icon.className), extraAttributes: {href: '#'}});
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsPicker.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsPicker';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsList = function(opt_data, opt_ignored) {
  var output = '<div id="project-shortcuts-icons-list-' + soy.$$escapeHtml(opt_data.cid) + '" hidden class="aui-style-default aui-dropdown2 project-shortcuts-icons-list aui-dropdown2-section"><ul>';
  var iconList140 = opt_data.iconsList;
  var iconListLen140 = iconList140.length;
  for (var iconIndex140 = 0; iconIndex140 < iconListLen140; iconIndex140++) {
    var iconData140 = iconList140[iconIndex140];
    output += '<li><a class="project-shortcuts-icons-icon" data-project-shortcuts-icons-id="' + soy.$$escapeHtml(iconData140.name) + '"><span class="aui-icon aui-icon-large ' + soy.$$escapeHtml(iconData140.className) + '">' + soy.$$escapeHtml('\u9879\u76ee\u7684\u5feb\u6377\u65b9\u5f0f\u56fe\u6807\u3002') + '</span></a></li>';
  }
  output += '</ul></div>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsList.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.iconsList';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.addDialog = function(opt_data, opt_ignored) {
  return '<form action="" method="post" class="aui"><h3>' + soy.$$escapeHtml('\u65b0\u589e\u94fe\u63a5') + '</h3><fieldset>' + JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcutFormFields(soy.$$augmentMap(opt_data, {action: 'add'})) + '<div class="buttons-container"><div class="buttons"><button class="aui-button project-shortcuts-submit aui-button-primary">' + soy.$$escapeHtml('\u6dfb\u52a0') + '</button><button class="aui-button aui-button-link project-shortcuts-cancel">' + soy.$$escapeHtml('\u53d6\u6d88') + '</button></div></div></fieldset></form>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.addDialog.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.addDialog';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.editDialogChrome = function(opt_data, opt_ignored) {
  return '<section role="dialog" id="edit-project-shortcut-dialog" class="aui-layer aui-dialog2 aui-dialog2-small" hidden data-aui-remove-on-hide="true"><header class="aui-dialog2-header"><h2 class="aui-dialog2-header-main">' + soy.$$escapeHtml('\u7f16\u8f91\u94fe\u63a5') + '</h2></header></section>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.editDialogChrome.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.editDialogChrome';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.editDialog = function(opt_data, opt_ignored) {
  return '<div class="aui-dialog2-content"><form class="aui" method="post">' + JIRA.Projects.Sidebar.ProjectShortcuts.Templates.shortcutFormFields(soy.$$augmentMap(opt_data, {action: 'edit'})) + '<button type="submit" class="project-shortcuts-hidden-submit"></button></form></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><button class="aui-button aui-button-primary project-shortcuts-submit">' + soy.$$escapeHtml('\u4fdd\u5b58') + '</button><button class="aui-button aui-button-link project-shortcuts-cancel">' + soy.$$escapeHtml('\u53d6\u6d88') + '</button></div></footer>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.editDialog.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.editDialog';
}


JIRA.Projects.Sidebar.ProjectShortcuts.Templates.deleteDialog = function(opt_data, opt_ignored) {
  return '<section role="dialog" id="delete-project-shortcut-dialog" class="aui-layer aui-dialog2 aui-dialog2-small" hidden data-aui-remove-on-hide="true"><header class="aui-dialog2-header"><h2 class="aui-dialog2-header-main">' + soy.$$escapeHtml('\u79fb\u9664\u94fe\u63a5') + '</h2></header><div class="aui-dialog2-content"><p>' + soy.$$escapeHtml('\u786e\u5b9e\u8981\u5220\u9664\u8fd9\u4e2a\u5feb\u6377\u94fe\u63a5?') + '</p></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><button class="aui-button aui-button-primary project-shortcuts-submit">' + soy.$$escapeHtml('\u5220\u9664') + '</button><button class="aui-button aui-button-link project-shortcuts-cancel">' + soy.$$escapeHtml('\u53d6\u6d88') + '</button></div></footer></section>';
};
if (goog.DEBUG) {
  JIRA.Projects.Sidebar.ProjectShortcuts.Templates.deleteDialog.soyTemplateName = 'JIRA.Projects.Sidebar.ProjectShortcuts.Templates.deleteDialog';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/templates.js' */
define("jira/projects/sidebar/project-shortcuts/templates",function(){return JIRA.Projects.Sidebar.ProjectShortcuts.Templates});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Templates",null,require("jira/projects/sidebar/project-shortcuts/templates"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/project-shortcuts-analytics.js' */
define("jira/projects/sidebar/project-shortcuts/analytics",["jira/ajs/dark-features","jira/analytics","jquery"],function(h,d,g){var e=h.isEnabled("com.atlassian.jira.projects.************************.ProjectShortcutIcons");return{initialize:function(a){this.projectId=a},initShortcutClick:function(a,b){var k=b.getAUISidebar(),c=g(a),f=this;c.on("click","a.project-shortcuts-group__link",function(a){a=c.find("a.project-shortcuts-group__link");var b=g(this);d.send({name:"jira.projects.shortcut.clicked",
data:{shortcutId:b.data("shortcutId"),shortcutPosition:b.closest("li").index()+1,shortcutIconName:e?b.find(".aui-icon").data("projectShortcutsIconId"):"",shortcutCount:a.size(),isWithIcons:e,isSidebarCollapsed:k.isCollapsed(),projectId:f.projectId}})})},initDialogActions:function(a){var b=this;a.on("childview:edit:open",function(a,c){a=c.collection.indexOf(c)+1;d.send({name:"jira.projects.shortcut.edit.dialog.opened",data:{isWithIcons:e,shortcutId:c.get("id"),shortcutPosition:a,shortcutCount:c.collection.size(),
projectId:b.projectId}})});a.on("childview:edit:close",function(a,c,f){a=c.collection.indexOf(c)+1;d.send({name:"jira.projects.shortcut.edit.dialog.closed",data:{isWithIcons:e,isSave:f,shortcutId:c.get("id"),shortcutPosition:a,shortcutCount:c.collection.size(),projectId:b.projectId}})});a.on("add:open",function(){d.send({name:"jira.projects.shortcut.add.dialog.opened",data:{isWithIcons:e,projectId:b.projectId}})});a.on("add:close",function(a){d.send({name:"jira.projects.shortcut.add.dialog.closed",
data:{isWithIcons:e,isSave:a,projectId:b.projectId}})})},iconChanged:function(a,b,e){d.send({name:"jira.projects.shortcut.icon.changed",data:{iconName:b,oldIconName:e,shortcutId:a.isNew()?"":a.get("id"),isNew:a.isNew(),cid:a.cid,projectId:this.projectId}})},iconChangeConfirmed:function(a,b){d.send({name:"jira.projects.shortcut.icon.confirmed",data:{oldIconName:a.get("icon"),iconName:b,shortcutId:a.isNew()?"":a.get("id"),isNew:a.isNew(),cid:a.cid,projectId:this.projectId}})},iconPickerOpened:function(a){d.send({name:"jira.projects.shortcut.icon.picker.opened",
data:{shortcutId:a.isNew()?"":a.get("id"),isNew:a.isNew(),cid:a.cid,projectId:this.projectId}})},iconPickerClosed:function(a,b){d.send({name:"jira.projects.shortcut.icon.picker.closed",data:{shortcutId:a.isNew()?"":a.get("id"),isNew:a.isNew(),cid:a.cid,isSave:b,projectId:this.projectId}})}}});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Analytics",null,require("jira/projects/sidebar/project-shortcuts/analytics"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/services/AvailableIcons.js' */
define("jira/projects/sidebar/project-shortcuts/services/available-icons",["underscore"],function(d){var b=[],c={},e=!1;return{initialize:function(a){b=a||b;c={};d.each(a,function(a){c[a.name]=a})},getIconsList:function(){return b},getIconsMap:function(){return c},getAllIconsClasses:function(){return d.reduce(b,function(a,b){return a+b.className+" "},"")},getIconFromName:function(a){return this.getIconsMap()[a]?this.getIconsMap()[a]:this.getIconsList()[0]?this.getIconsList()[0]:{}},setWithIcons:function(a){e=
a},isWithIcons:function(){return e}}});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Services.AvailableIcons",null,require("jira/projects/sidebar/project-shortcuts/services/available-icons"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/entities/Shortcut.js' */
define("jira/projects/sidebar/project-shortcuts/entities/shortcut",["jira/util/formatter","jira/util/logger","jira-projects-backbone","wrm/context-path"],function(g,d,e,h){function f(b){try{var a=JSON.parse(b.responseText)}catch(c){a={message:"\u6211\u4eec\u4e0d\u80fd\u5b8c\u6210\u8be5\u64cd\u4f5c\uff0c\u56e0\u4e3a\u4f3c\u4e4e\u6709\u4e00\u4e2a\u6c9f\u901a\u7684\u95ee\u9898\u3002"}}return a}return e.Model.extend({defaults:{url:"",name:"",icon:""},initialize:function(b,a){if(!a||!a.projectKey)throw"Project key is required";this.projectKey=a.projectKey},urlRoot:function(){return h()+
"/rest/projects/1.0/project/"+encodeURIComponent(this.projectKey)+"/shortcut"},clear:function(){this.unset("id");this.set("url",this.defaults.url);this.set("name",this.defaults.name);this.set("icon",this.defaults.icon)},save:function(){if(1!=this.saving){this.saving=!0;var b=this;b.trigger("save:start");var a=this.isNew()?"create":"update";this.sync(a,b).always(function(){b.saving=!1;b.trigger("save:finish")}).done(function(c){b.set(c,{silent:!0});d.trace("jira.projects.shortcuts."+a+".success");
b.trigger("save:success")}).fail(function(c){c=f(c);d.trace("jira.projects.shortcuts."+a+".fail");b.trigger("save:failure",c)})}},destroy:function(){if(1!=this.saving){this.saving=!0;var b=this;b.trigger("remove:start");var a=e.Model.prototype.destroy.apply(this,arguments);a.always(function(){b.saving=!1;b.trigger("remove:finish")}).done(function(){b.trigger("remove:success")}).fail(function(a){a=f(a);b.trigger("remove:failure",a)});return a}}})});
AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Entities.Shortcut",null,require("jira/projects/sidebar/project-shortcuts/entities/shortcut"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/entities/ShortcutErrors.js' */
define("jira/projects/sidebar/project-shortcuts/entities/shortcut-errors",["jira-projects-backbone"],function(b){return b.Model.extend({defaults:{urlError:"",nameError:"",iconError:"",generalError:""},initialize:function(c,b){c=b.model;this.listenTo(c,"save:failure remove:failure",function(a){this.set({urlError:a.errors&&a.errors.url,nameError:a.errors&&a.errors.name,iconError:a.errors&&a.errors.icon,generalError:a.message||a.errorMessages&&0<a.errorMessages.length?a.message||a.errorMessages[0]:void 0})});
this.listenTo(c,"save:success remove:success",this.clear)}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Entities.ShortcutErrors",null,require("jira/projects/sidebar/project-shortcuts/entities/shortcut-errors"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/entities/Shortcuts.js' */
define("jira/projects/sidebar/project-shortcuts/entities/shortcuts",["jira/projects/sidebar/project-shortcuts/entities/shortcut","jira-projects-backbone"],function(b,a){return a.Collection.extend({model:b,initialize:function(b,a){this.projectKey=a.projectKey}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Entities.Shortcuts",null,require("jira/projects/sidebar/project-shortcuts/entities/shortcuts"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/views/IconPickerContent.js' */
define("jira/projects/sidebar/project-shortcuts/views/icon-picker-content",["jira/projects/sidebar/project-shortcuts/templates","jira/projects/sidebar/project-shortcuts/analytics","jira/projects/sidebar/project-shortcuts/services/available-icons","jira/projects/libs/marionette","jquery"],function(f,g,h,k,e){return k.ItemView.extend({template:f.iconsPicker,ui:{icon:".project-shortcuts-icons-icon",iconList:".project-shortcuts-icons-list",iconPicker:".project-shortcuts-icons-picker"},modelEvents:{"change:icon":function(){var a=
this._modelIcon(),b=this.ui.iconPicker.children();b.removeClass(this.iconFactory.getAllIconsClasses());b.addClass(a.className);this.ui.iconPicker.data("projectShortcutsIconsId",a.name)},"save:start":function(){this.ui.iconPicker.attr("aria-disabled",!0)},"save:finish":function(){this.ui.iconPicker.attr("aria-disabled",!1)}},events:{"keydown @ui.iconPicker":function(a){if(this.isPickerActive())switch(a.keyCode){case AJS.keyCode.LEFT:case AJS.keyCode.RIGHT:case AJS.keyCode.DOWN:case AJS.keyCode.UP:var b=
this.ui.iconList.find(".active"),d=this.ui.iconList.find("li").size();switch(a.keyCode){case AJS.keyCode.LEFT:var c=-1;break;case AJS.keyCode.RIGHT:c=1;break;case AJS.keyCode.DOWN:c=5;break;case AJS.keyCode.UP:c=-5;break;default:c=0}c=b.closest("li").index()+c;if(0>c||c>=d)c=(c+d)%d;b.removeClass("active aui-dropdown2-active");this.ui.icon.eq(c).addClass("active aui-dropdown2-active");a.stopPropagation();a.preventDefault();this.ui.iconList.trigger("aui-dropdown2-item-selected");break;case AJS.keyCode.ESCAPE:this.hideIconPicker(),
a.stopPropagation(),a.preventDefault()}}},initialize:function(a){this.iconFactory=h;this.analytics=g},onRender:function(){var a=this;this.ui.icon.on("click",function(){var b=e(this).data("projectShortcutsIconsId");a.analytics.iconChangeConfirmed(a.model,b);a.analyticsOldIconId="";a.analyticsIconClicked=!0;a.model.set("icon",b);a.ui.iconPicker.focus()});this.ui.iconList.on("aui-dropdown2-show",function(){a.ui.iconPicker.focus();a.analyticsIconClicked=!1;a.analytics.iconPickerOpened(a.model)});this.ui.iconList.on("aui-dropdown2-hide",
function(){a.analytics.iconPickerClosed(a.model,a.analyticsIconClicked)});this.analyticsOldIconId="";this.ui.iconList.on("aui-dropdown2-item-selected",function(b){b=e(this).find(".active").data("projectShortcutsIconsId");a.analyticsOldIconId&&a.analyticsOldIconId!==b&&a.analytics.iconChanged(a.model,b,a.analyticsOldIconId);a.analyticsOldIconId=b});this.ui.iconPicker.data("projectShortcutsIconsId",this.model.get("icon"))},hideIconPicker:function(){this.isPickerActive()&&this.ui.iconPicker.trigger("aui-button-invoke")},
isPickerActive:function(){return this.ui.iconPicker.hasClass("active")},_modelIcon:function(){return this.iconFactory.getIconFromName(this.model.get("icon"))},onFormSubmit:function(){this.model.set("icon",this.ui.iconPicker.data("projectShortcutsIconsId")||"")},focus:function(){this.ui.iconPicker.focus()},serializeData:function(){return{iconsList:this.iconFactory.getIconsList(),icon:this._modelIcon(),cid:this.cid}}})});
AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Views.IconPickerContent",null,require("jira/projects/sidebar/project-shortcuts/views/icon-picker-content"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/views/DialogContent.js' */
define("jira/projects/sidebar/project-shortcuts/views/dialog-content","jira/util/formatter jira/flag jira/projects/sidebar/project-shortcuts/views/icon-picker-content jira/projects/sidebar/project-shortcuts/services/available-icons jira/projects/libs/marionette underscore".split(" "),function(d,e,f,g,h,k){function c(a){a.preventDefault();this.ensureProtocolPrefix();this.tryToAutomagicallyDeriveNameFromUrl();if(this.iconPickerContent)this.iconPickerContent.onFormSubmit();this.model.set("url",this.ui.url.val());
this.model.set("name",this.ui.name.val());this.model.save()}var b=h.LayoutView.extend({ui:{form:"form",inputs:"input, button",submit:".project-shortcuts-submit",cancel:".project-shortcuts-cancel",url:".project-shortcuts-url input",name:".project-shortcuts-name input"},regions:{iconPicker:".project-shortcuts-icon-picker-block"},events:{"click @ui.cancel":function(a){a.preventDefault();this.model.clear();this.setNameAutomagically=!0;this.errorModel.clear();this.trigger("cancel")},"click @ui.submit":c,
"submit @ui.form":c,"blur @ui.url":function(){this.ensureProtocolPrefix();this.tryToAutomagicallyDeriveNameFromUrl()},"input @ui.url":function(){this.model.set("url",this.ui.url.val());this.tryToAutomagicallyDeriveNameFromUrl()},"input @ui.name":function(){this.setNameAutomagically=!1;this.model.set("name",this.ui.name.val())},"keydown @ui.name":function(a){this.iconPickerContent&&a.shiftKey&&a.keyCode===AJS.keyCode.TAB&&(a.preventDefault(),this.iconPickerContent.focus())},"keydown @ui.url":function(a){this.iconPickerContent&&
!a.shiftKey&&a.keyCode===AJS.keyCode.TAB&&(a.preventDefault(),this.iconPickerContent.focus())}},modelEvents:{"save:start":function(){this.ui.inputs.prop("disabled",!0);this.ui.submit.addClass("loading");this.ui.submit[0].busy()},"save:finish":function(){this.ui.inputs.prop("disabled",!1);this.ui.submit.removeClass("loading");this.ui.submit[0].idle()},"save:failure":function(){this.render();this.errorModel.get("generalError")&&e({type:"error",title:"\u4fdd\u5b58\u94fe\u63a5\u5931\u8d25\u3002",
close:"auto",body:this.errorModel.get("generalError")});this.errorModel.get("nameError")&&this.ui.name.focus();this.errorModel.get("urlError")&&this.ui.url.focus();return this},"save:success":function(){this.setNameAutomagically=!0}},initialize:function(a){this.errorModel=a.errorModel;this.setNameAutomagically=0==this.model.get("name").length;this.iconFactory=g},serializeData:function(){return k.extend(this.model.toJSON(),{errors:this.errorModel.toJSON(),action:this.action,isWithIcon:this.iconFactory.isWithIcons()})},
onRender:function(){this.iconFactory.isWithIcons()?(this.iconPickerContent=new f({model:this.model,action:this.action,observable:this}),this.getRegion("iconPicker").show(this.iconPickerContent)):delete this.iconPickerContent},hideIconPicker:function(){this.iconPickerContent&&this.iconPickerContent.hideIconPicker()},setName:function(a){this.ui.name.val(a);this.model.set("name",a)},setUrl:function(a){this.ui.url.val(a);this.model.set("url",a)},ensureProtocolPrefix:function(){var a=this.ui.url.val().trim();
0<a.length&&!b.urlPattern.test(a)&&this.setUrl("http://"+a)},tryToAutomagicallyDeriveNameFromUrl:function(){var a=this.ui.url.val().trim();this.setNameAutomagically&&(b.urlOptionalProtocolPattern.test(a)?(a=b.urlOptionalProtocolPattern.exec(a),this.setName(a[3])):this.setName(a))}},{urlPattern:/^[a-zA-Z0-9]+:(\/\/)?([^\/]*).*/,urlOptionalProtocolPattern:/^([a-zA-Z0-9]+:(\/\/)?)?([^\/]*).*/});return b});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Views.DialogContent",null,require("jira/projects/sidebar/project-shortcuts/views/dialog-content"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/views/AddDialogContent.js' */
define("jira/projects/sidebar/project-shortcuts/views/add-dialog-content",["jira/projects/sidebar/project-shortcuts/templates","jira/projects/sidebar/project-shortcuts/views/dialog-content"],function(a,b){return b.extend({template:a.addDialog})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Views.AddDialogContent",null,require("jira/projects/sidebar/project-shortcuts/views/add-dialog-content"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/views/EditDialogContent.js' */
define("jira/projects/sidebar/project-shortcuts/views/edit-dialog-content",["jira/projects/sidebar/project-shortcuts/templates","jira/projects/sidebar/project-shortcuts/views/dialog-content"],function(a,b){return b.extend({template:a.editDialog})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Views.EditDialogContent",null,require("jira/projects/sidebar/project-shortcuts/views/edit-dialog-content"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/dialogs/Delete.js' */
define("jira/projects/sidebar/project-shortcuts/dialogs/delete","jira/projects/sidebar/project-shortcuts/templates jira/util/formatter jira/util/logger jira/flag jira/projects/libs/marionette underscore".split(" "),function(b,c,d,e,f,g){return f.ItemView.extend({template:b.deleteDialog,ui:{inputs:"input, button",submit:".project-shortcuts-submit",cancel:".project-shortcuts-cancel"},events:{"click @ui.cancel":function(a){a.preventDefault();this.dialog.hide()},"click @ui.submit":function(a){a.preventDefault();
this.model.destroy({wait:!0})}},modelEvents:{"remove:start":function(){this.ui.inputs.prop("disabled",!0);this.ui.submit.addClass("loading");this.ui.submit[0].busy()},"remove:finish":function(){this.ui.inputs.prop("disabled",!1);this.ui.submit.removeClass("loading");this.ui.submit[0].idle();this.dialog.hide();d.trace("jira.projects.shortcuts.deleted")},"remove:failure":function(a){(a.message||a.errorMessages&&0<a.errorMessages.length)&&e({type:"error",title:"\u60a8\u4e0d\u80fd\u5220\u9664\u6b64\u94fe\u63a5\u3002",
close:"auto",body:a.message||a.errorMessages[0]})}},initialize:function(){this.render();this.$el.appendTo("body");this.dialog=AJS.dialog2(this.$el);this.dialog.show();var a=this;this.dialog.on("hide",function(){g.defer(function(){a.destroy()})})},onRender:function(){this.unwrapTemplate()}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Dialogs.Delete",null,require("jira/projects/sidebar/project-shortcuts/dialogs/delete"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/dialogs/Edit.js' */
define("jira/projects/sidebar/project-shortcuts/dialogs/edit","jira/projects/sidebar/project-shortcuts/templates jira/projects/sidebar/project-shortcuts/views/edit-dialog-content jira/projects/sidebar/project-shortcuts/entities/shortcut jira/projects/sidebar/project-shortcuts/entities/shortcut-errors jira/projects/libs/marionette underscore jquery".split(" "),function(e,f,g,h,k,l,c){return k.Controller.extend({initialize:function(a){var b=this;l.bindAll(this,"hide");this.model=new g(a.model.toJSON(),
{projectKey:a.model.projectKey||a.model.collection.projectKey});this.errorModel=new h(void 0,{model:this.model});this.view=new f({model:this.model,errorModel:this.errorModel});this.analyticsSave=!1;this.view.render();var d=c(e.editDialogChrome({}));this.view.$el.appendTo(d);this.dialog=AJS.dialog2(d);this.dialog.show();this.view.ui.url.focus();this._onResizeWindow=function(){b.hideIconPicker()};c(window).on("resize",this._onResizeWindow);this.listenTo(this.view,"cancel",this.hide);this.listenTo(this.model,
"save:success",function(){this.analyticsSave=!0;this.hide();a.model.set(b.model.toJSON())});this.dialog.on("hide",function(){b.trigger("dialog:close",b.analyticsSave)})},hideIconPicker:function(){this.view.hideIconPicker()},hide:function(){this.dialog.hide();c(window).off("resize",this._onResizeWindow)}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Dialogs.Edit",null,require("jira/projects/sidebar/project-shortcuts/dialogs/edit"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/dialogs/Add.js' */
define("jira/projects/sidebar/project-shortcuts/dialogs/add","aui/inline-dialog jira/projects/sidebar/project-shortcuts/views/add-dialog-content jira/projects/sidebar/project-shortcuts/entities/shortcut jira/projects/sidebar/project-shortcuts/entities/shortcut-errors jira/projects/libs/marionette underscore jquery".split(" "),function(k,l,f,m,n,p,c){return n.Controller.extend({initialize:function(d){p.bindAll(this,"hide","refresh","focusForm");var a=this;this.analyticsSave=!1;this.sidebarItem=d.sidebarItem;
this.projectKey=d.projectKey;this.collection=d.collection;this.model=new f(null,{projectKey:this.projectKey});this.errorModel=new m(null,{model:this.model});this.view=new l({model:this.model,errorModel:this.errorModel});this.view.render();var e=c(window),g=c(document),h=c(".aui-sidebar-body");this.dialog=new k(this.sidebarItem.ui.link,"project-shortcuts-group__add-dialog",function(q,b,c){a.sidebarItem.$el.addClass("aui-nav-selected");a.view.render();a.view.$el.appendTo(q);a.view.ui.url.focus();e.on("scroll.project-shortcuts",
function(){a.refresh()});h.on("scroll.project-shortcuts",function(){a.hide()});g.on("showLayer",a.focusForm);e.on("resize",a.refresh);c();return!1},{gravity:"w",autoWidth:!0,initCallback:function(){a.trigger("dialog:open");a.analyticsSave=!1},hideCallback:function(){a.sidebarItem.$el.removeClass("aui-nav-selected");a.sidebarItem.ui.link.blur();e.off("scroll.project-shortcuts");h.off("scroll.project-shortcuts");g.off("showLayer",a.focusForm);a.trigger("dialog:close",a.analyticsSave)},persistent:!0,
closeOnTriggerClick:!0,offsetY:function(a,b){b=b.target.height();return a.height()/2-b-10},arrowOffsetY:function(a,b){b=b.target.height()/2;return-(a.height()/2)+22+b}});this.listenTo(this.view,"render",this.refresh);this.listenTo(this.view,"cancel",this.hideAndRender);this.listenTo(this.model,"save:success",function(){var a=new f(this.model.toJSON(),{projectKey:this.projectKey});this.model.clear();this.collection.add(a);this.analyticsSave=!0;this.hide()});this.listenTo(this.sidebarItem,"before:select",
function(a){a.preventDefault()});AJS.sidebar(".aui-sidebar").on("collapse-start",this.hide);c(".project-shortcuts-group").on("click","li",this.hide)},hide:function(){this.view.hideIconPicker();this.dialog.hide()},refresh:function(){this.view.hideIconPicker();this.dialog.refresh()},hideAndRender:function(){this.hide()},focusForm:function(){this.view.ui.url.focus()}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Dialogs.Add",null,require("jira/projects/sidebar/project-shortcuts/dialogs/add"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/views/Shortcut.js' */
define("jira/projects/sidebar/project-shortcuts/views/shortcut","jira/projects/sidebar/project-shortcuts/templates jira/projects/sidebar/project-shortcuts/services/available-icons jira/projects/sidebar/project-shortcuts/dialogs/edit jira/projects/sidebar/project-shortcuts/dialogs/delete jira/projects/libs/marionette underscore jquery".split(" "),function(f,d,g,h,k,c,e){return k.ItemView.extend({template:f.shortcut,initialize:function(){c.bindAll(this,"toggleDropdown");this.iconFactory=d},ui:{del:".project-shortcuts-group__actions__delete",
edit:".project-shortcuts-group__actions__edit",trigger:".project-shortcuts-group__actions",dropdown:".project-shortcuts-group__dropdown",link:".project-shortcuts-group__link"},modelEvents:{change:"render"},events:{"click @ui.link":function(){this.trigger("click:link",this.model)}},onRender:function(){var a=this;this.unwrapTemplate();e(window);e(".aui-sidebar-body");this.ui.edit.on("click",function(b){b.preventDefault();a.ui.trigger.blur();b=new g({model:a.model});a.trigger("edit:open",a.model);a.listenToOnce(b,
"dialog:close",function(b){a.trigger("edit:close",a.model,b)})});this.ui.del.on("click",function(b){b.preventDefault();a.ui.trigger.blur();new h({model:a.model})});this.ui.dropdown.on({"aui-dropdown2-show":function(){a.$el.addClass("aui-nav-selected")},"aui-dropdown2-hide":function(){a.$el.removeClass("aui-nav-selected")}})},serializeData:function(){var a=c.extend(this.model.toJSON(),{canManage:!0,isWithIcon:this.iconFactory.isWithIcons()});this.iconFactory.isWithIcons()&&(a=c.extend(a,{iconsMap:d.getIconsMap()}));
return a},toggleDropdown:function(){this.ui.trigger.trigger("aui-button-invoke")}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Views.Shortcut",null,require("jira/projects/sidebar/project-shortcuts/views/shortcut"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts', location = '/sidebar/project-shortcuts/views/ShortcutsList.js' */
define("jira/projects/sidebar/project-shortcuts/views/list","jira/api/projects/sidebar jira/projects/sidebar/project-shortcuts/templates jira/projects/sidebar/project-shortcuts/views/shortcut jira/projects/sidebar/project-shortcuts/dialogs/add jira/projects/sidebar/component/navigation-group jira/projects/libs/marionette jquery".split(" "),function(c,e,f,g,h,k,d){return k.CompositeView.extend({template:e.content,childView:f,ui:{itemsContainer:".aui-nav",description:".project-shortcuts-group__description",
add:".project-shortcuts-group__add"},collectionEvents:{"add remove":function(){if(0==this.collection.length&&0!=this.lastCollectionLength||0!=this.collection.length&&0==this.lastCollectionLength)this.lastCollectionLength=this.collection.length,this.ui.description.toggleClass("hidden",0<this.collection.length),this.$el.toggleClass("project-shortcuts-group_empty",0==this.collection.length)}},initialize:function(){var a=this;c.getSidebar().done(function(b){b=b.getAUISidebar();b.on("expand-end",function(){d(".aui-sidebar-submenu-dialog .project-shortcuts-group__dropdown").remove()});
b.on("collapse-start",function(){a.$(".project-shortcuts-group__actions.aui-dropdown2-active").trigger("aui-button-invoke")})});this.lastCollectionLength=this.collection.length},attachElContent:function(a){var b=new h({el:a});this.$el=d(b.getElement());c.getSidebar().done(function(a){a.replaceGroup(this.options.targetGroup,b)}.bind(this));return this},onRender:function(){c.getSidebar().done(function(a){a=new g({sidebarItem:a.getGroup("project-shortcuts-group").getItem("project-shortcut-add"),projectKey:this.collection.projectKey,
collection:this.collection});this.listenTo(a,"dialog:open",function(){this.trigger("add:open")});this.listenTo(a,"dialog:close",function(a){this.trigger("add:close",a)})}.bind(this))},serializeData:function(){return{canManage:!0,numberOfShortcuts:this.collection.length}},attachBuffer:function(a,b){this.ui.itemsContainer.prepend(b)},onAddChild:function(a){this.ui.add.parent().before(a.$el)}})});AJS.namespace("JIRA.Projects.Sidebar.ProjectShortcuts.Views.List",null,require("jira/projects/sidebar/project-shortcuts/views/list"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:sidebar-project-shortcuts-init', location = '/sidebar/project-shortcuts/project-shortcuts-init.js' */
require("jira/projects/sidebar/project-shortcuts/analytics jira/projects/sidebar/project-shortcuts/views/list jira/projects/sidebar/project-shortcuts/services/available-icons jira/projects/sidebar/project-shortcuts/entities/shortcuts jira/api/projects/sidebar jira/api/projects wrm/data".split(" "),function(c,f,e,g,d,h,b){d.getSidebar().done(function(d){var a=b.claim("com.atlassian.jira.projects.shortcuts:project-id");c.initialize(a);b.claim("com.atlassian.jira.projects.shortcuts:can-manage")&&((a=
b.claim("com.atlassian.jira.projects.shortcuts:with-icons"))&&e.initialize(b.claim("com.atlassian.jira.projects.shortcuts:icons-list")),e.setWithIcons(a),a=h.getCurrentProjectKey(),a=new g(b.claim("com.atlassian.jira.projects.shortcuts:shortcuts"),{projectKey:a}),a=new f({collection:a,targetGroup:"project-shortcuts-group"}),a.render(),c.initDialogActions(a));c.initShortcutClick(".project-shortcuts-list",d)})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:report-page-assets', location = '/page/project/report/analytics.js' */
define("jira/projects/page/report/analytics",["jira/analytics","jquery"],function(c,b){return function(){b(".reports .reports__list__report").on("click",function(a){a=b(this);c.send({name:"jira.projects.report.clicked",data:{report:a.attr("data-report-key"),category:a.closest("ul").attr("data-category-key"),listPosition:a.closest("li").prevAll().length+1}})})}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:reports-link-storage', location = '/sidebar/reports/reports-link-storage.js' */
define("jira/projects/sidebar/reports/link-storage",["jira/util/logger","jira/data/local-storage","jira/api/projects","jira/api/projects/sidebar"],function(d,e,g,c){function f(a){a=a.getSelectedScopeFilterId();var b="last.viewed.report."+g.getCurrentProjectKey();a&&(b+="."+a);return b}return{storeLastViewedReportLink:function(a){c.getSidebar().done(function(b){if(b.isProjectSidebar()){var c=f(b);e.setItem(c,a);b.setReportsItemLink(a)}d.trace("jira.projects.sidebar.reports.link.stored")})},restoreLastViewedReportLink:function(){c.getSidebar().done(function(a){if(a.isProjectSidebar()){var b=
f(a);(b=e.getItem(b))&&a.setReportsItemLink(b)}d.trace("jira.projects.sidebar.reports.link.restored")})}}});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.jira-projects-plugin:reports-link-init', location = '/sidebar/reports/reports-link-init.js' */
require(["jira/projects/sidebar/reports/link-storage"],function(a){a.restoreLastViewedReportLink()});AJS.namespace("JIRA.Projects.Sidebar.Reports",null,require("jira/projects/sidebar/reports/link-storage"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_jquery.ui.mouse', location = 'aui.chunk.29f1d3ac1f9e0380058e--5355ac10fee1e466715e.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["jquery.ui.mouse"],{mz4i:function(u,i,n){"use strict";n.r(i);n("XPYc");i.default="jquery"}},[["mz4i","runtime","aui.splitchunk.vendors--20a97d6a33","aui.splitchunk.vendors--d18e3cafa7","aui.splitchunk.vendors--db57146687","aui.splitchunk.0d131bcbf1"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.splitchunk.vendors--7c76061134', location = 'aui.chunk.6e91bd167992c2b91760--267f7f37208f9c6ae1d3.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.splitchunk.vendors--7c76061134"],{TRfs:function(e,t,s){},iCCw:function(e,t,s){var l,i,n;i=[s("oDIA"),s("XPYc"),s("O+lX"),s("yIBB")],void 0===(n="function"==typeof(l=function(e){return e.widget("ui.selectable",e.ui.mouse,{version:"1.12.1",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var t=this;this._addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){t.elementPos=e(t.element[0]).offset(),t.selectees=e(t.options.filter,t.element[0]),t._addClass(t.selectees,"ui-selectee"),t.selectees.each(function(){var s=e(this),l=s.offset(),i={left:l.left-t.elementPos.left,top:l.top-t.elementPos.top};e.data(this,"selectable-item",{element:this,$element:s,left:i.left,top:i.top,right:i.left+s.outerWidth(),bottom:i.top+s.outerHeight(),startselected:!1,selected:s.hasClass("ui-selected"),selecting:s.hasClass("ui-selecting"),unselecting:s.hasClass("ui-unselecting")})})},this.refresh(),this._mouseInit(),this.helper=e("<div>"),this._addClass(this.helper,"ui-selectable-helper")},_destroy:function(){this.selectees.removeData("selectable-item"),this._mouseDestroy()},_mouseStart:function(t){var s=this,l=this.options;this.opos=[t.pageX,t.pageY],this.elementPos=e(this.element[0]).offset(),this.options.disabled||(this.selectees=e(l.filter,this.element[0]),this._trigger("start",t),e(l.appendTo).append(this.helper),this.helper.css({left:t.pageX,top:t.pageY,width:0,height:0}),l.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var l=e.data(this,"selectable-item");l.startselected=!0,t.metaKey||t.ctrlKey||(s._removeClass(l.$element,"ui-selected"),l.selected=!1,s._addClass(l.$element,"ui-unselecting"),l.unselecting=!0,s._trigger("unselecting",t,{unselecting:l.element}))}),e(t.target).parents().addBack().each(function(){var l,i=e.data(this,"selectable-item");if(i)return l=!t.metaKey&&!t.ctrlKey||!i.$element.hasClass("ui-selected"),s._removeClass(i.$element,l?"ui-unselecting":"ui-selected")._addClass(i.$element,l?"ui-selecting":"ui-unselecting"),i.unselecting=!l,i.selecting=l,i.selected=l,l?s._trigger("selecting",t,{selecting:i.element}):s._trigger("unselecting",t,{unselecting:i.element}),!1}))},_mouseDrag:function(t){if(this.dragged=!0,!this.options.disabled){var s,l=this,i=this.options,n=this.opos[0],c=this.opos[1],a=t.pageX,o=t.pageY;return n>a&&(s=a,a=n,n=s),c>o&&(s=o,o=c,c=s),this.helper.css({left:n,top:c,width:a-n,height:o-c}),this.selectees.each(function(){var s=e.data(this,"selectable-item"),r=!1,u={};s&&s.element!==l.element[0]&&(u.left=s.left+l.elementPos.left,u.right=s.right+l.elementPos.left,u.top=s.top+l.elementPos.top,u.bottom=s.bottom+l.elementPos.top,"touch"===i.tolerance?r=!(u.left>a||u.right<n||u.top>o||u.bottom<c):"fit"===i.tolerance&&(r=u.left>n&&u.right<a&&u.top>c&&u.bottom<o),r?(s.selected&&(l._removeClass(s.$element,"ui-selected"),s.selected=!1),s.unselecting&&(l._removeClass(s.$element,"ui-unselecting"),s.unselecting=!1),s.selecting||(l._addClass(s.$element,"ui-selecting"),s.selecting=!0,l._trigger("selecting",t,{selecting:s.element}))):(s.selecting&&((t.metaKey||t.ctrlKey)&&s.startselected?(l._removeClass(s.$element,"ui-selecting"),s.selecting=!1,l._addClass(s.$element,"ui-selected"),s.selected=!0):(l._removeClass(s.$element,"ui-selecting"),s.selecting=!1,s.startselected&&(l._addClass(s.$element,"ui-unselecting"),s.unselecting=!0),l._trigger("unselecting",t,{unselecting:s.element}))),s.selected&&(t.metaKey||t.ctrlKey||s.startselected||(l._removeClass(s.$element,"ui-selected"),s.selected=!1,l._addClass(s.$element,"ui-unselecting"),s.unselecting=!0,l._trigger("unselecting",t,{unselecting:s.element})))))}),!1}},_mouseStop:function(t){var s=this;return this.dragged=!1,e(".ui-unselecting",this.element[0]).each(function(){var l=e.data(this,"selectable-item");s._removeClass(l.$element,"ui-unselecting"),l.unselecting=!1,l.startselected=!1,s._trigger("unselected",t,{unselected:l.element})}),e(".ui-selecting",this.element[0]).each(function(){var l=e.data(this,"selectable-item");s._removeClass(l.$element,"ui-selecting")._addClass(l.$element,"ui-selected"),l.selecting=!1,l.selected=!0,l.startselected=!0,s._trigger("selected",t,{selected:l.element})}),this._trigger("stop",t),this.helper.remove(),!1}})})?l.apply(t,i):l)||(e.exports=n)}}]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_jquery.ui.selectable', location = 'aui.chunk.3b7738f0d9add1f307e8--1446e5393da54c5ce6e6.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["jquery.ui.selectable"],{FvNQ:function(u,i,n){"use strict";n.r(i);n("TRfs"),n("iCCw");i.default="jquery"}},[["FvNQ","runtime","aui.splitchunk.vendors--20a97d6a33","aui.splitchunk.vendors--d18e3cafa7","aui.splitchunk.vendors--db57146687","aui.splitchunk.vendors--7c76061134","aui.splitchunk.0d131bcbf1"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.splitchunk.vendors--85718a7eef', location = 'aui.chunk.924c02d84b89ee08ace7--46ea663edd3c38a8171f.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.splitchunk.vendors--85718a7eef"],{"2V+C":function(t,i,e){},XnJT:function(t,i,e){var s,h,n;h=[e("oDIA"),e("XPYc"),e("zq/Y"),e("lSwU"),e("O+lX"),e("yIBB")],void 0===(n="function"==typeof(s=function(t){return t.widget("ui.resizable",t.ui.mouse,{version:"1.12.1",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,classes:{"ui-resizable-se":"ui-icon ui-icon-gripsmall-diagonal-se"},containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(t){return parseFloat(t)||0},_isNumber:function(t){return!isNaN(parseFloat(t))},_hasScroll:function(i,e){if("hidden"===t(i).css("overflow"))return!1;var s,h=e&&"left"===e?"scrollLeft":"scrollTop";return i[h]>0||(i[h]=1,s=i[h]>0,i[h]=0,s)},_create:function(){var i,e=this.options,s=this;this._addClass("ui-resizable"),t.extend(this,{_aspectRatio:!!e.aspectRatio,aspectRatio:e.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:e.helper||e.ghost||e.animate?e.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(t("<div class='ui-wrapper' style='overflow: hidden;'></div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,i={marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom"),marginLeft:this.originalElement.css("marginLeft")},this.element.css(i),this.originalElement.css("margin",0),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css(i),this._proportionallyResize()),this._setupHandles(),e.autoHide&&t(this.element).on("mouseenter",function(){e.disabled||(s._removeClass("ui-resizable-autohide"),s._handles.show())}).on("mouseleave",function(){e.disabled||s.resizing||(s._addClass("ui-resizable-autohide"),s._handles.hide())}),this._mouseInit()},_destroy:function(){this._mouseDestroy();var i,e=function(i){t(i).removeData("resizable").removeData("ui-resizable").off(".resizable").find(".ui-resizable-handle").remove()};return this.elementIsWrapper&&(e(this.element),i=this.element,this.originalElement.css({position:i.css("position"),width:i.outerWidth(),height:i.outerHeight(),top:i.css("top"),left:i.css("left")}).insertAfter(i),i.remove()),this.originalElement.css("resize",this.originalResizeStyle),e(this.originalElement),this},_setOption:function(t,i){switch(this._super(t,i),t){case"handles":this._removeHandles(),this._setupHandles()}},_setupHandles:function(){var i,e,s,h,n,o=this.options,a=this;if(this.handles=o.handles||(t(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=t(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),s=this.handles.split(","),this.handles={},e=0;e<s.length;e++)h="ui-resizable-"+(i=t.trim(s[e])),n=t("<div>"),this._addClass(n,"ui-resizable-handle "+h),n.css({zIndex:o.zIndex}),this.handles[i]=".ui-resizable-"+i,this.element.append(n);this._renderAxis=function(i){var e,s,h,n;for(e in i=i||this.element,this.handles)this.handles[e].constructor===String?this.handles[e]=this.element.children(this.handles[e]).first().show():(this.handles[e].jquery||this.handles[e].nodeType)&&(this.handles[e]=t(this.handles[e]),this._on(this.handles[e],{mousedown:a._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(s=t(this.handles[e],this.element),n=/sw|ne|nw|se|n|s/.test(e)?s.outerHeight():s.outerWidth(),h=["padding",/ne|nw|n/.test(e)?"Top":/se|sw|s/.test(e)?"Bottom":/^e$/.test(e)?"Right":"Left"].join(""),i.css(h,n),this._proportionallyResize()),this._handles=this._handles.add(this.handles[e])},this._renderAxis(this.element),this._handles=this._handles.add(this.element.find(".ui-resizable-handle")),this._handles.disableSelection(),this._handles.on("mouseover",function(){a.resizing||(this.className&&(n=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),a.axis=n&&n[1]?n[1]:"se")}),o.autoHide&&(this._handles.hide(),this._addClass("ui-resizable-autohide"))},_removeHandles:function(){this._handles.remove()},_mouseCapture:function(i){var e,s,h=!1;for(e in this.handles)((s=t(this.handles[e])[0])===i.target||t.contains(s,i.target))&&(h=!0);return!this.options.disabled&&h},_mouseStart:function(i){var e,s,h,n=this.options,o=this.element;return this.resizing=!0,this._renderProxy(),e=this._num(this.helper.css("left")),s=this._num(this.helper.css("top")),n.containment&&(e+=t(n.containment).scrollLeft()||0,s+=t(n.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:e,top:s},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:o.width(),height:o.height()},this.originalSize=this._helper?{width:o.outerWidth(),height:o.outerHeight()}:{width:o.width(),height:o.height()},this.sizeDiff={width:o.outerWidth()-o.width(),height:o.outerHeight()-o.height()},this.originalPosition={left:e,top:s},this.originalMousePosition={left:i.pageX,top:i.pageY},this.aspectRatio="number"==typeof n.aspectRatio?n.aspectRatio:this.originalSize.width/this.originalSize.height||1,h=t(".ui-resizable-"+this.axis).css("cursor"),t("body").css("cursor","auto"===h?this.axis+"-resize":h),this._addClass("ui-resizable-resizing"),this._propagate("start",i),!0},_mouseDrag:function(i){var e,s,h=this.originalMousePosition,n=this.axis,o=i.pageX-h.left||0,a=i.pageY-h.top||0,l=this._change[n];return this._updatePrevProperties(),!!l&&(e=l.apply(this,[i,o,a]),this._updateVirtualBoundaries(i.shiftKey),(this._aspectRatio||i.shiftKey)&&(e=this._updateRatio(e,i)),e=this._respectSize(e,i),this._updateCache(e),this._propagate("resize",i),s=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),t.isEmptyObject(s)||(this._updatePrevProperties(),this._trigger("resize",i,this.ui()),this._applyChanges()),!1)},_mouseStop:function(i){this.resizing=!1;var e,s,h,n,o,a,l,r=this.options;return this._helper&&(h=(s=(e=this._proportionallyResizeElements).length&&/textarea/i.test(e[0].nodeName))&&this._hasScroll(e[0],"left")?0:this.sizeDiff.height,n=s?0:this.sizeDiff.width,o={width:this.helper.width()-n,height:this.helper.height()-h},a=parseFloat(this.element.css("left"))+(this.position.left-this.originalPosition.left)||null,l=parseFloat(this.element.css("top"))+(this.position.top-this.originalPosition.top)||null,r.animate||this.element.css(t.extend(o,{top:l,left:a})),this.helper.height(this.size.height),this.helper.width(this.size.width),this._helper&&!r.animate&&this._proportionallyResize()),t("body").css("cursor","auto"),this._removeClass("ui-resizable-resizing"),this._propagate("stop",i),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var t={};return this.position.top!==this.prevPosition.top&&(t.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(t.left=this.position.left+"px"),this.size.width!==this.prevSize.width&&(t.width=this.size.width+"px"),this.size.height!==this.prevSize.height&&(t.height=this.size.height+"px"),this.helper.css(t),t},_updateVirtualBoundaries:function(t){var i,e,s,h,n,o=this.options;n={minWidth:this._isNumber(o.minWidth)?o.minWidth:0,maxWidth:this._isNumber(o.maxWidth)?o.maxWidth:1/0,minHeight:this._isNumber(o.minHeight)?o.minHeight:0,maxHeight:this._isNumber(o.maxHeight)?o.maxHeight:1/0},(this._aspectRatio||t)&&(i=n.minHeight*this.aspectRatio,s=n.minWidth/this.aspectRatio,e=n.maxHeight*this.aspectRatio,h=n.maxWidth/this.aspectRatio,i>n.minWidth&&(n.minWidth=i),s>n.minHeight&&(n.minHeight=s),e<n.maxWidth&&(n.maxWidth=e),h<n.maxHeight&&(n.maxHeight=h)),this._vBoundaries=n},_updateCache:function(t){this.offset=this.helper.offset(),this._isNumber(t.left)&&(this.position.left=t.left),this._isNumber(t.top)&&(this.position.top=t.top),this._isNumber(t.height)&&(this.size.height=t.height),this._isNumber(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var i=this.position,e=this.size,s=this.axis;return this._isNumber(t.height)?t.width=t.height*this.aspectRatio:this._isNumber(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===s&&(t.left=i.left+(e.width-t.width),t.top=null),"nw"===s&&(t.top=i.top+(e.height-t.height),t.left=i.left+(e.width-t.width)),t},_respectSize:function(t){var i=this._vBoundaries,e=this.axis,s=this._isNumber(t.width)&&i.maxWidth&&i.maxWidth<t.width,h=this._isNumber(t.height)&&i.maxHeight&&i.maxHeight<t.height,n=this._isNumber(t.width)&&i.minWidth&&i.minWidth>t.width,o=this._isNumber(t.height)&&i.minHeight&&i.minHeight>t.height,a=this.originalPosition.left+this.originalSize.width,l=this.originalPosition.top+this.originalSize.height,r=/sw|nw|w/.test(e),p=/nw|ne|n/.test(e);return n&&(t.width=i.minWidth),o&&(t.height=i.minHeight),s&&(t.width=i.maxWidth),h&&(t.height=i.maxHeight),n&&r&&(t.left=a-i.minWidth),s&&r&&(t.left=a-i.maxWidth),o&&p&&(t.top=l-i.minHeight),h&&p&&(t.top=l-i.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_getPaddingPlusBorderDimensions:function(t){for(var i=0,e=[],s=[t.css("borderTopWidth"),t.css("borderRightWidth"),t.css("borderBottomWidth"),t.css("borderLeftWidth")],h=[t.css("paddingTop"),t.css("paddingRight"),t.css("paddingBottom"),t.css("paddingLeft")];i<4;i++)e[i]=parseFloat(s[i])||0,e[i]+=parseFloat(h[i])||0;return{height:e[0]+e[2],width:e[1]+e[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,i=0,e=this.helper||this.element;i<this._proportionallyResizeElements.length;i++)t=this._proportionallyResizeElements[i],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(t)),t.css({height:e.height()-this.outerDimensions.height||0,width:e.width()-this.outerDimensions.width||0})},_renderProxy:function(){var i=this.element,e=this.options;this.elementOffset=i.offset(),this._helper?(this.helper=this.helper||t("<div style='overflow:hidden;'></div>"),this._addClass(this.helper,this._helper),this.helper.css({width:this.element.outerWidth(),height:this.element.outerHeight(),position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++e.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,i){return{width:this.originalSize.width+i}},w:function(t,i){var e=this.originalSize;return{left:this.originalPosition.left+i,width:e.width-i}},n:function(t,i,e){var s=this.originalSize;return{top:this.originalPosition.top+e,height:s.height-e}},s:function(t,i,e){return{height:this.originalSize.height+e}},se:function(i,e,s){return t.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[i,e,s]))},sw:function(i,e,s){return t.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[i,e,s]))},ne:function(i,e,s){return t.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[i,e,s]))},nw:function(i,e,s){return t.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[i,e,s]))}},_propagate:function(i,e){t.ui.plugin.call(this,i,[e,this.ui()]),"resize"!==i&&this._trigger(i,e,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),t.ui.plugin.add("resizable","animate",{stop:function(i){var e=t(this).resizable("instance"),s=e.options,h=e._proportionallyResizeElements,n=h.length&&/textarea/i.test(h[0].nodeName),o=n&&e._hasScroll(h[0],"left")?0:e.sizeDiff.height,a=n?0:e.sizeDiff.width,l={width:e.size.width-a,height:e.size.height-o},r=parseFloat(e.element.css("left"))+(e.position.left-e.originalPosition.left)||null,p=parseFloat(e.element.css("top"))+(e.position.top-e.originalPosition.top)||null;e.element.animate(t.extend(l,p&&r?{top:p,left:r}:{}),{duration:s.animateDuration,easing:s.animateEasing,step:function(){var s={width:parseFloat(e.element.css("width")),height:parseFloat(e.element.css("height")),top:parseFloat(e.element.css("top")),left:parseFloat(e.element.css("left"))};h&&h.length&&t(h[0]).css({width:s.width,height:s.height}),e._updateCache(s),e._propagate("resize",i)}})}}),t.ui.plugin.add("resizable","containment",{start:function(){var i,e,s,h,n,o,a,l=t(this).resizable("instance"),r=l.options,p=l.element,d=r.containment,g=d instanceof t?d.get(0):/parent/.test(d)?p.parent().get(0):d;g&&(l.containerElement=t(g),/document/.test(d)||d===document?(l.containerOffset={left:0,top:0},l.containerPosition={left:0,top:0},l.parentData={element:t(document),left:0,top:0,width:t(document).width(),height:t(document).height()||document.body.parentNode.scrollHeight}):(i=t(g),e=[],t(["Top","Right","Left","Bottom"]).each(function(t,s){e[t]=l._num(i.css("padding"+s))}),l.containerOffset=i.offset(),l.containerPosition=i.position(),l.containerSize={height:i.innerHeight()-e[3],width:i.innerWidth()-e[1]},s=l.containerOffset,h=l.containerSize.height,n=l.containerSize.width,o=l._hasScroll(g,"left")?g.scrollWidth:n,a=l._hasScroll(g)?g.scrollHeight:h,l.parentData={element:g,left:s.left,top:s.top,width:o,height:a}))},resize:function(i){var e,s,h,n,o=t(this).resizable("instance"),a=o.options,l=o.containerOffset,r=o.position,p=o._aspectRatio||i.shiftKey,d={top:0,left:0},g=o.containerElement,u=!0;g[0]!==document&&/static/.test(g.css("position"))&&(d=l),r.left<(o._helper?l.left:0)&&(o.size.width=o.size.width+(o._helper?o.position.left-l.left:o.position.left-d.left),p&&(o.size.height=o.size.width/o.aspectRatio,u=!1),o.position.left=a.helper?l.left:0),r.top<(o._helper?l.top:0)&&(o.size.height=o.size.height+(o._helper?o.position.top-l.top:o.position.top),p&&(o.size.width=o.size.height*o.aspectRatio,u=!1),o.position.top=o._helper?l.top:0),h=o.containerElement.get(0)===o.element.parent().get(0),n=/relative|absolute/.test(o.containerElement.css("position")),h&&n?(o.offset.left=o.parentData.left+o.position.left,o.offset.top=o.parentData.top+o.position.top):(o.offset.left=o.element.offset().left,o.offset.top=o.element.offset().top),e=Math.abs(o.sizeDiff.width+(o._helper?o.offset.left-d.left:o.offset.left-l.left)),s=Math.abs(o.sizeDiff.height+(o._helper?o.offset.top-d.top:o.offset.top-l.top)),e+o.size.width>=o.parentData.width&&(o.size.width=o.parentData.width-e,p&&(o.size.height=o.size.width/o.aspectRatio,u=!1)),s+o.size.height>=o.parentData.height&&(o.size.height=o.parentData.height-s,p&&(o.size.width=o.size.height*o.aspectRatio,u=!1)),u||(o.position.left=o.prevPosition.left,o.position.top=o.prevPosition.top,o.size.width=o.prevSize.width,o.size.height=o.prevSize.height)},stop:function(){var i=t(this).resizable("instance"),e=i.options,s=i.containerOffset,h=i.containerPosition,n=i.containerElement,o=t(i.helper),a=o.offset(),l=o.outerWidth()-i.sizeDiff.width,r=o.outerHeight()-i.sizeDiff.height;i._helper&&!e.animate&&/relative/.test(n.css("position"))&&t(this).css({left:a.left-h.left-s.left,width:l,height:r}),i._helper&&!e.animate&&/static/.test(n.css("position"))&&t(this).css({left:a.left-h.left-s.left,width:l,height:r})}}),t.ui.plugin.add("resizable","alsoResize",{start:function(){var i=t(this).resizable("instance").options;t(i.alsoResize).each(function(){var i=t(this);i.data("ui-resizable-alsoresize",{width:parseFloat(i.width()),height:parseFloat(i.height()),left:parseFloat(i.css("left")),top:parseFloat(i.css("top"))})})},resize:function(i,e){var s=t(this).resizable("instance"),h=s.options,n=s.originalSize,o=s.originalPosition,a={height:s.size.height-n.height||0,width:s.size.width-n.width||0,top:s.position.top-o.top||0,left:s.position.left-o.left||0};t(h.alsoResize).each(function(){var i=t(this),s=t(this).data("ui-resizable-alsoresize"),h={},n=i.parents(e.originalElement[0]).length?["width","height"]:["width","height","top","left"];t.each(n,function(t,i){var e=(s[i]||0)+(a[i]||0);e&&e>=0&&(h[i]=e||null)}),i.css(h)})},stop:function(){t(this).removeData("ui-resizable-alsoresize")}}),t.ui.plugin.add("resizable","ghost",{start:function(){var i=t(this).resizable("instance"),e=i.size;i.ghost=i.originalElement.clone(),i.ghost.css({opacity:.25,display:"block",position:"relative",height:e.height,width:e.width,margin:0,left:0,top:0}),i._addClass(i.ghost,"ui-resizable-ghost"),!1!==t.uiBackCompat&&"string"==typeof i.options.ghost&&i.ghost.addClass(this.options.ghost),i.ghost.appendTo(i.helper)},resize:function(){var i=t(this).resizable("instance");i.ghost&&i.ghost.css({position:"relative",height:i.size.height,width:i.size.width})},stop:function(){var i=t(this).resizable("instance");i.ghost&&i.helper&&i.helper.get(0).removeChild(i.ghost.get(0))}}),t.ui.plugin.add("resizable","grid",{resize:function(){var i,e=t(this).resizable("instance"),s=e.options,h=e.size,n=e.originalSize,o=e.originalPosition,a=e.axis,l="number"==typeof s.grid?[s.grid,s.grid]:s.grid,r=l[0]||1,p=l[1]||1,d=Math.round((h.width-n.width)/r)*r,g=Math.round((h.height-n.height)/p)*p,u=n.width+d,f=n.height+g,c=s.maxWidth&&s.maxWidth<u,m=s.maxHeight&&s.maxHeight<f,z=s.minWidth&&s.minWidth>u,w=s.minHeight&&s.minHeight>f;s.grid=l,z&&(u+=r),w&&(f+=p),c&&(u-=r),m&&(f-=p),/^(se|s|e)$/.test(a)?(e.size.width=u,e.size.height=f):/^(ne)$/.test(a)?(e.size.width=u,e.size.height=f,e.position.top=o.top-g):/^(sw)$/.test(a)?(e.size.width=u,e.size.height=f,e.position.left=o.left-d):((f-p<=0||u-r<=0)&&(i=e._getPaddingPlusBorderDimensions(this)),f-p>0?(e.size.height=f,e.position.top=o.top-g):(f=p-i.height,e.size.height=f,e.position.top=o.top+n.height-f),u-r>0?(e.size.width=u,e.position.left=o.left-d):(u=r-i.width,e.size.width=u,e.position.left=o.left+n.width-u))}}),t.ui.resizable})?s.apply(i,h):s)||(t.exports=n)},"zq/Y":function(t,i,e){var s,h,n;h=[e("oDIA"),e("O+lX")],void 0===(n="function"==typeof(s=function(t){return t.fn.extend({disableSelection:function(){var t="onselectstart"in document.createElement("div")?"selectstart":"mousedown";return function(){return this.on(t+".ui-disableSelection",function(t){t.preventDefault()})}}(),enableSelection:function(){return this.off(".ui-disableSelection")}})})?s.apply(i,h):s)||(t.exports=n)}}]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_jquery.ui.resizable', location = 'aui.chunk.da6b9689b400fe199817--10e1c619e132ff1211f6.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["jquery.ui.resizable"],{kW2n:function(u,i,n){"use strict";n.r(i);n("2V+C"),n("XnJT");i.default="jquery"}},[["kW2n","runtime","aui.splitchunk.vendors--20a97d6a33","aui.splitchunk.vendors--d18e3cafa7","aui.splitchunk.vendors--db57146687","aui.splitchunk.vendors--9c8c8c1546","aui.splitchunk.vendors--85718a7eef","aui.splitchunk.0d131bcbf1"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.splitchunk.vendors--825acd7236', location = 'aui.chunk.9bc05c26138feb62d9fa--629279f50cfcda2d5a3b.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.splitchunk.vendors--825acd7236"],{PTCr:function(e,t,i){var s,o,n;o=[i("oDIA"),i("z+ct"),i("XPYc"),i("O+lX"),i("yIBB")],void 0===(n="function"==typeof(s=function(e){e.widget("ui.droppable",{version:"1.12.1",widgetEventPrefix:"drop",options:{accept:"*",addClasses:!0,greedy:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var t,i=this.options,s=i.accept;this.isover=!1,this.isout=!0,this.accept=e.isFunction(s)?s:function(e){return e.is(s)},this.proportions=function(){if(!arguments.length)return t||(t={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight});t=arguments[0]},this._addToManager(i.scope),i.addClasses&&this._addClass("ui-droppable")},_addToManager:function(t){e.ui.ddmanager.droppables[t]=e.ui.ddmanager.droppables[t]||[],e.ui.ddmanager.droppables[t].push(this)},_splice:function(e){for(var t=0;t<e.length;t++)e[t]===this&&e.splice(t,1)},_destroy:function(){var t=e.ui.ddmanager.droppables[this.options.scope];this._splice(t)},_setOption:function(t,i){if("accept"===t)this.accept=e.isFunction(i)?i:function(e){return e.is(i)};else if("scope"===t){var s=e.ui.ddmanager.droppables[this.options.scope];this._splice(s),this._addToManager(i)}this._super(t,i)},_activate:function(t){var i=e.ui.ddmanager.current;this._addActiveClass(),i&&this._trigger("activate",t,this.ui(i))},_deactivate:function(t){var i=e.ui.ddmanager.current;this._removeActiveClass(),i&&this._trigger("deactivate",t,this.ui(i))},_over:function(t){var i=e.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._addHoverClass(),this._trigger("over",t,this.ui(i)))},_out:function(t){var i=e.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._removeHoverClass(),this._trigger("out",t,this.ui(i)))},_drop:function(i,s){var o=s||e.ui.ddmanager.current,n=!1;return!(!o||(o.currentItem||o.element)[0]===this.element[0])&&(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var s=e(this).droppable("instance");if(s.options.greedy&&!s.options.disabled&&s.options.scope===o.options.scope&&s.accept.call(s.element[0],o.currentItem||o.element)&&t(o,e.extend(s,{offset:s.element.offset()}),s.options.tolerance,i))return n=!0,!1}),!n&&!!this.accept.call(this.element[0],o.currentItem||o.element)&&(this._removeActiveClass(),this._removeHoverClass(),this._trigger("drop",i,this.ui(o)),this.element))},ui:function(e){return{draggable:e.currentItem||e.element,helper:e.helper,position:e.position,offset:e.positionAbs}},_addHoverClass:function(){this._addClass("ui-droppable-hover")},_removeHoverClass:function(){this._removeClass("ui-droppable-hover")},_addActiveClass:function(){this._addClass("ui-droppable-active")},_removeActiveClass:function(){this._removeClass("ui-droppable-active")}});var t=e.ui.intersect=function(){function e(e,t,i){return e>=t&&e<t+i}return function(t,i,s,o){if(!i.offset)return!1;var n=(t.positionAbs||t.position.absolute).left+t.margins.left,r=(t.positionAbs||t.position.absolute).top+t.margins.top,a=n+t.helperProportions.width,l=r+t.helperProportions.height,p=i.offset.left,c=i.offset.top,d=p+i.proportions().width,h=c+i.proportions().height;switch(s){case"fit":return p<=n&&a<=d&&c<=r&&l<=h;case"intersect":return p<n+t.helperProportions.width/2&&a-t.helperProportions.width/2<d&&c<r+t.helperProportions.height/2&&l-t.helperProportions.height/2<h;case"pointer":return e(o.pageY,c,i.proportions().height)&&e(o.pageX,p,i.proportions().width);case"touch":return(r>=c&&r<=h||l>=c&&l<=h||r<c&&l>h)&&(n>=p&&n<=d||a>=p&&a<=d||n<p&&a>d);default:return!1}}}();return e.ui.ddmanager={current:null,droppables:{default:[]},prepareOffsets:function(t,i){var s,o,n=e.ui.ddmanager.droppables[t.options.scope]||[],r=i?i.type:null,a=(t.currentItem||t.element).find(":data(ui-droppable)").addBack();e:for(s=0;s<n.length;s++)if(!(n[s].options.disabled||t&&!n[s].accept.call(n[s].element[0],t.currentItem||t.element))){for(o=0;o<a.length;o++)if(a[o]===n[s].element[0]){n[s].proportions().height=0;continue e}n[s].visible="none"!==n[s].element.css("display"),n[s].visible&&("mousedown"===r&&n[s]._activate.call(n[s],i),n[s].offset=n[s].element.offset(),n[s].proportions({width:n[s].element[0].offsetWidth,height:n[s].element[0].offsetHeight}))}},drop:function(i,s){var o=!1;return e.each((e.ui.ddmanager.droppables[i.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&t(i,this,this.options.tolerance,s)&&(o=this._drop.call(this,s)||o),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,s)))}),o},dragStart:function(t,i){t.element.parentsUntil("body").on("scroll.droppable",function(){t.options.refreshPositions||e.ui.ddmanager.prepareOffsets(t,i)})},drag:function(i,s){i.options.refreshPositions&&e.ui.ddmanager.prepareOffsets(i,s),e.each(e.ui.ddmanager.droppables[i.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var o,n,r,a=t(i,this,this.options.tolerance,s),l=!a&&this.isover?"isout":a&&!this.isover?"isover":null;l&&(this.options.greedy&&(n=this.options.scope,(r=this.element.parents(":data(ui-droppable)").filter(function(){return e(this).droppable("instance").options.scope===n})).length&&((o=e(r[0]).droppable("instance")).greedyChild="isover"===l)),o&&"isover"===l&&(o.isover=!1,o.isout=!0,o._out.call(o,s)),this[l]=!0,this["isout"===l?"isover":"isout"]=!1,this["isover"===l?"_over":"_out"].call(this,s),o&&"isout"===l&&(o.isout=!1,o.isover=!0,o._over.call(o,s)))}})},dragStop:function(t,i){t.element.parentsUntil("body").off("scroll.droppable"),t.options.refreshPositions||e.ui.ddmanager.prepareOffsets(t,i)}},!1!==e.uiBackCompat&&e.widget("ui.droppable",e.ui.droppable,{options:{hoverClass:!1,activeClass:!1},_addActiveClass:function(){this._super(),this.options.activeClass&&this.element.addClass(this.options.activeClass)},_removeActiveClass:function(){this._super(),this.options.activeClass&&this.element.removeClass(this.options.activeClass)},_addHoverClass:function(){this._super(),this.options.hoverClass&&this.element.addClass(this.options.hoverClass)},_removeHoverClass:function(){this._super(),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass)}}),e.ui.droppable})?s.apply(t,o):s)||(e.exports=n)}}]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.auiplugin:split_jquery.ui.droppable', location = 'aui.chunk.81d23ddb85c16442c4a0--5bb850f66f03da25d595.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["jquery.ui.droppable"],{qe8r:function(u,i,n){"use strict";n.r(i);n("PTCr");i.default="jquery"}},[["qe8r","runtime","aui.splitchunk.vendors--20a97d6a33","aui.splitchunk.vendors--d18e3cafa7","aui.splitchunk.vendors--db57146687","aui.splitchunk.vendors--351ae504d7","aui.splitchunk.vendors--9c8c8c1546","aui.splitchunk.vendors--51504ebf10","aui.splitchunk.vendors--06bc6ae5d7","aui.splitchunk.vendors--825acd7236","aui.splitchunk.0d131bcbf1"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-logger', location = 'includes/js/rapid/Logger.js' */
define("jira-agile/rapid/logger",["underscore","jira/util/logger"],function(e,t){var a={ajax:"ajax",event:"event",state:"state",ui:"ui",callback:"callback",refresh:"refresh"};return{Contexts:a,enabledContexts:[a.ajax,a.event,a.state,a.ui,a.callback,a.refresh],log:function(a,i){if(!i||e.contains(this.enabledContexts,i)){var n=i?this.pad(i,6)+": "+a:a;t.log(n)}},pad:function(e,t){return e.length<t?e+"                               ".substr(0,t-e.length):e},timeMap:{},timeStart:function(e){if(!e)throw"start: If starting a timer manually a name must be set";this.timeMap[e]=(new Date).getTime()},timeStop:function(e){if(!(e in this.timeMap))throw"stop:"+e+" not found";var t=(new Date).getTime()-this.timeMap[e];delete this.timeMap[e],this.log(t+"ms on "+e,this.Contexts.ui)}}}),AJS.namespace("GH.Logger",null,require("jira-agile/rapid/logger")),AJS.namespace("GH.log",null,require("jira-agile/rapid/logger").log);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:events', location = 'includes/js/rapid/Events.js' */
define("jira-agile/rapid/events",["require"],function(t){function n(t){return t?e.extend(t,n.prototype):t=this,t._bindings=[],t}var e=t("underscore");return n.prototype={on:function(t,n,e){return e&&this._off({type:t,context:e}),this._bindings.push({type:t,handler:n,context:e}),this},off:function(t){return e.isString(t)?this._off({type:t}):e.isFunction(t)?this._off({handler:t}):e.isObject(t)&&this._off({context:t}),this},_off:function(t){this._bindings=e.reject(this._bindings,function(n){var e=!t.type||t.type===n.type,i=!t.context||t.context===n.context,r=!t.handler||t.handler===n.handler;return e&&i&&r})},trigger:function(t){var n=Array.prototype.slice.call(arguments,1);return e.each(this._bindings,function(e){e.type===t&&e.handler.apply(e.context,n)}),this}},n}),AJS.namespace("GH.Events",null,require("jira-agile/rapid/events"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:global-events', location = 'includes/js/rapid/GlobalEvents.js' */
define("jira-agile/rapid/global-events",["require"],function(e){"use strict";var i=e("jira-agile/rapid/events");return new i});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-config', location = 'includes/js/gh-config.js' */
define("jira-agile/config",[],function(){"use strict";var i={};return i.isSampleDataAvailable=!1,i}),AJS.namespace("GH.Config",null,require("jira-agile/config"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:storage', location = 'includes/js/rapid/state/storage.js' */
define("jira-agile/rapid/state/storage",[],function(){"use strict";var e={};return e.get=function(e,t){var r=void 0;if(r=t?sessionStorage.getItem(e):localStorage.getItem(e),"string"!=typeof r)return r;try{return JSON.parse(r)}catch(e){return GH.log("GH.storage: Caught error: "+e),void GH.log("GH.storage: Cannot parse json string retrieved from "+(t?"session":"local")+" storage. Ignoring value "+r)}},e.put=function(e,t,r){if(null===t||void 0===t)r?sessionStorage.removeItem(e):localStorage.removeItem(e);else try{r?sessionStorage.setItem(e,JSON.stringify(t)):localStorage.setItem(e,JSON.stringify(t))}catch(e){return!1}return!0},window.localStorage&&window.sessionStorage||(e.get=function(){return null},e.put=function(){return!1}),e}),AJS.namespace("GH.storage",null,require("jira-agile/rapid/state/storage"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:validation', location = 'includes/js/rapid/Validation.js' */
define("jira-agile/rapid/validation",["jira/util/formatter","jquery"],function(r,t){var n={};return n.notBlank=function(r,e,a){var i=n.applyRules(r,e,a,function(){return t.trim(t(r).val()).length>0});return i},n.maxLength=function(r,e,a,i){var o=n.applyRules(r,a,i,function(){return t.trim(t(r).val()).length<=e});return o},n.isPositiveInteger=function(r,e,a){var i=n.applyRules(r,e,a,function(){return/^\d*$/.test(t.trim(t(r).val()))});return i},n.applyRules=function(r,e,a,i){r=t(r);var o=a?t(a):r,l=i.call(this);return!!l||(n.showContextualErrors(o,e),!1)},n.showContextualErrors=function(t,e,a,i,o){n.clearContextualErrors(t),t.after(GH.tpl.rapid.notification.renderContextualErrors({errors:[{message:r.I18n.getText(e,a,i,o)}]}))},n.clearContextualErrors=function(r){r=t(r),r.find(".ghx-error").remove(),r.siblings(".ghx-error").remove()},n}),AJS.namespace("GH.Validation",null,require("jira-agile/rapid/validation"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:help-paths', location = 'includes/js/gh-helppaths.js' */
AJS.namespace("GH.HelpPaths"),GH.HelpPaths.getHelpPath=function(e){return null};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:message', location = 'includes/js/gh-message.js' */
define("jira-agile/message",["jira/util/logger"],function(e){"use strict";var a={};return a.showIssueMessage=function(){e.warn("[deprecated] GH.Message.showIssueMessage has no effect, @see module:jira-agile/rapid/ui/notification")},a.showSuccess=function(){e.warn("[deprecated] GH.Message.showSuccess has no effect, @see module:jira-agile/rapid/ui/notification")},a.showWarning=function(){e.warn("[deprecated] GH.Message.showWarning has no effect, @see module:jira-agile/rapid/ui/notification")},a.showPersistentWarning=function(){e.warn("[deprecated] GH.Message.showPersistentWarning has no effect, @see module:jira-agile/rapid/ui/notification")},a}),AJS.namespace("GH.Message",null,require("jira-agile/message"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:util', location = 'includes/js/rapid/Util.js' */
define("jira-agile/rapid/util",["underscore","jquery"],function(e,r){var n={};return n.getPluginKey=function(){return r('meta[name="ghx-plugin-key"]').attr("content")},n.wrapPreventDefault=function(e,r){return function(n){return n.preventDefault(),e.apply(r||this,arguments)}},n.andThenSplice=function(){return{_needsSplice:Array.prototype.slice.call(arguments)}},n.andThen=function(n){var t=[{trigger:"resolveWith",on:"done",filter:arguments[1]},{trigger:"rejectWith",on:"fail",filter:arguments[2]},{trigger:"notifyWith",on:"progress",filter:arguments[3]}];return r.Deferred(function(r){e.each(t,function(t){var i=t.on,a=t.trigger,o=e.isFunction(t.filter)&&t.filter;n[i](function(){var n=o&&o.apply(this,arguments);if(n&&e.isFunction(n.promise))n.promise().done(r.resolve).fail(r.reject).progress(r.notify);else{var t;t=n&&e.isArray(n._needsSplice)?n._needsSplice:o?[n]:arguments,r[a](e.isFunction(this.promise)?r.promise():this,t)}})})}).promise()},r._OriginalDeferred=r.Deferred,r.Deferred=function(e){return r._OriginalDeferred(function(r){r.andThen=function(){var e=Array.prototype.slice.call(arguments);return e.unshift(r),n.andThen.apply(this,e)};var t=r.promise;r.promise=function(){var e=t.apply(this,arguments);return e.andThen=function(){var r=Array.prototype.slice.call(arguments);return r.unshift(e),n.andThen.apply(this,r)},e},e&&e.call(r,r)})},n.indexOf=function(r,n,t){var i=-1;return t&&(r=r.slice().reverse()),e.some(r,function(e,a){return!!n(e,a)&&(i=t?r.length-1-a:a,!0)}),i},n.swapArrayItems=function(e,r,n){var t;return!!(e&&r!==n&&Math.min(r,n)>=0&&Math.max(r,n)<e.length)&&(t=e[n],e[n]=e[r],e[r]=t,!0)},n.roundToDecimalPlaces=function(e,r){if("number"!=typeof e||e===Math.round(e))return e;if(0===r)return Math.round(e);var n=e.toString();if(n.indexOf("e")>=0)return e;var t=e<0,i=n.split("."),a=+i[1].charAt(r);if(i[1]=i[1].substr(0,r),a>5||!t&&5===a){var o=/^0*/.exec(i[1])[0];i[1]=o+(+i[1]+1),i[1].length>r&&(o?i[1]=i[1].substr(i[1].length-r):(i[0]=+i[0]+1,i[1]=0))}return parseFloat(i.join("."))},n}),AJS.namespace("GH.Util",null,require("jira-agile/rapid/util"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:user-data', location = 'includes/js/rapid/UserData.js' */
define("jira-agile/rapid/user-data",["jira/util/data/meta"],function(e){var r={};return r.remoteUser=e.get("remote-user"),r.loggedIn=!!r.remoteUser,r.hasUser=function(){return r.loggedIn},r.getUserName=function(){return r.remoteUser},r.canCreateProject=function(){return r.userConfig.canCreateProject},r.setGlobalConfig=function(e){r.quickCreateDefaultIssueTypeId=e.quickCreateDefaultIssueTypeId,r.userConfig=e.userConfig,r.userConfig&&r.userConfig.canCreateProject&&(r.userConfig.canCreateProject=!GH.Features.DISABLE_CREATE_PROJECT.isEnabled())},r}),AJS.namespace("GH.UserData",null,require("jira-agile/rapid/user-data"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:features', location = 'includes/js/gh-features.js' */
define("jira-agile/gh-features",["jira/ajs/dark-features"],function(e){"use strict";function a(e){this.featureKey=e,this.featureEmergencyDisabledKey=e+".emergencyDisabled"}return a.prototype.isEnabled=function(){return e.isEnabled(this.featureKey)&&!e.isEnabled(this.featureEmergencyDisabledKey)},Object.freeze({DISABLE_CREATE_PROJECT:new a("jag.DISABLE_CREATE_PROJECT"),EDITABLE_DETAIL_VIEW_ENABLED:new a("com.atlassian.jira.agile.darkfeature.editable.detailsview"),SPLIT_ISSUE:new a("com.atlassian.jira.agile.darkfeature.splitissue")})}),AJS.namespace("GH.Features",null,require("jira-agile/gh-features"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:navigation', location = 'includes/js/gh/navigation/Navigation.js' */
define("jira-agile/rapid/navigation",["wrm/context-path"],function(a){"use strict";var n={};return n.gotoDefaultRapidBoardPage=function(a,i){window.location.href=n.getDefaultRapidBoardPageUrl(a,i)},n.getDefaultRapidBoardPageUrl=function(a,i){var e=_.isUndefined(i)?"":"&view="+i;return n.getCanonicRapidBoardPageUrl(a)+e},n.getCanonicRapidBoardPageUrl=function(n){return a()+"/secure/RapidBoard.jspa?rapidView="+n},n.getLoginUrl=function(){return a()+"/login.jsp?os_destination="+encodeURIComponent(window.location.href)},n.updateWindowLocation=function(a){return window.location.assign(a)},n}),AJS.namespace("GH.Navigation",null,require("jira-agile/rapid/navigation"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-dialog', location = 'includes/js/dialog/dialog.js' */
AJS.namespace("GH.Dialog"),GH.Dialog.baseExtension={_getDefaultOptions:function(){return AJS.$.extend(this._super(),{onDialogFinished:function(){this.handleSuccess()}})},_performRedirect:function(){this.redirected=!0,this.handleSuccess()},hide:function(e,n){return JIRA.Dialog.current===this&&(this._super(e),void(n===!0||!n||n.reason!==JIRA.Dialog.HIDE_REASON.escape&&n.reason!==JIRA.Dialog.HIDE_REASON.cancel||this.doCancelCleanup()))},doCancelCleanup:function(){},handleSuccess:function(){}};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-dialog', location = 'includes/js/rapid/ops/DialogExtension.js' */
AJS.namespace("GH.Dialog.XBoardExtension"),GH.Dialog.XBoardExtension.getActionInfo=function(e){var t="workflow-transition-",a=e.options.id;return a.substring(0,t.length)===t?{formId:a,isTransition:!0,transitionId:a.substring(t.length)}:{formId:a,isTransition:!1}},GH.Dialog.XBoardExtension.handleSuccess=function(){GH.IssueOperationShortcuts.clearOverrideSelectedIssue(),this.hide(!0,!0),this._hideloadingIndicator();var e=GH.Dialog.XBoardExtension.getActionInfo(this),t=GH.IssueOperationShortcuts.getSelectedIssueId(),a=GH.IssueOperationShortcuts.getSelectedIssueKey();GH.WorkController.isActive()?GH.Dialog.handleWorkModeUpdate(t,a,e):GH.PlanController.isActive()&&GH.Dialog.handlePlanModeUpdate(t,a,e),AJS.$(GH).trigger("issueTransitioned",{issueId:t}),GH.Notification.showIssueMessage(t),GH.WorkDragAndDrop.transitionTargetStatus=void 0},GH.Dialog.handlePlanModeUpdate=function(e,t,a){if(!GH.IssueOperationShortcuts.isOverrideSelectedIssueActive()){var o=0,r=null,s=GH.BacklogModel.findModelWithIssue(t);s&&(GH.SprintModel.isSprintModel(s)&&(r=s.getSprintId()),o=s.getIssueList().getIssueIndex(t)),GH.PlanController.reload(function(){var e=GH.BacklogSelectionController.getSelectedIssueKey();if(!e){var t=null;r&&(t=GH.BacklogModel.getSprintModel(r)),t||(t=GH.BacklogModel.getBacklogModel2()),GH.BacklogSelectionController.selectIssue(t.getIssueList().getIssueKeyAtIndex(o))}})}},GH.Dialog.handleWorkModeUpdate=function(e,t,a){var o=GH.Dialog.createParentCompletedCheckCallback(t),r=GH.Dialog.createSelectMostAppropriateIssueCallback(t);if(a.isTransition)if(GH.WorkDragAndDrop.transitionTargetStatus){var s=GH.WorkDragAndDrop.transitionTargetStatus,n=GH.GridDataController.getModel(),i=n.getIssueDataByKey(t),l=n.getIssuePositionByKey(t),d=n.getColumns(),u=l.columnId,c=GH.GridColumnsData.getColumnIdForStatus(d,s);n.moveIssueToColumn(t,u,c),i.status=s,n.updateIssueData(i),GH.SwimlaneView.rerenderCell(l.swimlaneId,u),GH.SwimlaneView.rerenderCell(l.swimlaneId,c),o()}else GH.CallbackManager.registerCallback(GH.WorkController.CALLBACK_POOL_LOADED,"ParentWithAllSubsCompletedCheck",o);GH.CallbackManager.registerCallback(GH.WorkController.CALLBACK_POOL_LOADED,"SelectMostAppropriateIssueCallback",r),GH.WorkController.reload()},GH.Dialog.createParentCompletedCheckCallback=function(e){return function(){var t=GH.GridDataController.getModel();if(!_.isUndefined(t.data.issues[e])&&!_.isUndefined(t.data.issues[e].parentKey)){var a=t.data.issues[e].parentKey;if(_.contains(t.data.columnIssueMapping[_.last(t.data.columns).id],e)){var o=t.data.columnIssueMapping[_.last(t.data.columns).id],r=_.toArray(t.data.issues).reduce(function(e,t){return o.indexOf(t.key)>-1?e:e.concat(t.key)},[]),s=_.find(r,function(e){return t.data.issues[e].parentKey===a});s||GH.WorkController.resolveParentTask(t.data.issues[a])}}}},GH.Dialog.createSelectMostAppropriateIssueCallback=function(e){var t=GH.GridDataController.getModel().getIssuePositionByKey(e);return function(){var a=GH.GridDataController.getModel().getIssueDataByKey(e);if(!a){var o=GH.GridDataController.getModel().getIssueKeyAtPosition(t);o?(GH.WorkSelectionController.selectIssue(o,{pushState:!1,dontUpdateUI:!0}),GH.RapidBoard.State.replaceState()):(GH.WorkSelectionController.selectIssue(void 0,{pushState:!1,dontUpdateUI:!0}),GH.RapidBoard.State.replaceState())}}},GH.Dialog.XBoardExtension.doCancelCleanup=function(){GH.IssueOperationShortcuts.clearOverrideSelectedIssue(),GH.WorkDragAndDrop.transitionTargetStatus=void 0};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-dialog', location = 'includes/js/dialog/form-dialog.js' */
AJS.namespace("GH.FormDialog"),GH.FormDialog=JIRA.FormDialog.extend(GH.Dialog.baseExtension),GH.FormDialog=GH.FormDialog.extend(GH.Dialog.XBoardExtension);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-dialog', location = 'includes/js/dialog/labels-dialog.js' */
define("jira-agile/dialog/labels-dialog",["jira/dialog/labels-dialog"],function(i){var n=i.extend(GH.Dialog.baseExtension);return n=n.extend(GH.Dialog.XBoardExtension),n=n.extend({_handleSubmitResponse:function(i,n,t){this.serverIsDone&&(this.options.onSuccessfulSubmit&&this.options.onSuccessfulSubmit.call(this,i,n,t),this.options.autoClose&&this.hide(),this.options.onDialogFinished&&this.options.onDialogFinished.call(this,i,n,t))},decorateContent:function(){this._super();var i=this.get$popupContent(),n=i.find("input[name=customFieldId]");if(1===n.length){var t=i.find("#hint");t.hide()}}})});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-dialog', location = 'includes/js/rapid/ui/dialog/DialogUtil.js' */
!function(){AJS.namespace("GH.Dialog");var n=require("aui/dialog");GH.Dialog.create=function(e){AJS.$("#"+e.id).remove(),e=_.defaults(e,{bindEscape:!0});var i=new n(e),a=i.show,o=function(){GH.Dialog.preShow(),a.call(i);var n=AJS.$("#"+e.id).find(".dialog-button-panel .aui-button");n.is(":enabled")&&n.focus(),AJS.$("#"+e.id).addClass("ghx-dialog")};return _.extend(i,{dispose:GH.Dialog.generateCloseHandler(i),cancel:GH.Dialog.generateCloseHandler(i,e.onCancelFn),submit:GH.Dialog.generateCloseHandler(i,e.onConfirmFn),show:o,disableControls:GH.Dialog.generateDisableControlsFn(i),enableControls:GH.Dialog.generateEnableControlsFn(i),showSpinner:GH.Dialog.generateShowSpinnerFn(i),hideSpinner:GH.Dialog.generateHideSpinnerFn(i)}),GH.Dialog.bindEscapeHandler(i,!e.bindEscape),i},GH.Dialog.addCancelButton=function(n){n.addCancel("\u53d6\u6d88",function(e){n.cancel(n)})},GH.Dialog.bindEscapeHandler=function(n,e){e?JIRA.bind("Dialog.beforeHide",function(e,i,a,o){o===n.id&&a===JIRA.Dialog.HIDE_REASON.escape&&e.preventDefault()}):AJS.$(document).bind("keyup."+n.id,function(e){27==e.keyCode&&n.cancel(n)})},GH.Dialog.generateCloseHandler=function(n,e){return function(){return n.remove(),GH.Dialog.undim(),AJS.$(document).unbind("keyup."+n.id),AJS.unbind("remove.dialog"),e&&e(n),!1}},GH.Dialog.generateDisableControlsFn=function(n){return function(){AJS.$("#"+n.id+" button").attr("disabled","disabled")}},GH.Dialog.generateEnableControlsFn=function(n){return function(){AJS.$("#"+n.id+" button").removeAttr("disabled")}},GH.Dialog.generateShowSpinnerFn=function(n){return function(){AJS.$("#"+n.id).find(".button-panel-cancel-link").css("visibility","hidden").before('<span class="ghx-spinner"/>')}},GH.Dialog.generateHideSpinnerFn=function(n){return function(){AJS.$("#"+n.id).find(".ghx-spinner").remove().end().find(".button-panel-cancel-link").css("visibility","visible")}},GH.Dialog.sanitizeAJSDim=function(){var n=AJS.dim;AJS.dim=function(){try{n.apply(this,arguments)}catch(n){}}},GH.Dialog.registerDialog=function(n,e,i){AJS.$(document).undelegate(n,"click").delegate(n,"click",function(n){var a=AJS.$(this);a.hasClass("disabled")||(n.preventDefault(),e(n,i))})},GH.Dialog.dim=function(){AJS.dim(),AJS.$(".aui-blanket").html('<div class="jira-spinner-container"><aui-spinner size="medium" class="jira-page-loading-indicator"></aui-spinner></div>')},GH.Dialog.undim=function(){AJS.undim(),AJS.$(".aui-blanket").empty()},GH.Dialog.preShow=function(){AJS.$(".aui-blanket").empty()}}();
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-persistent-messages', location = 'includes/js/rapid/ui/persistent-messages/CreateViewMessage.js' */
AJS.namespace("GH.PersistentMessages.CreateViewMessage"),GH.PersistentMessages.CreateViewMessage={},GH.PersistentMessages.CreateViewMessage.setView=function(e,s,a,t){var r={id:e,name:s,type:a,subType:t};GH.storage.put("gh.createViewMessage",r,!0)},GH.PersistentMessages.CreateViewMessage.clear=function(){GH.storage.put("gh.createViewMessage",null,!0)},GH.PersistentMessages.CreateViewMessage.renderMessage=function(e){e&&GH.log(e.type+" handled","GH.PersistentMessages.CreateViewMessage.renderMessage");var s=GH.storage.get("gh.createViewMessage",!0);if(s){var a=null;s.name=AJS.escapeHTML(String(s.name));var t;"advanced"==s.type?t=GH.tpl.board.x.renderCreateViewMessage:"kanban"==s.subType?t=GH.tpl.board.x.renderWelcomeViewMessageKanban:"scrum"==s.subType&&(t=GH.tpl.board.x.renderWelcomeViewMessageScrum),a=t({rapidView:s,url:AJS.parseUri(window.location.href)}),GH.Notification.showSuccess(a,{title:"\u521b\u5efa\u6210\u529f",timeout:1e4}),GH.PersistentMessages.CreateViewMessage.clear()}};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-persistent-messages', location = 'includes/js/rapid/ui/persistent-messages/WelcomeMessage.js' */
AJS.namespace("GH.PersistentMessages.WelcomeMessage"),GH.PersistentMessages.WelcomeMessage={},GH.PersistentMessages.WelcomeMessage.show=function(e){GH.storage.put("gh.welcomeMessage",e,!0)},GH.PersistentMessages.WelcomeMessage.renderMessage=function(){var e=GH.storage.get("gh.welcomeMessage",!0);if(e){var s=null;e.rapidView.name=AJS.escapeHTML(String(e.rapidView.name)),e.project.name=AJS.escapeHTML(String(e.project.name));var a=null;/kanban/.test(e.preset)?a=e.sample?GH.tpl.board.x.renderWelcomeProjectSampleMessageKanban:GH.tpl.board.x.renderWelcomeProjectMessageKanban:/scrum/.test(e.preset)&&(a=e.sample?GH.tpl.board.x.renderWelcomeProjectSampleMessageScrum:GH.tpl.board.x.renderWelcomeProjectMessageScrum),s=a({project:e.project,rapidView:e.rapidView,url:AJS.parseUri(window.location.href)}),GH.Notification.showSuccess(s,{title:"\u521b\u5efa\u6210\u529f",timeout:1e4}),GH.storage.put("gh.welcomeMessage",null,!0)}};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-project-picker', location = 'includes/js/projects/project-picker.js' */
define("jira-agile/projects/project-picker",[],function(){"use strict";var t=function(t){if(!t||!t.selector)throw new Error("Project picker requires a selector");this.$select=AJS.$(t.selector),t.currentProjectId&&(this.currentProjectId=t.currentProjectId,this.currentProject=null),this.multiselect=new AJS.MultiSelect({element:this.$select,width:300,itemAttrDisplayed:"label",ajaxOptions:{url:GH.Ajax.buildRestUrl("/project.json"),formatResponse:this._processAjax.bind(this)}}),"function"==typeof t.change&&(this.$select.bind("unselect",t.change),this.$select.bind("change",t.change)),this.currentProjectId&&this.multiselect.requestSuggestions(!0).done(function(){this.currentProject&&this.multiselect.addItem(this._getProjectDescriptor(this.currentProject))}.bind(this))};return t.prototype.show=function(){AJS.warn("ProjectPicker.show is deprecated. It is no longer needed as all initialisation occurs in the constructor")},t.prototype.hasValue=function(){return!!this.$select.val()},t.prototype.hasErrors=function(){return this.multiselect.$container.parent().find(".error").length>0},t.prototype.getValue=function(){return this.$select.val()},t.prototype.getElement=function(){return this.$select},t.prototype._findProject=function(t,e){return(t||[]).filter(function(t){return e&&t.id===e}.bind(this))[0]},t.prototype._findUnmatchingProjects=function(t,e){return(t||[]).filter(function(t){return!e||t.id!==e}.bind(this))},t.prototype._processAjax=function(t){if(!t||!t.projects||!t.projects.length)return[];this.currentProject=this._findProject(t.projects,this.currentProjectId);var e=this._findUnmatchingProjects(t.projects,this.currentProjectId).map(this._getProjectDescriptor.bind(this));return this.currentProject?[this._getProjectDescriptor(this.currentProject)].concat(e):e},t.prototype._getProjectDescriptor=function(t){if(!t)throw new Error("ProjectPicker.prototype._getProjectDescriptor requires a project");return new AJS.ItemDescriptor({value:t.id+"",label:t.displayName,html:AJS.escapeHTML(String(t.displayName))})},t}),AJS.namespace("GH.ProjectPicker",null,require("jira-agile/projects/project-picker"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-create-board', location = 'includes/js/rapid/ui/startwizard/WizardView.soy' */
// This file was automatically generated from WizardView.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace GH.tpl.startwizard.
 */

if (typeof GH == 'undefined') { var GH = {}; }
if (typeof GH.tpl == 'undefined') { GH.tpl = {}; }
if (typeof GH.tpl.startwizard == 'undefined') { GH.tpl.startwizard = {}; }


GH.tpl.startwizard.renderMethodologySelectionStep = function(opt_data, opt_ignored) {
  return '<form class="aui ghx-halved-vertical"><div class="aui-group"><div class="aui-item"><h2>' + soy.$$escapeHtml('Scrum') + '</h2><p>' + soy.$$escapeHtml('Scrum \u4fa7\u91cd\u4e8e\u8ba1\u5212\u3001\u6267\u884c\u3001\u4ea4\u4ed8\u547d\u540d\u4e3aSprint\u7684\u65f6\u95f4\u9a71\u52a8\u7684\u5de5\u4f5c\u4efb\u52a1\u3002') + '</p></div><div class="aui-item"><h2>' + soy.$$escapeHtml('\u770b\u677f') + '</h2><p>' + soy.$$escapeHtml('Kanban\u4fa7\u91cd\u4e8e\u5c06\u5de5\u4f5c\u6d41\u7a0b\u548c\u5728\u5236\u54c1\u53ef\u89c6\u5316\uff0c\u4ee5\u4fc3\u8fdb\u589e\u91cf\u6539\u8fdb\u73b0\u6709\u7684\u6d41\u7a0b\u3002') + '</p></div></div><div class="aui-group ghx-button-group"><div class="aui-item"><button id="ghx-wizard-methodology-scrum" class="aui-button">' + soy.$$escapeHtml('\u521b\u5efa\u4e00\u4e2aScrum\u9762\u677f') + '</button></div><div class="aui-item"><button id="ghx-wizard-methodology-kanban" class="aui-button">' + soy.$$escapeHtml('\u521b\u5efa\u4e00\u4e2a\u770b\u677f') + '</button></div></div>' + ((opt_data.isSampleDataAvailable && opt_data.canCreateProject) ? '<div class="aui-group"><div class="aui-item"><button id="ghx-wizard-methodology-scrum-sample" class="aui-button aui-button-link">' + soy.$$escapeHtml('\u521b\u5efa\u4e00\u4e2aScrum\u677f\u548c\u793a\u4f8b\u6570\u636e') + '</button></div><div class="aui-item"><button id="ghx-wizard-methodology-kanban-sample" class="aui-button aui-button-link">' + soy.$$escapeHtml('\u521b\u5efa\u4e00\u4e2a\u770b\u677f\u793a\u4f8b') + '</button></div></div>' : '') + '</form>';
};
if (goog.DEBUG) {
  GH.tpl.startwizard.renderMethodologySelectionStep.soyTemplateName = 'GH.tpl.startwizard.renderMethodologySelectionStep';
}


GH.tpl.startwizard.renderMethodSelectionStep = function(opt_data, opt_ignored) {
  return '<div class="aui-group ghx-split" id="js-method-choice"><div class="aui-item"><form class="aui"><fieldset class="group ghx-space-saver">' + ((opt_data.canCreateProject) ? '<div class="radio"><input class="radio" type="radio" id="ghx-wizard-method-new-project" name="ghx-wizard-method" value="newProject" ' + ((opt_data.selectedOption == 'newProject') ? 'checked="checked"' : '') + '><label for="ghx-wizard-method-new-project">' + soy.$$escapeHtml('\u65b0\u8f6f\u4ef6\u9879\u76ee\u677f') + '</label><div class="description">' + soy.$$escapeHtml('\u4e00\u79cd\u65b0\u7684\u57fa\u4e8e\u4e00\u4e2a\u65b0\u8f6f\u4ef6\u9879\u76ee\u7684\u677f') + '</div></div>' : '') + ((opt_data.hasProjectsAccessible) ? '<div class="radio"><input class="radio" type="radio" id="ghx-wizard-method-existing-project" name="ghx-wizard-method" value="existingProject" ' + ((opt_data.selectedOption == 'existingProject') ? 'checked="checked"' : '') + '><label for="ghx-wizard-method-existing-project">' + soy.$$escapeHtml('\u9762\u677f\u4f9d\u8d56\u6765\u4e8e\u4e00\u4e2a\u5df2\u6709\u9879\u76ee') + '</label><div class="description">' + soy.$$escapeHtml('\u9762\u677f\u4e0a\u53ef\u5305\u542b\u4e00\u4e2a\u6216\u591a\u4e2a\u9879\u76ee\u3002') + '</div></div>' : '') + ((opt_data.hasFiltersAccessible) ? '<div class="radio"><input class="radio" type="radio" id="ghx-wizard-method-existing-filter" name="ghx-wizard-method" value="existingFilter" ' + ((opt_data.selectedOption == 'existingFilter') ? 'checked="checked"' : '') + '><label for="ghx-wizard-method-existing-filter">' + soy.$$escapeHtml('\u9762\u677f\u4f9d\u8d56\u6765\u4e8e\u4e00\u4e2a\u5df2\u6709\u7684\u7b5b\u9009\u5668') + '</label><div class="description">' + soy.$$escapeHtml('\u4f7f\u7528JQL\u7b5b\u9009\u5668\u7684\u9ad8\u7ea7\u9009\u9879\u3002') + '</div></div>' : '') + '</fieldset></form></div><div class="aui-item"></div></div>';
};
if (goog.DEBUG) {
  GH.tpl.startwizard.renderMethodSelectionStep.soyTemplateName = 'GH.tpl.startwizard.renderMethodSelectionStep';
}


GH.tpl.startwizard.renderProjectSelectionStep = function(opt_data, opt_ignored) {
  return '<div class="aui-group ghx-split"><div class="aui-item"><form class="aui ajs-dirty-warning-exempt"><fieldset><div class="field-group"><label for="ghx-wizard-project-board-name">' + soy.$$escapeHtml('\u9762\u677f\u540d\u79f0') + '<span class="aui-icon icon-required"></span></label><input class="text" type="text" id="ghx-wizard-project-board-name" maxlength="155"></div><div class="field-group"><label for="ghx-wizard-choose-project">' + soy.$$escapeHtml('\u9879\u76ee') + '<span class="aui-icon icon-required"></span></label><select id="ghx-wizard-choose-project" multiple="multiple" class="hidden"></select><div class="description">' + soy.$$escapeHtml('\u9009\u62e9\u8981\u5305\u542b\u5728\u6b64\u9762\u677f\u4e2d\u7684\u9879\u76ee\u3002') + '</div></div></fieldset></form></div><div class="aui-item"></div></div>';
};
if (goog.DEBUG) {
  GH.tpl.startwizard.renderProjectSelectionStep.soyTemplateName = 'GH.tpl.startwizard.renderProjectSelectionStep';
}


GH.tpl.startwizard.renderFilterSelectionStep = function(opt_data, opt_ignored) {
  return '<div class="aui-group ghx-split"><div class="aui-item"><form class="aui"><fieldset><div class="field-group"><label for="ghx-wizard-filter-view-name">' + soy.$$escapeHtml('\u9762\u677f\u540d\u79f0') + '<span class="aui-icon icon-required"></span></label><input class="text" type="text" id="ghx-wizard-filter-view-name" name="name" maxlength="155"></div><div class="field-group" id="ghx-filter-picker"><label for="ghx-wizard-filter-select">' + soy.$$escapeHtml('\u5df2\u4fdd\u5b58\u7684\u7b5b\u9009\u5668') + '<span class="aui-icon icon-required"></span></label><select id="ghx-wizard-filter-select" class="hidden"></select><div id="ghx-filter-error"></div></div><div id="ghx-create-permissioninfo-container"></div></fieldset></form></div><div class="aui-item ghx-alignwith-textgroup"><h3>' + soy.$$escapeHtml('\u5df2\u4fdd\u5b58\u7b5b\u9009\u5668') + '</h3><div class="description">' + soy.$$escapeHtml('\u4ece\u5217\u8868\u4e2d\u9009\u62e9\u73b0\u6709\u7684\u8fc7\u6ee4\u5668\u7684\u57fa\u7840\u4e0a\u8fdb\u884c\u65b0\u7684\u4e3b\u677f\u3002\u521b\u5efa\u65b0\u7684\u5df2\u4fdd\u5b58\u7684\u8fc7\u6ee4\u5668\u3001\u4fdd\u5b58\u641c\u7d22\u7684\u95ee\u9898 \u201c\u5bfc\u822a\u5668\u201d\u3002') + '</div></div></div>';
};
if (goog.DEBUG) {
  GH.tpl.startwizard.renderFilterSelectionStep.soyTemplateName = 'GH.tpl.startwizard.renderFilterSelectionStep';
}


GH.tpl.startwizard.renderProjectCreationStep = function(opt_data, opt_ignored) {
  return '<div class="aui-group ghx-split"><div class="aui-item"><form class="aui"><fieldset><div class="field-group"><label for="ghx-wizard-' + soy.$$escapeHtml(opt_data.prefix) + '-projectname">' + soy.$$escapeHtml('\u9879\u76ee\u540d\u79f0') + '<span class="aui-icon icon-required"></span></label><input class="text" type="text" id="ghx-wizard-' + soy.$$escapeHtml(opt_data.prefix) + '-projectname" value="' + soy.$$escapeHtml(opt_data.sampleInfo.projectName) + '"></div><div class="field-group"><label for="ghx-wizard-' + soy.$$escapeHtml(opt_data.prefix) + '-projectkey">' + soy.$$escapeHtml('\u9879\u76ee\u5173\u952e\u5b57') + '<span class="aui-icon icon-required"></span></label><input class="text short-field" type="text" id="ghx-wizard-' + soy.$$escapeHtml(opt_data.prefix) + '-projectkey" value="' + soy.$$escapeHtml(opt_data.sampleInfo.projectKey) + '"></div><div class="field-group"><label for="ghx-wizard-' + soy.$$escapeHtml(opt_data.prefix) + '-projectlead">' + soy.$$escapeHtml('\u9879\u76ee\u4e3b\u7ba1') + '<span class="aui-icon icon-required"></span></label><select id="ghx-wizard-' + soy.$$escapeHtml(opt_data.prefix) + '-projectlead" class="js-default-user-picker hidden"><option' + ((opt_data.user.avatarUrl) ? ' style="background-image:url(\'' + soy.$$escapeHtml(opt_data.user.avatarUrl) + '\')"' : '') + 'selected="selected" value="' + soy.$$escapeHtml(opt_data.user.name) + '">' + soy.$$escapeHtml(opt_data.user.displayName) + '</option></select><div class="description">' + soy.$$escapeHtml('\u8f93\u5165\u7528\u6237\u540d\uff0c\u7cfb\u7edf\u4f1a\u63d0\u4f9b\u5339\u914d\u7684\u7528\u6237\u5217\u8868\u4f9b\u60a8\u9009\u62e9\u3002') + '</div></div></fieldset></form></div><div class="aui-item ghx-alignwith-textgroup"><h3>' + soy.$$escapeHtml('\u521b\u5efa\u4e00\u4e0b\u9879\u76ee') + '</h3><div class="description">' + soy.$$escapeHtml('\u8463\u4e8b\u4f1a\u5c06\u4f1a\u521b\u5efa\u60a8\u7684\u9879\u76ee, \u5e76\u5c06\u88ab\u547d\u540d\u4e3a\u5728\u60a8\u7684\u9879\u76ee\u4e2d\u3002\u60a8\u53ef\u4ee5\u91cd\u547d\u540d\u60a8\u7684\u4e3b\u677f\u5728\u4e3b\u677f\u7684\u914d\u7f6e\u5c4f\u5e55\u3002') + '</div></div></div>';
};
if (goog.DEBUG) {
  GH.tpl.startwizard.renderProjectCreationStep.soyTemplateName = 'GH.tpl.startwizard.renderProjectCreationStep';
}


GH.tpl.startwizard.renderPermissionInfo = function(opt_data, opt_ignored) {
  return '<div class="field-group"><label for="ghx-create-permissioninfo">' + soy.$$escapeHtml('\u5171\u4eab') + '</label>' + GH.tpl.rapid.view.renderPermissionEntries({permissionEntries: opt_data.savedFilter.permissionEntries}) + '</div><div class="field-group"><label>' + soy.$$escapeHtml('\u81ea\u5df1\u7684') + '</label><span class="field-value">' + soy.$$escapeHtml(opt_data.savedFilter.owner.displayName) + '</span></div>';
};
if (goog.DEBUG) {
  GH.tpl.startwizard.renderPermissionInfo.soyTemplateName = 'GH.tpl.startwizard.renderPermissionInfo';
}


GH.tpl.startwizard.createProjectTimeout = function(opt_data, opt_ignored) {
  return '' + soy.$$filterNoAutoescape(AJS.format('\u64cd\u4f5c\u5df2\u8d85\u65f6, \u8be5\u9879\u76ee\u548c\u4e3b\u677f\u53ef\u80fd\u5df2\u88ab\u521b\u5efa\u3002\u60a8\u53ef\u4ee5\u5728\u786e\u8ba4\u8be5\u68c0\u67e5 \x3ca href\x3d\x22{0}\x22\x3e\u7ba1\u7406\u4e3b\u677f\x3c/a\x3e\u4e00\u9875\u3002',opt_data.manageBoardURL));
};
if (goog.DEBUG) {
  GH.tpl.startwizard.createProjectTimeout.soyTemplateName = 'GH.tpl.startwizard.createProjectTimeout';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-create-board', location = 'includes/js/rapid/configuration/RapidView.soy' */
// This file was automatically generated from RapidView.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace GH.tpl.rapid.view.
 */

if (typeof GH == 'undefined') { var GH = {}; }
if (typeof GH.tpl == 'undefined') { GH.tpl = {}; }
if (typeof GH.tpl.rapid == 'undefined') { GH.tpl.rapid = {}; }
if (typeof GH.tpl.rapid.view == 'undefined') { GH.tpl.rapid.view = {}; }


GH.tpl.rapid.view.renderConfigurationHeader = function(opt_data, opt_ignored) {
  return '<div id="ghx-modes-tools">' + aui.buttons.button({text: '\u8fd4\u56de\u81f3\u677f', type: 'subtle', href: GH.Ajax.CONTEXT_PATH + '/secure/RapidBoard.jspa?rapidView=' + opt_data.rapidViewId + '&useStoredSettings=true', id: 'back-to-board', iconType: 'aui', iconText: '\u8fd4\u56de\u81f3\u677f', iconClass: 'aui-icon-small aui-iconfont-back-page'}) + '</div><h2>' + soy.$$escapeHtml('\u914d\u7f6e') + ' <span id="js-nav-view-name">' + soy.$$escapeHtml(opt_data.name) + '</span></h2>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderConfigurationHeader.soyTemplateName = 'GH.tpl.rapid.view.renderConfigurationHeader';
}


GH.tpl.rapid.view.renderConfigurationTabs = function(opt_data, opt_ignored) {
  var output = '<div id="ghx-config-header"></div>';
  var param20 = '<nav class="aui-navgroup aui-navgroup-vertical" aria-label="' + soy.$$escapeHtml('\u9762\u677f\u914d\u7f6e') + '"><div class="aui-nav-heading"><strong>' + soy.$$escapeHtml('\u914d\u7f6e') + '</strong></div><ul id="ghx-config-nav" class="aui-nav">';
  var configurationTabList26 = opt_data.configurationTabs;
  var configurationTabListLen26 = configurationTabList26.length;
  for (var configurationTabIndex26 = 0; configurationTabIndex26 < configurationTabListLen26; configurationTabIndex26++) {
    var configurationTabData26 = configurationTabList26[configurationTabIndex26];
    param20 += '<li data-tabitem="' + soy.$$escapeHtml(configurationTabData26.key) + '"><a href="' + soy.$$escapeHtml(GH.Ajax.CONTEXT_PATH) + '/secure/RapidView.jspa?rapidView=' + soy.$$escapeHtml(opt_data.rapidViewId) + '&amp;tab=' + soy.$$escapeHtml(configurationTabData26.key) + '">' + soy.$$escapeHtml(configurationTabData26.label) + '</a></li>';
  }
  param20 += '</ul></nav>';
  var param19 = '' + aui.page.pagePanelNav({content: param20});
  var param43 = '';
  var configurationTabList44 = opt_data.configurationTabs;
  var configurationTabListLen44 = configurationTabList44.length;
  for (var configurationTabIndex44 = 0; configurationTabIndex44 < configurationTabListLen44; configurationTabIndex44++) {
    var configurationTabData44 = configurationTabList44[configurationTabIndex44];
    param43 += '<div class="ghx-page-panel-content-item" id="' + soy.$$escapeHtml(configurationTabData44.key) + '">' + soy.$$filterNoAutoescape(configurationTabData44.html) + '</div>';
  }
  param19 += aui.page.pagePanelContent({extraClasses: 'ghx-config-panel-content', content: param43});
  output += aui.page.pagePanel({content: param19});
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderConfigurationTabs.soyTemplateName = 'GH.tpl.rapid.view.renderConfigurationTabs';
}


GH.tpl.rapid.view.renderViewNameEditForm = function(opt_data, opt_ignored) {
  var output = '';
  var label__soy55 = '\u9762\u677f\u540d\u79f0';
  output += '<label for="ghx-field-viewname">' + soy.$$escapeHtml(label__soy55) + '</label>' + GH.tpl.rapid.view.renderViewNameField({rapidViewName: opt_data.rapidViewName, canEdit: opt_data.canEdit, label: label__soy55});
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderViewNameEditForm.soyTemplateName = 'GH.tpl.rapid.view.renderViewNameEditForm';
}


GH.tpl.rapid.view.renderViewNameField = function(opt_data, opt_ignored) {
  return '<span class="field-value ' + ((opt_data.canEdit) ? 'js-edit-rapidViewName' : '') + '" data-fieldname="viewname" data-fieldvalue="' + soy.$$escapeHtml(opt_data.rapidViewName) + '" data-ariaLabel="' + soy.$$escapeHtml(opt_data.label) + '"><span id="ghx-field-viewname" ' + ((opt_data.canEdit) ? 'class="ghx-editable"' : '') + '>' + soy.$$escapeHtml(opt_data.rapidViewName) + '</span></span>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderViewNameField.soyTemplateName = 'GH.tpl.rapid.view.renderViewNameField';
}


GH.tpl.rapid.view.renderViewBoardAdminsEditForm = function(opt_data, opt_ignored) {
  var output = '<label for="ghx-field-view-board-admins">' + soy.$$escapeHtml('\u7ba1\u7406\u5458') + '</label><span  class="field-value' + ((opt_data.canEdit) ? ' js-edit-rapidViewBoardAdmin' : '') + '" ><span id="ghx-field-view-board-admins-span" ' + ((opt_data.canEdit) ? 'class="ghx-editable"' : '') + '>';
  var itemList91 = opt_data.model;
  var itemListLen91 = itemList91.length;
  if (itemListLen91 > 0) {
    for (var itemIndex91 = 0; itemIndex91 < itemListLen91; itemIndex91++) {
      var itemData91 = itemList91[itemIndex91];
      output += soy.$$escapeHtml(itemData91.displayName) + ((! (itemIndex91 == itemListLen91 - 1)) ? '<span>, </span>' : '');
    }
  } else {
    output += '<span class="ghx-fa">' + soy.$$escapeHtml('\u65e0') + '</span>';
  }
  output += '</span></span><span id="ghx-field-view-board-admins">' + ((opt_data.canUsePicker) ? '<select id="ghx-board-admins" multiple="multiple" class="hidden" aria-hidden="true"></select>' : '') + '</span>';
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderViewBoardAdminsEditForm.soyTemplateName = 'GH.tpl.rapid.view.renderViewBoardAdminsEditForm';
}


GH.tpl.rapid.view.renderBoardAdminsEditNoPicker = function(opt_data, opt_ignored) {
  var output = '<span class="ghx-margin-right"><strong>' + soy.$$escapeHtml('\u7ec4') + '</strong>: <input class="text medium-field" id="ghx-board-admin-edit-groups" value="';
  var itemList110 = opt_data.model.groupKeys;
  var itemListLen110 = itemList110.length;
  for (var itemIndex110 = 0; itemIndex110 < itemListLen110; itemIndex110++) {
    var itemData110 = itemList110[itemIndex110];
    output += soy.$$escapeHtml(itemData110.key) + ((! (itemIndex110 == itemListLen110 - 1)) ? ',' : '');
  }
  output += '" /></span><strong>' + soy.$$escapeHtml('\u7528\u6237') + '</strong>: <input class="text medium-field " id="ghx-board-admin-edit-users" value="';
  var itemList119 = opt_data.model.userKeys;
  var itemListLen119 = itemList119.length;
  for (var itemIndex119 = 0; itemIndex119 < itemListLen119; itemIndex119++) {
    var itemData119 = itemList119[itemIndex119];
    output += soy.$$escapeHtml(itemData119.key) + ((! (itemIndex119 == itemListLen119 - 1)) ? ',' : '');
  }
  output += '" /><div class="description">' + soy.$$escapeHtml('\u81ea\u5b9a\u4e49\u6309\u952e\u4e0e\u7ec4\u540d\u7528\u9017\u53f7\u5206\u9694') + '</div>';
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderBoardAdminsEditNoPicker.soyTemplateName = 'GH.tpl.rapid.view.renderBoardAdminsEditNoPicker';
}


GH.tpl.rapid.view.renderConfigPermissionsWarning = function(opt_data, opt_ignored) {
  return '' + ((opt_data.model.canEdit != true) ? '<div class="aui-message aui-message-warning"><p class="title"><span class="aui-icon icon-warning"></span><strong>' + soy.$$escapeHtml('\u8054\u7edc Jira \u6216\u9762\u677f\u7ba1\u7406\u5458\u4ee5\u5bf9\u6b64\u9762\u677f\u8fdb\u884c\u914d\u7f6e\u3002') + '</strong></p></div>' : '');
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderConfigPermissionsWarning.soyTemplateName = 'GH.tpl.rapid.view.renderConfigPermissionsWarning';
}


GH.tpl.rapid.view.renderFilterDetails = function(opt_data, opt_ignored) {
  var output = '<form class="aui" id="ghx-savedfilter-form"><div class="aui-message aui-message-warning hidden" id="unmapped-issues-warning"><p>' + soy.$$escapeHtml('\u4e00\u4e9b\u95ee\u9898\u5728\u4fdd\u5b58\u7684\u7b5b\u9009\u5668\u4e2d\u5c06\u4e0d\u4f1a\u663e\u793a\u3002') + ' <a href="' + soy.$$escapeHtml(GH.Ajax.CONTEXT_PATH) + '/secure/RapidView.jspa?rapidView=' + soy.$$escapeHtml(opt_data.rapidViewId) + '&amp;tab=columns" id="js-view-columns">' + soy.$$escapeHtml('\u67e5\u770b\u5305\u542b\u8fd9\u4e9b\u95ee\u9898\u7684\u672a\u5bf9\u5e94\u72b6\u6001.') + '</a></p></div><div class="field-group"><label>' + soy.$$escapeHtml('\u4fdd\u5b58\u7684\u7b5b\u9009\u5668') + '</label><span id="ghx-filter-change" class="field-value' + ((opt_data.canEdit) ? ' ghx-editable ghx-ss' : '') + '">' + soy.$$escapeHtml(opt_data.savedFilter.name) + '</span><select id="ghx-filter-change-select" class="hidden" aria-hidden="true"></select><p class="ghx-additional"><a href="' + soy.$$escapeHtml(GH.Ajax.CONTEXT_PATH) + '/secure/IssueNavigator.jspa?mode=show&requestId=' + soy.$$escapeHtml(opt_data.savedFilter.id) + '">' + ((opt_data.savedFilter.canEdit) ? soy.$$escapeHtml('\u7f16\u8f91\u7b5b\u9009\u5668\u67e5\u8be2') : soy.$$escapeHtml('\u67e5\u770b\u7b5b\u9009\u5668\u67e5\u8be2')) + '</a></p></div>' + GH.tpl.rapid.view.renderPermissionInfo(opt_data) + '<div class="field-group ghx-jql-preview"><label>' + soy.$$escapeHtml('\u7b5b\u9009\u5668\u67e5\u8be2') + '</label><span class="field-value">' + ((opt_data.savedFilter.query) ? soy.$$escapeHtml(opt_data.savedFilter.query) : '') + '</span></div><div class="field-group"><label>' + soy.$$escapeHtml('\u8bc4\u7ea7') + '</label><span class="field-value">';
  if (opt_data.savedFilter.isOrderedByRank) {
    output += soy.$$escapeHtml('\u4f7f\u7528\u6392\u5e8f');
  } else {
    if (! opt_data.savedFilter.orderByWarnings) {
      output += soy.$$escapeHtml('\u8bc4\u7ea7\u7981\u7528\uff0c\u56e0\u4e3a\u7b5b\u9009\u5668\u67e5\u8be2\u4e0d\u662f\u6309\u7167\u8bc4\u7ea7\u8fdb\u884c\u6392\u5e8f');
    } else {
      var warningList181 = opt_data.savedFilter.orderByWarnings.errorMessages;
      var warningListLen181 = warningList181.length;
      for (var warningIndex181 = 0; warningIndex181 < warningListLen181; warningIndex181++) {
        var warningData181 = warningList181[warningIndex181];
        output += soy.$$escapeHtml(warningData181) + '<br>';
      }
    }
    output += (opt_data.savedFilter.canEdit && opt_data.savedFilter.canBeFixed) ? '<p><button class="aui-button js-update-filter-order-by">' + soy.$$escapeHtml('\u6dfb\u52a0\u8bc4\u7ea7') + '</button></p>' : '';
  }
  output += '</span></div>';
  if (opt_data.savedFilter.queryProjects) {
    output += '<div class="field-group"><label>' + soy.$$escapeHtml('\u677f\u4e2d\u7684\u9879\u76ee') + '</label><ul id="ghx-show-projects-in-board">';
    if (opt_data.savedFilter.queryProjects.displayMessage) {
      output += '<span class="field-value">' + soy.$$escapeHtml(opt_data.savedFilter.queryProjects.displayMessage) + '</span>';
    } else {
      var avatarSize__soy202 = 'small';
      var projectList203 = opt_data.savedFilter.queryProjects.projects;
      var projectListLen203 = projectList203.length;
      for (var projectIndex203 = 0; projectIndex203 < projectListLen203; projectIndex203++) {
        var projectData203 = projectList203[projectIndex203];
        output += '<li>' + aui.avatar.avatar({isProject: 'true', size: '' + soy.$$escapeHtml(avatarSize__soy202), avatarImageUrl: soy.$$escapeHtml("") + '/secure/projectavatar?size=' + soy.$$escapeHtml(avatarSize__soy202) + '&pid=' + soy.$$escapeHtml(projectData203.id), extraClasses: 'jira-system-avatar', accessibilityText: '' + soy.$$escapeHtml(AJS.format('{0} \u7684\u9879\u76ee\u5934\u50cf',projectData203.name))}) + '<strong>' + soy.$$escapeHtml(projectData203.name) + '</strong><p>';
        var viewProjectPermissionText__soy223 = '\u67e5\u770b\u6743\u9650';
        output += ((projectData203.isValidEditProjectConfigAction) ? '<a href="' + soy.$$escapeHtml("") + '/plugins/servlet/project-config/' + soy.$$escapeHtml(projectData203.key) + '/permissions">' + soy.$$escapeHtml(viewProjectPermissionText__soy223) + '</a>' : '<a class="disabled">' + soy.$$escapeHtml(viewProjectPermissionText__soy223) + '</a><span class="aui-icon aui-icon-small aui-iconfont-info" title="' + soy.$$escapeHtml('\u4e0d\u5141\u8bb8\u60a8\u67e5\u770b\u6b64\u9879\u76ee\u6743\u9650') + '"></span>') + '</p></li>';
      }
    }
    output += '</ul></div>';
  }
  output += (opt_data.isSubqueriesConfigurable) ? '<div class="field-group ghx-jql"><label for="ghx-sub-jql">' + soy.$$escapeHtml('Kanban \u677f\u5b50\u7b5b\u9009\u5668') + '</label><div id="js-sub-container-work">' + GH.tpl.rapid.view.renderSubQueryView({section: 'board.kanban.work', queryValue: opt_data.subqueries[0], canEdit: opt_data.canEdit}) + '</div><div class="description">' + soy.$$escapeHtml('\u8fdb\u4e00\u6b65\u7b5b\u9009\u672a\u53d1\u5e03\u7684\u5de5\u4f5c\u4efb\u52a1\u3002') + '</div></div>' : '';
  if (opt_data.isOldDoneIssuesCutoffConfigurable) {
    output += '<div class="field-group ghx-show-old-limit"><label for="ghx-limit-days">' + soy.$$escapeHtml('\u9690\u85cf\u5df2\u5b8c\u6210\u4e8b\u52a1\u665a\u4e8e') + '</label><select id="ghx-limit-days" class="select">';
    var optionList259 = opt_data.oldDoneIssuesCutoffOptions;
    var optionListLen259 = optionList259.length;
    for (var optionIndex259 = 0; optionIndex259 < optionListLen259; optionIndex259++) {
      var optionData259 = optionList259[optionIndex259];
      output += '<option value="' + soy.$$escapeHtml(optionData259.value) + '" ' + ((opt_data.oldDoneIssuesCutoff == optionData259.value) ? 'selected="selected"' : '') + '>' + soy.$$escapeHtml(optionData259.label) + '</option>';
    }
    output += '</select><div class="description">' + soy.$$escapeHtml('\u901a\u8fc7\u9690\u85cf\u60a8\u4e0d\u518d\u5904\u7406\u7684\u4e8b\u9879\u6765\u52a0\u901f\u9762\u677f\u3002') + '</div></div>';
  }
  output += '</form>';
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderFilterDetails.soyTemplateName = 'GH.tpl.rapid.view.renderFilterDetails';
}


GH.tpl.rapid.view.renderSubQueryView = function(opt_data, opt_ignored) {
  return '<span id="ghx-sub-work" class="field-value' + ((opt_data.canEdit) ? ' js-editable ghx-editable ghx-jql' : '') + '">' + ((opt_data.queryValue) ? soy.$$escapeHtml(opt_data.queryValue) : (opt_data.canEdit) ? '<em>' + soy.$$escapeHtml('\u6dfb\u52a0\u5b50\u7b5b\u9009\u5668') + '</em>' : soy.$$escapeHtml('\u6ca1\u6709\u5b50\u7b5b\u9009\u5668')) + '</span>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderSubQueryView.soyTemplateName = 'GH.tpl.rapid.view.renderSubQueryView';
}


GH.tpl.rapid.view.renderSubQueryControls = function(opt_data, opt_ignored) {
  return '<textarea class="js-jql-autocomplete-field textarea" id="ghx-sub-jql" data-section="' + soy.$$escapeHtml(opt_data.section) + '" cols="20" rows="2">' + soy.$$escapeHtml(opt_data.queryValue) + '</textarea><span id="ghx-sub-jql-errormsg" class="icon jqlgood js-jql-autocomplete-error"><span></span></span><button class="aui-button js-subquery-submit">' + soy.$$escapeHtml('\u66f4\u65b0') + '</button><button class="aui-button js-subquery-cancel">' + soy.$$escapeHtml('\u53d6\u6d88') + '</button>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderSubQueryControls.soyTemplateName = 'GH.tpl.rapid.view.renderSubQueryControls';
}


GH.tpl.rapid.view.renderPermissionInfo = function(opt_data, opt_ignored) {
  return '<div class="field-group"><label for="ghx-create-permissioninfo">' + soy.$$escapeHtml('\u5171\u4eab') + '</label>' + GH.tpl.rapid.view.renderPermissionEntries({permissionEntries: opt_data.savedFilter.permissionEntries}) + ((opt_data.savedFilter.canEdit) ? '<p class="ghx-additional"><a href="' + soy.$$escapeHtml(GH.Ajax.CONTEXT_PATH) + '/secure/EditFilter!default.jspa?filterId=' + soy.$$escapeHtml(opt_data.savedFilter.id) + '">' + soy.$$escapeHtml('\u7f16\u8f91\u7b5b\u9009\u5668\u5171\u4eab') + '</a></p>' : '</div><div class="field-group"><label>' + soy.$$escapeHtml('\u81ea\u5df1\u7684') + '</label><span class="field-value">' + soy.$$filterNoAutoescape(opt_data.savedFilter.owner.renderedLink) + '</span>') + '</div>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderPermissionInfo.soyTemplateName = 'GH.tpl.rapid.view.renderPermissionInfo';
}


GH.tpl.rapid.view.renderPermissionEntries = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.permissionEntries.length > 0) {
    output += '<span class="field-value"><ul id="ghx-create-permissioninfo">';
    var permissionEntryList329 = opt_data.permissionEntries;
    var permissionEntryListLen329 = permissionEntryList329.length;
    for (var permissionEntryIndex329 = 0; permissionEntryIndex329 < permissionEntryListLen329; permissionEntryIndex329++) {
      var permissionEntryData329 = permissionEntryList329[permissionEntryIndex329];
      output += '<li>';
      var permissionValueList331 = permissionEntryData329.values;
      var permissionValueListLen331 = permissionValueList331.length;
      for (var permissionValueIndex331 = 0; permissionValueIndex331 < permissionValueListLen331; permissionValueIndex331++) {
        var permissionValueData331 = permissionValueList331[permissionValueIndex331];
        output += '<span>' + soy.$$filterNoAutoescape(permissionValueData331.name) + '</span>';
      }
      output += '</li>';
    }
    output += '</ul></span>';
  } else {
    output += '<span class="field-value">' + soy.$$escapeHtml('\u6ca1\u6709\u5171\u4eab') + '</span>';
  }
  return output;
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.renderPermissionEntries.soyTemplateName = 'GH.tpl.rapid.view.renderPermissionEntries';
}


GH.tpl.rapid.view.deleteForm = function(opt_data, opt_ignored) {
  return '<section role="dialog" class="aui-dialog2 aui-dialog2-small gh-confirm-delete-dialog"><header class="aui-dialog2-header"><h2 class="aui-dialog2-header-main">' + soy.$$escapeHtml('\u5220\u9664') + '</h2><button type="button" class="aui-close-button" aria-label="' + soy.$$escapeHtml('\u5173\u95ed') + '"></button></header><div class="aui-dialog2-content"><p>' + soy.$$filterNoAutoescape(opt_data.message) + '</p></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><form>' + aui.buttons.button({tagName: 'button', type: 'primary', extraAttributes: {type: 'submit'}, text: '\u786e\u8ba4'}) + aui.buttons.button({tagName: 'button', type: 'link', extraAttributes: {type: 'button'}, extraClasses: 'cancel', text: '\u53d6\u6d88'}) + '</form></div></footer></section>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.deleteForm.soyTemplateName = 'GH.tpl.rapid.view.deleteForm';
}


GH.tpl.rapid.view.confirmForm = function(opt_data, opt_ignored) {
  return '<h2 class="dialog-title">' + soy.$$escapeHtml('\u786e\u8ba4') + '</h2><form class="aui" action="#" method="post"><div class="form-body"><p>' + soy.$$filterNoAutoescape(opt_data.message) + '</p></div><div class="form-footer buttons-container"><div class="buttons"><input id="submit" class="button" type="submit" value="' + soy.$$escapeHtml('\u786e\u8ba4') + '" /><a role="button" href="#" class="cancel">' + soy.$$escapeHtml('\u53d6\u6d88') + '</a></div></div></form></div>';
};
if (goog.DEBUG) {
  GH.tpl.rapid.view.confirmForm.soyTemplateName = 'GH.tpl.rapid.view.confirmForm';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-create-board', location = 'includes/js/rapid/ui/startwizard/WizardView.js' */
GH.StartWizardView={},GH.StartWizardView.dialog=void 0,GH.StartWizardView.model=void 0,GH.StartWizardView.stepsNameStack=void 0,GH.StartWizardView.pages={},GH.StartWizardView.filterPicker=void 0,GH.StartWizardView.projectPicker=void 0,function(){var e=require("jira-agile/rapid/analytics-tracker");GH.StartWizardView.analytics=new e("gh.create.wizard")}(),GH.StartWizardView.wizardStepPreRenderCallbacks=[],GH.StartWizardView.wizardStepPostRenderCallbacks=[],GH.StartWizardView.wizardStepOnValidateCallbacks=[],GH.StartWizardView.wizardStepOnCreateCallbacks=[],GH.StartWizardView.steps={methodologySelection:{render:function(e){var t={isSampleDataAvailable:GH.Config.isSampleDataAvailable,canCreateProject:GH.UserData.canCreateProject()};return{header:"\u521b\u5efa\u654f\u6377\u9762\u677f",content:GH.tpl.startwizard.renderMethodologySelectionStep(t)}},bind:function(e,t){AJS.$("#ghx-wizard-methodology-scrum").click(function(e){e.preventDefault(),t.sampleData=!1,GH.StartWizardView.selectMethodology("scrum",t)}),AJS.$("#ghx-wizard-methodology-kanban").click(function(e){e.preventDefault(),t.sampleData=!1,GH.StartWizardView.selectMethodology("kanban",t)}),AJS.$("#ghx-wizard-methodology-scrum-sample").click(function(e){e.preventDefault(),t.sampleData=!0,GH.StartWizardView.selectMethodology("scrum",t)}),AJS.$("#ghx-wizard-methodology-kanban-sample").click(function(e){e.preventDefault(),t.sampleData=!0,GH.StartWizardView.selectMethodology("kanban",t)})}},methodSelection:{render:function(e){var t=e.method;return t||(t=GH.UserData.userConfig.canCreateProject?"newProject":"existingProject"),{header:"\u521b\u5efa\u654f\u6377\u9762\u677f",content:GH.tpl.startwizard.renderMethodSelectionStep(_.extend({},e,{canCreateProject:GH.UserData.userConfig.canCreateProject,hasProjectsAccessible:GH.UserData.userConfig.hasProjectsAccessible,hasFiltersAccessible:GH.UserData.userConfig.hasFiltersAccessible,selectedOption:t}))}},bind:function(e){},next:function(e,t){var a=e.find("input[name=ghx-wizard-method]").filter(":checked").val();t.method=a;var r=AJS.$.Deferred();return"newProject"===a?r.resolve("projectCreation"):"existingProject"===a?r.resolve("projectSelection"):"existingFilter"===a?r.resolve("filterSelection"):r.reject(new Error("Unknown board creation method : "+a)),r.promise()}},projectCreation:{render:function(e){return e.prefix="project",GH.StartWizardView.renderProjectCreation({user:GH.UserData.userConfig,prefix:"project",sampleInfo:{projectName:"",projectKey:""}})},bind:function(e,t){GH.StartWizardView.bindProjectCreation(e,t)},completeActionName:"\u521b\u5efa\u9762\u677f",complete:function(e){return GH.StartWizardView.createProject(GH.StartWizardView.dialog,e)}},sampleProjectCreation:{render:function(e){return e.prefix="sample",GH.StartWizardView.renderProjectCreation({user:GH.UserData.userConfig,prefix:"sample",sampleInfo:e.sampleInfo})},bind:function(e,t){GH.StartWizardView.bindProjectCreation(e,t)},completeActionName:"\u521b\u5efa\u9762\u677f",complete:function(e){return GH.StartWizardView.createSampleProject(GH.StartWizardView.dialog,e)}},projectSelection:{render:function(e){return{header:"\u547d\u540d\u6b64\u9762\u677f",content:GH.tpl.startwizard.renderProjectSelectionStep()}},bind:function(e,t){GH.StartWizardView.bindProjectSelection(e,t)},completeActionName:"\u521b\u5efa\u9762\u677f",complete:function(e){return GH.StartWizardView.validateProjectSelection(e)}},filterSelection:{render:function(e){return{header:"\u547d\u540d\u6b64\u9762\u677f",content:GH.tpl.startwizard.renderFilterSelectionStep()}},bind:function(e,t){GH.StartWizardView.bindFilterSelection(e,t)},completeActionName:"\u521b\u5efa\u9762\u677f",complete:function(e){return GH.StartWizardView.validateFilterSelection(e)}}},GH.StartWizardView.show=function(e){GH.StartWizardView.dialog||(GH.StartWizardView.dialog=GH.Dialog.create({width:840,height:400,id:"ghx-wizard-dialog",onCancelFn:function(){GH.StartWizardView.disposeWizard(),GH.StartWizardView.analytics.trigger("cancel")}}),GH.StartWizardView.model={},GH.StartWizardView.stepsNameStack=[],GH.StartWizardView.dialog.show(),GH.StartWizardView.analytics.trigger("start"),GH.StartWizardView.preventSubmitOnEnter())},GH.StartWizardView.preventSubmitOnEnter=function(){var e=function(e){if(13==e.keyCode&&"ghx-wizard-filter-select-field"!=e.target.id)return e.preventDefault(),!1};AJS.$("input").keydown(e)},GH.StartWizardView.showStep=function(e){if(!_.has(GH.StartWizardView.steps,e))throw new Error("Unknown wizard step "+e);var t=GH.StartWizardView.dialog;if(GH.StartWizardView.pages[e])t.gotoPage(GH.StartWizardView.pages[e]),GH.StartWizardView.dialog.popup.element.find(".dialog-panel-body .aui-message-error").remove();else{var a=GH.StartWizardView.steps[e],r=a.render(GH.StartWizardView.model);_.each(GH.StartWizardView.wizardStepPreRenderCallbacks,function(t){t(e)}),t.addPage(),GH.StartWizardView.pages[e]=t.curpage,t.addHeader(r.header);var i=AJS.$(r.content);if(t.addPanel(null,i,"ghx-wizard-panel"),_.each(GH.StartWizardView.wizardStepPostRenderCallbacks,function(a){a(e,t)}),GH.StartWizardView.stepsNameStack.length>0&&t.addButton("\u56de\u9000",function(){var e=GH.StartWizardView.stepsNameStack.pop();GH.StartWizardView.showStep(e)},"aui-button"),_.has(a,"complete")){var o=function(){var e=AJS.$(this),t=e.attr("aria-disabled");"true"!=t&&a.complete(GH.StartWizardView.model)};t.addButton(a.completeActionName,o,"aui-button aui-button-primary js-wizard-button-complete")}else if(_.has(a,"next")){var n=function(){var r=AJS.$(this),o=r.attr("aria-disabled");if("true"!=o){var n=a.next(i,GH.StartWizardView.model),d=function(t){GH.StartWizardView.stepsNameStack.push(e),GH.StartWizardView.showStep(t)},l=function(e){console.log(e),t.updateHeight()},c=!0;_.each(GH.StartWizardView.wizardStepOnValidateCallbacks,function(a){a(e,t)||(c=!1)}),n.done(function(e){c?d(e):l("GH project create extension callback returned failure")}).fail(l)}};t.addButton("\u4e0b\u4e00\u6b65",n,"aui-button aui-button-primary js-wizard-button-next")}t.addCancel("\u53d6\u6d88",function(){t.cancel()}),a.bind(i,GH.StartWizardView.model)}t.updateHeight(),GH.StartWizardView.preventSubmitOnEnter()},GH.StartWizardView.startWizard=function(){GH.StartWizardView.show(),GH.StartWizardView.showStep("methodologySelection")},GH.StartWizardView.disposeWizard=function(){GH.StartWizardView.dialog=void 0,GH.StartWizardView.pages={}},GH.StartWizardView.genStartFunction=function(e,t){return function(){GH.StartWizardView.show(),GH.StartWizardView.model.sampleData=t,GH.StartWizardView.selectMethodology(e,GH.StartWizardView.model)}},GH.StartWizardView.bindWelcomePage=function(){GH.Dialog.registerDialog(".js-wizard-scrum",GH.StartWizardView.genStartFunction("scrum",!1)),GH.Dialog.registerDialog(".js-wizard-scrum-sample",GH.StartWizardView.genStartFunction("scrum",!0)),GH.Dialog.registerDialog(".js-wizard-kanban",GH.StartWizardView.genStartFunction("kanban",!1)),GH.Dialog.registerDialog(".js-wizard-kanban-sample",GH.StartWizardView.genStartFunction("kanban",!0))},GH.StartWizardView.getCreateProjectRequest=function(e){var t=e.project,a={projectName:t.name,projectKey:t.key,projectLeadUserName:t.lead,boardName:e.boardName,preset:e.methodology,sampleData:e.sampleData};return a},GH.StartWizardView.createProject=function(e,t){GH.StartWizardView.populateProjectCreationProperties(t);var a=GH.StartWizardView.getCreateProjectRequest(t);e.disableControls(),e.showSpinner();var r={url:"/welcome/createProject",data:a,errorContextMap:{projectName:"#ghx-wizard-"+t.prefix+"-projectname",projectKey:"#ghx-wizard-"+t.prefix+"-projectkey",projectLead:"#ghx-wizard-"+t.prefix+"-projectlead"},timeout:12e4,deferErrorHandling:!0};return GH.Ajax.put(r).done(function(a){function r(){if(0==GH.StartWizardView.wizardStepOnCreateCallbacks.length)i();else{var a=GH.StartWizardView.wizardStepOnCreateCallbacks.pop();a(e,r,t)}}a.success.sample=t.sampleData,GH.PersistentMessages.WelcomeMessage.show(a.success);var i=function(){GH.StartWizardView.gotoCreatedView(a.success.rapidView.id,t.methodology)};r()}).fail(function(t,a,i,o){_.each(t.getGlobalErrors(),function(e){504==e.status&&(e.message=GH.tpl.startwizard.createProjectTimeout({manageBoardURL:GH.RapidBoard.getManageViewsPageUrl()}),e.noAutoescape=!0)}),GH.Ajax.handleContextualErrors(t,r.errorContextMap),GH.Ajax.handleGlobalErrors(t,r.errorContextMap),e.hideSpinner(),e.enableControls(),e.updateHeight()})},GH.StartWizardView.validateProjectSelection=function(e){GH.StartWizardView.dialog.disableControls(),GH.StartWizardView.dialog.showSpinner();var t=AJS.$("#ghx-wizard-project-board-name");return GH.Validation.clearContextualErrors(GH.StartWizardView.projectPicker.getElement()),GH.Validation.clearContextualErrors(t),GH.Ajax.post({url:"/rapidview/create/presets",data:{name:t.val(),projectIds:GH.StartWizardView.projectPicker.getValue(),preset:e.methodology},errorContextMap:{name:"#ghx-wizard-project-board-name",projectIds:GH.StartWizardView.projectPicker.selector}}).done(function(t){console.log("mode: "+e),GH.StartWizardView.handleCreate(t,"simple",e.methodology)}).fail(function(){GH.StartWizardView.dialog.enableControls(),GH.StartWizardView.dialog.hideSpinner(),GH.StartWizardView.dialog.updateHeight()})},GH.StartWizardView.validateFilterSelection=function(e){var t=AJS.$("#ghx-wizard-filter-view-name"),a=GH.StartWizardView.filterPicker.getElement(),r=AJS.$(GH.StartWizardView.filterPicker.selector);GH.Validation.clearContextualErrors(t),GH.Validation.clearContextualErrors(a);var i=r.val()?r.val()[0]:null;return GH.StartWizardView.dialog.disableControls(),GH.StartWizardView.dialog.showSpinner(),GH.Ajax.post({url:"/rapidview/create/advanced",data:{name:t.val(),filterId:i,methodology:e.methodology},errorContextMap:{name:"#ghx-wizard-filter-view-name",filterId:GH.StartWizardView.filterPicker.selector}}).done(function(t){GH.StartWizardView.handleCreate(t,"simple",e.methodology)}).fail(function(){GH.StartWizardView.dialog.hideSpinner(),GH.StartWizardView.dialog.enableControls(),GH.StartWizardView.dialog.updateHeight()})},GH.StartWizardView.handleCreate=function(e,t,a){var r=t!=a&&a;GH.PersistentMessages.CreateViewMessage.setView(e.success.id,e.success.name,t,r),GH.StartWizardView.analytics.trigger("complete"),GH.StartWizardView.gotoCreatedView(e.success.id,a)},GH.StartWizardView.gotoCreatedView=function(e,t){var a="scrum"==t?"planning":null;GH.Navigation.gotoDefaultRapidBoardPage(e,a)},GH.StartWizardView.selectMethodology=function(e,t){if(t.methodology=e,GH.StartWizardView.stepsNameStack.push("methodologySelection"),t.sampleData)GH.Ajax.get({url:"/welcome/sampledataproject?preset="+t.methodology},"sampledataproject").done(function(e){t.sampleInfo=e,GH.StartWizardView.showStep("sampleProjectCreation")}).fail(function(){GH.StartWizardView.showStep("sampleProjectCreation")});else{var a=GH.UserData.userConfig.canCreateProject,r=GH.UserData.userConfig.hasProjectsAccessible,i=GH.UserData.userConfig.hasFiltersAccessible;a||i?r||i?GH.StartWizardView.showStep("methodSelection"):GH.StartWizardView.showStep("projectCreation"):GH.StartWizardView.showStep("projectSelection")}},GH.StartWizardView.populateProjectCreationProperties=function(e){var t=AJS.$("#ghx-wizard-"+e.prefix+"-projectname"),a=AJS.$("#ghx-wizard-"+e.prefix+"-projectkey"),r=AJS.$("#ghx-wizard-"+e.prefix+"-projectlead");GH.Validation.clearContextualErrors(t),GH.Validation.clearContextualErrors(a),GH.Validation.clearContextualErrors(r),e.project={name:t.val(),key:a.val().toUpperCase(),lead:AJS.$("#ghx-wizard-"+e.prefix+"-projectlead option:selected").val()}},GH.StartWizardView.createSampleProject=function(e,t){t.sampleData=!0,GH.StartWizardView.createProject(e,t)},GH.StartWizardView.validateBoardName=function(e,t){return GH.Validation.clearContextualErrors(e),t.boardName=e.val(),!!t.boardName||(GH.Validation.showContextualErrors(e,"\u8bf7\u4e3a\u60a8\u7684\u9762\u677f\u8f93\u5165\u540d\u79f0\u3002"),!1)},GH.StartWizardView.renderProjectCreation=function(e){return{header:"\u9762\u677f\u4e0a\u7684\u65b0\u9879\u76ee",content:GH.tpl.startwizard.renderProjectCreationStep(e)}},GH.StartWizardView.bindProjectSelection=function(e,t){GH.StartWizardView.projectPicker=new GH.ProjectPicker({selector:"#ghx-wizard-choose-project",change:function(){GH.StartWizardView.dialog.updateHeight()},currentProjectId:JIRA.API&&JIRA.API.Projects?JIRA.API.Projects.getCurrentProjectId():null}),GH.StartWizardView.projectPicker.multiselect.keys.Return=function(e){e.preventDefault()},GH.StartWizardView.attachDependantButton({buttonIndex:1,selectors:["#ghx-wizard-project-board-name","#ghx-wizard-choose-project"]})},GH.StartWizardView.bindProjectCreation=function(e,t){AJS.$("#ghx-wizard-"+t.prefix+"-projectkey").generateFrom(AJS.$("#ghx-wizard-"+t.prefix+"-projectname"),{maxNameLength:64,maxKeyLength:64}),JIRA.trigger(JIRA.Events.NEW_CONTENT_ADDED,[e]),e.trigger("contentRefresh");var a=GH.StartWizardView.dialog,r=a.page[a.curpage];r.button[1].item;GH.StartWizardView.attachDependantButton({buttonIndex:1,selectors:["#ghx-wizard-"+t.prefix+"-projectname","#ghx-wizard-"+t.prefix+"-projectkey","#ghx-wizard-"+t.prefix+"-projectlead"]})},GH.StartWizardView.bindFilterSelection=function(e,t){var a=function(e,a){var r="";if(delete t.savedFilter,a&&a.properties){var i=a.properties.savedFilter;r=null!=i?GH.tpl.startwizard.renderPermissionInfo({savedFilter:i}):"",t.savedFilter=i}AJS.$("#ghx-create-permissioninfo-container").html(r),GH.StartWizardView.dialog.updateHeight()};GH.StartWizardView.filterPicker=new GH.SavedFilterPicker({selector:"#ghx-wizard-filter-select",selected:a,stayActivated:!0}),GH.StartWizardView.filterPicker.show(t.savedFilter),GH.StartWizardView.filterPicker.singleSelect.keys.Return=function(e){e.preventDefault()},GH.StartWizardView.attachDependantButton({buttonIndex:1,selectors:["#ghx-wizard-filter-view-name","#ghx-wizard-filter-select"]})},GH.StartWizardView.attachDependantButton=function(e){var t=GH.StartWizardView.dialog,a=t.page[t.curpage],r=a.button[e.buttonIndex].item;GH.StartWizardView.dependantValidationListener({selectors:e.selectors,full:function(){r.attr("aria-disabled","false")},empty:function(){r.attr("aria-disabled","true")}})},GH.StartWizardView.dependantValidationListener=function(e){function t(){var t=_.filter(e.selectors,function(e){var t=AJS.$(e);return!AJS.$.trim(t.val())});return _.isEmpty(t)?e.full():e.empty()}function a(){e.nowait?t():setTimeout(t,50)}_.isEmpty(e.selectors)||(e.full=e.full||AJS.$.noop(),e.empty=e.empty||AJS.$.noop(),e.nowait=e.nowait||!1,_.each(e.selectors,function(e){var t=AJS.$(e);t.on("input",a),t.on("change",a),t.on("selected",a),t.on("unselect",a)}),t())},GH.StartWizardView.registerWizardStepPreRenderCallback=function(e){GH.StartWizardView.wizardStepPreRenderCallbacks.push(e)},GH.StartWizardView.registerWizardStepPostRenderCallback=function(e){GH.StartWizardView.wizardStepPostRenderCallbacks.push(e)},GH.StartWizardView.registerWizardStepOnValidateCallback=function(e){GH.StartWizardView.wizardStepOnValidateCallbacks.push(e)},GH.StartWizardView.registerWizardStepOnCreateCallbacks=function(e){GH.StartWizardView.wizardStepOnCreateCallbacks.push(e)};
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-create-board', location = 'includes/js/rapid/ui/component/SavedFilterPicker.js' */
define("jira-agile/rapid/ui/component/saved-filter-picker",["jira-agile/rapid/ajax","jira/ajs/list/item-descriptor","jira/ajs/select/single-select","jquery","underscore"],function(e,t,i,r,s){var l=function(e){this.selector=e.selector,this.fieldSelector=e.selector+"-field",this.blurHandler=e.blur,this.selectHandler=e.selected,this.stayActivated=e.stayActivated,this.focus=e.focus,this.ariaLabel=e.ariaLabel};return l.prototype.show=function(l){var n=r(this.selector),a=new i({element:n,width:300,removeOnUnSelect:!0,itemAttrDisplayed:"label",errorMessage:"",ajaxOptions:{query:!0,url:e.buildRestUrl("/savedfilter/list.json"),formatResponse:function(i){if(i.errors){var l=e._convertJiraErrors(i.errors),a={searchName:this.selector};return e.handleContextualErrors(l,a),!1}n.siblings(".ghx-error").remove();var o=[];return r(i.filters).each(function(){o.push(new t({value:this.id.toString(),label:this.name,html:s.escape(String(this.name)),savedFilter:this}))}),o}},ariaLabel:this.ariaLabel});this.singleSelect=a;var o=this.removeSingleSelect;this.stayActivated||r(this.fieldSelector).on("blur",function(){o(a)}),s.isFunction(this.blurHandler)&&r(this.fieldSelector).on("blur",this.blurHandler),this.focus&&r(this.fieldSelector).trigger("focus").trigger("select"),l&&this.setSelection(l);var c=this.selectHandler;this.stayActivated?(n.on("selected",c),n.on("unselect",c)):(n.on("selected",function(e,t){o(a),c(e,t)}),n.on("unselect",function(e,t){o(a),c(e,t)}))},l.prototype.setSelection=function(e){this.singleSelect.setSelection(new t({value:e.id.toString(),label:e.name,html:s.escape(String(e.name)),savedFilter:e,selected:!0}))},l.prototype.removeSingleSelect=function(e){e.hideSuggestions(),e.disable()},l.prototype.getElement=function(){return this.singleSelect.$field},l}),AJS.namespace("GH.SavedFilterPicker",null,require("jira-agile/rapid/ui/component/saved-filter-picker"));
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:gh-create-board', location = 'includes/js/lib/jquery/jquery.keygen.js' */
!function(e,t){e.KeyGenerator=function(){var e=["THE","A","AN","AS","AND","OF","OR"],n={},a=function(e){return e.join("").length},r=function(n){return t.grep(n,function(n,a){return t.inArray(n,e)===-1})},o=function(e){var n="";return t.each(e,function(e,t){n+=t.charAt(0)}),n},u=function(e){var t,n=!1;for(t=0;t<e.length;t++)if(i(e[t]))n=!0;else if(n)return e.substring(0,t+1);return e},i=function(e){return e&&1===e.length&&e.search("[AEIOUY]")!==-1};return n[199]="C",n[231]="c",n[252]="u",n[251]="u",n[250]="u",n[249]="u",n[233]="e",n[234]="e",n[235]="e",n[232]="e",n[226]="a",n[228]="a",n[224]="a",n[229]="a",n[225]="a",n[239]="i",n[238]="i",n[236]="i",n[237]="i",n[196]="A",n[197]="A",n[201]="E",n[230]="ae",n[198]="Ae",n[244]="o",n[246]="o",n[242]="o",n[243]="o",n[220]="U",n[255]="Y",n[214]="O",n[241]="n",n[209]="N",{generateKey:function(e,i){i=t.extend({},i);var c="number"==typeof i.desiredKeyLength?i.desiredKeyLength:4,h="number"==typeof i.maxKeyLength?i.maxKeyLength:1/0;if(e=t.trim(e),!e)return"";for(var s=[],g=0,f=e.length;g<f;g++){var l=n[e.charCodeAt(g)];s.push(l?l:e[g])}e=s.join("");var d=[];t.each(e.split(/\s+/),function(e,t){t&&(t=t.replace(/[^a-zA-Z]/g,""),t=t.toUpperCase(),t.length&&d.push(t))}),c&&a(d)>c&&(d=r(d));var m;if(0==d.length)m="";else if(1==d.length){var v=d[0],y=u(v);m=h<v.length||Math.abs(v.length-c)>=Math.abs(y.length-c)?y:v}else{var K=a(d),x=o(d);m=h<K||Math.abs(K-c)>=Math.abs(x.length-c)?x:d.join("")}return h&&m.length>h&&(m=m.substr(0,h)),m}}};var n=e.KeyGenerator();t.fn.generateFrom=function(e,a){var r={desiredKeyLength:4,maxKeyLength:10,maxNameLength:30,timeoutMS:100,validationCallback:function(){},errorCallback:function(){}},o=t(this).first(),e=e.first(),a=t.extend({},r,a);return function(){var r=function(){return o.data("autosuggest")!==!1},u=function(e){o.val()&&o.data("lastGeneratedValue")!==o.val()&&o.data("autosuggest",!1)},i=function(e){o.data("lastGeneratedValue",e),o.val(e)},c=function(){u(o.val()),f()},h=function(e){s(e,c)},s=function(e,n){var r,o=t(e.target);r=function(){g(e),n(),o.is(":visible")&&o.data("checkHook",setTimeout(r,a.timeoutMS))},o.data("checkHook")||o.data("checkHook",setTimeout(r,0))},g=function(e){var n=t(e.target);clearTimeout(n.data("checkHook")),n.removeData("checkHook")},f=function(){r()&&i(n.generateKey(e.val(),{desiredKeyLength:a.desiredKeyLength,maxKeyLength:a.maxKeyLength}))};e.attr("maxlength",a.maxNameLength),o.attr("maxlength",a.maxKeyLength),o.css("text-transform","uppercase"),document.activeElement&&document.activeElement===e[0]&&h({target:e[0]}),e.focus(h),e.blur(g)}(),this}}(window,jQuery);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.pyxis.greenhopper.jira:sidebar', location = 'includes/js/sidebar/create-board.js' */
AJS.$(function(){AJS.$("body").on("click",".js-create-board",function(e){e.preventDefault(),GH.UserData.userConfig={canCreateProject:!1,hasProjectsAccessible:!0,hasFiltersAccessible:!0},GH.StartWizardView.startWizard()})});
}catch(e){WRMCB(e)};