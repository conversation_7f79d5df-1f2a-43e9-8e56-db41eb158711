# Docker 离线安装 - 精简版

解决 "Failed to start docker.service: Unit not found" 问题的最简安装脚本。

## 使用方法

```bash
# 进入目录
cd docker-offline

# 设置执行权限
chmod +x install-docker.sh

# 运行安装（需要root权限）
sudo ./install-docker.sh
```

## 功能特点

✅ 自动创建systemd服务文件  
✅ 解决"Unit not found"错误  
✅ 支持CentOS 7.x离线安装  
✅ 自动配置Docker daemon  
✅ 智能启动和验证  

## 安装完成后

```bash
# 启动JIRA
cd ..
docker compose up -d

# 访问JIRA
http://localhost:8080
```

## 故障排除

如果安装失败：
1. 检查是否为CentOS 7.x系统
2. 确保所有RPM包都在当前目录
3. 以root权限运行脚本
4. 查看详细错误信息

## 文件清单

- `install-docker.sh` - 核心安装脚本
- `*.rpm` - Docker相关RPM包
