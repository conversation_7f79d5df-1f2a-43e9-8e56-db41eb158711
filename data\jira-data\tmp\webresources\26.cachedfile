WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:8', location = 'dist/8.4edca695b89465692a9a.js' */
jiraDevelopmentIntegrationPluginJsonp([8],{"./js/component/tooltip.js":function(a,b,c){"use strict";var d,e;d=[c("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),c("jira.webresources:jquery/require('jquery')")],e=function(a,b){function c(a){setTimeout(function(){a.$tip.is(":visible")&&(a.$element.is(":visible")?c(a):a.hide())},250)}function d(a){a&&(a.show(),a.$tip&&c(a))}return{tipsify:function(c){c=a.extend({},c,{trigger:"manual",title:"data-tooltip"}),c.selector&&b(c.context).on({mouseenter:function(){var e=b(this).tooltip(c).tipsy(!0);d(e),a.isFunction(c.onShow)&&c.onShow(e)},mouseleave:function(){var d=b(this).tipsy(!0);d&&(d.hide(),a.isFunction(c.onHide)&&c.onHide(d))}},c.selector)}}}.apply(b,d),!(void 0!==e&&(a.exports=e))},"./js/util/EventPublisher.js":function(a,b,c){"use strict";var d;d=function(){return{trigger:AJS.trigger}}.call(b,c,b,a),!(void 0!==d&&(a.exports=d))},"./js/util/analytics.js":function(a,b,c){"use strict";var d,e;d=[c("./js/util/EventPublisher.js"),b],e=function(a,b){b.sendEvent=function(b){var c=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};a.trigger("analyticsEvent",{name:b,data:c})}}.apply(b,d),!(void 0!==e&&(a.exports=e))},14:function(a){a.exports=void 0},"jira.webresources:ajs-underscorejs-amd-shim/require('underscore')":function(a){a.exports=require("underscore")},"jira.webresources:jquery/require('jquery')":function(a){a.exports=require("jquery")}});
//# sourceMappingURL=8.4edca695b89465692a9a.js.map
}catch(e){WRMCB(e)};