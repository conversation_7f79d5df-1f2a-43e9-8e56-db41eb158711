[2025-09-10T15:28:32.548+0800][0.007s][info][gc,heap] Heap region size: 1M
[2025-09-10T15:28:32.557+0800][0.016s][info][gc     ] Using G1
[2025-09-10T15:28:32.558+0800][0.017s][info][gc,heap,coops] Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
[2025-09-10T15:28:32.559+0800][0.018s][info][gc,cds       ] Mark closed archive regions in map: [0x00000000ffe00000, 0x00000000ffe6bff8]
[2025-09-10T15:28:32.560+0800][0.019s][info][gc,cds       ] Mark open archive regions in map: [0x00000000ffc00000, 0x00000000ffc46ff8]
[2025-09-10T15:28:33.359+0800][0.819s][info][gc,start     ] GC(0) Pa<PERSON> Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:33.362+0800][0.821s][info][gc,task      ] GC(0) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:33.369+0800][0.828s][info][gc,phases    ] GC(0)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:33.369+0800][0.828s][info][gc,phases    ] GC(0)   Evacuate Collection Set: 5.5ms
[2025-09-10T15:28:33.369+0800][0.828s][info][gc,phases    ] GC(0)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T15:28:33.370+0800][0.829s][info][gc,phases    ] GC(0)   Other: 2.9ms
[2025-09-10T15:28:33.370+0800][0.829s][info][gc,heap      ] GC(0) Eden regions: 51->0(46)
[2025-09-10T15:28:33.370+0800][0.829s][info][gc,heap      ] GC(0) Survivor regions: 0->7(7)
[2025-09-10T15:28:33.370+0800][0.829s][info][gc,heap      ] GC(0) Old regions: 2->2
[2025-09-10T15:28:33.370+0800][0.829s][info][gc,heap      ] GC(0) Humongous regions: 1->0
[2025-09-10T15:28:33.371+0800][0.830s][info][gc,metaspace ] GC(0) Metaspace: 10790K(11648K)->10790K(11648K) NonClass: 9686K(10240K)->9686K(10240K) Class: 1104K(1408K)->1104K(1408K)
[2025-09-10T15:28:33.371+0800][0.830s][info][gc           ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 52M->7M(1026M) 11.196ms
[2025-09-10T15:28:33.371+0800][0.830s][info][gc,cpu       ] GC(0) User=0.07s Sys=0.02s Real=0.01s
[2025-09-10T15:28:33.602+0800][1.061s][info][gc,start     ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:33.602+0800][1.062s][info][gc,task      ] GC(1) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:33.608+0800][1.067s][info][gc,phases    ] GC(1)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:33.608+0800][1.067s][info][gc,phases    ] GC(1)   Evacuate Collection Set: 3.9ms
[2025-09-10T15:28:33.609+0800][1.068s][info][gc,phases    ] GC(1)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T15:28:33.609+0800][1.068s][info][gc,phases    ] GC(1)   Other: 0.7ms
[2025-09-10T15:28:33.609+0800][1.068s][info][gc,heap      ] GC(1) Eden regions: 45->0(59)
[2025-09-10T15:28:33.609+0800][1.068s][info][gc,heap      ] GC(1) Survivor regions: 7->7(7)
[2025-09-10T15:28:33.609+0800][1.069s][info][gc,heap      ] GC(1) Old regions: 2->12
[2025-09-10T15:28:33.610+0800][1.069s][info][gc,heap      ] GC(1) Humongous regions: 1->1
[2025-09-10T15:28:33.610+0800][1.069s][info][gc,metaspace ] GC(1) Metaspace: 11866K(12544K)->11866K(12544K) NonClass: 10640K(11008K)->10640K(11008K) Class: 1226K(1536K)->1226K(1536K)
[2025-09-10T15:28:33.610+0800][1.069s][info][gc           ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 53M->18M(1026M) 7.816ms
[2025-09-10T15:28:33.610+0800][1.069s][info][gc,cpu       ] GC(1) User=0.06s Sys=0.01s Real=0.01s
[2025-09-10T15:28:34.008+0800][1.467s][info][gc,start     ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:34.009+0800][1.468s][info][gc,task      ] GC(2) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:34.011+0800][1.471s][info][gc,phases    ] GC(2)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:34.012+0800][1.471s][info][gc,phases    ] GC(2)   Evacuate Collection Set: 1.5ms
[2025-09-10T15:28:34.012+0800][1.471s][info][gc,phases    ] GC(2)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T15:28:34.012+0800][1.471s][info][gc,phases    ] GC(2)   Other: 0.9ms
[2025-09-10T15:28:34.013+0800][1.472s][info][gc,heap      ] GC(2) Eden regions: 59->0(80)
[2025-09-10T15:28:34.013+0800][1.472s][info][gc,heap      ] GC(2) Survivor regions: 7->3(9)
[2025-09-10T15:28:34.013+0800][1.472s][info][gc,heap      ] GC(2) Old regions: 12->20
[2025-09-10T15:28:34.013+0800][1.473s][info][gc,heap      ] GC(2) Humongous regions: 1->1
[2025-09-10T15:28:34.014+0800][1.473s][info][gc,metaspace ] GC(2) Metaspace: 12975K(13952K)->12975K(13952K) NonClass: 11654K(12288K)->11654K(12288K) Class: 1321K(1664K)->1321K(1664K)
[2025-09-10T15:28:34.014+0800][1.473s][info][gc           ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 77M->21M(1026M) 5.695ms
[2025-09-10T15:28:34.014+0800][1.473s][info][gc,cpu       ] GC(2) User=0.02s Sys=0.00s Real=0.00s
[2025-09-10T15:28:34.429+0800][1.888s][info][gc,start     ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:34.429+0800][1.888s][info][gc,task      ] GC(3) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:34.432+0800][1.891s][info][gc,phases    ] GC(3)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:34.432+0800][1.891s][info][gc,phases    ] GC(3)   Evacuate Collection Set: 2.0ms
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,phases    ] GC(3)   Post Evacuate Collection Set: 0.6ms
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,phases    ] GC(3)   Other: 0.5ms
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,heap      ] GC(3) Eden regions: 80->0(112)
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,heap      ] GC(3) Survivor regions: 3->4(11)
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,heap      ] GC(3) Old regions: 20->20
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,heap      ] GC(3) Humongous regions: 1->1
[2025-09-10T15:28:34.433+0800][1.892s][info][gc,metaspace ] GC(3) Metaspace: 13042K(13952K)->13042K(13952K) NonClass: 11716K(12288K)->11716K(12288K) Class: 1325K(1664K)->1325K(1664K)
[2025-09-10T15:28:34.433+0800][1.893s][info][gc           ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 101M->22M(1026M) 4.481ms
[2025-09-10T15:28:34.434+0800][1.893s][info][gc,cpu       ] GC(3) User=0.04s Sys=0.01s Real=0.00s
[2025-09-10T15:28:34.946+0800][2.405s][info][gc,start     ] GC(4) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:34.946+0800][2.406s][info][gc,task      ] GC(4) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:34.951+0800][2.410s][info][gc,phases    ] GC(4)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:34.952+0800][2.411s][info][gc,phases    ] GC(4)   Evacuate Collection Set: 1.9ms
[2025-09-10T15:28:34.952+0800][2.411s][info][gc,phases    ] GC(4)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T15:28:34.953+0800][2.412s][info][gc,phases    ] GC(4)   Other: 1.3ms
[2025-09-10T15:28:34.953+0800][2.412s][info][gc,heap      ] GC(4) Eden regions: 112->0(169)
[2025-09-10T15:28:34.954+0800][2.413s][info][gc,heap      ] GC(4) Survivor regions: 4->5(15)
[2025-09-10T15:28:34.954+0800][2.413s][info][gc,heap      ] GC(4) Old regions: 20->20
[2025-09-10T15:28:34.954+0800][2.413s][info][gc,heap      ] GC(4) Humongous regions: 1->1
[2025-09-10T15:28:34.954+0800][2.413s][info][gc,metaspace ] GC(4) Metaspace: 13047K(13952K)->13047K(13952K) NonClass: 11721K(12288K)->11721K(12288K) Class: 1325K(1664K)->1325K(1664K)
[2025-09-10T15:28:34.954+0800][2.413s][info][gc           ] GC(4) Pause Young (Normal) (G1 Evacuation Pause) 134M->23M(1026M) 8.398ms
[2025-09-10T15:28:34.954+0800][2.414s][info][gc,cpu       ] GC(4) User=0.05s Sys=0.01s Real=0.00s
[2025-09-10T15:28:35.632+0800][3.092s][info][gc,start     ] GC(5) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:35.633+0800][3.092s][info][gc,task      ] GC(5) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:35.636+0800][3.096s][info][gc,phases    ] GC(5)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:35.637+0800][3.096s][info][gc,phases    ] GC(5)   Evacuate Collection Set: 2.5ms
[2025-09-10T15:28:35.637+0800][3.096s][info][gc,phases    ] GC(5)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T15:28:35.637+0800][3.096s][info][gc,phases    ] GC(5)   Other: 0.6ms
[2025-09-10T15:28:35.637+0800][3.097s][info][gc,heap      ] GC(5) Eden regions: 169->0(278)
[2025-09-10T15:28:35.638+0800][3.097s][info][gc,heap      ] GC(5) Survivor regions: 5->6(22)
[2025-09-10T15:28:35.639+0800][3.098s][info][gc,heap      ] GC(5) Old regions: 20->20
[2025-09-10T15:28:35.639+0800][3.098s][info][gc,heap      ] GC(5) Humongous regions: 1->1
[2025-09-10T15:28:35.639+0800][3.098s][info][gc,metaspace ] GC(5) Metaspace: 13048K(13952K)->13048K(13952K) NonClass: 11722K(12288K)->11722K(12288K) Class: 1325K(1664K)->1325K(1664K)
[2025-09-10T15:28:35.639+0800][3.098s][info][gc           ] GC(5) Pause Young (Normal) (G1 Evacuation Pause) 192M->24M(1026M) 6.828ms
[2025-09-10T15:28:35.639+0800][3.099s][info][gc,cpu       ] GC(5) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T15:28:36.910+0800][4.370s][info][gc,start     ] GC(6) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:36.911+0800][4.370s][info][gc,task      ] GC(6) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:36.916+0800][4.375s][info][gc,phases    ] GC(6)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:36.916+0800][4.375s][info][gc,phases    ] GC(6)   Evacuate Collection Set: 2.3ms
[2025-09-10T15:28:36.916+0800][4.375s][info][gc,phases    ] GC(6)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T15:28:36.916+0800][4.376s][info][gc,phases    ] GC(6)   Other: 0.9ms
[2025-09-10T15:28:36.917+0800][4.376s][info][gc,heap      ] GC(6) Eden regions: 278->0(360)
[2025-09-10T15:28:36.917+0800][4.376s][info][gc,heap      ] GC(6) Survivor regions: 6->8(36)
[2025-09-10T15:28:36.917+0800][4.376s][info][gc,heap      ] GC(6) Old regions: 20->20
[2025-09-10T15:28:36.917+0800][4.376s][info][gc,heap      ] GC(6) Humongous regions: 1->1
[2025-09-10T15:28:36.917+0800][4.376s][info][gc,metaspace ] GC(6) Metaspace: 13078K(13952K)->13078K(13952K) NonClass: 11752K(12288K)->11752K(12288K) Class: 1325K(1664K)->1325K(1664K)
[2025-09-10T15:28:36.917+0800][4.377s][info][gc           ] GC(6) Pause Young (Normal) (G1 Evacuation Pause) 302M->26M(1026M) 7.054ms
[2025-09-10T15:28:36.918+0800][4.377s][info][gc,cpu       ] GC(6) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T15:28:37.693+0800][5.152s][info][gc,start     ] GC(7) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T15:28:37.693+0800][5.152s][info][gc,task      ] GC(7) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:37.698+0800][5.157s][info][gc,phases    ] GC(7)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T15:28:37.699+0800][5.158s][info][gc,phases    ] GC(7)   Evacuate Collection Set: 2.9ms
[2025-09-10T15:28:37.699+0800][5.158s][info][gc,phases    ] GC(7)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T15:28:37.699+0800][5.159s][info][gc,phases    ] GC(7)   Other: 0.8ms
[2025-09-10T15:28:37.700+0800][5.159s][info][gc,heap      ] GC(7) Eden regions: 263->0(434)
[2025-09-10T15:28:37.700+0800][5.159s][info][gc,heap      ] GC(7) Survivor regions: 8->9(46)
[2025-09-10T15:28:37.700+0800][5.159s][info][gc,heap      ] GC(7) Old regions: 20->20
[2025-09-10T15:28:37.700+0800][5.159s][info][gc,heap      ] GC(7) Humongous regions: 1->1
[2025-09-10T15:28:37.700+0800][5.159s][info][gc,metaspace ] GC(7) Metaspace: 20493K(21248K)->20493K(21248K) NonClass: 18219K(18688K)->18219K(18688K) Class: 2273K(2560K)->2273K(2560K)
[2025-09-10T15:28:37.700+0800][5.160s][info][gc           ] GC(7) Pause Young (Concurrent Start) (Metadata GC Threshold) 289M->27M(1026M) 7.797ms
[2025-09-10T15:28:37.701+0800][5.160s][info][gc,cpu       ] GC(7) User=0.05s Sys=0.01s Real=0.01s
[2025-09-10T15:28:37.701+0800][5.160s][info][gc           ] GC(8) Concurrent Cycle
[2025-09-10T15:28:37.701+0800][5.160s][info][gc,marking   ] GC(8) Concurrent Clear Claimed Marks
[2025-09-10T15:28:37.701+0800][5.161s][info][gc,marking   ] GC(8) Concurrent Clear Claimed Marks 0.216ms
[2025-09-10T15:28:37.702+0800][5.161s][info][gc,marking   ] GC(8) Concurrent Scan Root Regions
[2025-09-10T15:28:37.704+0800][5.163s][info][gc,marking   ] GC(8) Concurrent Scan Root Regions 2.322ms
[2025-09-10T15:28:37.704+0800][5.163s][info][gc,marking   ] GC(8) Concurrent Mark (5.163s)
[2025-09-10T15:28:37.704+0800][5.163s][info][gc,marking   ] GC(8) Concurrent Mark From Roots
[2025-09-10T15:28:37.704+0800][5.164s][info][gc,task      ] GC(8) Using 4 workers of 4 for marking
[2025-09-10T15:28:37.708+0800][5.167s][info][gc,marking   ] GC(8) Concurrent Mark From Roots 3.602ms
[2025-09-10T15:28:37.708+0800][5.167s][info][gc,marking   ] GC(8) Concurrent Preclean
[2025-09-10T15:28:37.708+0800][5.168s][info][gc,marking   ] GC(8) Concurrent Preclean 0.278ms
[2025-09-10T15:28:37.709+0800][5.168s][info][gc,marking   ] GC(8) Concurrent Mark (5.163s, 5.168s) 4.528ms
[2025-09-10T15:28:37.709+0800][5.168s][info][gc,start     ] GC(8) Pause Remark
[2025-09-10T15:28:37.711+0800][5.170s][info][gc,stringtable] GC(8) Cleaned string and symbol table, strings: 17647 processed, 38 removed, symbols: 57944 processed, 117 removed
[2025-09-10T15:28:37.711+0800][5.170s][info][gc            ] GC(8) Pause Remark 29M->29M(1026M) 2.122ms
[2025-09-10T15:28:37.711+0800][5.171s][info][gc,cpu        ] GC(8) User=0.02s Sys=0.00s Real=0.00s
[2025-09-10T15:28:37.712+0800][5.171s][info][gc,marking    ] GC(8) Concurrent Rebuild Remembered Sets
[2025-09-10T15:28:37.714+0800][5.173s][info][gc,marking    ] GC(8) Concurrent Rebuild Remembered Sets 2.090ms
[2025-09-10T15:28:37.714+0800][5.173s][info][gc,start      ] GC(8) Pause Cleanup
[2025-09-10T15:28:37.715+0800][5.174s][info][gc            ] GC(8) Pause Cleanup 29M->29M(1026M) 0.404ms
[2025-09-10T15:28:37.715+0800][5.174s][info][gc,cpu        ] GC(8) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T15:28:37.715+0800][5.174s][info][gc,marking    ] GC(8) Concurrent Cleanup for Next Mark
[2025-09-10T15:28:37.719+0800][5.178s][info][gc,marking    ] GC(8) Concurrent Cleanup for Next Mark 3.635ms
[2025-09-10T15:28:37.719+0800][5.178s][info][gc            ] GC(8) Concurrent Cycle 18.068ms
[2025-09-10T15:28:39.459+0800][6.918s][info][gc,start      ] GC(9) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:28:39.459+0800][6.919s][info][gc,task       ] GC(9) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:39.467+0800][6.927s][info][gc,phases     ] GC(9)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:28:39.468+0800][6.927s][info][gc,phases     ] GC(9)   Evacuate Collection Set: 5.1ms
[2025-09-10T15:28:39.468+0800][6.927s][info][gc,phases     ] GC(9)   Post Evacuate Collection Set: 2.4ms
[2025-09-10T15:28:39.468+0800][6.927s][info][gc,phases     ] GC(9)   Other: 0.7ms
[2025-09-10T15:28:39.468+0800][6.927s][info][gc,heap       ] GC(9) Eden regions: 440->0(589)
[2025-09-10T15:28:39.468+0800][6.928s][info][gc,heap       ] GC(9) Survivor regions: 9->25(57)
[2025-09-10T15:28:39.469+0800][6.928s][info][gc,heap       ] GC(9) Old regions: 20->20
[2025-09-10T15:28:39.469+0800][6.928s][info][gc,heap       ] GC(9) Humongous regions: 3->1
[2025-09-10T15:28:39.469+0800][6.928s][info][gc,metaspace  ] GC(9) Metaspace: 32564K(33664K)->32564K(33664K) NonClass: 28717K(29440K)->28717K(29440K) Class: 3846K(4224K)->3846K(4224K)
[2025-09-10T15:28:39.469+0800][6.928s][info][gc            ] GC(9) Pause Young (Normal) (G1 Evacuation Pause) 469M->43M(1026M) 10.172ms
[2025-09-10T15:28:39.469+0800][6.929s][info][gc,cpu        ] GC(9) User=0.08s Sys=0.02s Real=0.01s
[2025-09-10T15:28:42.941+0800][10.400s][info][gc,start      ] GC(10) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T15:28:42.941+0800][10.400s][info][gc,task       ] GC(10) Using 15 workers of 15 for evacuation
[2025-09-10T15:28:42.948+0800][10.407s][info][gc,phases     ] GC(10)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T15:28:42.948+0800][10.407s][info][gc,phases     ] GC(10)   Evacuate Collection Set: 5.5ms
[2025-09-10T15:28:42.948+0800][10.407s][info][gc,phases     ] GC(10)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T15:28:42.948+0800][10.408s][info][gc,phases     ] GC(10)   Other: 0.6ms
[2025-09-10T15:28:42.949+0800][10.408s][info][gc,heap       ] GC(10) Eden regions: 51->0(586)
[2025-09-10T15:28:42.949+0800][10.408s][info][gc,heap       ] GC(10) Survivor regions: 25->28(77)
[2025-09-10T15:28:42.950+0800][10.409s][info][gc,heap       ] GC(10) Old regions: 20->20
[2025-09-10T15:28:42.950+0800][10.409s][info][gc,heap       ] GC(10) Humongous regions: 1->1
[2025-09-10T15:28:42.950+0800][10.409s][info][gc,metaspace  ] GC(10) Metaspace: 34484K(35840K)->34484K(35840K) NonClass: 30436K(31232K)->30436K(31232K) Class: 4048K(4608K)->4048K(4608K)
[2025-09-10T15:28:42.950+0800][10.409s][info][gc            ] GC(10) Pause Young (Concurrent Start) (Metadata GC Threshold) 93M->46M(1026M) 9.153ms
[2025-09-10T15:28:42.950+0800][10.409s][info][gc,cpu        ] GC(10) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T15:28:42.950+0800][10.410s][info][gc            ] GC(11) Concurrent Cycle
[2025-09-10T15:28:42.951+0800][10.410s][info][gc,marking    ] GC(11) Concurrent Clear Claimed Marks
[2025-09-10T15:28:42.951+0800][10.410s][info][gc,marking    ] GC(11) Concurrent Clear Claimed Marks 0.276ms
[2025-09-10T15:28:42.951+0800][10.410s][info][gc,marking    ] GC(11) Concurrent Scan Root Regions
[2025-09-10T15:28:42.953+0800][10.412s][info][gc,marking    ] GC(11) Concurrent Scan Root Regions 1.584ms
[2025-09-10T15:28:42.953+0800][10.412s][info][gc,marking    ] GC(11) Concurrent Mark (10.412s)
[2025-09-10T15:28:42.953+0800][10.412s][info][gc,marking    ] GC(11) Concurrent Mark From Roots
[2025-09-10T15:28:42.953+0800][10.412s][info][gc,task       ] GC(11) Using 4 workers of 4 for marking
[2025-09-10T15:28:42.956+0800][10.415s][info][gc,marking    ] GC(11) Concurrent Mark From Roots 2.817ms
[2025-09-10T15:28:42.956+0800][10.415s][info][gc,marking    ] GC(11) Concurrent Preclean
[2025-09-10T15:28:42.956+0800][10.415s][info][gc,marking    ] GC(11) Concurrent Preclean 0.174ms
[2025-09-10T15:28:42.956+0800][10.415s][info][gc,marking    ] GC(11) Concurrent Mark (10.412s, 10.415s) 3.344ms
[2025-09-10T15:28:42.957+0800][10.416s][info][gc,start      ] GC(11) Pause Remark
[2025-09-10T15:28:42.961+0800][10.420s][info][gc,stringtable] GC(11) Cleaned string and symbol table, strings: 24294 processed, 0 removed, symbols: 99864 processed, 72 removed
[2025-09-10T15:28:42.961+0800][10.420s][info][gc            ] GC(11) Pause Remark 47M->47M(1026M) 4.736ms
[2025-09-10T15:28:42.962+0800][10.421s][info][gc,cpu        ] GC(11) User=0.06s Sys=0.00s Real=0.00s
[2025-09-10T15:28:42.962+0800][10.421s][info][gc,marking    ] GC(11) Concurrent Rebuild Remembered Sets
[2025-09-10T15:28:42.964+0800][10.423s][info][gc,marking    ] GC(11) Concurrent Rebuild Remembered Sets 1.708ms
[2025-09-10T15:28:42.964+0800][10.423s][info][gc,start      ] GC(11) Pause Cleanup
[2025-09-10T15:28:42.964+0800][10.424s][info][gc            ] GC(11) Pause Cleanup 47M->47M(1026M) 0.545ms
[2025-09-10T15:28:42.965+0800][10.424s][info][gc,cpu        ] GC(11) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T15:28:42.965+0800][10.424s][info][gc,marking    ] GC(11) Concurrent Cleanup for Next Mark
[2025-09-10T15:28:42.968+0800][10.427s][info][gc,marking    ] GC(11) Concurrent Cleanup for Next Mark 3.016ms
[2025-09-10T15:28:42.968+0800][10.427s][info][gc            ] GC(11) Concurrent Cycle 17.701ms
[2025-09-10T15:29:09.599+0800][37.060s][info][gc,start      ] GC(12) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:29:09.601+0800][37.062s][info][gc,task       ] GC(12) Using 15 workers of 15 for evacuation
[2025-09-10T15:29:09.622+0800][37.083s][info][gc,phases     ] GC(12)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:29:09.623+0800][37.084s][info][gc,phases     ] GC(12)   Evacuate Collection Set: 14.7ms
[2025-09-10T15:29:09.623+0800][37.084s][info][gc,phases     ] GC(12)   Post Evacuate Collection Set: 5.0ms
[2025-09-10T15:29:09.623+0800][37.084s][info][gc,phases     ] GC(12)   Other: 3.1ms
[2025-09-10T15:29:09.623+0800][37.084s][info][gc,heap       ] GC(12) Eden regions: 586->0(567)
[2025-09-10T15:29:09.624+0800][37.085s][info][gc,heap       ] GC(12) Survivor regions: 28->47(77)
[2025-09-10T15:29:09.624+0800][37.085s][info][gc,heap       ] GC(12) Old regions: 20->20
[2025-09-10T15:29:09.624+0800][37.085s][info][gc,heap       ] GC(12) Humongous regions: 3->1
[2025-09-10T15:29:09.624+0800][37.085s][info][gc,metaspace  ] GC(12) Metaspace: 52109K(54272K)->52109K(54272K) NonClass: 45961K(47360K)->45961K(47360K) Class: 6148K(6912K)->6148K(6912K)
[2025-09-10T15:29:09.625+0800][37.085s][info][gc            ] GC(12) Pause Young (Normal) (G1 Evacuation Pause) 634M->65M(1026M) 25.663ms
[2025-09-10T15:29:09.625+0800][37.086s][info][gc,cpu        ] GC(12) User=0.23s Sys=0.08s Real=0.03s
[2025-09-10T15:29:10.674+0800][38.135s][info][gc,start      ] GC(13) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T15:29:10.674+0800][38.135s][info][gc,task       ] GC(13) Using 15 workers of 15 for evacuation
[2025-09-10T15:29:10.695+0800][38.156s][info][gc,phases     ] GC(13)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T15:29:10.696+0800][38.157s][info][gc,phases     ] GC(13)   Evacuate Collection Set: 16.2ms
[2025-09-10T15:29:10.696+0800][38.157s][info][gc,phases     ] GC(13)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T15:29:10.697+0800][38.157s][info][gc,phases     ] GC(13)   Other: 4.0ms
[2025-09-10T15:29:10.697+0800][38.158s][info][gc,heap       ] GC(13) Eden regions: 176->0(569)
[2025-09-10T15:29:10.697+0800][38.158s][info][gc,heap       ] GC(13) Survivor regions: 47->45(77)
[2025-09-10T15:29:10.697+0800][38.158s][info][gc,heap       ] GC(13) Old regions: 20->27
[2025-09-10T15:29:10.698+0800][38.158s][info][gc,heap       ] GC(13) Humongous regions: 1->1
[2025-09-10T15:29:10.698+0800][38.159s][info][gc,metaspace  ] GC(13) Metaspace: 58105K(60160K)->58105K(60160K) NonClass: 51195K(52480K)->51195K(52480K) Class: 6909K(7680K)->6909K(7680K)
[2025-09-10T15:29:10.698+0800][38.159s][info][gc            ] GC(13) Pause Young (Concurrent Start) (Metadata GC Threshold) 240M->71M(1026M) 24.689ms
[2025-09-10T15:29:10.699+0800][38.160s][info][gc,cpu        ] GC(13) User=0.14s Sys=0.02s Real=0.02s
[2025-09-10T15:29:10.699+0800][38.160s][info][gc            ] GC(14) Concurrent Cycle
[2025-09-10T15:29:10.701+0800][38.162s][info][gc,marking    ] GC(14) Concurrent Clear Claimed Marks
[2025-09-10T15:29:10.702+0800][38.163s][info][gc,marking    ] GC(14) Concurrent Clear Claimed Marks 0.873ms
[2025-09-10T15:29:10.702+0800][38.163s][info][gc,marking    ] GC(14) Concurrent Scan Root Regions
[2025-09-10T15:29:10.707+0800][38.168s][info][gc,marking    ] GC(14) Concurrent Scan Root Regions 4.926ms
[2025-09-10T15:29:10.708+0800][38.169s][info][gc,marking    ] GC(14) Concurrent Mark (38.169s)
[2025-09-10T15:29:10.708+0800][38.169s][info][gc,marking    ] GC(14) Concurrent Mark From Roots
[2025-09-10T15:29:10.709+0800][38.170s][info][gc,task       ] GC(14) Using 4 workers of 4 for marking
[2025-09-10T15:29:10.715+0800][38.176s][info][gc,marking    ] GC(14) Concurrent Mark From Roots 6.746ms
[2025-09-10T15:29:10.716+0800][38.177s][info][gc,marking    ] GC(14) Concurrent Preclean
[2025-09-10T15:29:10.717+0800][38.178s][info][gc,marking    ] GC(14) Concurrent Preclean 1.578ms
[2025-09-10T15:29:10.718+0800][38.179s][info][gc,marking    ] GC(14) Concurrent Mark (38.169s, 38.179s) 9.648ms
[2025-09-10T15:29:10.718+0800][38.179s][info][gc,start      ] GC(14) Pause Remark
[2025-09-10T15:29:10.722+0800][38.183s][info][gc,stringtable] GC(14) Cleaned string and symbol table, strings: 57729 processed, 0 removed, symbols: 152942 processed, 125 removed
[2025-09-10T15:29:10.724+0800][38.185s][info][gc            ] GC(14) Pause Remark 76M->76M(1026M) 5.337ms
[2025-09-10T15:29:10.724+0800][38.185s][info][gc,cpu        ] GC(14) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T15:29:10.724+0800][38.185s][info][gc,marking    ] GC(14) Concurrent Rebuild Remembered Sets
[2025-09-10T15:29:10.729+0800][38.189s][info][gc,marking    ] GC(14) Concurrent Rebuild Remembered Sets 4.358ms
[2025-09-10T15:29:10.729+0800][38.190s][info][gc,start      ] GC(14) Pause Cleanup
[2025-09-10T15:29:10.730+0800][38.191s][info][gc            ] GC(14) Pause Cleanup 80M->80M(1026M) 0.537ms
[2025-09-10T15:29:10.730+0800][38.191s][info][gc,cpu        ] GC(14) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T15:29:10.731+0800][38.191s][info][gc,marking    ] GC(14) Concurrent Cleanup for Next Mark
[2025-09-10T15:29:10.732+0800][38.192s][info][gc,marking    ] GC(14) Concurrent Cleanup for Next Mark 0.990ms
[2025-09-10T15:29:10.732+0800][38.193s][info][gc            ] GC(14) Concurrent Cycle 33.414ms
[2025-09-10T15:29:18.473+0800][45.933s][info][gc,start      ] GC(15) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T15:29:18.473+0800][45.934s][info][gc,task       ] GC(15) Using 15 workers of 15 for evacuation
[2025-09-10T15:29:18.486+0800][45.947s][info][gc,phases     ] GC(15)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T15:29:18.487+0800][45.948s][info][gc,phases     ] GC(15)   Evacuate Collection Set: 11.8ms
[2025-09-10T15:29:18.488+0800][45.948s][info][gc,phases     ] GC(15)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T15:29:18.488+0800][45.949s][info][gc,phases     ] GC(15)   Other: 0.6ms
[2025-09-10T15:29:18.488+0800][45.949s][info][gc,heap       ] GC(15) Eden regions: 569->0(567)
[2025-09-10T15:29:18.488+0800][45.949s][info][gc,heap       ] GC(15) Survivor regions: 45->47(77)
[2025-09-10T15:29:18.489+0800][45.950s][info][gc,heap       ] GC(15) Old regions: 27->35
[2025-09-10T15:29:18.489+0800][45.950s][info][gc,heap       ] GC(15) Humongous regions: 2->1
[2025-09-10T15:29:18.489+0800][45.950s][info][gc,metaspace  ] GC(15) Metaspace: 63761K(66048K)->63761K(66048K) NonClass: 56351K(57856K)->56351K(57856K) Class: 7410K(8192K)->7410K(8192K)
[2025-09-10T15:29:18.489+0800][45.950s][info][gc            ] GC(15) Pause Young (Normal) (G1 Evacuation Pause) 641M->81M(1026M) 16.577ms
[2025-09-10T15:29:18.489+0800][45.950s][info][gc,cpu        ] GC(15) User=0.15s Sys=0.05s Real=0.02s
