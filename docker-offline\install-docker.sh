#!/bin/bash

# =======================================================
# Docker CE 离线安装脚本 - 精简版
# 解决 "Failed to start docker.service: Unit not found" 问题
# 支持: CentOS 7.x
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Docker CE 离线安装脚本 - 精简版"
echo "========================================"

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    log_error "需要root权限运行"
    log_info "请使用: sudo $0"
    exit 1
fi

# 检查系统版本
if [ ! -f /etc/redhat-release ] || ! grep -q "CentOS.*7" /etc/redhat-release; then
    log_error "仅支持CentOS 7.x系统"
    exit 1
fi

log_info "系统检查通过: $(cat /etc/redhat-release)"

# 卸载旧版本
log_step "清理旧版本..."
yum remove -y docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine podman runc 2>/dev/null || true

# 检查RPM包
log_step "检查RPM包..."
cd "$SCRIPT_DIR"

required_packages=(
    "libcgroup-0.41-21.el7.x86_64.rpm"
    "libseccomp-2.3.1-4.el7.x86_64.rpm"
    "container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm"
    "containerd.io-1.6.9-3.1.el7.x86_64.rpm"
    "docker-ce-cli-26.1.4-1.el7.x86_64.rpm"
    "docker-ce-26.1.4-1.el7.x86_64.rpm"
)

for package in "${required_packages[@]}"; do
    if [ ! -f "$package" ]; then
        log_error "缺失包: $package"
        exit 1
    fi
done

log_info "RPM包检查完成"

# 安装包
log_step "安装Docker包..."
for package in "${required_packages[@]}"; do
    if [ -f "$package" ]; then
        log_info "安装: $package"
        rpm -ivh "$package" --force --nodeps --nosignature --nodigest 2>/dev/null || \
        rpm -Uvh "$package" --force --nodeps --nosignature --nodigest 2>/dev/null || true
    fi
done

# 创建systemd服务文件
log_step "创建Docker服务文件..."
mkdir -p /usr/lib/systemd/system

cat > /usr/lib/systemd/system/docker.service << 'EOF'
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service containerd.service
Wants=network-online.target
Requires=docker.socket containerd.service

[Service]
Type=notify
ExecStart=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock
ExecReload=/bin/kill -s HUP $MAINPID
TimeoutSec=0
RestartSec=2
Restart=always
StartLimitBurst=3
StartLimitInterval=60s
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
TasksMax=infinity
Delegate=yes
KillMode=process

[Install]
WantedBy=multi-user.target
EOF

cat > /usr/lib/systemd/system/docker.socket << 'EOF'
[Unit]
Description=Docker Socket for the API
PartOf=docker.service

[Socket]
ListenStream=/var/run/docker.sock
SocketMode=0660
SocketUser=root
SocketGroup=docker

[Install]
WantedBy=sockets.target
EOF

chmod 644 /usr/lib/systemd/system/docker.service
chmod 644 /usr/lib/systemd/system/docker.socket

# 配置Docker
log_step "配置Docker..."
mkdir -p /etc/docker /var/lib/docker

cat > /etc/docker/daemon.json << 'EOF'
{
    "storage-driver": "overlay2",
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    }
}
EOF

# 启动服务
log_step "启动Docker服务..."
groupadd docker 2>/dev/null || true
systemctl daemon-reload
systemctl enable docker
systemctl enable docker.socket

# 处理SELinux
if command -v getenforce >/dev/null 2>&1 && [ "$(getenforce)" = "Enforcing" ]; then
    log_warn "临时设置SELinux为宽松模式"
    setenforce 0 2>/dev/null || true
fi

# 启动服务
if systemctl start docker; then
    log_info "✅ Docker服务启动成功"
else
    log_warn "systemctl启动失败，尝试手动启动..."
    nohup /usr/bin/dockerd > /var/log/docker.log 2>&1 &
    sleep 5
    if pgrep dockerd >/dev/null; then
        log_info "✅ Docker手动启动成功"
    else
        log_error "❌ Docker启动失败"
        exit 1
    fi
fi

# 验证安装
log_step "验证安装..."
sleep 3

if command -v docker >/dev/null 2>&1; then
    docker_version=$(docker --version 2>/dev/null || echo "版本获取失败")
    log_info "Docker版本: $docker_version"
    
    # 等待Docker API就绪
    for i in {1..10}; do
        if docker info >/dev/null 2>&1; then
            log_info "✅ Docker验证成功"
            break
        fi
        sleep 2
    done
else
    log_error "❌ Docker命令未找到"
    exit 1
fi

echo ""
echo "========================================"
echo "  Docker CE 安装完成"
echo "========================================"
echo ""
echo "✅ Docker版本: $(docker --version 2>/dev/null || echo '未知')"
echo "✅ 服务状态: $(systemctl is-active docker 2>/dev/null || echo '手动运行')"
echo ""
echo "常用命令:"
echo "  检查状态: systemctl status docker"
echo "  查看信息: docker info"
echo "  启动JIRA: cd .. && docker compose up -d"
echo ""
echo "如遇权限问题，请运行:"
echo "  usermod -aG docker \$USER"
echo "  然后重新登录"
echo ""

log_info "安装完成！"
