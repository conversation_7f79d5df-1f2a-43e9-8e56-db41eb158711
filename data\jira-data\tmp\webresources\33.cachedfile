/* module-key = 'jira.webresources:loginpage-responsive', location = '/static/page/context/jira-login/jira-login.less' */
.page-type-login #content{padding:0 20px;text-align:center}.page-type-login .aui-page-panel{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;-ms-box-sizing:border-box;box-sizing:border-box;border:1px solid #c1c7d0;border-radius:5px;display:inline-block;margin:50px auto 0;padding:40px;text-align:left}.page-type-login .aui-page-panel-inner,.page-type-login .aui-page-panel-content{padding:0}@media screen and (max-width:768px){.page-type-login #aui-flag-container,.page-type-login .aui-flag{display:none}.page-type-login .aui-dialog,.page-type-login .jira-dialog,.page-type-login #atlwdg-container{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;-ms-box-sizing:border-box;box-sizing:border-box;bottom:0;max-height:calc(100% - 100px);position:fixed;margin:0 !important;left:0;right:0;top:40px;width:auto !important}}.jira-login-method{margin:20px 0 0;width:auto}@media screen and (max-width:1024px){.page-type-login{min-width:unset}}@media screen and (max-width:768px){.page-type-login,.page-type-login .aui-page-header,.page-type-login #footer,.page-type-login #footer .footer-body{width:auto !important}.page-type-login #header #quicksearch-menu,.page-type-login #header #create-menu,.page-type-login #project_template_create_link,.page-type-login #keyshortscuthelp,.page-type-login #view_about,.page-type-login #view_credits{display:none}.page-type-login #content{padding:0}.page-type-login .aui-page-panel{margin:20px auto 0;padding:20px;width:300px !important}.page-type-login .aui-message.aui-message{padding:10px;padding-left:40px}.page-type-login .aui-message.aui-message::before{width:30px}.page-type-login .aui-message.aui-message::after{left:7px}.jira-login-method>.jira-login-item{display:block;padding:0;text-align:left;width:100%}.page-type-login form.aui .field-group,.page-type-login form.aui .group,.page-type-login form.aui .buttons-container{padding-left:0;padding-top:8px;padding-bottom:8px}.page-type-login form.aui .group>legend,.page-type-login form.aui .field-group>label{display:block;float:none;margin-left:0;padding:0 0 4px;text-align:inherit;width:auto}.page-type-login form.aui .group>legend{top:8px}.page-type-login form.aui .group>legend+.matrix{padding-top:0}.page-type-login form.aui .icon-required{position:static;margin-left:0.2em}.page-type-login form.aui .medium-field{max-width:none}#footer,#footer .footer-body{margin-left:auto;margin-right:auto;padding-left:0;padding-right:0;max-width:300px !important}#footer{float:none;margin:0 auto}#footer .footer-body>ul>li{display:block;white-space:normal}#footer .footer-body>ul>li::after{display:none}#footer .footer-body>ul>li+li{margin-top:8px}#footer-build-information{display:inline-block}}@media screen and (max-width:320px){.page-type-login #header .aui-header-primary .aui-nav{display:none}.page-type-login #header .aui-header-secondary .aui-nav li{display:none}.page-type-login #header .aui-header-secondary .aui-nav li#system-help-menu,.page-type-login #header .aui-header-secondary .aui-nav li#user-options{display:list-item}.page-type-login #content{padding:0}.page-type-login .aui-page-panel{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;-ms-box-sizing:border-box;box-sizing:border-box;border-left:none;border-right:none;border-top:none;border-radius:0;display:block;margin:0;width:100% !important}.jira-login-method{margin-top:10px;width:100%}.jira-login-method>.jira-login-item{display:block;width:100%}}.page-type-login input{-webkit-user-modify:read-write-plaintext-only}