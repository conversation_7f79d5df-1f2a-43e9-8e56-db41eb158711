[2025-09-10T16:48:11.551+0800][0.008s][info][gc,heap] Heap region size: 1M
[2025-09-10T16:48:11.559+0800][0.015s][info][gc     ] Using G1
[2025-09-10T16:48:11.559+0800][0.016s][info][gc,heap,coops] Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
[2025-09-10T16:48:11.560+0800][0.016s][info][gc,cds       ] Mark closed archive regions in map: [0x00000000ffe00000, 0x00000000ffe6bff8]
[2025-09-10T16:48:11.560+0800][0.017s][info][gc,cds       ] Mark open archive regions in map: [0x00000000ffc00000, 0x00000000ffc46ff8]
[2025-09-10T16:48:12.432+0800][0.889s][info][gc,start     ] GC(0) Pa<PERSON> Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:13.442+0800][1.898s][info][gc,task      ] GC(0) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:13.449+0800][1.905s][info][gc,phases    ] GC(0)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:13.449+0800][1.906s][info][gc,phases    ] GC(0)   Evacuate Collection Set: 2.9ms
[2025-09-10T16:48:13.449+0800][1.906s][info][gc,phases    ] GC(0)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T16:48:13.450+0800][1.906s][info][gc,phases    ] GC(0)   Other: 1011.4ms
[2025-09-10T16:48:13.450+0800][1.907s][info][gc,heap      ] GC(0) Eden regions: 51->0(63)
[2025-09-10T16:48:13.452+0800][1.909s][info][gc,heap      ] GC(0) Survivor regions: 0->6(7)
[2025-09-10T16:48:13.454+0800][1.910s][info][gc,heap      ] GC(0) Old regions: 2->2
[2025-09-10T16:48:13.455+0800][1.911s][info][gc,heap      ] GC(0) Humongous regions: 1->0
[2025-09-10T16:48:13.455+0800][1.912s][info][gc,metaspace ] GC(0) Metaspace: 13030K(13952K)->13030K(13952K) NonClass: 11714K(12288K)->11714K(12288K) Class: 1315K(1664K)->1315K(1664K)
[2025-09-10T16:48:13.456+0800][1.912s][info][gc           ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 52M->6M(1026M) 1023.197ms
[2025-09-10T16:48:13.456+0800][1.912s][info][gc,cpu       ] GC(0) User=0.06s Sys=0.01s Real=1.02s
[2025-09-10T16:48:13.841+0800][2.298s][info][gc,start     ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:13.842+0800][2.298s][info][gc,task      ] GC(1) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:13.848+0800][2.304s][info][gc,phases    ] GC(1)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:13.848+0800][2.304s][info][gc,phases    ] GC(1)   Evacuate Collection Set: 4.8ms
[2025-09-10T16:48:13.848+0800][2.305s][info][gc,phases    ] GC(1)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T16:48:13.848+0800][2.305s][info][gc,phases    ] GC(1)   Other: 0.7ms
[2025-09-10T16:48:13.849+0800][2.305s][info][gc,heap      ] GC(1) Eden regions: 49->0(76)
[2025-09-10T16:48:13.849+0800][2.305s][info][gc,heap      ] GC(1) Survivor regions: 6->7(7)
[2025-09-10T16:48:13.849+0800][2.306s][info][gc,heap      ] GC(1) Old regions: 2->13
[2025-09-10T16:48:13.849+0800][2.306s][info][gc,heap      ] GC(1) Humongous regions: 1->1
[2025-09-10T16:48:13.850+0800][2.306s][info][gc,metaspace ] GC(1) Metaspace: 14332K(15104K)->14332K(15104K) NonClass: 12893K(13312K)->12893K(13312K) Class: 1438K(1792K)->1438K(1792K)
[2025-09-10T16:48:13.850+0800][2.306s][info][gc           ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 56M->19M(1026M) 8.650ms
[2025-09-10T16:48:13.850+0800][2.307s][info][gc,cpu       ] GC(1) User=0.07s Sys=0.01s Real=0.02s
[2025-09-10T16:48:14.296+0800][2.753s][info][gc,start     ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:14.297+0800][2.754s][info][gc,task      ] GC(2) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:14.303+0800][2.759s][info][gc,phases    ] GC(2)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:14.303+0800][2.759s][info][gc,phases    ] GC(2)   Evacuate Collection Set: 3.1ms
[2025-09-10T16:48:14.304+0800][2.760s][info][gc,phases    ] GC(2)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T16:48:14.304+0800][2.761s][info][gc,phases    ] GC(2)   Other: 2.4ms
[2025-09-10T16:48:14.305+0800][2.761s][info][gc,heap      ] GC(2) Eden regions: 76->0(96)
[2025-09-10T16:48:14.306+0800][2.762s][info][gc,heap      ] GC(2) Survivor regions: 7->4(11)
[2025-09-10T16:48:14.306+0800][2.763s][info][gc,heap      ] GC(2) Old regions: 13->20
[2025-09-10T16:48:14.307+0800][2.763s][info][gc,heap      ] GC(2) Humongous regions: 1->1
[2025-09-10T16:48:14.307+0800][2.764s][info][gc,metaspace ] GC(2) Metaspace: 15612K(16512K)->15612K(16512K) NonClass: 14059K(14592K)->14059K(14592K) Class: 1552K(1920K)->1552K(1920K)
[2025-09-10T16:48:14.307+0800][2.764s][info][gc           ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 95M->22M(1026M) 10.818ms
[2025-09-10T16:48:14.307+0800][2.764s][info][gc,cpu       ] GC(2) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T16:48:14.717+0800][3.173s][info][gc,start     ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:14.718+0800][3.175s][info][gc,task      ] GC(3) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:14.725+0800][3.181s][info][gc,phases    ] GC(3)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:14.727+0800][3.183s][info][gc,phases    ] GC(3)   Evacuate Collection Set: 1.8ms
[2025-09-10T16:48:14.727+0800][3.184s][info][gc,phases    ] GC(3)   Post Evacuate Collection Set: 2.7ms
[2025-09-10T16:48:14.727+0800][3.184s][info][gc,phases    ] GC(3)   Other: 3.3ms
[2025-09-10T16:48:14.728+0800][3.184s][info][gc,heap      ] GC(3) Eden regions: 96->0(167)
[2025-09-10T16:48:14.728+0800][3.184s][info][gc,heap      ] GC(3) Survivor regions: 4->4(13)
[2025-09-10T16:48:14.728+0800][3.184s][info][gc,heap      ] GC(3) Old regions: 20->20
[2025-09-10T16:48:14.728+0800][3.185s][info][gc,heap      ] GC(3) Humongous regions: 1->1
[2025-09-10T16:48:14.728+0800][3.185s][info][gc,metaspace ] GC(3) Metaspace: 15659K(16512K)->15659K(16512K) NonClass: 14106K(14592K)->14106K(14592K) Class: 1552K(1920K)->1552K(1920K)
[2025-09-10T16:48:14.729+0800][3.185s][info][gc           ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 118M->23M(1026M) 11.777ms
[2025-09-10T16:48:14.729+0800][3.185s][info][gc,cpu       ] GC(3) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T16:48:15.458+0800][3.915s][info][gc,start     ] GC(4) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:15.459+0800][3.915s][info][gc,task      ] GC(4) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:15.463+0800][3.919s][info][gc,phases    ] GC(4)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:15.465+0800][3.922s][info][gc,phases    ] GC(4)   Evacuate Collection Set: 1.9ms
[2025-09-10T16:48:15.466+0800][3.922s][info][gc,phases    ] GC(4)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T16:48:15.466+0800][3.923s][info][gc,phases    ] GC(4)   Other: 1.6ms
[2025-09-10T16:48:15.467+0800][3.923s][info][gc,heap      ] GC(4) Eden regions: 167->0(211)
[2025-09-10T16:48:15.469+0800][3.925s][info][gc,heap      ] GC(4) Survivor regions: 4->6(22)
[2025-09-10T16:48:15.471+0800][3.927s][info][gc,heap      ] GC(4) Old regions: 20->20
[2025-09-10T16:48:15.473+0800][3.929s][info][gc,heap      ] GC(4) Humongous regions: 1->1
[2025-09-10T16:48:15.474+0800][3.931s][info][gc,metaspace ] GC(4) Metaspace: 15660K(16512K)->15660K(16512K) NonClass: 14107K(14592K)->14107K(14592K) Class: 1552K(1920K)->1552K(1920K)
[2025-09-10T16:48:15.476+0800][3.932s][info][gc           ] GC(4) Pause Young (Normal) (G1 Evacuation Pause) 190M->24M(1026M) 17.680ms
[2025-09-10T16:48:15.477+0800][3.934s][info][gc,cpu       ] GC(4) User=0.05s Sys=0.00s Real=0.02s
[2025-09-10T16:48:16.303+0800][4.760s][info][gc,start     ] GC(5) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:16.304+0800][4.760s][info][gc,task      ] GC(5) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:16.308+0800][4.764s][info][gc,phases    ] GC(5)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:16.309+0800][4.766s][info][gc,phases    ] GC(5)   Evacuate Collection Set: 3.0ms
[2025-09-10T16:48:16.309+0800][4.766s][info][gc,phases    ] GC(5)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T16:48:16.310+0800][4.766s][info][gc,phases    ] GC(5)   Other: 0.9ms
[2025-09-10T16:48:16.310+0800][4.766s][info][gc,heap      ] GC(5) Eden regions: 211->0(607)
[2025-09-10T16:48:16.310+0800][4.766s][info][gc,heap      ] GC(5) Survivor regions: 6->7(28)
[2025-09-10T16:48:16.310+0800][4.767s][info][gc,heap      ] GC(5) Old regions: 20->20
[2025-09-10T16:48:16.310+0800][4.767s][info][gc,heap      ] GC(5) Humongous regions: 1->1
[2025-09-10T16:48:16.311+0800][4.767s][info][gc,metaspace ] GC(5) Metaspace: 15663K(16768K)->15663K(16768K) NonClass: 14110K(14848K)->14110K(14848K) Class: 1552K(1920K)->1552K(1920K)
[2025-09-10T16:48:16.311+0800][4.768s][info][gc           ] GC(5) Pause Young (Normal) (G1 Evacuation Pause) 235M->26M(1026M) 7.715ms
[2025-09-10T16:48:16.311+0800][4.768s][info][gc,cpu       ] GC(5) User=0.03s Sys=0.01s Real=0.01s
[2025-09-10T16:48:18.116+0800][6.572s][info][gc,start     ] GC(6) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T16:48:18.118+0800][6.574s][info][gc,task      ] GC(6) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:18.123+0800][6.580s][info][gc,phases    ] GC(6)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:18.124+0800][6.580s][info][gc,phases    ] GC(6)   Evacuate Collection Set: 4.0ms
[2025-09-10T16:48:18.124+0800][6.580s][info][gc,phases    ] GC(6)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T16:48:18.124+0800][6.580s][info][gc,phases    ] GC(6)   Other: 2.3ms
[2025-09-10T16:48:18.124+0800][6.580s][info][gc,heap      ] GC(6) Eden regions: 336->0(606)
[2025-09-10T16:48:18.124+0800][6.581s][info][gc,heap      ] GC(6) Survivor regions: 7->8(77)
[2025-09-10T16:48:18.124+0800][6.581s][info][gc,heap      ] GC(6) Old regions: 20->20
[2025-09-10T16:48:18.125+0800][6.581s][info][gc,heap      ] GC(6) Humongous regions: 1->1
[2025-09-10T16:48:18.125+0800][6.581s][info][gc,metaspace ] GC(6) Metaspace: 20474K(21296K)->20474K(21296K) NonClass: 18341K(18864K)->18341K(18864K) Class: 2133K(2432K)->2133K(2432K)
[2025-09-10T16:48:18.125+0800][6.581s][info][gc           ] GC(6) Pause Young (Concurrent Start) (Metadata GC Threshold) 361M->27M(1026M) 8.940ms
[2025-09-10T16:48:18.125+0800][6.581s][info][gc,cpu       ] GC(6) User=0.04s Sys=0.01s Real=0.01s
[2025-09-10T16:48:18.125+0800][6.581s][info][gc           ] GC(7) Concurrent Cycle
[2025-09-10T16:48:18.125+0800][6.582s][info][gc,marking   ] GC(7) Concurrent Clear Claimed Marks
[2025-09-10T16:48:18.125+0800][6.582s][info][gc,marking   ] GC(7) Concurrent Clear Claimed Marks 0.226ms
[2025-09-10T16:48:18.126+0800][6.582s][info][gc,marking   ] GC(7) Concurrent Scan Root Regions
[2025-09-10T16:48:18.127+0800][6.583s][info][gc,marking   ] GC(7) Concurrent Scan Root Regions 1.365ms
[2025-09-10T16:48:18.127+0800][6.584s][info][gc,marking   ] GC(7) Concurrent Mark (6.584s)
[2025-09-10T16:48:18.127+0800][6.584s][info][gc,marking   ] GC(7) Concurrent Mark From Roots
[2025-09-10T16:48:18.127+0800][6.584s][info][gc,task      ] GC(7) Using 4 workers of 4 for marking
[2025-09-10T16:48:18.132+0800][6.589s][info][gc,marking   ] GC(7) Concurrent Mark From Roots 5.027ms
[2025-09-10T16:48:18.133+0800][6.589s][info][gc,marking   ] GC(7) Concurrent Preclean
[2025-09-10T16:48:18.133+0800][6.590s][info][gc,marking   ] GC(7) Concurrent Preclean 0.527ms
[2025-09-10T16:48:18.134+0800][6.590s][info][gc,marking   ] GC(7) Concurrent Mark (6.584s, 6.590s) 6.600ms
[2025-09-10T16:48:18.135+0800][6.591s][info][gc,start     ] GC(7) Pause Remark
[2025-09-10T16:48:18.137+0800][6.594s][info][gc,stringtable] GC(7) Cleaned string and symbol table, strings: 16497 processed, 38 removed, symbols: 49818 processed, 88 removed
[2025-09-10T16:48:18.138+0800][6.594s][info][gc            ] GC(7) Pause Remark 28M->28M(1026M) 2.492ms
[2025-09-10T16:48:18.138+0800][6.594s][info][gc,cpu        ] GC(7) User=0.02s Sys=0.00s Real=0.00s
[2025-09-10T16:48:18.138+0800][6.594s][info][gc,marking    ] GC(7) Concurrent Rebuild Remembered Sets
[2025-09-10T16:48:18.141+0800][6.597s][info][gc,marking    ] GC(7) Concurrent Rebuild Remembered Sets 2.686ms
[2025-09-10T16:48:18.141+0800][6.598s][info][gc,start      ] GC(7) Pause Cleanup
[2025-09-10T16:48:18.142+0800][6.598s][info][gc            ] GC(7) Pause Cleanup 30M->30M(1026M) 0.858ms
[2025-09-10T16:48:18.142+0800][6.599s][info][gc,cpu        ] GC(7) User=0.00s Sys=0.00s Real=0.01s
[2025-09-10T16:48:18.142+0800][6.599s][info][gc,marking    ] GC(7) Concurrent Cleanup for Next Mark
[2025-09-10T16:48:18.148+0800][6.605s][info][gc,marking    ] GC(7) Concurrent Cleanup for Next Mark 6.030ms
[2025-09-10T16:48:18.149+0800][6.605s][info][gc            ] GC(7) Concurrent Cycle 23.927ms
[2025-09-10T16:48:20.152+0800][8.608s][info][gc,start      ] GC(8) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T16:48:20.152+0800][8.609s][info][gc,task       ] GC(8) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:20.170+0800][8.626s][info][gc,phases     ] GC(8)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:48:20.170+0800][8.626s][info][gc,phases     ] GC(8)   Evacuate Collection Set: 13.3ms
[2025-09-10T16:48:20.170+0800][8.627s][info][gc,phases     ] GC(8)   Post Evacuate Collection Set: 3.6ms
[2025-09-10T16:48:20.170+0800][8.627s][info][gc,phases     ] GC(8)   Other: 0.7ms
[2025-09-10T16:48:20.171+0800][8.627s][info][gc,heap       ] GC(8) Eden regions: 468->0(591)
[2025-09-10T16:48:20.171+0800][8.627s][info][gc,heap       ] GC(8) Survivor regions: 8->23(77)
[2025-09-10T16:48:20.171+0800][8.628s][info][gc,heap       ] GC(8) Old regions: 20->20
[2025-09-10T16:48:20.171+0800][8.628s][info][gc,heap       ] GC(8) Humongous regions: 3->1
[2025-09-10T16:48:20.171+0800][8.628s][info][gc,metaspace  ] GC(8) Metaspace: 34845K(35888K)->34845K(35888K) NonClass: 30811K(31408K)->30811K(31408K) Class: 4034K(4480K)->4034K(4480K)
[2025-09-10T16:48:20.172+0800][8.628s][info][gc            ] GC(8) Pause Young (Concurrent Start) (Metadata GC Threshold) 497M->42M(1026M) 19.836ms
[2025-09-10T16:48:20.172+0800][8.628s][info][gc,cpu        ] GC(8) User=0.10s Sys=0.02s Real=0.02s
[2025-09-10T16:48:20.172+0800][8.629s][info][gc            ] GC(9) Concurrent Cycle
[2025-09-10T16:48:20.172+0800][8.629s][info][gc,marking    ] GC(9) Concurrent Clear Claimed Marks
[2025-09-10T16:48:20.173+0800][8.629s][info][gc,marking    ] GC(9) Concurrent Clear Claimed Marks 0.214ms
[2025-09-10T16:48:20.173+0800][8.629s][info][gc,marking    ] GC(9) Concurrent Scan Root Regions
[2025-09-10T16:48:20.175+0800][8.631s][info][gc,marking    ] GC(9) Concurrent Scan Root Regions 1.892ms
[2025-09-10T16:48:20.176+0800][8.632s][info][gc,marking    ] GC(9) Concurrent Mark (8.632s)
[2025-09-10T16:48:20.176+0800][8.632s][info][gc,marking    ] GC(9) Concurrent Mark From Roots
[2025-09-10T16:48:20.176+0800][8.632s][info][gc,task       ] GC(9) Using 4 workers of 4 for marking
[2025-09-10T16:48:20.182+0800][8.638s][info][gc,marking    ] GC(9) Concurrent Mark From Roots 5.990ms
[2025-09-10T16:48:20.182+0800][8.639s][info][gc,marking    ] GC(9) Concurrent Preclean
[2025-09-10T16:48:20.182+0800][8.639s][info][gc,marking    ] GC(9) Concurrent Preclean 0.200ms
[2025-09-10T16:48:20.182+0800][8.639s][info][gc,marking    ] GC(9) Concurrent Mark (8.632s, 8.639s) 6.805ms
[2025-09-10T16:48:20.183+0800][8.639s][info][gc,start      ] GC(9) Pause Remark
[2025-09-10T16:48:20.185+0800][8.642s][info][gc,stringtable] GC(9) Cleaned string and symbol table, strings: 23057 processed, 0 removed, symbols: 94528 processed, 97 removed
[2025-09-10T16:48:20.187+0800][8.643s][info][gc            ] GC(9) Pause Remark 43M->43M(1026M) 3.876ms
[2025-09-10T16:48:20.187+0800][8.643s][info][gc,cpu        ] GC(9) User=0.02s Sys=0.00s Real=0.00s
[2025-09-10T16:48:20.187+0800][8.644s][info][gc,marking    ] GC(9) Concurrent Rebuild Remembered Sets
[2025-09-10T16:48:20.190+0800][8.646s][info][gc,marking    ] GC(9) Concurrent Rebuild Remembered Sets 2.559ms
[2025-09-10T16:48:20.190+0800][8.647s][info][gc,start      ] GC(9) Pause Cleanup
[2025-09-10T16:48:20.191+0800][8.647s][info][gc            ] GC(9) Pause Cleanup 43M->43M(1026M) 0.348ms
[2025-09-10T16:48:20.191+0800][8.647s][info][gc,cpu        ] GC(9) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T16:48:20.191+0800][8.647s][info][gc,marking    ] GC(9) Concurrent Cleanup for Next Mark
[2025-09-10T16:48:20.196+0800][8.652s][info][gc,marking    ] GC(9) Concurrent Cleanup for Next Mark 4.808ms
[2025-09-10T16:48:20.196+0800][8.653s][info][gc            ] GC(9) Concurrent Cycle 24.048ms
[2025-09-10T16:48:43.695+0800][32.152s][info][gc,start      ] GC(10) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:43.695+0800][32.153s][info][gc,task       ] GC(10) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:43.710+0800][32.168s][info][gc,phases     ] GC(10)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:43.711+0800][32.169s][info][gc,phases     ] GC(10)   Evacuate Collection Set: 13.0ms
[2025-09-10T16:48:43.711+0800][32.169s][info][gc,phases     ] GC(10)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T16:48:43.711+0800][32.169s][info][gc,phases     ] GC(10)   Other: 0.8ms
[2025-09-10T16:48:43.712+0800][32.169s][info][gc,heap       ] GC(10) Eden regions: 591->0(570)
[2025-09-10T16:48:43.712+0800][32.170s][info][gc,heap       ] GC(10) Survivor regions: 23->44(77)
[2025-09-10T16:48:43.712+0800][32.170s][info][gc,heap       ] GC(10) Old regions: 20->20
[2025-09-10T16:48:43.712+0800][32.170s][info][gc,heap       ] GC(10) Humongous regions: 3->1
[2025-09-10T16:48:43.713+0800][32.170s][info][gc,metaspace  ] GC(10) Metaspace: 53970K(56112K)->53970K(56112K) NonClass: 47720K(49072K)->47720K(49072K) Class: 6250K(7040K)->6250K(7040K)
[2025-09-10T16:48:43.713+0800][32.171s][info][gc            ] GC(10) Pause Young (Normal) (G1 Evacuation Pause) 635M->63M(1026M) 18.059ms
[2025-09-10T16:48:43.713+0800][32.171s][info][gc,cpu        ] GC(10) User=0.14s Sys=0.06s Real=0.02s
[2025-09-10T16:48:44.653+0800][33.111s][info][gc,start      ] GC(11) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T16:48:44.653+0800][33.111s][info][gc,task       ] GC(11) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:44.668+0800][33.125s][info][gc,phases     ] GC(11)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:48:44.668+0800][33.126s][info][gc,phases     ] GC(11)   Evacuate Collection Set: 12.4ms
[2025-09-10T16:48:44.668+0800][33.126s][info][gc,phases     ] GC(11)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T16:48:44.668+0800][33.126s][info][gc,phases     ] GC(11)   Other: 0.4ms
[2025-09-10T16:48:44.668+0800][33.126s][info][gc,heap       ] GC(11) Eden regions: 136->0(571)
[2025-09-10T16:48:44.669+0800][33.126s][info][gc,heap       ] GC(11) Survivor regions: 44->43(77)
[2025-09-10T16:48:44.669+0800][33.127s][info][gc,heap       ] GC(11) Old regions: 20->27
[2025-09-10T16:48:44.669+0800][33.127s][info][gc,heap       ] GC(11) Humongous regions: 1->1
[2025-09-10T16:48:44.669+0800][33.127s][info][gc,metaspace  ] GC(11) Metaspace: 58226K(60240K)->58226K(60240K) NonClass: 51275K(52560K)->51275K(52560K) Class: 6950K(7680K)->6950K(7680K)
[2025-09-10T16:48:44.669+0800][33.127s][info][gc            ] GC(11) Pause Young (Concurrent Start) (Metadata GC Threshold) 198M->68M(1026M) 15.973ms
[2025-09-10T16:48:44.669+0800][33.127s][info][gc,cpu        ] GC(11) User=0.08s Sys=0.01s Real=0.02s
[2025-09-10T16:48:44.669+0800][33.127s][info][gc            ] GC(12) Concurrent Cycle
[2025-09-10T16:48:44.670+0800][33.127s][info][gc,marking    ] GC(12) Concurrent Clear Claimed Marks
[2025-09-10T16:48:44.670+0800][33.128s][info][gc,marking    ] GC(12) Concurrent Clear Claimed Marks 0.239ms
[2025-09-10T16:48:44.670+0800][33.128s][info][gc,marking    ] GC(12) Concurrent Scan Root Regions
[2025-09-10T16:48:44.673+0800][33.130s][info][gc,marking    ] GC(12) Concurrent Scan Root Regions 2.533ms
[2025-09-10T16:48:44.673+0800][33.130s][info][gc,marking    ] GC(12) Concurrent Mark (33.130s)
[2025-09-10T16:48:44.673+0800][33.131s][info][gc,marking    ] GC(12) Concurrent Mark From Roots
[2025-09-10T16:48:44.673+0800][33.131s][info][gc,task       ] GC(12) Using 4 workers of 4 for marking
[2025-09-10T16:48:44.677+0800][33.134s][info][gc,marking    ] GC(12) Concurrent Mark From Roots 3.790ms
[2025-09-10T16:48:44.677+0800][33.135s][info][gc,marking    ] GC(12) Concurrent Preclean
[2025-09-10T16:48:44.677+0800][33.135s][info][gc,marking    ] GC(12) Concurrent Preclean 0.311ms
[2025-09-10T16:48:44.677+0800][33.135s][info][gc,marking    ] GC(12) Concurrent Mark (33.130s, 33.135s) 4.754ms
[2025-09-10T16:48:44.678+0800][33.136s][info][gc,start      ] GC(12) Pause Remark
[2025-09-10T16:48:44.681+0800][33.139s][info][gc,stringtable] GC(12) Cleaned string and symbol table, strings: 56535 processed, 0 removed, symbols: 149324 processed, 138 removed
[2025-09-10T16:48:44.682+0800][33.140s][info][gc            ] GC(12) Pause Remark 69M->69M(1026M) 3.581ms
[2025-09-10T16:48:44.682+0800][33.140s][info][gc,cpu        ] GC(12) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T16:48:44.682+0800][33.140s][info][gc,marking    ] GC(12) Concurrent Rebuild Remembered Sets
[2025-09-10T16:48:44.685+0800][33.143s][info][gc,marking    ] GC(12) Concurrent Rebuild Remembered Sets 2.823ms
[2025-09-10T16:48:44.686+0800][33.143s][info][gc,start      ] GC(12) Pause Cleanup
[2025-09-10T16:48:44.686+0800][33.144s][info][gc            ] GC(12) Pause Cleanup 69M->69M(1026M) 0.301ms
[2025-09-10T16:48:44.686+0800][33.144s][info][gc,cpu        ] GC(12) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T16:48:44.686+0800][33.144s][info][gc,marking    ] GC(12) Concurrent Cleanup for Next Mark
[2025-09-10T16:48:44.687+0800][33.145s][info][gc,marking    ] GC(12) Concurrent Cleanup for Next Mark 0.974ms
[2025-09-10T16:48:44.688+0800][33.145s][info][gc            ] GC(12) Concurrent Cycle 18.255ms
[2025-09-10T16:48:52.123+0800][40.580s][info][gc,start      ] GC(13) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:48:52.123+0800][40.581s][info][gc,task       ] GC(13) Using 15 workers of 15 for evacuation
[2025-09-10T16:48:52.138+0800][40.596s][info][gc,phases     ] GC(13)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:48:52.138+0800][40.596s][info][gc,phases     ] GC(13)   Evacuate Collection Set: 13.3ms
[2025-09-10T16:48:52.139+0800][40.596s][info][gc,phases     ] GC(13)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T16:48:52.139+0800][40.596s][info][gc,phases     ] GC(13)   Other: 0.7ms
[2025-09-10T16:48:52.139+0800][40.597s][info][gc,heap       ] GC(13) Eden regions: 571->0(566)
[2025-09-10T16:48:52.139+0800][40.597s][info][gc,heap       ] GC(13) Survivor regions: 43->48(77)
[2025-09-10T16:48:52.139+0800][40.597s][info][gc,heap       ] GC(13) Old regions: 27->33
[2025-09-10T16:48:52.139+0800][40.597s][info][gc,heap       ] GC(13) Humongous regions: 2->1
[2025-09-10T16:48:52.139+0800][40.597s][info][gc,metaspace  ] GC(13) Metaspace: 66366K(68688K)->66366K(68688K) NonClass: 58735K(60240K)->58735K(60240K) Class: 7631K(8448K)->7631K(8448K)
[2025-09-10T16:48:52.140+0800][40.597s][info][gc            ] GC(13) Pause Young (Normal) (G1 Evacuation Pause) 640M->80M(1026M) 16.981ms
[2025-09-10T16:48:52.140+0800][40.598s][info][gc,cpu        ] GC(13) User=0.20s Sys=0.01s Real=0.01s
[2025-09-10T16:52:02.487+0800][230.954s][info][gc,start      ] GC(14) Pause Young (Concurrent Start) (System.gc())
[2025-09-10T16:52:02.487+0800][230.954s][info][gc,task       ] GC(14) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:02.501+0800][230.968s][info][gc,phases     ] GC(14)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T16:52:02.501+0800][230.968s][info][gc,phases     ] GC(14)   Evacuate Collection Set: 11.3ms
[2025-09-10T16:52:02.501+0800][230.968s][info][gc,phases     ] GC(14)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T16:52:02.502+0800][230.969s][info][gc,phases     ] GC(14)   Other: 0.8ms
[2025-09-10T16:52:02.502+0800][230.969s][info][gc,heap       ] GC(14) Eden regions: 363->0(574)
[2025-09-10T16:52:02.502+0800][230.969s][info][gc,heap       ] GC(14) Survivor regions: 48->40(77)
[2025-09-10T16:52:02.502+0800][230.969s][info][gc,heap       ] GC(14) Old regions: 33->50
[2025-09-10T16:52:02.502+0800][230.969s][info][gc,heap       ] GC(14) Humongous regions: 4->1
[2025-09-10T16:52:02.502+0800][230.969s][info][gc,metaspace  ] GC(14) Metaspace: 74469K(76880K)->74469K(76880K) NonClass: 66135K(67664K)->66135K(67664K) Class: 8333K(9216K)->8333K(9216K)
[2025-09-10T16:52:02.502+0800][230.969s][info][gc            ] GC(14) Pause Young (Concurrent Start) (System.gc()) 445M->88M(1026M) 15.548ms
[2025-09-10T16:52:02.502+0800][230.970s][info][gc,cpu        ] GC(14) User=0.16s Sys=0.01s Real=0.02s
[2025-09-10T16:52:02.503+0800][230.970s][info][gc            ] GC(15) Concurrent Cycle
[2025-09-10T16:52:02.503+0800][230.970s][info][gc,marking    ] GC(15) Concurrent Clear Claimed Marks
[2025-09-10T16:52:02.503+0800][230.970s][info][gc,marking    ] GC(15) Concurrent Clear Claimed Marks 0.426ms
[2025-09-10T16:52:02.504+0800][230.971s][info][gc,marking    ] GC(15) Concurrent Scan Root Regions
[2025-09-10T16:52:02.506+0800][230.973s][info][gc,marking    ] GC(15) Concurrent Scan Root Regions 2.645ms
[2025-09-10T16:52:02.506+0800][230.974s][info][gc,marking    ] GC(15) Concurrent Mark (230.974s)
[2025-09-10T16:52:02.507+0800][230.974s][info][gc,marking    ] GC(15) Concurrent Mark From Roots
[2025-09-10T16:52:02.507+0800][230.974s][info][gc,task       ] GC(15) Using 4 workers of 4 for marking
[2025-09-10T16:52:02.514+0800][230.981s][info][gc,marking    ] GC(15) Concurrent Mark From Roots 7.637ms
[2025-09-10T16:52:02.515+0800][230.982s][info][gc,marking    ] GC(15) Concurrent Preclean
[2025-09-10T16:52:02.515+0800][230.982s][info][gc,marking    ] GC(15) Concurrent Preclean 0.411ms
[2025-09-10T16:52:02.515+0800][230.982s][info][gc,marking    ] GC(15) Concurrent Mark (230.974s, 230.982s) 8.725ms
[2025-09-10T16:52:02.516+0800][230.983s][info][gc,start      ] GC(15) Pause Remark
[2025-09-10T16:52:02.525+0800][230.992s][info][gc,stringtable] GC(15) Cleaned string and symbol table, strings: 102515 processed, 329 removed, symbols: 186168 processed, 37 removed
[2025-09-10T16:52:02.526+0800][230.993s][info][gc            ] GC(15) Pause Remark 89M->88M(1026M) 10.192ms
[2025-09-10T16:52:02.526+0800][230.993s][info][gc,cpu        ] GC(15) User=0.11s Sys=0.00s Real=0.01s
[2025-09-10T16:52:02.526+0800][230.993s][info][gc,marking    ] GC(15) Concurrent Rebuild Remembered Sets
[2025-09-10T16:52:02.532+0800][230.999s][info][gc,marking    ] GC(15) Concurrent Rebuild Remembered Sets 5.674ms
[2025-09-10T16:52:02.532+0800][230.999s][info][gc,start      ] GC(15) Pause Cleanup
[2025-09-10T16:52:02.533+0800][231.000s][info][gc            ] GC(15) Pause Cleanup 88M->88M(1026M) 0.499ms
[2025-09-10T16:52:02.533+0800][231.000s][info][gc,cpu        ] GC(15) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T16:52:02.533+0800][231.000s][info][gc,marking    ] GC(15) Concurrent Cleanup for Next Mark
[2025-09-10T16:52:02.534+0800][231.001s][info][gc,marking    ] GC(15) Concurrent Cleanup for Next Mark 0.956ms
[2025-09-10T16:52:02.534+0800][231.001s][info][gc            ] GC(15) Concurrent Cycle 31.721ms
[2025-09-10T16:52:07.550+0800][236.017s][info][gc,start      ] GC(16) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:07.551+0800][236.018s][info][gc,task       ] GC(16) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:07.565+0800][236.032s][info][gc,phases     ] GC(16)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:52:07.565+0800][236.032s][info][gc,phases     ] GC(16)   Evacuate Collection Set: 12.0ms
[2025-09-10T16:52:07.565+0800][236.032s][info][gc,phases     ] GC(16)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T16:52:07.566+0800][236.033s][info][gc,phases     ] GC(16)   Other: 0.8ms
[2025-09-10T16:52:07.566+0800][236.033s][info][gc,heap       ] GC(16) Eden regions: 574->0(560)
[2025-09-10T16:52:07.566+0800][236.033s][info][gc,heap       ] GC(16) Survivor regions: 40->54(77)
[2025-09-10T16:52:07.566+0800][236.033s][info][gc,heap       ] GC(16) Old regions: 49->49
[2025-09-10T16:52:07.566+0800][236.034s][info][gc,heap       ] GC(16) Humongous regions: 1->1
[2025-09-10T16:52:07.567+0800][236.034s][info][gc,metaspace  ] GC(16) Metaspace: 99821K(102608K)->99821K(102608K) NonClass: 88133K(89936K)->88133K(89936K) Class: 11688K(12672K)->11688K(12672K)
[2025-09-10T16:52:07.567+0800][236.034s][info][gc            ] GC(16) Pause Young (Normal) (G1 Evacuation Pause) 662M->102M(1026M) 16.723ms
[2025-09-10T16:52:07.567+0800][236.034s][info][gc,cpu        ] GC(16) User=0.19s Sys=0.00s Real=0.02s
[2025-09-10T16:52:14.160+0800][242.627s][info][gc,start      ] GC(17) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:14.161+0800][242.628s][info][gc,task       ] GC(17) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:14.180+0800][242.647s][info][gc,phases     ] GC(17)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:14.181+0800][242.648s][info][gc,phases     ] GC(17)   Evacuate Collection Set: 15.9ms
[2025-09-10T16:52:14.181+0800][242.648s][info][gc,phases     ] GC(17)   Post Evacuate Collection Set: 3.4ms
[2025-09-10T16:52:14.181+0800][242.648s][info][gc,phases     ] GC(17)   Other: 0.6ms
[2025-09-10T16:52:14.181+0800][242.648s][info][gc,heap       ] GC(17) Eden regions: 560->0(576)
[2025-09-10T16:52:14.181+0800][242.649s][info][gc,heap       ] GC(17) Survivor regions: 54->38(77)
[2025-09-10T16:52:14.182+0800][242.649s][info][gc,heap       ] GC(17) Old regions: 49->78
[2025-09-10T16:52:14.182+0800][242.649s][info][gc,heap       ] GC(17) Humongous regions: 5->1
[2025-09-10T16:52:14.182+0800][242.649s][info][gc,metaspace  ] GC(17) Metaspace: 110120K(113488K)->110120K(113488K) NonClass: 97196K(99408K)->97196K(99408K) Class: 12923K(14080K)->12923K(14080K)
[2025-09-10T16:52:14.182+0800][242.649s][info][gc            ] GC(17) Pause Young (Normal) (G1 Evacuation Pause) 666M->114M(1026M) 21.743ms
[2025-09-10T16:52:14.182+0800][242.649s][info][gc,cpu        ] GC(17) User=0.25s Sys=0.01s Real=0.02s
[2025-09-10T16:52:16.778+0800][245.245s][info][gc,start      ] GC(18) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:16.779+0800][245.246s][info][gc,task       ] GC(18) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:16.788+0800][245.255s][info][gc,phases     ] GC(18)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:16.788+0800][245.255s][info][gc,phases     ] GC(18)   Evacuate Collection Set: 7.9ms
[2025-09-10T16:52:16.788+0800][245.255s][info][gc,phases     ] GC(18)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,phases     ] GC(18)   Other: 0.6ms
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,heap       ] GC(18) Eden regions: 576->0(574)
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,heap       ] GC(18) Survivor regions: 38->40(77)
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,heap       ] GC(18) Old regions: 78->78
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,heap       ] GC(18) Humongous regions: 1->1
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,metaspace  ] GC(18) Metaspace: 110558K(113744K)->110558K(113744K) NonClass: 97603K(99664K)->97603K(99664K) Class: 12954K(14080K)->12954K(14080K)
[2025-09-10T16:52:16.789+0800][245.256s][info][gc            ] GC(18) Pause Young (Normal) (G1 Evacuation Pause) 690M->116M(1026M) 10.869ms
[2025-09-10T16:52:16.789+0800][245.256s][info][gc,cpu        ] GC(18) User=0.12s Sys=0.00s Real=0.01s
[2025-09-10T16:52:20.108+0800][248.575s][info][gc,start      ] GC(19) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:20.108+0800][248.575s][info][gc,task       ] GC(19) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:20.121+0800][248.588s][info][gc,phases     ] GC(19)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:20.121+0800][248.588s][info][gc,phases     ] GC(19)   Evacuate Collection Set: 9.9ms
[2025-09-10T16:52:20.122+0800][248.589s][info][gc,phases     ] GC(19)   Post Evacuate Collection Set: 2.5ms
[2025-09-10T16:52:20.122+0800][248.589s][info][gc,phases     ] GC(19)   Other: 0.6ms
[2025-09-10T16:52:20.122+0800][248.589s][info][gc,heap       ] GC(19) Eden regions: 574->0(567)
[2025-09-10T16:52:20.122+0800][248.589s][info][gc,heap       ] GC(19) Survivor regions: 40->47(77)
[2025-09-10T16:52:20.122+0800][248.589s][info][gc,heap       ] GC(19) Old regions: 78->78
[2025-09-10T16:52:20.123+0800][248.590s][info][gc,heap       ] GC(19) Humongous regions: 2->1
[2025-09-10T16:52:20.123+0800][248.590s][info][gc,metaspace  ] GC(19) Metaspace: 111145K(114256K)->111145K(114256K) NonClass: 98125K(100176K)->98125K(100176K) Class: 13019K(14080K)->13019K(14080K)
[2025-09-10T16:52:20.123+0800][248.590s][info][gc            ] GC(19) Pause Young (Normal) (G1 Evacuation Pause) 691M->123M(1026M) 14.931ms
[2025-09-10T16:52:20.124+0800][248.591s][info][gc,cpu        ] GC(19) User=0.17s Sys=0.01s Real=0.02s
[2025-09-10T16:52:21.113+0800][249.580s][info][gc,start      ] GC(20) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:21.113+0800][249.580s][info][gc,task       ] GC(20) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:21.123+0800][249.590s][info][gc,phases     ] GC(20)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:21.124+0800][249.591s][info][gc,phases     ] GC(20)   Evacuate Collection Set: 8.4ms
[2025-09-10T16:52:21.125+0800][249.592s][info][gc,phases     ] GC(20)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T16:52:21.125+0800][249.592s][info][gc,phases     ] GC(20)   Other: 0.9ms
[2025-09-10T16:52:21.125+0800][249.592s][info][gc,heap       ] GC(20) Eden regions: 567->0(582)
[2025-09-10T16:52:21.125+0800][249.593s][info][gc,heap       ] GC(20) Survivor regions: 47->32(77)
[2025-09-10T16:52:21.126+0800][249.593s][info][gc,heap       ] GC(20) Old regions: 78->92
[2025-09-10T16:52:21.126+0800][249.593s][info][gc,heap       ] GC(20) Humongous regions: 1->1
[2025-09-10T16:52:21.126+0800][249.593s][info][gc,metaspace  ] GC(20) Metaspace: 111609K(114896K)->111609K(114896K) NonClass: 98509K(100688K)->98509K(100688K) Class: 13099K(14208K)->13099K(14208K)
[2025-09-10T16:52:21.126+0800][249.593s][info][gc            ] GC(20) Pause Young (Normal) (G1 Evacuation Pause) 690M->123M(1026M) 13.012ms
[2025-09-10T16:52:21.126+0800][249.593s][info][gc,cpu        ] GC(20) User=0.08s Sys=0.04s Real=0.01s
[2025-09-10T16:52:23.878+0800][252.345s][info][gc,start      ] GC(21) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:23.878+0800][252.346s][info][gc,task       ] GC(21) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:23.886+0800][252.353s][info][gc,phases     ] GC(21)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:23.887+0800][252.354s][info][gc,phases     ] GC(21)   Evacuate Collection Set: 5.1ms
[2025-09-10T16:52:23.888+0800][252.355s][info][gc,phases     ] GC(21)   Post Evacuate Collection Set: 2.2ms
[2025-09-10T16:52:23.888+0800][252.355s][info][gc,phases     ] GC(21)   Other: 0.4ms
[2025-09-10T16:52:23.888+0800][252.355s][info][gc,heap       ] GC(21) Eden regions: 582->0(579)
[2025-09-10T16:52:23.888+0800][252.355s][info][gc,heap       ] GC(21) Survivor regions: 32->35(77)
[2025-09-10T16:52:23.888+0800][252.356s][info][gc,heap       ] GC(21) Old regions: 92->92
[2025-09-10T16:52:23.889+0800][252.356s][info][gc,heap       ] GC(21) Humongous regions: 1->1
[2025-09-10T16:52:23.889+0800][252.356s][info][gc,metaspace  ] GC(21) Metaspace: 111870K(115280K)->111870K(115280K) NonClass: 98739K(100944K)->98739K(100944K) Class: 13130K(14336K)->13130K(14336K)
[2025-09-10T16:52:23.889+0800][252.356s][info][gc            ] GC(21) Pause Young (Normal) (G1 Evacuation Pause) 705M->126M(1026M) 10.573ms
[2025-09-10T16:52:23.889+0800][252.356s][info][gc,cpu        ] GC(21) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T16:52:30.703+0800][259.172s][info][gc,start      ] GC(22) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:30.703+0800][259.172s][info][gc,task       ] GC(22) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:30.711+0800][259.180s][info][gc,phases     ] GC(22)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:30.711+0800][259.180s][info][gc,phases     ] GC(22)   Evacuate Collection Set: 5.0ms
[2025-09-10T16:52:30.711+0800][259.180s][info][gc,phases     ] GC(22)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T16:52:30.711+0800][259.180s][info][gc,phases     ] GC(22)   Other: 0.9ms
[2025-09-10T16:52:30.713+0800][259.182s][info][gc,heap       ] GC(22) Eden regions: 579->0(576)
[2025-09-10T16:52:30.713+0800][259.182s][info][gc,heap       ] GC(22) Survivor regions: 35->38(77)
[2025-09-10T16:52:30.714+0800][259.183s][info][gc,heap       ] GC(22) Old regions: 92->92
[2025-09-10T16:52:30.714+0800][259.183s][info][gc,heap       ] GC(22) Humongous regions: 7->2
[2025-09-10T16:52:30.714+0800][259.183s][info][gc,metaspace  ] GC(22) Metaspace: 112102K(115536K)->112102K(115536K) NonClass: 98930K(101200K)->98930K(101200K) Class: 13171K(14336K)->13171K(14336K)
[2025-09-10T16:52:30.714+0800][259.183s][info][gc            ] GC(22) Pause Young (Normal) (G1 Evacuation Pause) 711M->129M(1026M) 11.443ms
[2025-09-10T16:52:30.714+0800][259.183s][info][gc,cpu        ] GC(22) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T16:52:46.634+0800][275.103s][info][gc,start      ] GC(23) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:52:46.634+0800][275.103s][info][gc,task       ] GC(23) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:46.650+0800][275.119s][info][gc,phases     ] GC(23)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:52:46.650+0800][275.119s][info][gc,phases     ] GC(23)   Evacuate Collection Set: 14.6ms
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,phases     ] GC(23)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,phases     ] GC(23)   Other: 0.4ms
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,heap       ] GC(23) Eden regions: 576->0(545)
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,heap       ] GC(23) Survivor regions: 38->69(77)
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,heap       ] GC(23) Old regions: 92->92
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,heap       ] GC(23) Humongous regions: 9->1
[2025-09-10T16:52:46.651+0800][275.120s][info][gc,metaspace  ] GC(23) Metaspace: 112572K(116048K)->112572K(116048K) NonClass: 99382K(101712K)->99382K(101712K) Class: 13189K(14336K)->13189K(14336K)
[2025-09-10T16:52:46.651+0800][275.120s][info][gc            ] GC(23) Pause Young (Normal) (G1 Evacuation Pause) 712M->160M(1026M) 17.677ms
[2025-09-10T16:52:46.652+0800][275.121s][info][gc,cpu        ] GC(23) User=0.21s Sys=0.02s Real=0.02s
[2025-09-10T16:52:47.534+0800][276.003s][info][gc,start      ] GC(24) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T16:52:47.534+0800][276.003s][info][gc,task       ] GC(24) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:47.567+0800][276.036s][info][gc,phases     ] GC(24)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T16:52:47.567+0800][276.036s][info][gc,phases     ] GC(24)   Evacuate Collection Set: 30.5ms
[2025-09-10T16:52:47.567+0800][276.036s][info][gc,phases     ] GC(24)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T16:52:47.567+0800][276.036s][info][gc,phases     ] GC(24)   Other: 0.5ms
[2025-09-10T16:52:47.568+0800][276.037s][info][gc,heap       ] GC(24) Eden regions: 210->0(594)
[2025-09-10T16:52:47.569+0800][276.038s][info][gc,heap       ] GC(24) Survivor regions: 69->20(77)
[2025-09-10T16:52:47.569+0800][276.038s][info][gc,heap       ] GC(24) Old regions: 92->160
[2025-09-10T16:52:47.569+0800][276.038s][info][gc,heap       ] GC(24) Humongous regions: 2->2
[2025-09-10T16:52:47.569+0800][276.038s][info][gc,metaspace  ] GC(24) Metaspace: 123550K(128080K)->123550K(128080K) NonClass: 109052K(112208K)->109052K(112208K) Class: 14497K(15872K)->14497K(15872K)
[2025-09-10T16:52:47.569+0800][276.038s][info][gc            ] GC(24) Pause Young (Concurrent Start) (Metadata GC Threshold) 371M->180M(1026M) 35.222ms
[2025-09-10T16:52:47.570+0800][276.039s][info][gc,cpu        ] GC(24) User=0.18s Sys=0.11s Real=0.04s
[2025-09-10T16:52:47.570+0800][276.039s][info][gc            ] GC(25) Concurrent Cycle
[2025-09-10T16:52:47.570+0800][276.039s][info][gc,marking    ] GC(25) Concurrent Clear Claimed Marks
[2025-09-10T16:52:47.571+0800][276.040s][info][gc,marking    ] GC(25) Concurrent Clear Claimed Marks 1.254ms
[2025-09-10T16:52:47.572+0800][276.041s][info][gc,marking    ] GC(25) Concurrent Scan Root Regions
[2025-09-10T16:52:47.574+0800][276.043s][info][gc,marking    ] GC(25) Concurrent Scan Root Regions 2.032ms
[2025-09-10T16:52:47.574+0800][276.043s][info][gc,marking    ] GC(25) Concurrent Mark (276.043s)
[2025-09-10T16:52:47.574+0800][276.043s][info][gc,marking    ] GC(25) Concurrent Mark From Roots
[2025-09-10T16:52:47.574+0800][276.043s][info][gc,task       ] GC(25) Using 4 workers of 4 for marking
[2025-09-10T16:52:47.621+0800][276.089s][info][gc,marking    ] GC(25) Concurrent Mark From Roots 46.260ms
[2025-09-10T16:52:47.621+0800][276.090s][info][gc,marking    ] GC(25) Concurrent Preclean
[2025-09-10T16:52:47.621+0800][276.090s][info][gc,marking    ] GC(25) Concurrent Preclean 0.641ms
[2025-09-10T16:52:47.622+0800][276.091s][info][gc,marking    ] GC(25) Concurrent Mark (276.043s, 276.091s) 47.716ms
[2025-09-10T16:52:47.623+0800][276.091s][info][gc,start      ] GC(25) Pause Remark
[2025-09-10T16:52:47.636+0800][276.105s][info][gc,stringtable] GC(25) Cleaned string and symbol table, strings: 126364 processed, 274 removed, symbols: 307730 processed, 1263 removed
[2025-09-10T16:52:47.638+0800][276.107s][info][gc            ] GC(25) Pause Remark 212M->212M(1026M) 15.602ms
[2025-09-10T16:52:47.639+0800][276.108s][info][gc,cpu        ] GC(25) User=0.18s Sys=0.00s Real=0.02s
[2025-09-10T16:52:47.639+0800][276.108s][info][gc,marking    ] GC(25) Concurrent Rebuild Remembered Sets
[2025-09-10T16:52:47.670+0800][276.139s][info][gc,marking    ] GC(25) Concurrent Rebuild Remembered Sets 31.603ms
[2025-09-10T16:52:47.674+0800][276.142s][info][gc,start      ] GC(25) Pause Cleanup
[2025-09-10T16:52:47.677+0800][276.146s][info][gc            ] GC(25) Pause Cleanup 231M->231M(1026M) 3.187ms
[2025-09-10T16:52:47.680+0800][276.149s][info][gc,cpu        ] GC(25) User=0.00s Sys=0.00s Real=0.01s
[2025-09-10T16:52:47.683+0800][276.152s][info][gc,marking    ] GC(25) Concurrent Cleanup for Next Mark
[2025-09-10T16:52:47.686+0800][276.155s][info][gc,marking    ] GC(25) Concurrent Cleanup for Next Mark 3.657ms
[2025-09-10T16:52:47.688+0800][276.157s][info][gc            ] GC(25) Concurrent Cycle 118.713ms
[2025-09-10T16:52:56.480+0800][284.949s][info][gc,start      ] GC(26) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T16:52:56.480+0800][284.949s][info][gc,task       ] GC(26) Using 15 workers of 15 for evacuation
[2025-09-10T16:52:56.494+0800][284.963s][info][gc,phases     ] GC(26)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,phases     ] GC(26)   Evacuate Collection Set: 11.3ms
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,phases     ] GC(26)   Post Evacuate Collection Set: 2.5ms
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,phases     ] GC(26)   Other: 0.5ms
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,heap       ] GC(26) Eden regions: 594->0(569)
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,heap       ] GC(26) Survivor regions: 20->45(77)
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,heap       ] GC(26) Old regions: 160->160
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,heap       ] GC(26) Humongous regions: 3->2
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,metaspace  ] GC(26) Metaspace: 136125K(143648K)->136125K(143648K) NonClass: 120045K(125264K)->120045K(125264K) Class: 16080K(18384K)->16080K(18384K)
[2025-09-10T16:52:56.495+0800][284.964s][info][gc            ] GC(26) Pause Young (Concurrent Start) (G1 Evacuation Pause) 775M->205M(1026M) 15.649ms
[2025-09-10T16:52:56.495+0800][284.964s][info][gc,cpu        ] GC(26) User=0.14s Sys=0.01s Real=0.01s
[2025-09-10T16:52:56.496+0800][284.965s][info][gc            ] GC(27) Concurrent Cycle
[2025-09-10T16:52:56.496+0800][284.965s][info][gc,marking    ] GC(27) Concurrent Clear Claimed Marks
[2025-09-10T16:52:56.496+0800][284.965s][info][gc,marking    ] GC(27) Concurrent Clear Claimed Marks 0.499ms
[2025-09-10T16:52:56.497+0800][284.966s][info][gc,marking    ] GC(27) Concurrent Scan Root Regions
[2025-09-10T16:52:56.500+0800][284.969s][info][gc,marking    ] GC(27) Concurrent Scan Root Regions 3.733ms
[2025-09-10T16:52:56.501+0800][284.969s][info][gc,marking    ] GC(27) Concurrent Mark (284.969s)
[2025-09-10T16:52:56.501+0800][284.970s][info][gc,marking    ] GC(27) Concurrent Mark From Roots
[2025-09-10T16:52:56.501+0800][284.970s][info][gc,task       ] GC(27) Using 4 workers of 4 for marking
[2025-09-10T16:52:56.541+0800][285.010s][info][gc,marking    ] GC(27) Concurrent Mark From Roots 40.474ms
[2025-09-10T16:52:56.541+0800][285.010s][info][gc,marking    ] GC(27) Concurrent Preclean
[2025-09-10T16:52:56.542+0800][285.011s][info][gc,marking    ] GC(27) Concurrent Preclean 0.424ms
[2025-09-10T16:52:56.542+0800][285.011s][info][gc,marking    ] GC(27) Concurrent Mark (284.969s, 285.011s) 41.574ms
[2025-09-10T16:52:56.543+0800][285.012s][info][gc,start      ] GC(27) Pause Remark
[2025-09-10T16:52:56.551+0800][285.020s][info][gc,stringtable] GC(27) Cleaned string and symbol table, strings: 130650 processed, 139 removed, symbols: 340715 processed, 267 removed
[2025-09-10T16:52:56.552+0800][285.021s][info][gc            ] GC(27) Pause Remark 217M->217M(1026M) 8.876ms
[2025-09-10T16:52:56.552+0800][285.021s][info][gc,cpu        ] GC(27) User=0.12s Sys=0.00s Real=0.01s
[2025-09-10T16:52:56.552+0800][285.021s][info][gc,marking    ] GC(27) Concurrent Rebuild Remembered Sets
[2025-09-10T16:52:56.576+0800][285.045s][info][gc,marking    ] GC(27) Concurrent Rebuild Remembered Sets 23.901ms
[2025-09-10T16:52:56.577+0800][285.046s][info][gc,start      ] GC(27) Pause Cleanup
[2025-09-10T16:52:56.577+0800][285.046s][info][gc            ] GC(27) Pause Cleanup 220M->220M(1026M) 0.452ms
[2025-09-10T16:52:56.578+0800][285.046s][info][gc,cpu        ] GC(27) User=0.00s Sys=0.00s Real=0.01s
[2025-09-10T16:52:56.578+0800][285.047s][info][gc,marking    ] GC(27) Concurrent Cleanup for Next Mark
[2025-09-10T16:52:56.579+0800][285.048s][info][gc,marking    ] GC(27) Concurrent Cleanup for Next Mark 0.978ms
[2025-09-10T16:52:56.579+0800][285.048s][info][gc            ] GC(27) Concurrent Cycle 83.466ms
[2025-09-10T16:53:01.109+0800][289.579s][info][gc,start      ] GC(28) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:01.110+0800][289.580s][info][gc,task       ] GC(28) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:01.122+0800][289.592s][info][gc,phases     ] GC(28)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:01.123+0800][289.593s][info][gc,phases     ] GC(28)   Evacuate Collection Set: 11.0ms
[2025-09-10T16:53:01.123+0800][289.593s][info][gc,phases     ] GC(28)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T16:53:01.123+0800][289.593s][info][gc,phases     ] GC(28)   Other: 0.4ms
[2025-09-10T16:53:01.123+0800][289.593s][info][gc,heap       ] GC(28) Eden regions: 569->0(562)
[2025-09-10T16:53:01.124+0800][289.594s][info][gc,heap       ] GC(28) Survivor regions: 45->52(77)
[2025-09-10T16:53:01.124+0800][289.594s][info][gc,heap       ] GC(28) Old regions: 160->169
[2025-09-10T16:53:01.124+0800][289.594s][info][gc,heap       ] GC(28) Humongous regions: 3->2
[2025-09-10T16:53:01.124+0800][289.594s][info][gc,metaspace  ] GC(28) Metaspace: 142017K(150304K)->142017K(150304K) NonClass: 125175K(130896K)->125175K(130896K) Class: 16841K(19408K)->16841K(19408K)
[2025-09-10T16:53:01.125+0800][289.595s][info][gc            ] GC(28) Pause Young (Normal) (G1 Evacuation Pause) 775M->220M(1026M) 15.216ms
[2025-09-10T16:53:01.125+0800][289.595s][info][gc,cpu        ] GC(28) User=0.14s Sys=0.02s Real=0.01s
[2025-09-10T16:53:11.518+0800][299.988s][info][gc,start      ] GC(29) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:11.519+0800][299.989s][info][gc,task       ] GC(29) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:11.539+0800][300.009s][info][gc,phases     ] GC(29)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:11.540+0800][300.010s][info][gc,phases     ] GC(29)   Evacuate Collection Set: 18.1ms
[2025-09-10T16:53:11.541+0800][300.011s][info][gc,phases     ] GC(29)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T16:53:11.542+0800][300.011s][info][gc,phases     ] GC(29)   Other: 1.7ms
[2025-09-10T16:53:11.543+0800][300.013s][info][gc,heap       ] GC(29) Eden regions: 562->0(571)
[2025-09-10T16:53:11.544+0800][300.014s][info][gc,heap       ] GC(29) Survivor regions: 52->43(77)
[2025-09-10T16:53:11.545+0800][300.015s][info][gc,heap       ] GC(29) Old regions: 169->196
[2025-09-10T16:53:11.546+0800][300.016s][info][gc,heap       ] GC(29) Humongous regions: 3->2
[2025-09-10T16:53:11.546+0800][300.016s][info][gc,metaspace  ] GC(29) Metaspace: 149485K(160160K)->149485K(160160K) NonClass: 131571K(139088K)->131571K(139088K) Class: 17913K(21072K)->17913K(21072K)
[2025-09-10T16:53:11.546+0800][300.016s][info][gc            ] GC(29) Pause Young (Normal) (G1 Evacuation Pause) 783M->238M(1026M) 28.337ms
[2025-09-10T16:53:11.547+0800][300.017s][info][gc,cpu        ] GC(29) User=0.20s Sys=0.08s Real=0.02s
[2025-09-10T16:53:12.509+0800][300.979s][info][gc,start      ] GC(30) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:12.510+0800][300.980s][info][gc,task       ] GC(30) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:12.530+0800][301.000s][info][gc,phases     ] GC(30)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:12.531+0800][301.001s][info][gc,phases     ] GC(30)   Evacuate Collection Set: 19.1ms
[2025-09-10T16:53:12.531+0800][301.001s][info][gc,phases     ] GC(30)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T16:53:12.531+0800][301.001s][info][gc,phases     ] GC(30)   Other: 0.5ms
[2025-09-10T16:53:12.531+0800][301.001s][info][gc,heap       ] GC(30) Eden regions: 571->0(563)
[2025-09-10T16:53:12.532+0800][301.002s][info][gc,heap       ] GC(30) Survivor regions: 43->51(77)
[2025-09-10T16:53:12.532+0800][301.002s][info][gc,heap       ] GC(30) Old regions: 196->210
[2025-09-10T16:53:12.533+0800][301.003s][info][gc,heap       ] GC(30) Humongous regions: 2->2
[2025-09-10T16:53:12.534+0800][301.004s][info][gc,metaspace  ] GC(30) Metaspace: 163544K(176288K)->163544K(176288K) NonClass: 143912K(152656K)->143912K(152656K) Class: 19631K(23632K)->19631K(23632K)
[2025-09-10T16:53:12.536+0800][301.006s][info][gc            ] GC(30) Pause Young (Normal) (G1 Evacuation Pause) 809M->261M(1026M) 26.807ms
[2025-09-10T16:53:12.538+0800][301.008s][info][gc,cpu        ] GC(30) User=0.33s Sys=0.05s Real=0.03s
[2025-09-10T16:53:14.755+0800][303.225s][info][gc,start      ] GC(31) Pause Young (Normal) (GCLocker Initiated GC)
[2025-09-10T16:53:14.757+0800][303.227s][info][gc,task       ] GC(31) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:14.786+0800][303.256s][info][gc,phases     ] GC(31)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:14.788+0800][303.258s][info][gc,phases     ] GC(31)   Evacuate Collection Set: 22.5ms
[2025-09-10T16:53:14.791+0800][303.261s][info][gc,phases     ] GC(31)   Post Evacuate Collection Set: 3.8ms
[2025-09-10T16:53:14.792+0800][303.262s][info][gc,phases     ] GC(31)   Other: 4.4ms
[2025-09-10T16:53:14.792+0800][303.262s][info][gc,heap       ] GC(31) Eden regions: 565->0(545)
[2025-09-10T16:53:14.793+0800][303.263s][info][gc,heap       ] GC(31) Survivor regions: 51->52(77)
[2025-09-10T16:53:14.793+0800][303.263s][info][gc,heap       ] GC(31) Old regions: 210->235
[2025-09-10T16:53:14.794+0800][303.264s][info][gc,heap       ] GC(31) Humongous regions: 2->2
[2025-09-10T16:53:14.794+0800][303.264s][info][gc,metaspace  ] GC(31) Metaspace: 182624K(198048K)->182624K(198048K) NonClass: 160507K(171088K)->160507K(171088K) Class: 22116K(26960K)->22116K(26960K)
[2025-09-10T16:53:14.794+0800][303.264s][info][gc            ] GC(31) Pause Young (Normal) (GCLocker Initiated GC) 826M->287M(1026M) 39.208ms
[2025-09-10T16:53:14.794+0800][303.264s][info][gc,cpu        ] GC(31) User=0.45s Sys=0.02s Real=0.04s
[2025-09-10T16:53:15.357+0800][303.827s][info][gc,start      ] GC(32) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:15.358+0800][303.828s][info][gc,task       ] GC(32) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:15.379+0800][303.849s][info][gc,phases     ] GC(32)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,phases     ] GC(32)   Evacuate Collection Set: 20.0ms
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,phases     ] GC(32)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,phases     ] GC(32)   Other: 0.5ms
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,heap       ] GC(32) Eden regions: 545->0(537)
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,heap       ] GC(32) Survivor regions: 52->49(75)
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,heap       ] GC(32) Old regions: 235->256
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,heap       ] GC(32) Humongous regions: 2->2
[2025-09-10T16:53:15.380+0800][303.850s][info][gc,metaspace  ] GC(32) Metaspace: 194024K(210208K)->194024K(210208K) NonClass: 170487K(181328K)->170487K(181328K) Class: 23536K(28880K)->23536K(28880K)
[2025-09-10T16:53:15.381+0800][303.851s][info][gc            ] GC(32) Pause Young (Normal) (G1 Evacuation Pause) 832M->304M(1026M) 23.231ms
[2025-09-10T16:53:15.381+0800][303.851s][info][gc,cpu        ] GC(32) User=0.31s Sys=0.07s Real=0.02s
[2025-09-10T16:53:16.461+0800][304.931s][info][gc,start      ] GC(33) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:16.461+0800][304.931s][info][gc,task       ] GC(33) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:16.481+0800][304.951s][info][gc,phases     ] GC(33)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:16.482+0800][304.952s][info][gc,phases     ] GC(33)   Evacuate Collection Set: 18.7ms
[2025-09-10T16:53:16.482+0800][304.952s][info][gc,phases     ] GC(33)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T16:53:16.482+0800][304.952s][info][gc,phases     ] GC(33)   Other: 0.6ms
[2025-09-10T16:53:16.482+0800][304.952s][info][gc,heap       ] GC(33) Eden regions: 537->0(525)
[2025-09-10T16:53:16.483+0800][304.953s][info][gc,heap       ] GC(33) Survivor regions: 49->40(74)
[2025-09-10T16:53:16.483+0800][304.953s][info][gc,heap       ] GC(33) Old regions: 256->284
[2025-09-10T16:53:16.483+0800][304.953s][info][gc,heap       ] GC(33) Humongous regions: 3->2
[2025-09-10T16:53:16.483+0800][304.953s][info][gc,metaspace  ] GC(33) Metaspace: 211853K(229280K)->211853K(229280K) NonClass: 186385K(198224K)->186385K(198224K) Class: 25467K(31056K)->25467K(31056K)
[2025-09-10T16:53:16.483+0800][304.953s][info][gc            ] GC(33) Pause Young (Normal) (G1 Evacuation Pause) 842M->324M(1026M) 22.601ms
[2025-09-10T16:53:16.483+0800][304.953s][info][gc,cpu        ] GC(33) User=0.28s Sys=0.03s Real=0.02s
[2025-09-10T16:53:17.668+0800][306.138s][info][gc,start      ] GC(34) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:17.670+0800][306.140s][info][gc,task       ] GC(34) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:17.686+0800][306.156s][info][gc,phases     ] GC(34)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,phases     ] GC(34)   Evacuate Collection Set: 12.7ms
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,phases     ] GC(34)   Post Evacuate Collection Set: 1.7ms
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,phases     ] GC(34)   Other: 3.5ms
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,heap       ] GC(34) Eden regions: 525->0(513)
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,heap       ] GC(34) Survivor regions: 40->39(71)
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,heap       ] GC(34) Old regions: 284->303
[2025-09-10T16:53:17.687+0800][306.157s][info][gc,heap       ] GC(34) Humongous regions: 2->2
[2025-09-10T16:53:17.688+0800][306.157s][info][gc,metaspace  ] GC(34) Metaspace: 221318K(238752K)->221318K(238752K) NonClass: 194676K(206416K)->194676K(206416K) Class: 26642K(32336K)->26642K(32336K)
[2025-09-10T16:53:17.688+0800][306.158s][info][gc            ] GC(34) Pause Young (Normal) (G1 Evacuation Pause) 849M->342M(1026M) 19.425ms
[2025-09-10T16:53:17.688+0800][306.158s][info][gc,cpu        ] GC(34) User=0.18s Sys=0.00s Real=0.01s
[2025-09-10T16:53:18.180+0800][306.650s][info][gc,start      ] GC(35) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-09-10T16:53:18.180+0800][306.650s][info][gc,task       ] GC(35) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:18.192+0800][306.662s][info][gc,phases     ] GC(35)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T16:53:18.192+0800][306.662s][info][gc,phases     ] GC(35)   Evacuate Collection Set: 10.0ms
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,phases     ] GC(35)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,phases     ] GC(35)   Other: 0.6ms
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,heap       ] GC(35) Eden regions: 66->0(515)
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,heap       ] GC(35) Survivor regions: 39->22(69)
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,heap       ] GC(35) Old regions: 303->324
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,heap       ] GC(35) Humongous regions: 2->2
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,metaspace  ] GC(35) Metaspace: 222149K(239416K)->222149K(239416K) NonClass: 195387K(207080K)->195387K(207080K) Class: 26761K(32336K)->26761K(32336K)
[2025-09-10T16:53:18.193+0800][306.663s][info][gc            ] GC(35) Pause Young (Concurrent Start) (Metadata GC Threshold) 408M->346M(1026M) 13.540ms
[2025-09-10T16:53:18.193+0800][306.663s][info][gc,cpu        ] GC(35) User=0.11s Sys=0.04s Real=0.01s
[2025-09-10T16:53:18.194+0800][306.664s][info][gc            ] GC(36) Concurrent Cycle
[2025-09-10T16:53:18.194+0800][306.664s][info][gc,marking    ] GC(36) Concurrent Clear Claimed Marks
[2025-09-10T16:53:18.194+0800][306.664s][info][gc,marking    ] GC(36) Concurrent Clear Claimed Marks 0.733ms
[2025-09-10T16:53:18.195+0800][306.665s][info][gc,marking    ] GC(36) Concurrent Scan Root Regions
[2025-09-10T16:53:18.198+0800][306.668s][info][gc,marking    ] GC(36) Concurrent Scan Root Regions 3.128ms
[2025-09-10T16:53:18.198+0800][306.668s][info][gc,marking    ] GC(36) Concurrent Mark (306.668s)
[2025-09-10T16:53:18.198+0800][306.668s][info][gc,marking    ] GC(36) Concurrent Mark From Roots
[2025-09-10T16:53:18.198+0800][306.668s][info][gc,task       ] GC(36) Using 4 workers of 4 for marking
[2025-09-10T16:53:18.259+0800][306.729s][info][gc,marking    ] GC(36) Concurrent Mark From Roots 60.627ms
[2025-09-10T16:53:18.260+0800][306.730s][info][gc,marking    ] GC(36) Concurrent Preclean
[2025-09-10T16:53:18.260+0800][306.730s][info][gc,marking    ] GC(36) Concurrent Preclean 0.750ms
[2025-09-10T16:53:18.261+0800][306.731s][info][gc,marking    ] GC(36) Concurrent Mark (306.668s, 306.731s) 62.451ms
[2025-09-10T16:53:18.261+0800][306.731s][info][gc,start      ] GC(36) Pause Remark
[2025-09-10T16:53:18.283+0800][306.753s][info][gc,stringtable] GC(36) Cleaned string and symbol table, strings: 168642 processed, 2052 removed, symbols: 467041 processed, 4425 removed
[2025-09-10T16:53:18.288+0800][306.758s][info][gc            ] GC(36) Pause Remark 366M->366M(1026M) 26.419ms
[2025-09-10T16:53:18.288+0800][306.758s][info][gc,cpu        ] GC(36) User=0.23s Sys=0.00s Real=0.02s
[2025-09-10T16:53:18.288+0800][306.758s][info][gc,marking    ] GC(36) Concurrent Rebuild Remembered Sets
[2025-09-10T16:53:18.345+0800][306.815s][info][gc,marking    ] GC(36) Concurrent Rebuild Remembered Sets 56.416ms
[2025-09-10T16:53:18.349+0800][306.819s][info][gc,start      ] GC(36) Pause Cleanup
[2025-09-10T16:53:18.349+0800][306.819s][info][gc            ] GC(36) Pause Cleanup 396M->396M(1026M) 0.665ms
[2025-09-10T16:53:18.350+0800][306.820s][info][gc,cpu        ] GC(36) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T16:53:18.350+0800][306.820s][info][gc,marking    ] GC(36) Concurrent Cleanup for Next Mark
[2025-09-10T16:53:18.351+0800][306.821s][info][gc,marking    ] GC(36) Concurrent Cleanup for Next Mark 0.869ms
[2025-09-10T16:53:18.351+0800][306.821s][info][gc            ] GC(36) Concurrent Cycle 157.453ms
[2025-09-10T16:53:21.717+0800][310.187s][info][gc,start      ] GC(37) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:21.717+0800][310.187s][info][gc,task       ] GC(37) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:21.727+0800][310.197s][info][gc,phases     ] GC(37)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:53:21.728+0800][310.198s][info][gc,phases     ] GC(37)   Evacuate Collection Set: 8.4ms
[2025-09-10T16:53:21.728+0800][310.198s][info][gc,phases     ] GC(37)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T16:53:21.728+0800][310.198s][info][gc,phases     ] GC(37)   Other: 0.7ms
[2025-09-10T16:53:21.728+0800][310.198s][info][gc,heap       ] GC(37) Eden regions: 515->0(500)
[2025-09-10T16:53:21.729+0800][310.199s][info][gc,heap       ] GC(37) Survivor regions: 22->38(68)
[2025-09-10T16:53:21.729+0800][310.199s][info][gc,heap       ] GC(37) Old regions: 324->324
[2025-09-10T16:53:21.729+0800][310.199s][info][gc,heap       ] GC(37) Humongous regions: 3->2
[2025-09-10T16:53:21.729+0800][310.199s][info][gc,metaspace  ] GC(37) Metaspace: 215960K(239748K)->215960K(239748K) NonClass: 189932K(207336K)->189932K(207336K) Class: 26028K(32412K)->26028K(32412K)
[2025-09-10T16:53:21.729+0800][310.199s][info][gc            ] GC(37) Pause Young (Normal) (G1 Evacuation Pause) 862M->362M(1026M) 12.501ms
[2025-09-10T16:53:21.729+0800][310.199s][info][gc,cpu        ] GC(37) User=0.13s Sys=0.00s Real=0.01s
[2025-09-10T16:53:23.630+0800][312.100s][info][gc,start      ] GC(38) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:53:23.631+0800][312.101s][info][gc,task       ] GC(38) Using 15 workers of 15 for evacuation
[2025-09-10T16:53:23.647+0800][312.117s][info][gc,phases     ] GC(38)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:53:23.648+0800][312.118s][info][gc,phases     ] GC(38)   Evacuate Collection Set: 12.3ms
[2025-09-10T16:53:23.648+0800][312.118s][info][gc,phases     ] GC(38)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T16:53:23.648+0800][312.118s][info][gc,phases     ] GC(38)   Other: 3.5ms
[2025-09-10T16:53:23.649+0800][312.119s][info][gc,heap       ] GC(38) Eden regions: 500->0(486)
[2025-09-10T16:53:23.649+0800][312.119s][info][gc,heap       ] GC(38) Survivor regions: 38->39(68)
[2025-09-10T16:53:23.649+0800][312.119s][info][gc,heap       ] GC(38) Old regions: 324->339
[2025-09-10T16:53:23.649+0800][312.119s][info][gc,heap       ] GC(38) Humongous regions: 2->2
[2025-09-10T16:53:23.650+0800][312.120s][info][gc,metaspace  ] GC(38) Metaspace: 218477K(239748K)->218477K(239748K) NonClass: 192093K(207336K)->192093K(207336K) Class: 26383K(32412K)->26383K(32412K)
[2025-09-10T16:53:23.650+0800][312.120s][info][gc            ] GC(38) Pause Young (Normal) (G1 Evacuation Pause) 862M->378M(1026M) 19.664ms
[2025-09-10T16:53:23.650+0800][312.120s][info][gc,cpu        ] GC(38) User=0.16s Sys=0.03s Real=0.02s
[2025-09-10T16:55:04.778+0800][413.254s][info][gc,start      ] GC(39) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:55:04.779+0800][413.254s][info][gc,task       ] GC(39) Using 15 workers of 15 for evacuation
[2025-09-10T16:55:04.789+0800][413.264s][info][gc,phases     ] GC(39)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:55:04.789+0800][413.264s][info][gc,phases     ] GC(39)   Evacuate Collection Set: 7.7ms
[2025-09-10T16:55:04.789+0800][413.264s][info][gc,phases     ] GC(39)   Post Evacuate Collection Set: 2.3ms
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,phases     ] GC(39)   Other: 0.5ms
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,heap       ] GC(39) Eden regions: 486->0(498)
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,heap       ] GC(39) Survivor regions: 39->23(66)
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,heap       ] GC(39) Old regions: 339->352
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,heap       ] GC(39) Humongous regions: 6->1
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,metaspace  ] GC(39) Metaspace: 219722K(239748K)->219722K(239748K) NonClass: 193229K(207336K)->193229K(207336K) Class: 26493K(32412K)->26493K(32412K)
[2025-09-10T16:55:04.790+0800][413.265s][info][gc            ] GC(39) Pause Young (Normal) (G1 Evacuation Pause) 868M->374M(1026M) 11.678ms
[2025-09-10T16:55:04.790+0800][413.265s][info][gc,cpu        ] GC(39) User=0.12s Sys=0.01s Real=0.01s
[2025-09-10T16:55:45.347+0800][453.823s][info][gc,start      ] GC(40) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:55:45.347+0800][453.823s][info][gc,task       ] GC(40) Using 15 workers of 15 for evacuation
[2025-09-10T16:55:45.359+0800][453.835s][info][gc,phases     ] GC(40)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:55:45.359+0800][453.836s][info][gc,phases     ] GC(40)   Evacuate Collection Set: 7.9ms
[2025-09-10T16:55:45.360+0800][453.836s][info][gc,phases     ] GC(40)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T16:55:45.360+0800][453.836s][info][gc,phases     ] GC(40)   Other: 2.3ms
[2025-09-10T16:55:45.360+0800][453.836s][info][gc,heap       ] GC(40) Eden regions: 498->0(487)
[2025-09-10T16:55:45.360+0800][453.836s][info][gc,heap       ] GC(40) Survivor regions: 23->34(66)
[2025-09-10T16:55:45.360+0800][453.837s][info][gc,heap       ] GC(40) Old regions: 352->352
[2025-09-10T16:55:45.361+0800][453.837s][info][gc,heap       ] GC(40) Humongous regions: 4->2
[2025-09-10T16:55:45.361+0800][453.837s][info][gc,metaspace  ] GC(40) Metaspace: 223241K(240900K)->223241K(240900K) NonClass: 196249K(208360K)->196249K(208360K) Class: 26992K(32540K)->26992K(32540K)
[2025-09-10T16:55:45.361+0800][453.837s][info][gc            ] GC(40) Pause Young (Normal) (G1 Evacuation Pause) 875M->385M(1026M) 13.967ms
[2025-09-10T16:55:45.361+0800][453.837s][info][gc,cpu        ] GC(40) User=0.11s Sys=0.00s Real=0.01s
[2025-09-10T16:56:17.590+0800][486.067s][info][gc,start      ] GC(41) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:56:17.590+0800][486.068s][info][gc,task       ] GC(41) Using 15 workers of 15 for evacuation
[2025-09-10T16:56:17.599+0800][486.077s][info][gc,phases     ] GC(41)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:56:17.601+0800][486.078s][info][gc,phases     ] GC(41)   Evacuate Collection Set: 7.0ms
[2025-09-10T16:56:17.602+0800][486.080s][info][gc,phases     ] GC(41)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T16:56:17.602+0800][486.080s][info][gc,phases     ] GC(41)   Other: 0.9ms
[2025-09-10T16:56:17.603+0800][486.081s][info][gc,heap       ] GC(41) Eden regions: 487->0(490)
[2025-09-10T16:56:17.603+0800][486.081s][info][gc,heap       ] GC(41) Survivor regions: 34->30(66)
[2025-09-10T16:56:17.604+0800][486.081s][info][gc,heap       ] GC(41) Old regions: 352->352
[2025-09-10T16:56:17.604+0800][486.082s][info][gc,heap       ] GC(41) Humongous regions: 5->2
[2025-09-10T16:56:17.604+0800][486.082s][info][gc,metaspace  ] GC(41) Metaspace: 227496K(245124K)->227496K(245124K) NonClass: 199872K(211944K)->199872K(211944K) Class: 27623K(33180K)->27623K(33180K)
[2025-09-10T16:56:17.604+0800][486.082s][info][gc            ] GC(41) Pause Young (Normal) (G1 Evacuation Pause) 875M->382M(1026M) 14.386ms
[2025-09-10T16:56:17.604+0800][486.082s][info][gc,cpu        ] GC(41) User=0.11s Sys=0.00s Real=0.01s
[2025-09-10T16:57:10.349+0800][538.829s][info][gc,start      ] GC(42) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:57:10.350+0800][538.830s][info][gc,task       ] GC(42) Using 15 workers of 15 for evacuation
[2025-09-10T16:57:10.359+0800][538.839s][info][gc,phases     ] GC(42)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:57:10.359+0800][538.839s][info][gc,phases     ] GC(42)   Evacuate Collection Set: 6.7ms
[2025-09-10T16:57:10.359+0800][538.839s][info][gc,phases     ] GC(42)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T16:57:10.359+0800][538.839s][info][gc,phases     ] GC(42)   Other: 1.5ms
[2025-09-10T16:57:10.359+0800][538.839s][info][gc,heap       ] GC(42) Eden regions: 490->0(489)
[2025-09-10T16:57:10.360+0800][538.841s][info][gc,heap       ] GC(42) Survivor regions: 30->33(65)
[2025-09-10T16:57:10.361+0800][538.841s][info][gc,heap       ] GC(42) Old regions: 352->352
[2025-09-10T16:57:10.361+0800][538.841s][info][gc,heap       ] GC(42) Humongous regions: 4->1
[2025-09-10T16:57:10.361+0800][538.842s][info][gc,metaspace  ] GC(42) Metaspace: 229690K(247428K)->229690K(247428K) NonClass: 201793K(213992K)->201793K(213992K) Class: 27896K(33436K)->27896K(33436K)
[2025-09-10T16:57:10.362+0800][538.843s][info][gc            ] GC(42) Pause Young (Normal) (G1 Evacuation Pause) 874M->384M(1026M) 13.128ms
[2025-09-10T16:57:10.363+0800][538.843s][info][gc,cpu        ] GC(42) User=0.10s Sys=0.01s Real=0.01s
[2025-09-10T16:58:22.852+0800][611.335s][info][gc,start      ] GC(43) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:58:22.852+0800][611.336s][info][gc,task       ] GC(43) Using 15 workers of 15 for evacuation
[2025-09-10T16:58:22.862+0800][611.345s][info][gc,phases     ] GC(43)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:58:22.863+0800][611.346s][info][gc,phases     ] GC(43)   Evacuate Collection Set: 7.4ms
[2025-09-10T16:58:22.863+0800][611.346s][info][gc,phases     ] GC(43)   Post Evacuate Collection Set: 2.1ms
[2025-09-10T16:58:22.863+0800][611.346s][info][gc,phases     ] GC(43)   Other: 0.7ms
[2025-09-10T16:58:22.863+0800][611.347s][info][gc,heap       ] GC(43) Eden regions: 489->0(482)
[2025-09-10T16:58:22.864+0800][611.347s][info][gc,heap       ] GC(43) Survivor regions: 33->41(66)
[2025-09-10T16:58:22.864+0800][611.347s][info][gc,heap       ] GC(43) Old regions: 352->352
[2025-09-10T16:58:22.864+0800][611.347s][info][gc,heap       ] GC(43) Humongous regions: 2->2
[2025-09-10T16:58:22.865+0800][611.348s][info][gc,metaspace  ] GC(43) Metaspace: 229992K(247812K)->229992K(247812K) NonClass: 202067K(214248K)->202067K(214248K) Class: 27925K(33564K)->27925K(33564K)
[2025-09-10T16:58:22.865+0800][611.348s][info][gc            ] GC(43) Pause Young (Normal) (G1 Evacuation Pause) 874M->393M(1026M) 12.835ms
[2025-09-10T16:58:22.865+0800][611.348s][info][gc,cpu        ] GC(43) User=0.13s Sys=0.00s Real=0.02s
[2025-09-10T16:58:23.836+0800][612.319s][info][gc,start      ] GC(44) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:58:23.836+0800][612.319s][info][gc,task       ] GC(44) Using 15 workers of 15 for evacuation
[2025-09-10T16:58:23.848+0800][612.331s][info][gc,phases     ] GC(44)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:58:23.848+0800][612.332s][info][gc,phases     ] GC(44)   Evacuate Collection Set: 10.0ms
[2025-09-10T16:58:23.849+0800][612.332s][info][gc,phases     ] GC(44)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T16:58:23.849+0800][612.332s][info][gc,phases     ] GC(44)   Other: 0.7ms
[2025-09-10T16:58:23.849+0800][612.332s][info][gc,heap       ] GC(44) Eden regions: 482->0(488)
[2025-09-10T16:58:23.849+0800][612.333s][info][gc,heap       ] GC(44) Survivor regions: 41->35(66)
[2025-09-10T16:58:23.850+0800][612.333s][info][gc,heap       ] GC(44) Old regions: 352->359
[2025-09-10T16:58:23.850+0800][612.333s][info][gc,heap       ] GC(44) Humongous regions: 2->2
[2025-09-10T16:58:23.850+0800][612.334s][info][gc,metaspace  ] GC(44) Metaspace: 230130K(248068K)->230130K(248068K) NonClass: 202193K(214504K)->202193K(214504K) Class: 27936K(33564K)->27936K(33564K)
[2025-09-10T16:58:23.850+0800][612.334s][info][gc            ] GC(44) Pause Young (Normal) (G1 Evacuation Pause) 875M->393M(1026M) 14.799ms
[2025-09-10T16:58:23.851+0800][612.334s][info][gc,cpu        ] GC(44) User=0.16s Sys=0.00s Real=0.01s
[2025-09-10T16:58:24.621+0800][613.105s][info][gc,start      ] GC(45) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:58:24.622+0800][613.105s][info][gc,task       ] GC(45) Using 15 workers of 15 for evacuation
[2025-09-10T16:58:24.629+0800][613.112s][info][gc,phases     ] GC(45)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:58:24.630+0800][613.113s][info][gc,phases     ] GC(45)   Evacuate Collection Set: 6.0ms
[2025-09-10T16:58:24.630+0800][613.113s][info][gc,phases     ] GC(45)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T16:58:24.630+0800][613.114s][info][gc,phases     ] GC(45)   Other: 0.8ms
[2025-09-10T16:58:24.631+0800][613.114s][info][gc,heap       ] GC(45) Eden regions: 488->0(488)
[2025-09-10T16:58:24.631+0800][613.114s][info][gc,heap       ] GC(45) Survivor regions: 35->37(66)
[2025-09-10T16:58:24.631+0800][613.115s][info][gc,heap       ] GC(45) Old regions: 359->359
[2025-09-10T16:58:24.632+0800][613.115s][info][gc,heap       ] GC(45) Humongous regions: 2->2
[2025-09-10T16:58:24.632+0800][613.115s][info][gc,metaspace  ] GC(45) Metaspace: 230245K(248068K)->230245K(248068K) NonClass: 202303K(214504K)->202303K(214504K) Class: 27942K(33564K)->27942K(33564K)
[2025-09-10T16:58:24.632+0800][613.115s][info][gc            ] GC(45) Pause Young (Normal) (G1 Evacuation Pause) 881M->396M(1026M) 10.675ms
[2025-09-10T16:58:24.632+0800][613.115s][info][gc,cpu        ] GC(45) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T16:58:25.469+0800][613.952s][info][gc,start      ] GC(46) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:58:25.469+0800][613.952s][info][gc,task       ] GC(46) Using 15 workers of 15 for evacuation
[2025-09-10T16:58:25.479+0800][613.962s][info][gc,phases     ] GC(46)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:58:25.479+0800][613.962s][info][gc,phases     ] GC(46)   Evacuate Collection Set: 7.3ms
[2025-09-10T16:58:25.479+0800][613.962s][info][gc,phases     ] GC(46)   Post Evacuate Collection Set: 2.2ms
[2025-09-10T16:58:25.479+0800][613.963s][info][gc,phases     ] GC(46)   Other: 0.5ms
[2025-09-10T16:58:25.480+0800][613.963s][info][gc,heap       ] GC(46) Eden regions: 488->0(487)
[2025-09-10T16:58:25.480+0800][613.963s][info][gc,heap       ] GC(46) Survivor regions: 37->36(66)
[2025-09-10T16:58:25.480+0800][613.963s][info][gc,heap       ] GC(46) Old regions: 359->362
[2025-09-10T16:58:25.480+0800][613.963s][info][gc,heap       ] GC(46) Humongous regions: 2->2
[2025-09-10T16:58:25.480+0800][613.963s][info][gc,metaspace  ] GC(46) Metaspace: 230306K(248324K)->230306K(248324K) NonClass: 202358K(214760K)->202358K(214760K) Class: 27947K(33564K)->27947K(33564K)
[2025-09-10T16:58:25.480+0800][613.963s][info][gc            ] GC(46) Pause Young (Normal) (G1 Evacuation Pause) 884M->397M(1026M) 11.580ms
[2025-09-10T16:58:25.480+0800][613.964s][info][gc,cpu        ] GC(46) User=0.14s Sys=0.00s Real=0.01s
[2025-09-10T16:58:30.044+0800][618.529s][info][gc,start      ] GC(47) Pause Young (Concurrent Start) (System.gc())
[2025-09-10T16:58:30.045+0800][618.530s][info][gc,task       ] GC(47) Using 15 workers of 15 for evacuation
[2025-09-10T16:58:30.053+0800][618.538s][info][gc,phases     ] GC(47)   Pre Evacuate Collection Set: 0.6ms
[2025-09-10T16:58:30.054+0800][618.538s][info][gc,phases     ] GC(47)   Evacuate Collection Set: 5.9ms
[2025-09-10T16:58:30.054+0800][618.539s][info][gc,phases     ] GC(47)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T16:58:30.054+0800][618.539s][info][gc,phases     ] GC(47)   Other: 1.5ms
[2025-09-10T16:58:30.054+0800][618.539s][info][gc,heap       ] GC(47) Eden regions: 68->0(498)
[2025-09-10T16:58:30.054+0800][618.539s][info][gc,heap       ] GC(47) Survivor regions: 36->27(66)
[2025-09-10T16:58:30.055+0800][618.539s][info][gc,heap       ] GC(47) Old regions: 362->362
[2025-09-10T16:58:30.055+0800][618.539s][info][gc,heap       ] GC(47) Humongous regions: 2->0
[2025-09-10T16:58:30.055+0800][618.539s][info][gc,metaspace  ] GC(47) Metaspace: 230349K(248324K)->230349K(248324K) NonClass: 202401K(214760K)->202401K(214760K) Class: 27947K(33564K)->27947K(33564K)
[2025-09-10T16:58:30.055+0800][618.540s][info][gc            ] GC(47) Pause Young (Concurrent Start) (System.gc()) 464M->386M(1026M) 10.488ms
[2025-09-10T16:58:30.055+0800][618.540s][info][gc,cpu        ] GC(47) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T16:58:30.055+0800][618.540s][info][gc            ] GC(48) Concurrent Cycle
[2025-09-10T16:58:30.055+0800][618.540s][info][gc,marking    ] GC(48) Concurrent Clear Claimed Marks
[2025-09-10T16:58:30.056+0800][618.540s][info][gc,marking    ] GC(48) Concurrent Clear Claimed Marks 0.555ms
[2025-09-10T16:58:30.056+0800][618.541s][info][gc,marking    ] GC(48) Concurrent Scan Root Regions
[2025-09-10T16:58:30.059+0800][618.544s][info][gc,marking    ] GC(48) Concurrent Scan Root Regions 3.369ms
[2025-09-10T16:58:30.060+0800][618.544s][info][gc,marking    ] GC(48) Concurrent Mark (618.544s)
[2025-09-10T16:58:30.060+0800][618.544s][info][gc,marking    ] GC(48) Concurrent Mark From Roots
[2025-09-10T16:58:30.060+0800][618.545s][info][gc,task       ] GC(48) Using 4 workers of 4 for marking
[2025-09-10T16:58:30.121+0800][618.606s][info][gc,marking    ] GC(48) Concurrent Mark From Roots 61.514ms
[2025-09-10T16:58:30.122+0800][618.606s][info][gc,marking    ] GC(48) Concurrent Preclean
[2025-09-10T16:58:30.122+0800][618.607s][info][gc,marking    ] GC(48) Concurrent Preclean 0.531ms
[2025-09-10T16:58:30.122+0800][618.607s][info][gc,marking    ] GC(48) Concurrent Mark (618.544s, 618.607s) 62.794ms
[2025-09-10T16:58:30.123+0800][618.608s][info][gc,start      ] GC(48) Pause Remark
[2025-09-10T16:58:30.148+0800][618.633s][info][gc,stringtable] GC(48) Cleaned string and symbol table, strings: 193554 processed, 11765 removed, symbols: 490810 processed, 9027 removed
[2025-09-10T16:58:30.151+0800][618.636s][info][gc            ] GC(48) Pause Remark 386M->384M(1026M) 28.017ms
[2025-09-10T16:58:30.151+0800][618.636s][info][gc,cpu        ] GC(48) User=0.27s Sys=0.01s Real=0.02s
[2025-09-10T16:58:30.152+0800][618.637s][info][gc,marking    ] GC(48) Concurrent Rebuild Remembered Sets
[2025-09-10T16:58:30.201+0800][618.686s][info][gc,marking    ] GC(48) Concurrent Rebuild Remembered Sets 48.981ms
[2025-09-10T16:58:30.202+0800][618.686s][info][gc,start      ] GC(48) Pause Cleanup
[2025-09-10T16:58:30.202+0800][618.687s][info][gc            ] GC(48) Pause Cleanup 384M->384M(1026M) 0.661ms
[2025-09-10T16:58:30.203+0800][618.688s][info][gc,cpu        ] GC(48) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T16:58:30.204+0800][618.688s][info][gc,marking    ] GC(48) Concurrent Cleanup for Next Mark
[2025-09-10T16:58:30.205+0800][618.689s][info][gc,marking    ] GC(48) Concurrent Cleanup for Next Mark 1.188ms
[2025-09-10T16:58:30.205+0800][618.690s][info][gc            ] GC(48) Concurrent Cycle 149.910ms
[2025-09-10T16:59:00.303+0800][648.788s][info][gc,start      ] GC(49) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:00.303+0800][648.788s][info][gc,task       ] GC(49) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:00.318+0800][648.804s][info][gc,phases     ] GC(49)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:00.319+0800][648.804s][info][gc,phases     ] GC(49)   Evacuate Collection Set: 12.7ms
[2025-09-10T16:59:00.319+0800][648.805s][info][gc,phases     ] GC(49)   Post Evacuate Collection Set: 2.2ms
[2025-09-10T16:59:00.319+0800][648.805s][info][gc,phases     ] GC(49)   Other: 0.6ms
[2025-09-10T16:59:00.319+0800][648.805s][info][gc,heap       ] GC(49) Eden regions: 498->0(1)
[2025-09-10T16:59:00.320+0800][648.805s][info][gc,heap       ] GC(49) Survivor regions: 27->55(66)
[2025-09-10T16:59:00.321+0800][648.807s][info][gc,heap       ] GC(49) Old regions: 360->360
[2025-09-10T16:59:00.321+0800][648.807s][info][gc,heap       ] GC(49) Humongous regions: 5->1
[2025-09-10T16:59:00.322+0800][648.807s][info][gc,metaspace  ] GC(49) Metaspace: 226340K(248324K)->226340K(248324K) NonClass: 198480K(214760K)->198480K(214760K) Class: 27859K(33564K)->27859K(33564K)
[2025-09-10T16:59:00.322+0800][648.808s][info][gc            ] GC(49) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 887M->413M(1026M) 19.413ms
[2025-09-10T16:59:00.322+0800][648.808s][info][gc,cpu        ] GC(49) User=0.20s Sys=0.00s Real=0.02s
[2025-09-10T16:59:00.331+0800][648.817s][info][gc,start      ] GC(50) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:00.332+0800][648.817s][info][gc,task       ] GC(50) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:00.355+0800][648.841s][info][gc,phases     ] GC(50)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T16:59:00.356+0800][648.841s][info][gc,phases     ] GC(50)   Evacuate Collection Set: 22.0ms
[2025-09-10T16:59:00.356+0800][648.842s][info][gc,phases     ] GC(50)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T16:59:00.357+0800][648.842s][info][gc,phases     ] GC(50)   Other: 0.6ms
[2025-09-10T16:59:00.357+0800][648.842s][info][gc,heap       ] GC(50) Eden regions: 1->0(517)
[2025-09-10T16:59:00.357+0800][648.843s][info][gc,heap       ] GC(50) Survivor regions: 55->6(7)
[2025-09-10T16:59:00.357+0800][648.843s][info][gc,heap       ] GC(50) Old regions: 360->340
[2025-09-10T16:59:00.357+0800][648.843s][info][gc,heap       ] GC(50) Humongous regions: 1->1
[2025-09-10T16:59:00.358+0800][648.843s][info][gc,metaspace  ] GC(50) Metaspace: 226341K(248324K)->226341K(248324K) NonClass: 198482K(214760K)->198482K(214760K) Class: 27859K(33564K)->27859K(33564K)
[2025-09-10T16:59:00.358+0800][648.843s][info][gc            ] GC(50) Pause Young (Mixed) (G1 Evacuation Pause) 414M->344M(1026M) 26.295ms
[2025-09-10T16:59:00.358+0800][648.843s][info][gc,cpu        ] GC(50) User=0.32s Sys=0.00s Real=0.03s
[2025-09-10T16:59:11.058+0800][659.544s][info][gc,start      ] GC(51) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:11.060+0800][659.545s][info][gc,task       ] GC(51) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:11.073+0800][659.559s][info][gc,phases     ] GC(51)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:11.083+0800][659.569s][info][gc,phases     ] GC(51)   Evacuate Collection Set: 8.4ms
[2025-09-10T16:59:11.088+0800][659.573s][info][gc,phases     ] GC(51)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T16:59:11.093+0800][659.579s][info][gc,phases     ] GC(51)   Other: 4.6ms
[2025-09-10T16:59:11.098+0800][659.583s][info][gc,heap       ] GC(51) Eden regions: 517->0(478)
[2025-09-10T16:59:11.101+0800][659.587s][info][gc,heap       ] GC(51) Survivor regions: 6->35(66)
[2025-09-10T16:59:11.109+0800][659.594s][info][gc,heap       ] GC(51) Old regions: 340->340
[2025-09-10T16:59:11.113+0800][659.598s][info][gc,heap       ] GC(51) Humongous regions: 13->1
[2025-09-10T16:59:11.118+0800][659.603s][info][gc,metaspace  ] GC(51) Metaspace: 226447K(248324K)->226447K(248324K) NonClass: 198586K(214760K)->198586K(214760K) Class: 27861K(33564K)->27861K(33564K)
[2025-09-10T16:59:11.123+0800][659.609s][info][gc            ] GC(51) Pause Young (Normal) (G1 Evacuation Pause) 873M->374M(1026M) 64.739ms
[2025-09-10T16:59:11.127+0800][659.613s][info][gc,cpu        ] GC(51) User=0.14s Sys=0.00s Real=0.07s
[2025-09-10T16:59:12.678+0800][661.163s][info][gc,start      ] GC(52) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:12.678+0800][661.164s][info][gc,task       ] GC(52) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:12.692+0800][661.178s][info][gc,phases     ] GC(52)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:12.692+0800][661.178s][info][gc,phases     ] GC(52)   Evacuate Collection Set: 12.0ms
[2025-09-10T16:59:12.692+0800][661.178s][info][gc,phases     ] GC(52)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T16:59:12.693+0800][661.178s][info][gc,phases     ] GC(52)   Other: 0.9ms
[2025-09-10T16:59:12.693+0800][661.178s][info][gc,heap       ] GC(52) Eden regions: 478->0(442)
[2025-09-10T16:59:12.693+0800][661.179s][info][gc,heap       ] GC(52) Survivor regions: 35->33(65)
[2025-09-10T16:59:12.693+0800][661.179s][info][gc,heap       ] GC(52) Old regions: 340->374
[2025-09-10T16:59:12.693+0800][661.179s][info][gc,heap       ] GC(52) Humongous regions: 2->2
[2025-09-10T16:59:12.693+0800][661.179s][info][gc,metaspace  ] GC(52) Metaspace: 236609K(253444K)->236609K(253444K) NonClass: 207548K(219112K)->207548K(219112K) Class: 29060K(34332K)->29060K(34332K)
[2025-09-10T16:59:12.693+0800][661.179s][info][gc            ] GC(52) Pause Young (Normal) (G1 Evacuation Pause) 853M->406M(1026M) 15.897ms
[2025-09-10T16:59:12.694+0800][661.179s][info][gc,cpu        ] GC(52) User=0.18s Sys=0.02s Real=0.02s
[2025-09-10T16:59:20.878+0800][669.364s][info][gc,start      ] GC(53) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:20.878+0800][669.364s][info][gc,task       ] GC(53) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:20.891+0800][669.377s][info][gc,phases     ] GC(53)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T16:59:20.891+0800][669.377s][info][gc,phases     ] GC(53)   Evacuate Collection Set: 10.7ms
[2025-09-10T16:59:20.892+0800][669.377s][info][gc,phases     ] GC(53)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T16:59:20.892+0800][669.377s][info][gc,phases     ] GC(53)   Other: 0.6ms
[2025-09-10T16:59:20.892+0800][669.378s][info][gc,heap       ] GC(53) Eden regions: 442->0(415)
[2025-09-10T16:59:20.892+0800][669.378s][info][gc,heap       ] GC(53) Survivor regions: 33->32(60)
[2025-09-10T16:59:20.892+0800][669.378s][info][gc,heap       ] GC(53) Old regions: 374->398
[2025-09-10T16:59:20.892+0800][669.378s][info][gc,heap       ] GC(53) Humongous regions: 3->2
[2025-09-10T16:59:20.893+0800][669.378s][info][gc,metaspace  ] GC(53) Metaspace: 244262K(263940K)->244262K(263940K) NonClass: 214213K(227816K)->214213K(227816K) Class: 30048K(36124K)->30048K(36124K)
[2025-09-10T16:59:20.893+0800][669.378s][info][gc            ] GC(53) Pause Young (Normal) (G1 Evacuation Pause) 849M->429M(1026M) 14.742ms
[2025-09-10T16:59:20.893+0800][669.379s][info][gc,cpu        ] GC(53) User=0.16s Sys=0.00s Real=0.02s
[2025-09-10T16:59:23.670+0800][672.155s][info][gc,start      ] GC(54) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:23.671+0800][672.156s][info][gc,task       ] GC(54) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:23.680+0800][672.166s][info][gc,phases     ] GC(54)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:23.680+0800][672.166s][info][gc,phases     ] GC(54)   Evacuate Collection Set: 7.8ms
[2025-09-10T16:59:23.680+0800][672.166s][info][gc,phases     ] GC(54)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T16:59:23.681+0800][672.166s][info][gc,phases     ] GC(54)   Other: 1.1ms
[2025-09-10T16:59:23.681+0800][672.166s][info][gc,heap       ] GC(54) Eden regions: 415->0(412)
[2025-09-10T16:59:23.681+0800][672.166s][info][gc,heap       ] GC(54) Survivor regions: 32->14(56)
[2025-09-10T16:59:23.681+0800][672.167s][info][gc,heap       ] GC(54) Old regions: 398->429
[2025-09-10T16:59:23.681+0800][672.167s][info][gc,heap       ] GC(54) Humongous regions: 2->2
[2025-09-10T16:59:23.681+0800][672.167s][info][gc,metaspace  ] GC(54) Metaspace: 246892K(267396K)->246892K(267396K) NonClass: 216467K(230632K)->216467K(230632K) Class: 30425K(36764K)->30425K(36764K)
[2025-09-10T16:59:23.681+0800][672.167s][info][gc            ] GC(54) Pause Young (Normal) (G1 Evacuation Pause) 844M->442M(1026M) 11.422ms
[2025-09-10T16:59:23.681+0800][672.167s][info][gc,cpu        ] GC(54) User=0.11s Sys=0.01s Real=0.02s
[2025-09-10T16:59:26.431+0800][674.916s][info][gc,start      ] GC(55) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:26.431+0800][674.917s][info][gc,task       ] GC(55) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:26.437+0800][674.923s][info][gc,phases     ] GC(55)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:26.438+0800][674.923s][info][gc,phases     ] GC(55)   Evacuate Collection Set: 4.5ms
[2025-09-10T16:59:26.438+0800][674.923s][info][gc,phases     ] GC(55)   Post Evacuate Collection Set: 1.7ms
[2025-09-10T16:59:26.438+0800][674.923s][info][gc,phases     ] GC(55)   Other: 0.4ms
[2025-09-10T16:59:26.438+0800][674.924s][info][gc,heap       ] GC(55) Eden regions: 412->0(412)
[2025-09-10T16:59:26.438+0800][674.924s][info][gc,heap       ] GC(55) Survivor regions: 14->22(54)
[2025-09-10T16:59:26.438+0800][674.924s][info][gc,heap       ] GC(55) Old regions: 429->429
[2025-09-10T16:59:26.438+0800][674.924s][info][gc,heap       ] GC(55) Humongous regions: 2->2
[2025-09-10T16:59:26.438+0800][674.924s][info][gc,metaspace  ] GC(55) Metaspace: 253124K(274308K)->253124K(274308K) NonClass: 221894K(236520K)->221894K(236520K) Class: 31230K(37788K)->31230K(37788K)
[2025-09-10T16:59:26.438+0800][674.924s][info][gc            ] GC(55) Pause Young (Normal) (G1 Evacuation Pause) 854M->450M(1026M) 7.550ms
[2025-09-10T16:59:26.438+0800][674.924s][info][gc,cpu        ] GC(55) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T16:59:33.842+0800][682.330s][info][gc,start      ] GC(56) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:33.842+0800][682.330s][info][gc,task       ] GC(56) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:33.855+0800][682.343s][info][gc,phases     ] GC(56)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:33.856+0800][682.343s][info][gc,phases     ] GC(56)   Evacuate Collection Set: 10.0ms
[2025-09-10T16:59:33.856+0800][682.344s][info][gc,phases     ] GC(56)   Post Evacuate Collection Set: 2.5ms
[2025-09-10T16:59:33.856+0800][682.344s][info][gc,phases     ] GC(56)   Other: 0.6ms
[2025-09-10T16:59:33.856+0800][682.344s][info][gc,heap       ] GC(56) Eden regions: 412->0(380)
[2025-09-10T16:59:33.856+0800][682.344s][info][gc,heap       ] GC(56) Survivor regions: 22->50(55)
[2025-09-10T16:59:33.857+0800][682.344s][info][gc,heap       ] GC(56) Old regions: 429->429
[2025-09-10T16:59:33.857+0800][682.345s][info][gc,heap       ] GC(56) Humongous regions: 3->2
[2025-09-10T16:59:33.857+0800][682.345s][info][gc,metaspace  ] GC(56) Metaspace: 260172K(283780K)->260172K(283780K) NonClass: 227952K(244456K)->227952K(244456K) Class: 32219K(39324K)->32219K(39324K)
[2025-09-10T16:59:33.857+0800][682.345s][info][gc            ] GC(56) Pause Young (Normal) (G1 Evacuation Pause) 863M->478M(1026M) 14.857ms
[2025-09-10T16:59:33.857+0800][682.345s][info][gc,cpu        ] GC(56) User=0.18s Sys=0.00s Real=0.01s
[2025-09-10T16:59:34.411+0800][682.899s][info][gc,start      ] GC(57) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:34.411+0800][682.899s][info][gc,task       ] GC(57) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:34.433+0800][682.921s][info][gc,phases     ] GC(57)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:34.445+0800][682.933s][info][gc,phases     ] GC(57)   Evacuate Collection Set: 17.8ms
[2025-09-10T16:59:34.449+0800][682.937s][info][gc,phases     ] GC(57)   Post Evacuate Collection Set: 2.9ms
[2025-09-10T16:59:34.453+0800][682.941s][info][gc,phases     ] GC(57)   Other: 1.1ms
[2025-09-10T16:59:34.457+0800][682.945s][info][gc,heap       ] GC(57) Eden regions: 380->0(371)
[2025-09-10T16:59:34.461+0800][682.949s][info][gc,heap       ] GC(57) Survivor regions: 50->18(54)
[2025-09-10T16:59:34.465+0800][682.952s][info][gc,heap       ] GC(57) Old regions: 429->476
[2025-09-10T16:59:34.472+0800][682.960s][info][gc,heap       ] GC(57) Humongous regions: 2->2
[2025-09-10T16:59:34.477+0800][682.965s][info][gc,metaspace  ] GC(57) Metaspace: 271559K(297092K)->271559K(297092K) NonClass: 237902K(255464K)->237902K(255464K) Class: 33656K(41628K)->33656K(41628K)
[2025-09-10T16:59:34.480+0800][682.968s][info][gc            ] GC(57) Pause Young (Normal) (G1 Evacuation Pause) 858M->493M(1026M) 69.424ms
[2025-09-10T16:59:34.489+0800][682.976s][info][gc,cpu        ] GC(57) User=0.51s Sys=0.04s Real=0.07s
[2025-09-10T16:59:36.634+0800][685.122s][info][gc,start      ] GC(58) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T16:59:36.634+0800][685.122s][info][gc,task       ] GC(58) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:36.648+0800][685.136s][info][gc,phases     ] GC(58)   Pre Evacuate Collection Set: 0.6ms
[2025-09-10T16:59:36.649+0800][685.137s][info][gc,phases     ] GC(58)   Evacuate Collection Set: 11.6ms
[2025-09-10T16:59:36.649+0800][685.137s][info][gc,phases     ] GC(58)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T16:59:36.650+0800][685.137s][info][gc,phases     ] GC(58)   Other: 0.7ms
[2025-09-10T16:59:36.650+0800][685.138s][info][gc,heap       ] GC(58) Eden regions: 371->0(352)
[2025-09-10T16:59:36.650+0800][685.138s][info][gc,heap       ] GC(58) Survivor regions: 18->36(49)
[2025-09-10T16:59:36.650+0800][685.138s][info][gc,heap       ] GC(58) Old regions: 476->476
[2025-09-10T16:59:36.650+0800][685.138s][info][gc,heap       ] GC(58) Humongous regions: 2->2
[2025-09-10T16:59:36.650+0800][685.138s][info][gc,metaspace  ] GC(58) Metaspace: 282399K(308612K)->282399K(308612K) NonClass: 247205K(265192K)->247205K(265192K) Class: 35194K(43420K)->35194K(43420K)
[2025-09-10T16:59:36.651+0800][685.139s][info][gc            ] GC(58) Pause Young (Concurrent Start) (G1 Evacuation Pause) 864M->511M(1026M) 16.822ms
[2025-09-10T16:59:36.651+0800][685.139s][info][gc,cpu        ] GC(58) User=0.18s Sys=0.00s Real=0.02s
[2025-09-10T16:59:36.651+0800][685.139s][info][gc            ] GC(59) Concurrent Cycle
[2025-09-10T16:59:36.652+0800][685.139s][info][gc,marking    ] GC(59) Concurrent Clear Claimed Marks
[2025-09-10T16:59:36.653+0800][685.140s][info][gc,marking    ] GC(59) Concurrent Clear Claimed Marks 1.097ms
[2025-09-10T16:59:36.653+0800][685.141s][info][gc,marking    ] GC(59) Concurrent Scan Root Regions
[2025-09-10T16:59:36.661+0800][685.148s][info][gc,marking    ] GC(59) Concurrent Scan Root Regions 7.630ms
[2025-09-10T16:59:36.661+0800][685.149s][info][gc,marking    ] GC(59) Concurrent Mark (685.149s)
[2025-09-10T16:59:36.662+0800][685.149s][info][gc,marking    ] GC(59) Concurrent Mark From Roots
[2025-09-10T16:59:36.662+0800][685.150s][info][gc,task       ] GC(59) Using 4 workers of 4 for marking
[2025-09-10T16:59:36.762+0800][685.249s][info][gc,marking    ] GC(59) Concurrent Mark From Roots 100.167ms
[2025-09-10T16:59:36.762+0800][685.250s][info][gc,marking    ] GC(59) Concurrent Preclean
[2025-09-10T16:59:36.763+0800][685.251s][info][gc,marking    ] GC(59) Concurrent Preclean 0.959ms
[2025-09-10T16:59:36.763+0800][685.251s][info][gc,marking    ] GC(59) Concurrent Mark (685.149s, 685.251s) 102.072ms
[2025-09-10T16:59:36.764+0800][685.251s][info][gc,start      ] GC(59) Pause Remark
[2025-09-10T16:59:36.788+0800][685.276s][info][gc,stringtable] GC(59) Cleaned string and symbol table, strings: 183330 processed, 928 removed, symbols: 485707 processed, 2607 removed
[2025-09-10T16:59:36.791+0800][685.279s][info][gc            ] GC(59) Pause Remark 557M->557M(1026M) 27.366ms
[2025-09-10T16:59:36.791+0800][685.279s][info][gc,cpu        ] GC(59) User=0.27s Sys=0.00s Real=0.03s
[2025-09-10T16:59:36.792+0800][685.279s][info][gc,marking    ] GC(59) Concurrent Rebuild Remembered Sets
[2025-09-10T16:59:36.884+0800][685.372s][info][gc,marking    ] GC(59) Concurrent Rebuild Remembered Sets 92.742ms
[2025-09-10T16:59:36.885+0800][685.373s][info][gc,start      ] GC(59) Pause Cleanup
[2025-09-10T16:59:36.886+0800][685.374s][info][gc            ] GC(59) Pause Cleanup 630M->630M(1026M) 0.806ms
[2025-09-10T16:59:36.886+0800][685.374s][info][gc,cpu        ] GC(59) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T16:59:36.886+0800][685.374s][info][gc,marking    ] GC(59) Concurrent Cleanup for Next Mark
[2025-09-10T16:59:36.888+0800][685.375s][info][gc,marking    ] GC(59) Concurrent Cleanup for Next Mark 1.109ms
[2025-09-10T16:59:36.888+0800][685.376s][info][gc            ] GC(59) Concurrent Cycle 236.584ms
[2025-09-10T16:59:37.157+0800][685.645s][info][gc,start      ] GC(60) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:37.157+0800][685.645s][info][gc,task       ] GC(60) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:37.172+0800][685.660s][info][gc,phases     ] GC(60)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:37.175+0800][685.663s][info][gc,phases     ] GC(60)   Evacuate Collection Set: 13.3ms
[2025-09-10T16:59:37.177+0800][685.665s][info][gc,phases     ] GC(60)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T16:59:37.180+0800][685.668s][info][gc,phases     ] GC(60)   Other: 0.7ms
[2025-09-10T16:59:37.181+0800][685.669s][info][gc,heap       ] GC(60) Eden regions: 352->0(15)
[2025-09-10T16:59:37.182+0800][685.670s][info][gc,heap       ] GC(60) Survivor regions: 36->36(49)
[2025-09-10T16:59:37.183+0800][685.670s][info][gc,heap       ] GC(60) Old regions: 476->489
[2025-09-10T16:59:37.183+0800][685.670s][info][gc,heap       ] GC(60) Humongous regions: 2->2
[2025-09-10T16:59:37.183+0800][685.671s][info][gc,metaspace  ] GC(60) Metaspace: 285077K(311300K)->285077K(311300K) NonClass: 249448K(267240K)->249448K(267240K) Class: 35628K(44060K)->35628K(44060K)
[2025-09-10T16:59:37.183+0800][685.671s][info][gc            ] GC(60) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 863M->524M(1026M) 26.205ms
[2025-09-10T16:59:37.183+0800][685.671s][info][gc,cpu        ] GC(60) User=0.23s Sys=0.00s Real=0.03s
[2025-09-10T16:59:37.195+0800][685.683s][info][gc,start      ] GC(61) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:37.195+0800][685.683s][info][gc,task       ] GC(61) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:37.219+0800][685.707s][info][gc,phases     ] GC(61)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T16:59:37.224+0800][685.712s][info][gc,phases     ] GC(61)   Evacuate Collection Set: 21.9ms
[2025-09-10T16:59:37.225+0800][685.712s][info][gc,phases     ] GC(61)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T16:59:37.225+0800][685.712s][info][gc,phases     ] GC(61)   Other: 0.5ms
[2025-09-10T16:59:37.225+0800][685.713s][info][gc,heap       ] GC(61) Eden regions: 15->0(46)
[2025-09-10T16:59:37.225+0800][685.713s][info][gc,heap       ] GC(61) Survivor regions: 36->5(7)
[2025-09-10T16:59:37.225+0800][685.713s][info][gc,heap       ] GC(61) Old regions: 489->470
[2025-09-10T16:59:37.225+0800][685.713s][info][gc,heap       ] GC(61) Humongous regions: 2->2
[2025-09-10T16:59:37.225+0800][685.713s][info][gc,metaspace  ] GC(61) Metaspace: 285750K(311812K)->285750K(311812K) NonClass: 250031K(267752K)->250031K(267752K) Class: 35719K(44060K)->35719K(44060K)
[2025-09-10T16:59:37.225+0800][685.713s][info][gc            ] GC(61) Pause Young (Mixed) (G1 Evacuation Pause) 539M->475M(1026M) 30.525ms
[2025-09-10T16:59:37.226+0800][685.713s][info][gc,cpu        ] GC(61) User=0.35s Sys=0.00s Real=0.03s
[2025-09-10T16:59:37.267+0800][685.755s][info][gc,start      ] GC(62) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:37.267+0800][685.755s][info][gc,task       ] GC(62) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:37.283+0800][685.771s][info][gc,phases     ] GC(62)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T16:59:37.284+0800][685.772s][info][gc,phases     ] GC(62)   Evacuate Collection Set: 14.3ms
[2025-09-10T16:59:37.284+0800][685.772s][info][gc,phases     ] GC(62)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T16:59:37.284+0800][685.772s][info][gc,phases     ] GC(62)   Other: 0.6ms
[2025-09-10T16:59:37.284+0800][685.772s][info][gc,heap       ] GC(62) Eden regions: 46->0(398)
[2025-09-10T16:59:37.284+0800][685.772s][info][gc,heap       ] GC(62) Survivor regions: 5->5(7)
[2025-09-10T16:59:37.284+0800][685.772s][info][gc,heap       ] GC(62) Old regions: 470->454
[2025-09-10T16:59:37.285+0800][685.772s][info][gc,heap       ] GC(62) Humongous regions: 2->2
[2025-09-10T16:59:37.285+0800][685.772s][info][gc,metaspace  ] GC(62) Metaspace: 286810K(312964K)->286810K(312964K) NonClass: 250951K(268776K)->250951K(268776K) Class: 35858K(44188K)->35858K(44188K)
[2025-09-10T16:59:37.285+0800][685.773s][info][gc            ] GC(62) Pause Young (Mixed) (G1 Evacuation Pause) 521M->458M(1026M) 18.074ms
[2025-09-10T16:59:37.285+0800][685.773s][info][gc,cpu        ] GC(62) User=0.25s Sys=0.01s Real=0.02s
[2025-09-10T16:59:37.974+0800][686.462s][info][gc,start      ] GC(63) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:37.975+0800][686.462s][info][gc,task       ] GC(63) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:37.983+0800][686.471s][info][gc,phases     ] GC(63)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:37.983+0800][686.471s][info][gc,phases     ] GC(63)   Evacuate Collection Set: 6.9ms
[2025-09-10T16:59:37.983+0800][686.471s][info][gc,phases     ] GC(63)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T16:59:37.983+0800][686.471s][info][gc,phases     ] GC(63)   Other: 0.5ms
[2025-09-10T16:59:37.984+0800][686.471s][info][gc,heap       ] GC(63) Eden regions: 398->0(391)
[2025-09-10T16:59:37.984+0800][686.471s][info][gc,heap       ] GC(63) Survivor regions: 5->19(51)
[2025-09-10T16:59:37.984+0800][686.472s][info][gc,heap       ] GC(63) Old regions: 454->454
[2025-09-10T16:59:37.984+0800][686.472s][info][gc,heap       ] GC(63) Humongous regions: 2->2
[2025-09-10T16:59:37.984+0800][686.472s][info][gc,metaspace  ] GC(63) Metaspace: 299228K(326020K)->299228K(326020K) NonClass: 261929K(280040K)->261929K(280040K) Class: 37299K(45980K)->37299K(45980K)
[2025-09-10T16:59:37.984+0800][686.472s][info][gc            ] GC(63) Pause Young (Normal) (G1 Evacuation Pause) 856M->473M(1026M) 9.691ms
[2025-09-10T16:59:37.984+0800][686.472s][info][gc,cpu        ] GC(63) User=0.10s Sys=0.01s Real=0.01s
[2025-09-10T16:59:38.615+0800][687.103s][info][gc,start      ] GC(64) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:38.615+0800][687.103s][info][gc,task       ] GC(64) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:38.629+0800][687.117s][info][gc,phases     ] GC(64)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:38.630+0800][687.118s][info][gc,phases     ] GC(64)   Evacuate Collection Set: 11.7ms
[2025-09-10T16:59:38.631+0800][687.119s][info][gc,phases     ] GC(64)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T16:59:38.631+0800][687.119s][info][gc,phases     ] GC(64)   Other: 0.7ms
[2025-09-10T16:59:38.632+0800][687.120s][info][gc,heap       ] GC(64) Eden regions: 391->0(364)
[2025-09-10T16:59:38.632+0800][687.120s][info][gc,heap       ] GC(64) Survivor regions: 19->45(52)
[2025-09-10T16:59:38.632+0800][687.120s][info][gc,heap       ] GC(64) Old regions: 454->454
[2025-09-10T16:59:38.633+0800][687.120s][info][gc,heap       ] GC(64) Humongous regions: 4->2
[2025-09-10T16:59:38.633+0800][687.121s][info][gc,metaspace  ] GC(64) Metaspace: 311475K(339204K)->311475K(339204K) NonClass: 272919K(291816K)->272919K(291816K) Class: 38555K(47388K)->38555K(47388K)
[2025-09-10T16:59:38.633+0800][687.121s][info][gc            ] GC(64) Pause Young (Normal) (G1 Evacuation Pause) 866M->498M(1026M) 17.869ms
[2025-09-10T16:59:38.633+0800][687.121s][info][gc,cpu        ] GC(64) User=0.22s Sys=0.00s Real=0.02s
[2025-09-10T16:59:39.468+0800][687.956s][info][gc,start      ] GC(65) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T16:59:39.468+0800][687.956s][info][gc,task       ] GC(65) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:39.483+0800][687.971s][info][gc,phases     ] GC(65)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:39.484+0800][687.972s][info][gc,phases     ] GC(65)   Evacuate Collection Set: 11.3ms
[2025-09-10T16:59:39.484+0800][687.972s][info][gc,phases     ] GC(65)   Post Evacuate Collection Set: 2.6ms
[2025-09-10T16:59:39.486+0800][687.974s][info][gc,phases     ] GC(65)   Other: 0.8ms
[2025-09-10T16:59:39.486+0800][687.974s][info][gc,heap       ] GC(65) Eden regions: 364->0(362)
[2025-09-10T16:59:39.487+0800][687.975s][info][gc,heap       ] GC(65) Survivor regions: 45->37(52)
[2025-09-10T16:59:39.490+0800][687.978s][info][gc,heap       ] GC(65) Old regions: 454->472
[2025-09-10T16:59:39.492+0800][687.980s][info][gc,heap       ] GC(65) Humongous regions: 2->2
[2025-09-10T16:59:39.494+0800][687.982s][info][gc,metaspace  ] GC(65) Metaspace: 317718K(345476K)->317718K(345476K) NonClass: 278363K(297192K)->278363K(297192K) Class: 39354K(48284K)->39354K(48284K)
[2025-09-10T16:59:39.494+0800][687.982s][info][gc            ] GC(65) Pause Young (Normal) (G1 Evacuation Pause) 862M->509M(1026M) 26.412ms
[2025-09-10T16:59:39.495+0800][687.982s][info][gc,cpu        ] GC(65) User=0.18s Sys=0.00s Real=0.03s
[2025-09-10T16:59:40.619+0800][689.107s][info][gc,start      ] GC(66) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T16:59:40.620+0800][689.107s][info][gc,task       ] GC(66) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:40.631+0800][689.119s][info][gc,phases     ] GC(66)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T16:59:40.631+0800][689.119s][info][gc,phases     ] GC(66)   Evacuate Collection Set: 9.2ms
[2025-09-10T16:59:40.632+0800][689.119s][info][gc,phases     ] GC(66)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T16:59:40.632+0800][689.120s][info][gc,phases     ] GC(66)   Other: 0.5ms
[2025-09-10T16:59:40.632+0800][689.120s][info][gc,heap       ] GC(66) Eden regions: 362->0(356)
[2025-09-10T16:59:40.632+0800][689.120s][info][gc,heap       ] GC(66) Survivor regions: 37->22(50)
[2025-09-10T16:59:40.632+0800][689.120s][info][gc,heap       ] GC(66) Old regions: 472->498
[2025-09-10T16:59:40.633+0800][689.120s][info][gc,heap       ] GC(66) Humongous regions: 2->2
[2025-09-10T16:59:40.633+0800][689.120s][info][gc,metaspace  ] GC(66) Metaspace: 320694K(348420K)->320694K(348420K) NonClass: 280936K(299752K)->280936K(299752K) Class: 39757K(48668K)->39757K(48668K)
[2025-09-10T16:59:40.633+0800][689.121s][info][gc            ] GC(66) Pause Young (Concurrent Start) (G1 Evacuation Pause) 871M->519M(1026M) 13.409ms
[2025-09-10T16:59:40.633+0800][689.121s][info][gc,cpu        ] GC(66) User=0.13s Sys=0.00s Real=0.01s
[2025-09-10T16:59:40.633+0800][689.121s][info][gc            ] GC(67) Concurrent Cycle
[2025-09-10T16:59:40.633+0800][689.121s][info][gc,marking    ] GC(67) Concurrent Clear Claimed Marks
[2025-09-10T16:59:40.634+0800][689.122s][info][gc,marking    ] GC(67) Concurrent Clear Claimed Marks 0.721ms
[2025-09-10T16:59:40.634+0800][689.122s][info][gc,marking    ] GC(67) Concurrent Scan Root Regions
[2025-09-10T16:59:40.638+0800][689.125s][info][gc,marking    ] GC(67) Concurrent Scan Root Regions 3.343ms
[2025-09-10T16:59:40.638+0800][689.126s][info][gc,marking    ] GC(67) Concurrent Mark (689.126s)
[2025-09-10T16:59:40.638+0800][689.126s][info][gc,marking    ] GC(67) Concurrent Mark From Roots
[2025-09-10T16:59:40.638+0800][689.126s][info][gc,task       ] GC(67) Using 4 workers of 4 for marking
[2025-09-10T16:59:40.749+0800][689.236s][info][gc,marking    ] GC(67) Concurrent Mark From Roots 110.712ms
[2025-09-10T16:59:40.749+0800][689.237s][info][gc,marking    ] GC(67) Concurrent Preclean
[2025-09-10T16:59:40.750+0800][689.238s][info][gc,marking    ] GC(67) Concurrent Preclean 1.322ms
[2025-09-10T16:59:40.751+0800][689.238s][info][gc,marking    ] GC(67) Concurrent Mark (689.126s, 689.238s) 112.799ms
[2025-09-10T16:59:40.753+0800][689.240s][info][gc,start      ] GC(67) Pause Remark
[2025-09-10T16:59:40.768+0800][689.256s][info][gc,stringtable] GC(67) Cleaned string and symbol table, strings: 183913 processed, 26 removed, symbols: 488431 processed, 4082 removed
[2025-09-10T16:59:40.772+0800][689.260s][info][gc            ] GC(67) Pause Remark 536M->536M(1026M) 19.089ms
[2025-09-10T16:59:40.776+0800][689.263s][info][gc,cpu        ] GC(67) User=0.17s Sys=0.00s Real=0.02s
[2025-09-10T16:59:40.778+0800][689.266s][info][gc,marking    ] GC(67) Concurrent Rebuild Remembered Sets
[2025-09-10T16:59:40.883+0800][689.371s][info][gc,marking    ] GC(67) Concurrent Rebuild Remembered Sets 105.122ms
[2025-09-10T16:59:40.885+0800][689.373s][info][gc,start      ] GC(67) Pause Cleanup
[2025-09-10T16:59:40.887+0800][689.375s][info][gc            ] GC(67) Pause Cleanup 538M->538M(1026M) 2.120ms
[2025-09-10T16:59:40.890+0800][689.378s][info][gc,cpu        ] GC(67) User=0.00s Sys=0.00s Real=0.01s
[2025-09-10T16:59:40.893+0800][689.380s][info][gc,marking    ] GC(67) Concurrent Cleanup for Next Mark
[2025-09-10T16:59:40.894+0800][689.382s][info][gc,marking    ] GC(67) Concurrent Cleanup for Next Mark 1.298ms
[2025-09-10T16:59:40.894+0800][689.382s][info][gc            ] GC(67) Concurrent Cycle 261.159ms
[2025-09-10T16:59:42.745+0800][691.233s][info][gc,start      ] GC(68) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:42.746+0800][691.233s][info][gc,task       ] GC(68) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:42.754+0800][691.242s][info][gc,phases     ] GC(68)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T16:59:42.754+0800][691.242s][info][gc,phases     ] GC(68)   Evacuate Collection Set: 6.9ms
[2025-09-10T16:59:42.754+0800][691.242s][info][gc,phases     ] GC(68)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T16:59:42.755+0800][691.242s][info][gc,phases     ] GC(68)   Other: 0.6ms
[2025-09-10T16:59:42.755+0800][691.243s][info][gc,heap       ] GC(68) Eden regions: 356->0(25)
[2025-09-10T16:59:42.755+0800][691.243s][info][gc,heap       ] GC(68) Survivor regions: 22->26(48)
[2025-09-10T16:59:42.755+0800][691.243s][info][gc,heap       ] GC(68) Old regions: 498->498
[2025-09-10T16:59:42.755+0800][691.243s][info][gc,heap       ] GC(68) Humongous regions: 2->2
[2025-09-10T16:59:42.756+0800][691.243s][info][gc,metaspace  ] GC(68) Metaspace: 324254K(352004K)->324254K(352004K) NonClass: 284032K(302824K)->284032K(302824K) Class: 40222K(49180K)->40222K(49180K)
[2025-09-10T16:59:42.756+0800][691.243s][info][gc            ] GC(68) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 875M->523M(1026M) 10.260ms
[2025-09-10T16:59:42.756+0800][691.244s][info][gc,cpu        ] GC(68) User=0.09s Sys=0.01s Real=0.01s
[2025-09-10T16:59:42.884+0800][691.372s][info][gc,start      ] GC(69) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T16:59:42.884+0800][691.372s][info][gc,task       ] GC(69) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:42.906+0800][691.394s][info][gc,phases     ] GC(69)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T16:59:42.907+0800][691.394s][info][gc,phases     ] GC(69)   Evacuate Collection Set: 19.0ms
[2025-09-10T16:59:42.907+0800][691.395s][info][gc,phases     ] GC(69)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T16:59:42.907+0800][691.395s][info][gc,phases     ] GC(69)   Other: 0.6ms
[2025-09-10T16:59:42.908+0800][691.395s][info][gc,heap       ] GC(69) Eden regions: 25->0(379)
[2025-09-10T16:59:42.908+0800][691.396s][info][gc,heap       ] GC(69) Survivor regions: 26->4(7)
[2025-09-10T16:59:42.908+0800][691.396s][info][gc,heap       ] GC(69) Old regions: 498->496
[2025-09-10T16:59:42.908+0800][691.396s][info][gc,heap       ] GC(69) Humongous regions: 2->2
[2025-09-10T16:59:42.908+0800][691.396s][info][gc,metaspace  ] GC(69) Metaspace: 324459K(352260K)->324459K(352260K) NonClass: 284200K(303080K)->284200K(303080K) Class: 40258K(49180K)->40258K(49180K)
[2025-09-10T16:59:42.909+0800][691.396s][info][gc            ] GC(69) Pause Young (Mixed) (G1 Evacuation Pause) 548M->500M(1026M) 24.708ms
[2025-09-10T16:59:42.909+0800][691.396s][info][gc,cpu        ] GC(69) User=0.29s Sys=0.00s Real=0.02s
[2025-09-10T16:59:52.211+0800][700.699s][info][gc,start      ] GC(70) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T16:59:52.213+0800][700.701s][info][gc,task       ] GC(70) Using 15 workers of 15 for evacuation
[2025-09-10T16:59:52.224+0800][700.712s][info][gc,phases     ] GC(70)   Pre Evacuate Collection Set: 0.6ms
[2025-09-10T16:59:52.225+0800][700.712s][info][gc,phases     ] GC(70)   Evacuate Collection Set: 6.1ms
[2025-09-10T16:59:52.227+0800][700.714s][info][gc,phases     ] GC(70)   Post Evacuate Collection Set: 2.1ms
[2025-09-10T16:59:52.227+0800][700.715s][info][gc,phases     ] GC(70)   Other: 4.2ms
[2025-09-10T16:59:52.228+0800][700.716s][info][gc,heap       ] GC(70) Eden regions: 379->0(377)
[2025-09-10T16:59:52.228+0800][700.716s][info][gc,heap       ] GC(70) Survivor regions: 4->10(48)
[2025-09-10T16:59:52.228+0800][700.716s][info][gc,heap       ] GC(70) Old regions: 496->496
[2025-09-10T16:59:52.229+0800][700.716s][info][gc,heap       ] GC(70) Humongous regions: 2->1
[2025-09-10T16:59:52.229+0800][700.717s][info][gc,metaspace  ] GC(70) Metaspace: 326014K(353668K)->326014K(353668K) NonClass: 285555K(304360K)->285555K(304360K) Class: 40459K(49308K)->40459K(49308K)
[2025-09-10T16:59:52.229+0800][700.717s][info][gc            ] GC(70) Pause Young (Concurrent Start) (G1 Evacuation Pause) 879M->504M(1026M) 17.635ms
[2025-09-10T16:59:52.229+0800][700.717s][info][gc,cpu        ] GC(70) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T16:59:52.230+0800][700.717s][info][gc            ] GC(71) Concurrent Cycle
[2025-09-10T16:59:52.230+0800][700.718s][info][gc,marking    ] GC(71) Concurrent Clear Claimed Marks
[2025-09-10T16:59:52.231+0800][700.718s][info][gc,marking    ] GC(71) Concurrent Clear Claimed Marks 0.890ms
[2025-09-10T16:59:52.231+0800][700.719s][info][gc,marking    ] GC(71) Concurrent Scan Root Regions
[2025-09-10T16:59:52.234+0800][700.722s][info][gc,marking    ] GC(71) Concurrent Scan Root Regions 2.805ms
[2025-09-10T16:59:52.234+0800][700.722s][info][gc,marking    ] GC(71) Concurrent Mark (700.722s)
[2025-09-10T16:59:52.234+0800][700.722s][info][gc,marking    ] GC(71) Concurrent Mark From Roots
[2025-09-10T16:59:52.234+0800][700.722s][info][gc,task       ] GC(71) Using 4 workers of 4 for marking
[2025-09-10T16:59:52.343+0800][700.831s][info][gc,marking    ] GC(71) Concurrent Mark From Roots 109.273ms
[2025-09-10T16:59:52.344+0800][700.831s][info][gc,marking    ] GC(71) Concurrent Preclean
[2025-09-10T16:59:52.344+0800][700.832s][info][gc,marking    ] GC(71) Concurrent Preclean 0.811ms
[2025-09-10T16:59:52.345+0800][700.833s][info][gc,marking    ] GC(71) Concurrent Mark (700.722s, 700.833s) 110.858ms
[2025-09-10T16:59:52.345+0800][700.833s][info][gc,start      ] GC(71) Pause Remark
[2025-09-10T16:59:52.355+0800][700.843s][info][gc,stringtable] GC(71) Cleaned string and symbol table, strings: 194989 processed, 39 removed, symbols: 484856 processed, 374 removed
[2025-09-10T16:59:52.356+0800][700.843s][info][gc            ] GC(71) Pause Remark 506M->506M(1026M) 10.340ms
[2025-09-10T16:59:52.356+0800][700.844s][info][gc,cpu        ] GC(71) User=0.12s Sys=0.01s Real=0.01s
[2025-09-10T16:59:52.356+0800][700.844s][info][gc,marking    ] GC(71) Concurrent Rebuild Remembered Sets
[2025-09-10T16:59:52.444+0800][700.932s][info][gc,marking    ] GC(71) Concurrent Rebuild Remembered Sets 87.773ms
[2025-09-10T16:59:52.444+0800][700.932s][info][gc,start      ] GC(71) Pause Cleanup
[2025-09-10T16:59:52.445+0800][700.933s][info][gc            ] GC(71) Pause Cleanup 506M->506M(1026M) 0.709ms
[2025-09-10T16:59:52.445+0800][700.933s][info][gc,cpu        ] GC(71) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T16:59:52.445+0800][700.933s][info][gc,marking    ] GC(71) Concurrent Cleanup for Next Mark
[2025-09-10T16:59:52.446+0800][700.934s][info][gc,marking    ] GC(71) Concurrent Cleanup for Next Mark 1.039ms
[2025-09-10T16:59:52.447+0800][700.934s][info][gc            ] GC(71) Concurrent Cycle 217.171ms
[2025-09-10T17:01:36.709+0800][805.203s][info][gc,start      ] GC(72) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:01:36.710+0800][805.203s][info][gc,task       ] GC(72) Using 15 workers of 15 for evacuation
[2025-09-10T17:01:36.716+0800][805.210s][info][gc,phases     ] GC(72)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:01:36.717+0800][805.210s][info][gc,phases     ] GC(72)   Evacuate Collection Set: 4.1ms
[2025-09-10T17:01:36.718+0800][805.212s][info][gc,phases     ] GC(72)   Post Evacuate Collection Set: 1.7ms
[2025-09-10T17:01:36.718+0800][805.212s][info][gc,phases     ] GC(72)   Other: 1.1ms
[2025-09-10T17:01:36.718+0800][805.212s][info][gc,heap       ] GC(72) Eden regions: 377->0(41)
[2025-09-10T17:01:36.719+0800][805.212s][info][gc,heap       ] GC(72) Survivor regions: 10->10(49)
[2025-09-10T17:01:36.719+0800][805.213s][info][gc,heap       ] GC(72) Old regions: 496->496
[2025-09-10T17:01:36.719+0800][805.213s][info][gc,heap       ] GC(72) Humongous regions: 2->1
[2025-09-10T17:01:36.719+0800][805.213s][info][gc,metaspace  ] GC(72) Metaspace: 326467K(354180K)->326467K(354180K) NonClass: 285962K(304872K)->285962K(304872K) Class: 40505K(49308K)->40505K(49308K)
[2025-09-10T17:01:36.720+0800][805.213s][info][gc            ] GC(72) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 882M->505M(1026M) 10.325ms
[2025-09-10T17:01:36.720+0800][805.214s][info][gc,cpu        ] GC(72) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:01:38.791+0800][807.285s][info][gc,start      ] GC(73) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:01:38.792+0800][807.286s][info][gc,task       ] GC(73) Using 15 workers of 15 for evacuation
[2025-09-10T17:01:38.811+0800][807.305s][info][gc,phases     ] GC(73)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T17:01:38.813+0800][807.306s][info][gc,phases     ] GC(73)   Evacuate Collection Set: 11.1ms
[2025-09-10T17:01:38.814+0800][807.308s][info][gc,phases     ] GC(73)   Post Evacuate Collection Set: 3.0ms
[2025-09-10T17:01:38.814+0800][807.308s][info][gc,phases     ] GC(73)   Other: 6.0ms
[2025-09-10T17:01:38.814+0800][807.308s][info][gc,heap       ] GC(73) Eden regions: 41->0(385)
[2025-09-10T17:01:38.815+0800][807.308s][info][gc,heap       ] GC(73) Survivor regions: 10->7(7)
[2025-09-10T17:01:38.815+0800][807.309s][info][gc,heap       ] GC(73) Old regions: 496->489
[2025-09-10T17:01:38.815+0800][807.309s][info][gc,heap       ] GC(73) Humongous regions: 2->2
[2025-09-10T17:01:38.816+0800][807.309s][info][gc,metaspace  ] GC(73) Metaspace: 327178K(355076K)->327178K(355076K) NonClass: 286569K(305640K)->286569K(305640K) Class: 40608K(49436K)->40608K(49436K)
[2025-09-10T17:01:38.817+0800][807.311s][info][gc            ] GC(73) Pause Young (Mixed) (G1 Evacuation Pause) 547M->495M(1026M) 26.431ms
[2025-09-10T17:01:38.817+0800][807.311s][info][gc,cpu        ] GC(73) User=0.19s Sys=0.01s Real=0.03s
[2025-09-10T17:01:50.377+0800][818.871s][info][gc,start      ] GC(74) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:01:50.379+0800][818.872s][info][gc,task       ] GC(74) Using 15 workers of 15 for evacuation
[2025-09-10T17:01:50.385+0800][818.879s][info][gc,phases     ] GC(74)   Pre Evacuate Collection Set: 0.7ms
[2025-09-10T17:01:50.386+0800][818.879s][info][gc,phases     ] GC(74)   Evacuate Collection Set: 4.5ms
[2025-09-10T17:01:50.386+0800][818.880s][info][gc,phases     ] GC(74)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:01:50.386+0800][818.880s][info][gc,phases     ] GC(74)   Other: 2.1ms
[2025-09-10T17:01:50.386+0800][818.880s][info][gc,heap       ] GC(74) Eden regions: 134->0(376)
[2025-09-10T17:01:50.387+0800][818.880s][info][gc,heap       ] GC(74) Survivor regions: 7->5(49)
[2025-09-10T17:01:50.387+0800][818.881s][info][gc,heap       ] GC(74) Old regions: 489->489
[2025-09-10T17:01:50.388+0800][818.881s][info][gc,heap       ] GC(74) Humongous regions: 2->1
[2025-09-10T17:01:50.388+0800][818.882s][info][gc,metaspace  ] GC(74) Metaspace: 328269K(355972K)->328269K(355972K) NonClass: 287524K(306408K)->287524K(306408K) Class: 40745K(49564K)->40745K(49564K)
[2025-09-10T17:01:50.390+0800][818.884s][info][gc            ] GC(74) Pause Young (Concurrent Start) (G1 Humongous Allocation) 629M->492M(1026M) 13.130ms
[2025-09-10T17:01:50.391+0800][818.884s][info][gc,cpu        ] GC(74) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:01:50.395+0800][818.889s][info][gc            ] GC(75) Concurrent Cycle
[2025-09-10T17:01:50.400+0800][818.894s][info][gc,marking    ] GC(75) Concurrent Clear Claimed Marks
[2025-09-10T17:01:50.406+0800][818.900s][info][gc,marking    ] GC(75) Concurrent Clear Claimed Marks 5.620ms
[2025-09-10T17:01:50.407+0800][818.901s][info][gc,marking    ] GC(75) Concurrent Scan Root Regions
[2025-09-10T17:01:50.409+0800][818.903s][info][gc,marking    ] GC(75) Concurrent Scan Root Regions 2.039ms
[2025-09-10T17:01:50.411+0800][818.905s][info][gc,marking    ] GC(75) Concurrent Mark (818.905s)
[2025-09-10T17:01:50.412+0800][818.906s][info][gc,marking    ] GC(75) Concurrent Mark From Roots
[2025-09-10T17:01:50.413+0800][818.907s][info][gc,task       ] GC(75) Using 4 workers of 4 for marking
[2025-09-10T17:01:50.528+0800][819.021s][info][gc,marking    ] GC(75) Concurrent Mark From Roots 115.652ms
[2025-09-10T17:01:50.528+0800][819.022s][info][gc,marking    ] GC(75) Concurrent Preclean
[2025-09-10T17:01:50.529+0800][819.023s][info][gc,marking    ] GC(75) Concurrent Preclean 1.063ms
[2025-09-10T17:01:50.529+0800][819.023s][info][gc,marking    ] GC(75) Concurrent Mark (818.905s, 819.023s) 118.152ms
[2025-09-10T17:01:50.530+0800][819.024s][info][gc,start      ] GC(75) Pause Remark
[2025-09-10T17:01:50.561+0800][819.055s][info][gc,stringtable] GC(75) Cleaned string and symbol table, strings: 195025 processed, 24 removed, symbols: 484674 processed, 137 removed
[2025-09-10T17:01:50.561+0800][819.055s][info][gc            ] GC(75) Pause Remark 544M->544M(1026M) 31.351ms
[2025-09-10T17:01:50.562+0800][819.056s][info][gc,cpu        ] GC(75) User=0.36s Sys=0.00s Real=0.03s
[2025-09-10T17:01:50.562+0800][819.056s][info][gc,marking    ] GC(75) Concurrent Rebuild Remembered Sets
[2025-09-10T17:01:50.656+0800][819.149s][info][gc,marking    ] GC(75) Concurrent Rebuild Remembered Sets 93.533ms
[2025-09-10T17:01:50.656+0800][819.150s][info][gc,start      ] GC(75) Pause Cleanup
[2025-09-10T17:01:50.657+0800][819.150s][info][gc            ] GC(75) Pause Cleanup 581M->581M(1026M) 0.680ms
[2025-09-10T17:01:50.657+0800][819.151s][info][gc,cpu        ] GC(75) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:01:50.657+0800][819.151s][info][gc,marking    ] GC(75) Concurrent Cleanup for Next Mark
[2025-09-10T17:01:50.658+0800][819.152s][info][gc,marking    ] GC(75) Concurrent Cleanup for Next Mark 0.851ms
[2025-09-10T17:01:50.658+0800][819.152s][info][gc            ] GC(75) Concurrent Cycle 262.559ms
[2025-09-10T17:02:16.155+0800][844.651s][info][gc,start      ] GC(76) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:02:16.155+0800][844.651s][info][gc,task       ] GC(76) Using 15 workers of 15 for evacuation
[2025-09-10T17:02:16.161+0800][844.656s][info][gc,phases     ] GC(76)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:02:16.161+0800][844.656s][info][gc,phases     ] GC(76)   Evacuate Collection Set: 3.6ms
[2025-09-10T17:02:16.161+0800][844.656s][info][gc,phases     ] GC(76)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:02:16.161+0800][844.656s][info][gc,phases     ] GC(76)   Other: 0.8ms
[2025-09-10T17:02:16.161+0800][844.657s][info][gc,heap       ] GC(76) Eden regions: 376->0(37)
[2025-09-10T17:02:16.161+0800][844.657s][info][gc,heap       ] GC(76) Survivor regions: 5->14(48)
[2025-09-10T17:02:16.161+0800][844.657s][info][gc,heap       ] GC(76) Old regions: 489->489
[2025-09-10T17:02:16.162+0800][844.657s][info][gc,heap       ] GC(76) Humongous regions: 3->2
[2025-09-10T17:02:16.162+0800][844.657s][info][gc,metaspace  ] GC(76) Metaspace: 329882K(358020K)->329882K(358020K) NonClass: 288907K(308200K)->288907K(308200K) Class: 40975K(49820K)->40975K(49820K)
[2025-09-10T17:02:16.162+0800][844.657s][info][gc            ] GC(76) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 870M->502M(1026M) 6.626ms
[2025-09-10T17:02:16.162+0800][844.657s][info][gc,cpu        ] GC(76) User=0.05s Sys=0.00s Real=0.00s
[2025-09-10T17:02:16.228+0800][844.724s][info][gc,start      ] GC(77) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:02:16.229+0800][844.724s][info][gc,task       ] GC(77) Using 15 workers of 15 for evacuation
[2025-09-10T17:02:16.234+0800][844.730s][info][gc,phases     ] GC(77)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T17:02:16.235+0800][844.730s][info][gc,phases     ] GC(77)   Evacuate Collection Set: 4.7ms
[2025-09-10T17:02:16.235+0800][844.730s][info][gc,phases     ] GC(77)   Post Evacuate Collection Set: 0.6ms
[2025-09-10T17:02:16.235+0800][844.730s][info][gc,phases     ] GC(77)   Other: 0.6ms
[2025-09-10T17:02:16.235+0800][844.730s][info][gc,heap       ] GC(77) Eden regions: 37->0(373)
[2025-09-10T17:02:16.235+0800][844.730s][info][gc,heap       ] GC(77) Survivor regions: 14->2(7)
[2025-09-10T17:02:16.235+0800][844.731s][info][gc,heap       ] GC(77) Old regions: 489->499
[2025-09-10T17:02:16.235+0800][844.731s][info][gc,heap       ] GC(77) Humongous regions: 2->2
[2025-09-10T17:02:16.235+0800][844.731s][info][gc,metaspace  ] GC(77) Metaspace: 329888K(358020K)->329888K(358020K) NonClass: 288912K(308200K)->288912K(308200K) Class: 40975K(49820K)->40975K(49820K)
[2025-09-10T17:02:16.236+0800][844.731s][info][gc            ] GC(77) Pause Young (Mixed) (G1 Evacuation Pause) 539M->500M(1026M) 7.228ms
[2025-09-10T17:02:16.236+0800][844.731s][info][gc,cpu        ] GC(77) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:02:36.255+0800][864.751s][info][gc,start      ] GC(78) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:02:36.256+0800][864.753s][info][gc,task       ] GC(78) Using 15 workers of 15 for evacuation
[2025-09-10T17:02:36.263+0800][864.760s][info][gc,phases     ] GC(78)   Pre Evacuate Collection Set: 1.1ms
[2025-09-10T17:02:36.264+0800][864.761s][info][gc,phases     ] GC(78)   Evacuate Collection Set: 4.1ms
[2025-09-10T17:02:36.265+0800][864.762s][info][gc,phases     ] GC(78)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:02:36.266+0800][864.762s][info][gc,phases     ] GC(78)   Other: 2.1ms
[2025-09-10T17:02:36.266+0800][864.762s][info][gc,heap       ] GC(78) Eden regions: 212->0(374)
[2025-09-10T17:02:36.266+0800][864.763s][info][gc,heap       ] GC(78) Survivor regions: 2->6(47)
[2025-09-10T17:02:36.267+0800][864.764s][info][gc,heap       ] GC(78) Old regions: 499->499
[2025-09-10T17:02:36.268+0800][864.764s][info][gc,heap       ] GC(78) Humongous regions: 2->1
[2025-09-10T17:02:36.268+0800][864.764s][info][gc,metaspace  ] GC(78) Metaspace: 330347K(358660K)->330347K(358660K) NonClass: 289315K(308712K)->289315K(308712K) Class: 41032K(49948K)->41032K(49948K)
[2025-09-10T17:02:36.268+0800][864.765s][info][gc            ] GC(78) Pause Young (Concurrent Start) (G1 Humongous Allocation) 712M->503M(1026M) 13.502ms
[2025-09-10T17:02:36.268+0800][864.765s][info][gc,cpu        ] GC(78) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:02:36.269+0800][864.765s][info][gc            ] GC(79) Concurrent Cycle
[2025-09-10T17:02:36.269+0800][864.765s][info][gc,marking    ] GC(79) Concurrent Clear Claimed Marks
[2025-09-10T17:02:36.270+0800][864.766s][info][gc,marking    ] GC(79) Concurrent Clear Claimed Marks 0.870ms
[2025-09-10T17:02:36.270+0800][864.767s][info][gc,marking    ] GC(79) Concurrent Scan Root Regions
[2025-09-10T17:02:36.272+0800][864.769s][info][gc,marking    ] GC(79) Concurrent Scan Root Regions 2.519ms
[2025-09-10T17:02:36.273+0800][864.769s][info][gc,marking    ] GC(79) Concurrent Mark (864.769s)
[2025-09-10T17:02:36.273+0800][864.769s][info][gc,marking    ] GC(79) Concurrent Mark From Roots
[2025-09-10T17:02:36.273+0800][864.770s][info][gc,task       ] GC(79) Using 4 workers of 4 for marking
[2025-09-10T17:02:36.375+0800][864.872s][info][gc,marking    ] GC(79) Concurrent Mark From Roots 102.231ms
[2025-09-10T17:02:36.375+0800][864.872s][info][gc,marking    ] GC(79) Concurrent Preclean
[2025-09-10T17:02:36.377+0800][864.873s][info][gc,marking    ] GC(79) Concurrent Preclean 1.501ms
[2025-09-10T17:02:36.377+0800][864.874s][info][gc,marking    ] GC(79) Concurrent Mark (864.769s, 864.874s) 104.398ms
[2025-09-10T17:02:36.378+0800][864.874s][info][gc,start      ] GC(79) Pause Remark
[2025-09-10T17:02:36.388+0800][864.884s][info][gc,stringtable] GC(79) Cleaned string and symbol table, strings: 195238 processed, 100 removed, symbols: 485485 processed, 77 removed
[2025-09-10T17:02:36.388+0800][864.885s][info][gc            ] GC(79) Pause Remark 513M->513M(1026M) 10.554ms
[2025-09-10T17:02:36.388+0800][864.885s][info][gc,cpu        ] GC(79) User=0.13s Sys=0.01s Real=0.01s
[2025-09-10T17:02:36.389+0800][864.885s][info][gc,marking    ] GC(79) Concurrent Rebuild Remembered Sets
[2025-09-10T17:02:36.490+0800][864.987s][info][gc,marking    ] GC(79) Concurrent Rebuild Remembered Sets 101.408ms
[2025-09-10T17:02:36.491+0800][864.987s][info][gc,start      ] GC(79) Pause Cleanup
[2025-09-10T17:02:36.491+0800][864.988s][info][gc            ] GC(79) Pause Cleanup 513M->513M(1026M) 0.672ms
[2025-09-10T17:02:36.492+0800][864.988s][info][gc,cpu        ] GC(79) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:02:36.492+0800][864.988s][info][gc,marking    ] GC(79) Concurrent Cleanup for Next Mark
[2025-09-10T17:02:36.493+0800][864.989s][info][gc,marking    ] GC(79) Concurrent Cleanup for Next Mark 0.956ms
[2025-09-10T17:02:36.493+0800][864.990s][info][gc            ] GC(79) Concurrent Cycle 224.533ms
[2025-09-10T17:02:56.099+0800][884.596s][info][gc,start      ] GC(80) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:02:56.100+0800][884.596s][info][gc,task       ] GC(80) Using 15 workers of 15 for evacuation
[2025-09-10T17:02:56.107+0800][884.603s][info][gc,phases     ] GC(80)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:02:56.107+0800][884.604s][info][gc,phases     ] GC(80)   Evacuate Collection Set: 5.2ms
[2025-09-10T17:02:56.107+0800][884.604s][info][gc,phases     ] GC(80)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:02:56.107+0800][884.604s][info][gc,phases     ] GC(80)   Other: 0.8ms
[2025-09-10T17:02:56.108+0800][884.604s][info][gc,heap       ] GC(80) Eden regions: 374->0(32)
[2025-09-10T17:02:56.108+0800][884.604s][info][gc,heap       ] GC(80) Survivor regions: 6->19(48)
[2025-09-10T17:02:56.108+0800][884.605s][info][gc,heap       ] GC(80) Old regions: 499->499
[2025-09-10T17:02:56.108+0800][884.605s][info][gc,heap       ] GC(80) Humongous regions: 4->2
[2025-09-10T17:02:56.109+0800][884.605s][info][gc,metaspace  ] GC(80) Metaspace: 337393K(366084K)->337393K(366084K) NonClass: 295435K(315112K)->295435K(315112K) Class: 41957K(50972K)->41957K(50972K)
[2025-09-10T17:02:56.109+0800][884.605s][info][gc            ] GC(80) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 880M->517M(1026M) 9.548ms
[2025-09-10T17:02:56.109+0800][884.606s][info][gc,cpu        ] GC(80) User=0.09s Sys=0.01s Real=0.01s
[2025-09-10T17:02:56.144+0800][884.641s][info][gc,start      ] GC(81) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:02:56.145+0800][884.641s][info][gc,task       ] GC(81) Using 15 workers of 15 for evacuation
[2025-09-10T17:02:56.157+0800][884.654s][info][gc,phases     ] GC(81)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:02:56.157+0800][884.654s][info][gc,phases     ] GC(81)   Evacuate Collection Set: 10.4ms
[2025-09-10T17:02:56.158+0800][884.654s][info][gc,phases     ] GC(81)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:02:56.158+0800][884.654s][info][gc,phases     ] GC(81)   Other: 0.9ms
[2025-09-10T17:02:56.158+0800][884.655s][info][gc,heap       ] GC(81) Eden regions: 32->0(373)
[2025-09-10T17:02:56.158+0800][884.655s][info][gc,heap       ] GC(81) Survivor regions: 19->2(7)
[2025-09-10T17:02:56.159+0800][884.655s][info][gc,heap       ] GC(81) Old regions: 499->501
[2025-09-10T17:02:56.159+0800][884.655s][info][gc,heap       ] GC(81) Humongous regions: 2->2
[2025-09-10T17:02:56.160+0800][884.656s][info][gc,metaspace  ] GC(81) Metaspace: 337441K(366084K)->337441K(366084K) NonClass: 295482K(315112K)->295482K(315112K) Class: 41959K(50972K)->41959K(50972K)
[2025-09-10T17:02:56.160+0800][884.657s][info][gc            ] GC(81) Pause Young (Mixed) (G1 Evacuation Pause) 549M->502M(1026M) 15.580ms
[2025-09-10T17:02:56.160+0800][884.657s][info][gc,cpu        ] GC(81) User=0.17s Sys=0.00s Real=0.01s
[2025-09-10T17:03:34.171+0800][922.670s][info][gc,start      ] GC(82) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:03:34.171+0800][922.671s][info][gc,task       ] GC(82) Using 15 workers of 15 for evacuation
[2025-09-10T17:03:34.177+0800][922.677s][info][gc,phases     ] GC(82)   Pre Evacuate Collection Set: 1.0ms
[2025-09-10T17:03:34.178+0800][922.677s][info][gc,phases     ] GC(82)   Evacuate Collection Set: 3.8ms
[2025-09-10T17:03:34.178+0800][922.677s][info][gc,phases     ] GC(82)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:03:34.178+0800][922.678s][info][gc,phases     ] GC(82)   Other: 0.9ms
[2025-09-10T17:03:34.178+0800][922.678s][info][gc,heap       ] GC(82) Eden regions: 100->0(343)
[2025-09-10T17:03:34.178+0800][922.678s][info][gc,heap       ] GC(82) Survivor regions: 2->4(47)
[2025-09-10T17:03:34.179+0800][922.678s][info][gc,heap       ] GC(82) Old regions: 501->501
[2025-09-10T17:03:34.179+0800][922.678s][info][gc,heap       ] GC(82) Humongous regions: 2->1
[2025-09-10T17:03:34.179+0800][922.679s][info][gc,metaspace  ] GC(82) Metaspace: 338075K(366980K)->338075K(366980K) NonClass: 296039K(315880K)->296039K(315880K) Class: 42035K(51100K)->42035K(51100K)
[2025-09-10T17:03:34.179+0800][922.679s][info][gc            ] GC(82) Pause Young (Concurrent Start) (G1 Humongous Allocation) 601M->503M(1026M) 8.491ms
[2025-09-10T17:03:34.179+0800][922.679s][info][gc,cpu        ] GC(82) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:03:34.179+0800][922.679s][info][gc            ] GC(83) Concurrent Cycle
[2025-09-10T17:03:34.180+0800][922.679s][info][gc,marking    ] GC(83) Concurrent Clear Claimed Marks
[2025-09-10T17:03:34.180+0800][922.680s][info][gc,marking    ] GC(83) Concurrent Clear Claimed Marks 0.809ms
[2025-09-10T17:03:34.181+0800][922.680s][info][gc,marking    ] GC(83) Concurrent Scan Root Regions
[2025-09-10T17:03:34.183+0800][922.682s][info][gc,marking    ] GC(83) Concurrent Scan Root Regions 2.282ms
[2025-09-10T17:03:34.183+0800][922.683s][info][gc,marking    ] GC(83) Concurrent Mark (922.683s)
[2025-09-10T17:03:34.183+0800][922.683s][info][gc,marking    ] GC(83) Concurrent Mark From Roots
[2025-09-10T17:03:34.183+0800][922.683s][info][gc,task       ] GC(83) Using 4 workers of 4 for marking
[2025-09-10T17:03:34.300+0800][922.799s][info][gc,marking    ] GC(83) Concurrent Mark From Roots 116.204ms
[2025-09-10T17:03:34.300+0800][922.799s][info][gc,marking    ] GC(83) Concurrent Preclean
[2025-09-10T17:03:34.301+0800][922.800s][info][gc,marking    ] GC(83) Concurrent Preclean 0.746ms
[2025-09-10T17:03:34.301+0800][922.800s][info][gc,marking    ] GC(83) Concurrent Mark (922.683s, 922.800s) 117.595ms
[2025-09-10T17:03:34.301+0800][922.801s][info][gc,start      ] GC(83) Pause Remark
[2025-09-10T17:03:34.334+0800][922.834s][info][gc,stringtable] GC(83) Cleaned string and symbol table, strings: 198087 processed, 172 removed, symbols: 496243 processed, 92 removed
[2025-09-10T17:03:34.335+0800][922.835s][info][gc            ] GC(83) Pause Remark 519M->519M(1026M) 34.006ms
[2025-09-10T17:03:34.335+0800][922.835s][info][gc,cpu        ] GC(83) User=0.38s Sys=0.00s Real=0.03s
[2025-09-10T17:03:34.336+0800][922.835s][info][gc,marking    ] GC(83) Concurrent Rebuild Remembered Sets
[2025-09-10T17:03:34.440+0800][922.939s][info][gc,marking    ] GC(83) Concurrent Rebuild Remembered Sets 104.201ms
[2025-09-10T17:03:34.442+0800][922.941s][info][gc,start      ] GC(83) Pause Cleanup
[2025-09-10T17:03:34.445+0800][922.945s][info][gc            ] GC(83) Pause Cleanup 519M->519M(1026M) 3.392ms
[2025-09-10T17:03:34.448+0800][922.947s][info][gc,cpu        ] GC(83) User=0.01s Sys=0.01s Real=0.01s
[2025-09-10T17:03:34.450+0800][922.949s][info][gc,marking    ] GC(83) Concurrent Cleanup for Next Mark
[2025-09-10T17:03:34.453+0800][922.952s][info][gc,marking    ] GC(83) Concurrent Cleanup for Next Mark 2.935ms
[2025-09-10T17:03:34.455+0800][922.954s][info][gc            ] GC(83) Concurrent Cycle 275.279ms
[2025-09-10T17:05:01.439+0800][1009.943s][info][gc,start      ] GC(84) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:01.439+0800][1009.943s][info][gc,task       ] GC(84) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:01.446+0800][1009.949s][info][gc,phases     ] GC(84)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:01.447+0800][1009.951s][info][gc,phases     ] GC(84)   Evacuate Collection Set: 2.7ms
[2025-09-10T17:05:01.448+0800][1009.952s][info][gc,phases     ] GC(84)   Post Evacuate Collection Set: 3.1ms
[2025-09-10T17:05:01.449+0800][1009.953s][info][gc,phases     ] GC(84)   Other: 0.7ms
[2025-09-10T17:05:01.449+0800][1009.953s][info][gc,heap       ] GC(84) Eden regions: 343->0(38)
[2025-09-10T17:05:01.449+0800][1009.953s][info][gc,heap       ] GC(84) Survivor regions: 4->13(44)
[2025-09-10T17:05:01.449+0800][1009.953s][info][gc,heap       ] GC(84) Old regions: 501->501
[2025-09-10T17:05:01.449+0800][1009.953s][info][gc,heap       ] GC(84) Humongous regions: 6->2
[2025-09-10T17:05:01.450+0800][1009.953s][info][gc,metaspace  ] GC(84) Metaspace: 339854K(368644K)->339854K(368644K) NonClass: 297623K(317416K)->297623K(317416K) Class: 42230K(51228K)->42230K(51228K)
[2025-09-10T17:05:01.450+0800][1009.954s][info][gc            ] GC(84) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 851M->513M(1026M) 10.739ms
[2025-09-10T17:05:01.450+0800][1009.954s][info][gc,cpu        ] GC(84) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:05:02.792+0800][1011.296s][info][gc,start      ] GC(85) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:02.792+0800][1011.296s][info][gc,task       ] GC(85) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:02.806+0800][1011.310s][info][gc,phases     ] GC(85)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T17:05:02.807+0800][1011.311s][info][gc,phases     ] GC(85)   Evacuate Collection Set: 10.6ms
[2025-09-10T17:05:02.807+0800][1011.311s][info][gc,phases     ] GC(85)   Post Evacuate Collection Set: 2.6ms
[2025-09-10T17:05:02.807+0800][1011.311s][info][gc,phases     ] GC(85)   Other: 0.8ms
[2025-09-10T17:05:02.807+0800][1011.311s][info][gc,heap       ] GC(85) Eden regions: 38->0(359)
[2025-09-10T17:05:02.808+0800][1011.311s][info][gc,heap       ] GC(85) Survivor regions: 13->2(7)
[2025-09-10T17:05:02.808+0800][1011.311s][info][gc,heap       ] GC(85) Old regions: 501->492
[2025-09-10T17:05:02.808+0800][1011.312s][info][gc,heap       ] GC(85) Humongous regions: 2->2
[2025-09-10T17:05:02.808+0800][1011.312s][info][gc,metaspace  ] GC(85) Metaspace: 340969K(370052K)->340969K(370052K) NonClass: 298559K(318440K)->298559K(318440K) Class: 42409K(51612K)->42409K(51612K)
[2025-09-10T17:05:02.808+0800][1011.312s][info][gc            ] GC(85) Pause Young (Mixed) (G1 Evacuation Pause) 551M->492M(1026M) 16.026ms
[2025-09-10T17:05:02.808+0800][1011.312s][info][gc,cpu        ] GC(85) User=0.17s Sys=0.00s Real=0.02s
[2025-09-10T17:05:03.626+0800][1012.130s][info][gc,start      ] GC(86) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:05:03.626+0800][1012.130s][info][gc,task       ] GC(86) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:03.632+0800][1012.136s][info][gc,phases     ] GC(86)   Pre Evacuate Collection Set: 0.7ms
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,phases     ] GC(86)   Evacuate Collection Set: 4.2ms
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,phases     ] GC(86)   Post Evacuate Collection Set: 0.6ms
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,phases     ] GC(86)   Other: 1.3ms
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,heap       ] GC(86) Eden regions: 359->0(371)
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,heap       ] GC(86) Survivor regions: 2->2(46)
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,heap       ] GC(86) Old regions: 492->492
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,heap       ] GC(86) Humongous regions: 2->2
[2025-09-10T17:05:03.633+0800][1012.137s][info][gc,metaspace  ] GC(86) Metaspace: 340987K(370052K)->340987K(370052K) NonClass: 298577K(318440K)->298577K(318440K) Class: 42409K(51612K)->42409K(51612K)
[2025-09-10T17:05:03.634+0800][1012.137s][info][gc            ] GC(86) Pause Young (Concurrent Start) (G1 Evacuation Pause) 851M->493M(1026M) 7.818ms
[2025-09-10T17:05:03.634+0800][1012.138s][info][gc,cpu        ] GC(86) User=0.04s Sys=0.00s Real=0.00s
[2025-09-10T17:05:03.634+0800][1012.138s][info][gc            ] GC(87) Concurrent Cycle
[2025-09-10T17:05:03.634+0800][1012.138s][info][gc,marking    ] GC(87) Concurrent Clear Claimed Marks
[2025-09-10T17:05:03.635+0800][1012.138s][info][gc,marking    ] GC(87) Concurrent Clear Claimed Marks 0.384ms
[2025-09-10T17:05:03.635+0800][1012.139s][info][gc,marking    ] GC(87) Concurrent Scan Root Regions
[2025-09-10T17:05:03.637+0800][1012.141s][info][gc,marking    ] GC(87) Concurrent Scan Root Regions 2.704ms
[2025-09-10T17:05:03.638+0800][1012.142s][info][gc,marking    ] GC(87) Concurrent Mark (1012.142s)
[2025-09-10T17:05:03.638+0800][1012.142s][info][gc,marking    ] GC(87) Concurrent Mark From Roots
[2025-09-10T17:05:03.638+0800][1012.142s][info][gc,task       ] GC(87) Using 4 workers of 4 for marking
[2025-09-10T17:05:03.765+0800][1012.269s][info][gc,marking    ] GC(87) Concurrent Mark From Roots 126.845ms
[2025-09-10T17:05:03.765+0800][1012.269s][info][gc,marking    ] GC(87) Concurrent Preclean
[2025-09-10T17:05:03.766+0800][1012.270s][info][gc,marking    ] GC(87) Concurrent Preclean 1.226ms
[2025-09-10T17:05:03.769+0800][1012.273s][info][gc,marking    ] GC(87) Concurrent Mark (1012.142s, 1012.273s) 131.060ms
[2025-09-10T17:05:03.770+0800][1012.273s][info][gc,start      ] GC(87) Pause Remark
[2025-09-10T17:05:03.803+0800][1012.306s][info][gc,stringtable] GC(87) Cleaned string and symbol table, strings: 199261 processed, 46688 removed, symbols: 500523 processed, 43 removed
[2025-09-10T17:05:03.805+0800][1012.309s][info][gc            ] GC(87) Pause Remark 547M->547M(1026M) 35.229ms
[2025-09-10T17:05:03.805+0800][1012.309s][info][gc,cpu        ] GC(87) User=0.39s Sys=0.00s Real=0.04s
[2025-09-10T17:05:03.805+0800][1012.309s][info][gc,marking    ] GC(87) Concurrent Rebuild Remembered Sets
[2025-09-10T17:05:03.912+0800][1012.416s][info][gc,marking    ] GC(87) Concurrent Rebuild Remembered Sets 106.899ms
[2025-09-10T17:05:03.913+0800][1012.417s][info][gc,start      ] GC(87) Pause Cleanup
[2025-09-10T17:05:03.914+0800][1012.417s][info][gc            ] GC(87) Pause Cleanup 596M->596M(1026M) 0.662ms
[2025-09-10T17:05:03.914+0800][1012.418s][info][gc,cpu        ] GC(87) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:05:03.914+0800][1012.418s][info][gc,marking    ] GC(87) Concurrent Cleanup for Next Mark
[2025-09-10T17:05:03.915+0800][1012.419s][info][gc,marking    ] GC(87) Concurrent Cleanup for Next Mark 1.095ms
[2025-09-10T17:05:03.915+0800][1012.419s][info][gc            ] GC(87) Concurrent Cycle 281.321ms
[2025-09-10T17:05:04.538+0800][1013.042s][info][gc,start      ] GC(88) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:04.539+0800][1013.043s][info][gc,task       ] GC(88) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:04.542+0800][1013.046s][info][gc,phases     ] GC(88)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,phases     ] GC(88)   Evacuate Collection Set: 2.2ms
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,phases     ] GC(88)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,phases     ] GC(88)   Other: 0.8ms
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,heap       ] GC(88) Eden regions: 371->0(47)
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,heap       ] GC(88) Survivor regions: 2->4(47)
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,heap       ] GC(88) Old regions: 492->492
[2025-09-10T17:05:04.543+0800][1013.047s][info][gc,heap       ] GC(88) Humongous regions: 2->2
[2025-09-10T17:05:04.544+0800][1013.047s][info][gc,metaspace  ] GC(88) Metaspace: 340745K(370052K)->340745K(370052K) NonClass: 298356K(318440K)->298356K(318440K) Class: 42389K(51612K)->42389K(51612K)
[2025-09-10T17:05:04.544+0800][1013.047s][info][gc            ] GC(88) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 864M->494M(1026M) 5.293ms
[2025-09-10T17:05:04.544+0800][1013.048s][info][gc,cpu        ] GC(88) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T17:05:04.665+0800][1013.168s][info][gc,start      ] GC(89) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:04.665+0800][1013.169s][info][gc,task       ] GC(89) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:04.674+0800][1013.178s][info][gc,phases     ] GC(89)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:05:04.674+0800][1013.178s][info][gc,phases     ] GC(89)   Evacuate Collection Set: 7.0ms
[2025-09-10T17:05:04.674+0800][1013.178s][info][gc,phases     ] GC(89)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:05:04.674+0800][1013.178s][info][gc,phases     ] GC(89)   Other: 0.7ms
[2025-09-10T17:05:04.674+0800][1013.178s][info][gc,heap       ] GC(89) Eden regions: 47->0(396)
[2025-09-10T17:05:04.675+0800][1013.178s][info][gc,heap       ] GC(89) Survivor regions: 4->4(7)
[2025-09-10T17:05:04.675+0800][1013.178s][info][gc,heap       ] GC(89) Old regions: 492->470
[2025-09-10T17:05:04.675+0800][1013.179s][info][gc,heap       ] GC(89) Humongous regions: 2->2
[2025-09-10T17:05:04.675+0800][1013.179s][info][gc,metaspace  ] GC(89) Metaspace: 340749K(370052K)->340749K(370052K) NonClass: 298359K(318440K)->298359K(318440K) Class: 42390K(51612K)->42390K(51612K)
[2025-09-10T17:05:04.675+0800][1013.179s][info][gc            ] GC(89) Pause Young (Mixed) (G1 Evacuation Pause) 541M->474M(1026M) 10.299ms
[2025-09-10T17:05:04.675+0800][1013.179s][info][gc,cpu        ] GC(89) User=0.10s Sys=0.00s Real=0.01s
[2025-09-10T17:05:05.408+0800][1013.912s][info][gc,start      ] GC(90) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:05:05.408+0800][1013.912s][info][gc,task       ] GC(90) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:05.415+0800][1013.919s][info][gc,phases     ] GC(90)   Pre Evacuate Collection Set: 0.7ms
[2025-09-10T17:05:05.415+0800][1013.919s][info][gc,phases     ] GC(90)   Evacuate Collection Set: 4.4ms
[2025-09-10T17:05:05.415+0800][1013.919s][info][gc,phases     ] GC(90)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:05:05.416+0800][1013.919s][info][gc,phases     ] GC(90)   Other: 0.6ms
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc,heap       ] GC(90) Eden regions: 396->0(398)
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc,heap       ] GC(90) Survivor regions: 4->8(50)
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc,heap       ] GC(90) Old regions: 470->470
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc,heap       ] GC(90) Humongous regions: 2->2
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc,metaspace  ] GC(90) Metaspace: 340798K(370052K)->340798K(370052K) NonClass: 298407K(318440K)->298407K(318440K) Class: 42391K(51612K)->42391K(51612K)
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc            ] GC(90) Pause Young (Concurrent Start) (G1 Evacuation Pause) 870M->477M(1026M) 8.110ms
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc,cpu        ] GC(90) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:05:05.416+0800][1013.920s][info][gc            ] GC(91) Concurrent Cycle
[2025-09-10T17:05:05.417+0800][1013.920s][info][gc,marking    ] GC(91) Concurrent Clear Claimed Marks
[2025-09-10T17:05:05.417+0800][1013.921s][info][gc,marking    ] GC(91) Concurrent Clear Claimed Marks 0.648ms
[2025-09-10T17:05:05.418+0800][1013.921s][info][gc,marking    ] GC(91) Concurrent Scan Root Regions
[2025-09-10T17:05:05.419+0800][1013.923s][info][gc,marking    ] GC(91) Concurrent Scan Root Regions 1.749ms
[2025-09-10T17:05:05.420+0800][1013.923s][info][gc,marking    ] GC(91) Concurrent Mark (1013.923s)
[2025-09-10T17:05:05.420+0800][1013.923s][info][gc,marking    ] GC(91) Concurrent Mark From Roots
[2025-09-10T17:05:05.420+0800][1013.924s][info][gc,task       ] GC(91) Using 4 workers of 4 for marking
[2025-09-10T17:05:05.518+0800][1014.022s][info][gc,marking    ] GC(91) Concurrent Mark From Roots 98.205ms
[2025-09-10T17:05:05.518+0800][1014.022s][info][gc,marking    ] GC(91) Concurrent Preclean
[2025-09-10T17:05:05.519+0800][1014.023s][info][gc,marking    ] GC(91) Concurrent Preclean 0.911ms
[2025-09-10T17:05:05.520+0800][1014.023s][info][gc,marking    ] GC(91) Concurrent Mark (1013.923s, 1014.023s) 100.073ms
[2025-09-10T17:05:05.520+0800][1014.024s][info][gc,start      ] GC(91) Pause Remark
[2025-09-10T17:05:05.549+0800][1014.053s][info][gc,stringtable] GC(91) Cleaned string and symbol table, strings: 152579 processed, 294 removed, symbols: 500494 processed, 30 removed
[2025-09-10T17:05:05.551+0800][1014.055s][info][gc            ] GC(91) Pause Remark 535M->535M(1026M) 30.436ms
[2025-09-10T17:05:05.551+0800][1014.055s][info][gc,cpu        ] GC(91) User=0.34s Sys=0.00s Real=0.03s
[2025-09-10T17:05:05.552+0800][1014.056s][info][gc,marking    ] GC(91) Concurrent Rebuild Remembered Sets
[2025-09-10T17:05:05.638+0800][1014.141s][info][gc,marking    ] GC(91) Concurrent Rebuild Remembered Sets 85.842ms
[2025-09-10T17:05:05.638+0800][1014.142s][info][gc,start      ] GC(91) Pause Cleanup
[2025-09-10T17:05:05.639+0800][1014.143s][info][gc            ] GC(91) Pause Cleanup 563M->563M(1026M) 0.915ms
[2025-09-10T17:05:05.640+0800][1014.144s][info][gc,cpu        ] GC(91) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:05:05.640+0800][1014.144s][info][gc,marking    ] GC(91) Concurrent Cleanup for Next Mark
[2025-09-10T17:05:05.641+0800][1014.145s][info][gc,marking    ] GC(91) Concurrent Cleanup for Next Mark 1.186ms
[2025-09-10T17:05:05.642+0800][1014.146s][info][gc            ] GC(91) Concurrent Cycle 225.329ms
[2025-09-10T17:05:06.196+0800][1014.700s][info][gc,start      ] GC(92) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:06.196+0800][1014.700s][info][gc,task       ] GC(92) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:06.203+0800][1014.707s][info][gc,phases     ] GC(92)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:06.203+0800][1014.707s][info][gc,phases     ] GC(92)   Evacuate Collection Set: 3.9ms
[2025-09-10T17:05:06.203+0800][1014.707s][info][gc,phases     ] GC(92)   Post Evacuate Collection Set: 2.5ms
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc,phases     ] GC(92)   Other: 0.8ms
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc,heap       ] GC(92) Eden regions: 398->0(42)
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc,heap       ] GC(92) Survivor regions: 8->9(51)
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc,heap       ] GC(92) Old regions: 470->470
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc,heap       ] GC(92) Humongous regions: 2->2
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc,metaspace  ] GC(92) Metaspace: 337777K(370052K)->337777K(370052K) NonClass: 295621K(318440K)->295621K(318440K) Class: 42155K(51612K)->42155K(51612K)
[2025-09-10T17:05:06.204+0800][1014.708s][info][gc            ] GC(92) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 875M->478M(1026M) 8.648ms
[2025-09-10T17:05:06.205+0800][1014.708s][info][gc,cpu        ] GC(92) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:05:06.257+0800][1014.761s][info][gc,start      ] GC(93) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:06.259+0800][1014.762s][info][gc,task       ] GC(93) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:06.284+0800][1014.788s][info][gc,phases     ] GC(93)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:05:06.285+0800][1014.789s][info][gc,phases     ] GC(93)   Evacuate Collection Set: 19.7ms
[2025-09-10T17:05:06.285+0800][1014.789s][info][gc,phases     ] GC(93)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T17:05:06.286+0800][1014.789s][info][gc,phases     ] GC(93)   Other: 5.2ms
[2025-09-10T17:05:06.286+0800][1014.790s][info][gc,heap       ] GC(93) Eden regions: 42->0(419)
[2025-09-10T17:05:06.286+0800][1014.790s][info][gc,heap       ] GC(93) Survivor regions: 9->3(7)
[2025-09-10T17:05:06.286+0800][1014.790s][info][gc,heap       ] GC(93) Old regions: 470->449
[2025-09-10T17:05:06.286+0800][1014.790s][info][gc,heap       ] GC(93) Humongous regions: 2->2
[2025-09-10T17:05:06.286+0800][1014.790s][info][gc,metaspace  ] GC(93) Metaspace: 337778K(370052K)->337778K(370052K) NonClass: 295622K(318440K)->295622K(318440K) Class: 42155K(51612K)->42155K(51612K)
[2025-09-10T17:05:06.287+0800][1014.790s][info][gc            ] GC(93) Pause Young (Mixed) (G1 Evacuation Pause) 520M->451M(1026M) 29.866ms
[2025-09-10T17:05:06.287+0800][1014.791s][info][gc,cpu        ] GC(93) User=0.29s Sys=0.01s Real=0.03s
[2025-09-10T17:05:10.825+0800][1019.328s][info][gc,start      ] GC(94) Pause Young (Concurrent Start) (System.gc())
[2025-09-10T17:05:10.825+0800][1019.329s][info][gc,task       ] GC(94) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:10.832+0800][1019.336s][info][gc,phases     ] GC(94)   Pre Evacuate Collection Set: 0.7ms
[2025-09-10T17:05:10.832+0800][1019.336s][info][gc,phases     ] GC(94)   Evacuate Collection Set: 4.5ms
[2025-09-10T17:05:10.833+0800][1019.337s][info][gc,phases     ] GC(94)   Post Evacuate Collection Set: 1.7ms
[2025-09-10T17:05:10.833+0800][1019.337s][info][gc,phases     ] GC(94)   Other: 0.5ms
[2025-09-10T17:05:10.833+0800][1019.337s][info][gc,heap       ] GC(94) Eden regions: 175->0(429)
[2025-09-10T17:05:10.833+0800][1019.337s][info][gc,heap       ] GC(94) Survivor regions: 3->2(53)
[2025-09-10T17:05:10.833+0800][1019.337s][info][gc,heap       ] GC(94) Old regions: 449->449
[2025-09-10T17:05:10.834+0800][1019.337s][info][gc,heap       ] GC(94) Humongous regions: 2->0
[2025-09-10T17:05:10.834+0800][1019.338s][info][gc,metaspace  ] GC(94) Metaspace: 337807K(370052K)->337807K(370052K) NonClass: 295651K(318440K)->295651K(318440K) Class: 42155K(51612K)->42155K(51612K)
[2025-09-10T17:05:10.834+0800][1019.338s][info][gc            ] GC(94) Pause Young (Concurrent Start) (System.gc()) 626M->448M(1026M) 9.267ms
[2025-09-10T17:05:10.834+0800][1019.338s][info][gc,cpu        ] GC(94) User=0.05s Sys=0.00s Real=0.00s
[2025-09-10T17:05:10.834+0800][1019.338s][info][gc            ] GC(95) Concurrent Cycle
[2025-09-10T17:05:10.834+0800][1019.338s][info][gc,marking    ] GC(95) Concurrent Clear Claimed Marks
[2025-09-10T17:05:10.835+0800][1019.339s][info][gc,marking    ] GC(95) Concurrent Clear Claimed Marks 0.804ms
[2025-09-10T17:05:10.835+0800][1019.339s][info][gc,marking    ] GC(95) Concurrent Scan Root Regions
[2025-09-10T17:05:10.837+0800][1019.341s][info][gc,marking    ] GC(95) Concurrent Scan Root Regions 1.404ms
[2025-09-10T17:05:10.837+0800][1019.341s][info][gc,marking    ] GC(95) Concurrent Mark (1019.341s)
[2025-09-10T17:05:10.837+0800][1019.341s][info][gc,marking    ] GC(95) Concurrent Mark From Roots
[2025-09-10T17:05:10.837+0800][1019.341s][info][gc,task       ] GC(95) Using 4 workers of 4 for marking
[2025-09-10T17:05:10.916+0800][1019.419s][info][gc,marking    ] GC(95) Concurrent Mark From Roots 78.538ms
[2025-09-10T17:05:10.916+0800][1019.420s][info][gc,marking    ] GC(95) Concurrent Preclean
[2025-09-10T17:05:10.917+0800][1019.420s][info][gc,marking    ] GC(95) Concurrent Preclean 0.616ms
[2025-09-10T17:05:10.917+0800][1019.421s][info][gc,marking    ] GC(95) Concurrent Mark (1019.341s, 1019.421s) 79.896ms
[2025-09-10T17:05:10.917+0800][1019.421s][info][gc,start      ] GC(95) Pause Remark
[2025-09-10T17:05:10.947+0800][1019.451s][info][gc,stringtable] GC(95) Cleaned string and symbol table, strings: 152288 processed, 5182 removed, symbols: 500470 processed, 317 removed
[2025-09-10T17:05:10.950+0800][1019.454s][info][gc            ] GC(95) Pause Remark 448M->448M(1026M) 33.235ms
[2025-09-10T17:05:10.951+0800][1019.455s][info][gc,cpu        ] GC(95) User=0.34s Sys=0.01s Real=0.03s
[2025-09-10T17:05:10.951+0800][1019.455s][info][gc,marking    ] GC(95) Concurrent Rebuild Remembered Sets
[2025-09-10T17:05:11.030+0800][1019.534s][info][gc,marking    ] GC(95) Concurrent Rebuild Remembered Sets 78.323ms
[2025-09-10T17:05:11.031+0800][1019.534s][info][gc,start      ] GC(95) Pause Cleanup
[2025-09-10T17:05:11.034+0800][1019.538s][info][gc            ] GC(95) Pause Cleanup 448M->448M(1026M) 3.276ms
[2025-09-10T17:05:11.035+0800][1019.539s][info][gc,cpu        ] GC(95) User=0.01s Sys=0.00s Real=0.01s
[2025-09-10T17:05:11.036+0800][1019.540s][info][gc,marking    ] GC(95) Concurrent Cleanup for Next Mark
[2025-09-10T17:05:11.037+0800][1019.541s][info][gc,marking    ] GC(95) Concurrent Cleanup for Next Mark 1.129ms
[2025-09-10T17:05:11.038+0800][1019.541s][info][gc            ] GC(95) Concurrent Cycle 203.528ms
[2025-09-10T17:05:35.567+0800][1044.073s][info][gc,start      ] GC(96) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:35.568+0800][1044.073s][info][gc,task       ] GC(96) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:35.580+0800][1044.085s][info][gc,phases     ] GC(96)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:35.580+0800][1044.086s][info][gc,phases     ] GC(96)   Evacuate Collection Set: 9.5ms
[2025-09-10T17:05:35.580+0800][1044.086s][info][gc,phases     ] GC(96)   Post Evacuate Collection Set: 2.1ms
[2025-09-10T17:05:35.580+0800][1044.086s][info][gc,phases     ] GC(96)   Other: 0.9ms
[2025-09-10T17:05:35.581+0800][1044.086s][info][gc,heap       ] GC(96) Eden regions: 429->0(21)
[2025-09-10T17:05:35.581+0800][1044.086s][info][gc,heap       ] GC(96) Survivor regions: 2->30(54)
[2025-09-10T17:05:35.581+0800][1044.086s][info][gc,heap       ] GC(96) Old regions: 449->449
[2025-09-10T17:05:35.581+0800][1044.086s][info][gc,heap       ] GC(96) Humongous regions: 3->1
[2025-09-10T17:05:35.581+0800][1044.087s][info][gc,metaspace  ] GC(96) Metaspace: 332062K(370052K)->332062K(370052K) NonClass: 290158K(318440K)->290158K(318440K) Class: 41903K(51612K)->41903K(51612K)
[2025-09-10T17:05:35.581+0800][1044.087s][info][gc            ] GC(96) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 880M->477M(1026M) 14.153ms
[2025-09-10T17:05:35.582+0800][1044.087s][info][gc,cpu        ] GC(96) User=0.17s Sys=0.00s Real=0.02s
[2025-09-10T17:05:35.603+0800][1044.108s][info][gc,start      ] GC(97) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:35.603+0800][1044.108s][info][gc,task       ] GC(97) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:35.629+0800][1044.135s][info][gc,phases     ] GC(97)   Pre Evacuate Collection Set: 1.6ms
[2025-09-10T17:05:35.630+0800][1044.135s][info][gc,phases     ] GC(97)   Evacuate Collection Set: 22.3ms
[2025-09-10T17:05:35.630+0800][1044.135s][info][gc,phases     ] GC(97)   Post Evacuate Collection Set: 2.1ms
[2025-09-10T17:05:35.630+0800][1044.135s][info][gc,phases     ] GC(97)   Other: 0.7ms
[2025-09-10T17:05:35.630+0800][1044.136s][info][gc,heap       ] GC(97) Eden regions: 21->0(48)
[2025-09-10T17:05:35.630+0800][1044.136s][info][gc,heap       ] GC(97) Survivor regions: 30->3(7)
[2025-09-10T17:05:35.630+0800][1044.136s][info][gc,heap       ] GC(97) Old regions: 449->426
[2025-09-10T17:05:35.631+0800][1044.136s][info][gc,heap       ] GC(97) Humongous regions: 1->1
[2025-09-10T17:05:35.631+0800][1044.136s][info][gc,metaspace  ] GC(97) Metaspace: 332062K(370052K)->332062K(370052K) NonClass: 290158K(318440K)->290158K(318440K) Class: 41903K(51612K)->41903K(51612K)
[2025-09-10T17:05:35.631+0800][1044.136s][info][gc            ] GC(97) Pause Young (Mixed) (G1 Evacuation Pause) 498M->428M(1026M) 28.288ms
[2025-09-10T17:05:35.631+0800][1044.136s][info][gc,cpu        ] GC(97) User=0.37s Sys=0.00s Real=0.02s
[2025-09-10T17:05:35.715+0800][1044.221s][info][gc,start      ] GC(98) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:35.716+0800][1044.221s][info][gc,task       ] GC(98) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:35.739+0800][1044.245s][info][gc,phases     ] GC(98)   Pre Evacuate Collection Set: 0.8ms
[2025-09-10T17:05:35.740+0800][1044.245s][info][gc,phases     ] GC(98)   Evacuate Collection Set: 20.3ms
[2025-09-10T17:05:35.740+0800][1044.246s][info][gc,phases     ] GC(98)   Post Evacuate Collection Set: 2.2ms
[2025-09-10T17:05:35.741+0800][1044.246s][info][gc,phases     ] GC(98)   Other: 0.7ms
[2025-09-10T17:05:35.741+0800][1044.246s][info][gc,heap       ] GC(98) Eden regions: 48->0(447)
[2025-09-10T17:05:35.741+0800][1044.246s][info][gc,heap       ] GC(98) Survivor regions: 3->3(7)
[2025-09-10T17:05:35.741+0800][1044.247s][info][gc,heap       ] GC(98) Old regions: 426->407
[2025-09-10T17:05:35.741+0800][1044.247s][info][gc,heap       ] GC(98) Humongous regions: 1->1
[2025-09-10T17:05:35.741+0800][1044.247s][info][gc,metaspace  ] GC(98) Metaspace: 332063K(370052K)->332063K(370052K) NonClass: 290159K(318440K)->290159K(318440K) Class: 41903K(51612K)->41903K(51612K)
[2025-09-10T17:05:35.742+0800][1044.247s][info][gc            ] GC(98) Pause Young (Mixed) (G1 Evacuation Pause) 476M->409M(1026M) 26.282ms
[2025-09-10T17:05:35.742+0800][1044.247s][info][gc,cpu        ] GC(98) User=0.30s Sys=0.00s Real=0.03s
[2025-09-10T17:05:46.045+0800][1054.550s][info][gc,start      ] GC(99) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:05:46.045+0800][1054.550s][info][gc,task       ] GC(99) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:46.055+0800][1054.560s][info][gc,phases     ] GC(99)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:46.056+0800][1054.561s][info][gc,phases     ] GC(99)   Evacuate Collection Set: 8.5ms
[2025-09-10T17:05:46.056+0800][1054.561s][info][gc,phases     ] GC(99)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:05:46.056+0800][1054.561s][info][gc,phases     ] GC(99)   Other: 0.5ms
[2025-09-10T17:05:46.056+0800][1054.562s][info][gc,heap       ] GC(99) Eden regions: 447->0(416)
[2025-09-10T17:05:46.056+0800][1054.562s][info][gc,heap       ] GC(99) Survivor regions: 3->32(57)
[2025-09-10T17:05:46.057+0800][1054.562s][info][gc,heap       ] GC(99) Old regions: 407->407
[2025-09-10T17:05:46.057+0800][1054.562s][info][gc,heap       ] GC(99) Humongous regions: 13->1
[2025-09-10T17:05:46.057+0800][1054.562s][info][gc,metaspace  ] GC(99) Metaspace: 332093K(370052K)->332093K(370052K) NonClass: 290189K(318440K)->290189K(318440K) Class: 41904K(51612K)->41904K(51612K)
[2025-09-10T17:05:46.057+0800][1054.562s][info][gc            ] GC(99) Pause Young (Normal) (G1 Evacuation Pause) 868M->437M(1026M) 12.445ms
[2025-09-10T17:05:46.057+0800][1054.563s][info][gc,cpu        ] GC(99) User=0.13s Sys=0.00s Real=0.01s
[2025-09-10T17:05:48.057+0800][1056.562s][info][gc,start      ] GC(100) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:05:48.057+0800][1056.562s][info][gc,task       ] GC(100) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:48.069+0800][1056.574s][info][gc,phases     ] GC(100)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:05:48.069+0800][1056.574s][info][gc,phases     ] GC(100)   Evacuate Collection Set: 9.4ms
[2025-09-10T17:05:48.069+0800][1056.575s][info][gc,phases     ] GC(100)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:05:48.069+0800][1056.575s][info][gc,phases     ] GC(100)   Other: 0.6ms
[2025-09-10T17:05:48.069+0800][1056.575s][info][gc,heap       ] GC(100) Eden regions: 323->0(381)
[2025-09-10T17:05:48.070+0800][1056.575s][info][gc,heap       ] GC(100) Survivor regions: 32->53(56)
[2025-09-10T17:05:48.070+0800][1056.575s][info][gc,heap       ] GC(100) Old regions: 407->411
[2025-09-10T17:05:48.070+0800][1056.575s][info][gc,heap       ] GC(100) Humongous regions: 1->1
[2025-09-10T17:05:48.070+0800][1056.575s][info][gc,metaspace  ] GC(100) Metaspace: 332205K(370052K)->332205K(370052K) NonClass: 290282K(318440K)->290282K(318440K) Class: 41922K(51612K)->41922K(51612K)
[2025-09-10T17:05:48.070+0800][1056.576s][info][gc            ] GC(100) Pause Young (Concurrent Start) (G1 Humongous Allocation) 760M->462M(1026M) 13.607ms
[2025-09-10T17:05:48.071+0800][1056.576s][info][gc,cpu        ] GC(100) User=0.14s Sys=0.00s Real=0.01s
[2025-09-10T17:05:48.071+0800][1056.576s][info][gc            ] GC(101) Concurrent Cycle
[2025-09-10T17:05:48.071+0800][1056.577s][info][gc,marking    ] GC(101) Concurrent Clear Claimed Marks
[2025-09-10T17:05:48.072+0800][1056.578s][info][gc,marking    ] GC(101) Concurrent Clear Claimed Marks 1.386ms
[2025-09-10T17:05:48.073+0800][1056.578s][info][gc,marking    ] GC(101) Concurrent Scan Root Regions
[2025-09-10T17:05:48.077+0800][1056.582s][info][gc,marking    ] GC(101) Concurrent Scan Root Regions 4.129ms
[2025-09-10T17:05:48.077+0800][1056.583s][info][gc,marking    ] GC(101) Concurrent Mark (1056.583s)
[2025-09-10T17:05:48.078+0800][1056.584s][info][gc,marking    ] GC(101) Concurrent Mark From Roots
[2025-09-10T17:05:48.078+0800][1056.584s][info][gc,task       ] GC(101) Using 4 workers of 4 for marking
[2025-09-10T17:05:48.218+0800][1056.723s][info][gc,marking    ] GC(101) Concurrent Mark From Roots 139.635ms
[2025-09-10T17:05:48.218+0800][1056.723s][info][gc,marking    ] GC(101) Concurrent Preclean
[2025-09-10T17:05:48.218+0800][1056.724s][info][gc,marking    ] GC(101) Concurrent Preclean 0.481ms
[2025-09-10T17:05:48.219+0800][1056.724s][info][gc,marking    ] GC(101) Concurrent Mark (1056.583s, 1056.724s) 141.546ms
[2025-09-10T17:05:48.219+0800][1056.724s][info][gc,start      ] GC(101) Pause Remark
[2025-09-10T17:05:48.249+0800][1056.754s][info][gc,stringtable] GC(101) Cleaned string and symbol table, strings: 148524 processed, 409 removed, symbols: 500375 processed, 1416 removed
[2025-09-10T17:05:48.250+0800][1056.755s][info][gc            ] GC(101) Pause Remark 500M->500M(1026M) 30.870ms
[2025-09-10T17:05:48.250+0800][1056.756s][info][gc,cpu        ] GC(101) User=0.38s Sys=0.00s Real=0.03s
[2025-09-10T17:05:48.251+0800][1056.756s][info][gc,marking    ] GC(101) Concurrent Rebuild Remembered Sets
[2025-09-10T17:05:48.348+0800][1056.853s][info][gc,marking    ] GC(101) Concurrent Rebuild Remembered Sets 97.001ms
[2025-09-10T17:05:48.348+0800][1056.853s][info][gc,start      ] GC(101) Pause Cleanup
[2025-09-10T17:05:48.349+0800][1056.854s][info][gc            ] GC(101) Pause Cleanup 517M->517M(1026M) 0.723ms
[2025-09-10T17:05:48.349+0800][1056.854s][info][gc,cpu        ] GC(101) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:05:48.349+0800][1056.855s][info][gc,marking    ] GC(101) Concurrent Cleanup for Next Mark
[2025-09-10T17:05:48.350+0800][1056.855s][info][gc,marking    ] GC(101) Concurrent Cleanup for Next Mark 0.862ms
[2025-09-10T17:05:48.350+0800][1056.856s][info][gc            ] GC(101) Concurrent Cycle 279.357ms
[2025-09-10T17:05:53.758+0800][1062.264s][info][gc,start      ] GC(102) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:53.759+0800][1062.264s][info][gc,task       ] GC(102) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:53.773+0800][1062.279s][info][gc,phases     ] GC(102)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:53.774+0800][1062.279s][info][gc,phases     ] GC(102)   Evacuate Collection Set: 10.7ms
[2025-09-10T17:05:53.774+0800][1062.279s][info][gc,phases     ] GC(102)   Post Evacuate Collection Set: 3.4ms
[2025-09-10T17:05:53.774+0800][1062.279s][info][gc,phases     ] GC(102)   Other: 1.1ms
[2025-09-10T17:05:53.774+0800][1062.280s][info][gc,heap       ] GC(102) Eden regions: 381->0(7)
[2025-09-10T17:05:53.774+0800][1062.280s][info][gc,heap       ] GC(102) Survivor regions: 53->44(55)
[2025-09-10T17:05:53.774+0800][1062.280s][info][gc,heap       ] GC(102) Old regions: 411->438
[2025-09-10T17:05:53.775+0800][1062.280s][info][gc,heap       ] GC(102) Humongous regions: 3->2
[2025-09-10T17:05:53.775+0800][1062.280s][info][gc,metaspace  ] GC(102) Metaspace: 343764K(371076K)->343764K(371076K) NonClass: 300404K(318952K)->300404K(318952K) Class: 43360K(52124K)->43360K(52124K)
[2025-09-10T17:05:53.775+0800][1062.280s][info][gc            ] GC(102) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 845M->482M(1026M) 16.734ms
[2025-09-10T17:05:53.775+0800][1062.281s][info][gc,cpu        ] GC(102) User=0.18s Sys=0.00s Real=0.02s
[2025-09-10T17:05:55.259+0800][1063.765s][info][gc,start      ] GC(103) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:05:55.260+0800][1063.765s][info][gc,task       ] GC(103) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:55.280+0800][1063.786s][info][gc,phases     ] GC(103)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T17:05:55.281+0800][1063.786s][info][gc,phases     ] GC(103)   Evacuate Collection Set: 17.2ms
[2025-09-10T17:05:55.281+0800][1063.786s][info][gc,phases     ] GC(103)   Post Evacuate Collection Set: 2.5ms
[2025-09-10T17:05:55.281+0800][1063.787s][info][gc,phases     ] GC(103)   Other: 0.7ms
[2025-09-10T17:05:55.281+0800][1063.787s][info][gc,heap       ] GC(103) Eden regions: 7->0(379)
[2025-09-10T17:05:55.281+0800][1063.787s][info][gc,heap       ] GC(103) Survivor regions: 44->7(7)
[2025-09-10T17:05:55.282+0800][1063.787s][info][gc,heap       ] GC(103) Old regions: 438->461
[2025-09-10T17:05:55.282+0800][1063.787s][info][gc,heap       ] GC(103) Humongous regions: 2->2
[2025-09-10T17:05:55.282+0800][1063.787s][info][gc,metaspace  ] GC(103) Metaspace: 343965K(371332K)->343965K(371332K) NonClass: 300572K(319208K)->300572K(319208K) Class: 43392K(52124K)->43392K(52124K)
[2025-09-10T17:05:55.282+0800][1063.787s][info][gc            ] GC(103) Pause Young (Mixed) (G1 Evacuation Pause) 489M->468M(1026M) 22.513ms
[2025-09-10T17:05:55.282+0800][1063.787s][info][gc,cpu        ] GC(103) User=0.27s Sys=0.00s Real=0.03s
[2025-09-10T17:05:57.961+0800][1066.468s][info][gc,start      ] GC(104) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:05:57.961+0800][1066.468s][info][gc,task       ] GC(104) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:57.971+0800][1066.478s][info][gc,phases     ] GC(104)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:05:57.971+0800][1066.478s][info][gc,phases     ] GC(104)   Evacuate Collection Set: 8.0ms
[2025-09-10T17:05:57.972+0800][1066.478s][info][gc,phases     ] GC(104)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:05:57.972+0800][1066.479s][info][gc,phases     ] GC(104)   Other: 0.5ms
[2025-09-10T17:05:57.972+0800][1066.479s][info][gc,heap       ] GC(104) Eden regions: 379->0(371)
[2025-09-10T17:05:57.972+0800][1066.479s][info][gc,heap       ] GC(104) Survivor regions: 7->21(49)
[2025-09-10T17:05:57.972+0800][1066.479s][info][gc,heap       ] GC(104) Old regions: 461->461
[2025-09-10T17:05:57.972+0800][1066.479s][info][gc,heap       ] GC(104) Humongous regions: 2->2
[2025-09-10T17:05:57.972+0800][1066.479s][info][gc,metaspace  ] GC(104) Metaspace: 349446K(378372K)->349446K(378372K) NonClass: 305300K(325096K)->305300K(325096K) Class: 44146K(53276K)->44146K(53276K)
[2025-09-10T17:05:57.973+0800][1066.479s][info][gc            ] GC(104) Pause Young (Normal) (G1 Evacuation Pause) 847M->482M(1026M) 11.394ms
[2025-09-10T17:05:57.973+0800][1066.479s][info][gc,cpu        ] GC(104) User=0.13s Sys=0.00s Real=0.01s
[2025-09-10T17:05:58.473+0800][1066.980s][info][gc,start      ] GC(105) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:05:58.474+0800][1066.981s][info][gc,task       ] GC(105) Using 15 workers of 15 for evacuation
[2025-09-10T17:05:58.487+0800][1066.994s][info][gc,phases     ] GC(105)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:05:58.488+0800][1066.995s][info][gc,phases     ] GC(105)   Evacuate Collection Set: 10.4ms
[2025-09-10T17:05:58.488+0800][1066.995s][info][gc,phases     ] GC(105)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T17:05:58.488+0800][1066.995s][info][gc,phases     ] GC(105)   Other: 1.4ms
[2025-09-10T17:05:58.488+0800][1066.995s][info][gc,heap       ] GC(105) Eden regions: 165->0(361)
[2025-09-10T17:05:58.488+0800][1066.995s][info][gc,heap       ] GC(105) Survivor regions: 21->26(49)
[2025-09-10T17:05:58.488+0800][1066.995s][info][gc,heap       ] GC(105) Old regions: 461->461
[2025-09-10T17:05:58.489+0800][1066.995s][info][gc,heap       ] GC(105) Humongous regions: 2->1
[2025-09-10T17:05:58.489+0800][1066.995s][info][gc,metaspace  ] GC(105) Metaspace: 349990K(379012K)->349990K(379012K) NonClass: 305774K(325608K)->305774K(325608K) Class: 44215K(53404K)->44215K(53404K)
[2025-09-10T17:05:58.489+0800][1066.996s][info][gc            ] GC(105) Pause Young (Concurrent Start) (G1 Humongous Allocation) 646M->485M(1026M) 15.888ms
[2025-09-10T17:05:58.489+0800][1066.996s][info][gc,cpu        ] GC(105) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:05:58.489+0800][1066.996s][info][gc            ] GC(106) Concurrent Cycle
[2025-09-10T17:05:58.489+0800][1066.996s][info][gc,marking    ] GC(106) Concurrent Clear Claimed Marks
[2025-09-10T17:05:58.490+0800][1066.997s][info][gc,marking    ] GC(106) Concurrent Clear Claimed Marks 0.998ms
[2025-09-10T17:05:58.491+0800][1066.998s][info][gc,marking    ] GC(106) Concurrent Scan Root Regions
[2025-09-10T17:05:58.495+0800][1067.002s][info][gc,marking    ] GC(106) Concurrent Scan Root Regions 4.089ms
[2025-09-10T17:05:58.496+0800][1067.003s][info][gc,marking    ] GC(106) Concurrent Mark (1067.003s)
[2025-09-10T17:05:58.497+0800][1067.003s][info][gc,marking    ] GC(106) Concurrent Mark From Roots
[2025-09-10T17:05:58.497+0800][1067.004s][info][gc,task       ] GC(106) Using 4 workers of 4 for marking
[2025-09-10T17:05:58.607+0800][1067.114s][info][gc,marking    ] GC(106) Concurrent Mark From Roots 110.535ms
[2025-09-10T17:05:58.607+0800][1067.114s][info][gc,marking    ] GC(106) Concurrent Preclean
[2025-09-10T17:05:58.608+0800][1067.115s][info][gc,marking    ] GC(106) Concurrent Preclean 0.843ms
[2025-09-10T17:05:58.609+0800][1067.115s][info][gc,marking    ] GC(106) Concurrent Mark (1067.003s, 1067.115s) 112.699ms
[2025-09-10T17:05:58.609+0800][1067.116s][info][gc,start      ] GC(106) Pause Remark
[2025-09-10T17:05:58.640+0800][1067.147s][info][gc,stringtable] GC(106) Cleaned string and symbol table, strings: 148337 processed, 35 removed, symbols: 499722 processed, 409 removed
[2025-09-10T17:05:58.642+0800][1067.148s][info][gc            ] GC(106) Pause Remark 502M->502M(1026M) 32.382ms
[2025-09-10T17:05:58.642+0800][1067.149s][info][gc,cpu        ] GC(106) User=0.37s Sys=0.00s Real=0.04s
[2025-09-10T17:05:58.643+0800][1067.149s][info][gc,marking    ] GC(106) Concurrent Rebuild Remembered Sets
[2025-09-10T17:05:58.733+0800][1067.240s][info][gc,marking    ] GC(106) Concurrent Rebuild Remembered Sets 90.549ms
[2025-09-10T17:05:58.734+0800][1067.241s][info][gc,start      ] GC(106) Pause Cleanup
[2025-09-10T17:05:58.735+0800][1067.241s][info][gc            ] GC(106) Pause Cleanup 515M->515M(1026M) 0.679ms
[2025-09-10T17:05:58.735+0800][1067.242s][info][gc,cpu        ] GC(106) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:05:58.735+0800][1067.242s][info][gc,marking    ] GC(106) Concurrent Cleanup for Next Mark
[2025-09-10T17:05:58.736+0800][1067.243s][info][gc,marking    ] GC(106) Concurrent Cleanup for Next Mark 1.122ms
[2025-09-10T17:05:58.737+0800][1067.244s][info][gc            ] GC(106) Concurrent Cycle 247.789ms
[2025-09-10T17:06:02.805+0800][1071.311s][info][gc,start      ] GC(107) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:02.805+0800][1071.312s][info][gc,task       ] GC(107) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:02.814+0800][1071.321s][info][gc,phases     ] GC(107)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:02.815+0800][1071.321s][info][gc,phases     ] GC(107)   Evacuate Collection Set: 6.9ms
[2025-09-10T17:06:02.815+0800][1071.322s][info][gc,phases     ] GC(107)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T17:06:02.815+0800][1071.322s][info][gc,phases     ] GC(107)   Other: 0.8ms
[2025-09-10T17:06:02.815+0800][1071.322s][info][gc,heap       ] GC(107) Eden regions: 361->0(9)
[2025-09-10T17:06:02.816+0800][1071.322s][info][gc,heap       ] GC(107) Survivor regions: 26->42(49)
[2025-09-10T17:06:02.816+0800][1071.322s][info][gc,heap       ] GC(107) Old regions: 461->461
[2025-09-10T17:06:02.816+0800][1071.323s][info][gc,heap       ] GC(107) Humongous regions: 2->2
[2025-09-10T17:06:02.816+0800][1071.323s][info][gc,metaspace  ] GC(107) Metaspace: 355801K(385668K)->355801K(385668K) NonClass: 310827K(331240K)->310827K(331240K) Class: 44974K(54428K)->44974K(54428K)
[2025-09-10T17:06:02.816+0800][1071.323s][info][gc            ] GC(107) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 847M->503M(1026M) 11.597ms
[2025-09-10T17:06:02.816+0800][1071.323s][info][gc,cpu        ] GC(107) User=0.11s Sys=0.00s Real=0.01s
[2025-09-10T17:06:02.833+0800][1071.340s][info][gc,start      ] GC(108) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:02.833+0800][1071.340s][info][gc,task       ] GC(108) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:02.850+0800][1071.356s][info][gc,phases     ] GC(108)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T17:06:02.850+0800][1071.357s][info][gc,phases     ] GC(108)   Evacuate Collection Set: 12.8ms
[2025-09-10T17:06:02.850+0800][1071.357s][info][gc,phases     ] GC(108)   Post Evacuate Collection Set: 2.8ms
[2025-09-10T17:06:02.850+0800][1071.357s][info][gc,phases     ] GC(108)   Other: 0.7ms
[2025-09-10T17:06:02.851+0800][1071.357s][info][gc,heap       ] GC(108) Eden regions: 9->0(357)
[2025-09-10T17:06:02.851+0800][1071.358s][info][gc,heap       ] GC(108) Survivor regions: 42->5(7)
[2025-09-10T17:06:02.851+0800][1071.358s][info][gc,heap       ] GC(108) Old regions: 461->492
[2025-09-10T17:06:02.851+0800][1071.358s][info][gc,heap       ] GC(108) Humongous regions: 2->2
[2025-09-10T17:06:02.851+0800][1071.358s][info][gc,metaspace  ] GC(108) Metaspace: 355801K(385668K)->355801K(385668K) NonClass: 310827K(331240K)->310827K(331240K) Class: 44974K(54428K)->44974K(54428K)
[2025-09-10T17:06:02.851+0800][1071.358s][info][gc            ] GC(108) Pause Young (Mixed) (G1 Evacuation Pause) 512M->496M(1026M) 18.330ms
[2025-09-10T17:06:02.852+0800][1071.358s][info][gc,cpu        ] GC(108) User=0.19s Sys=0.00s Real=0.02s
[2025-09-10T17:06:08.029+0800][1076.536s][info][gc,start      ] GC(109) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:06:08.029+0800][1076.536s][info][gc,task       ] GC(109) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:08.037+0800][1076.544s][info][gc,phases     ] GC(109)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:08.037+0800][1076.544s][info][gc,phases     ] GC(109)   Evacuate Collection Set: 6.1ms
[2025-09-10T17:06:08.037+0800][1076.544s][info][gc,phases     ] GC(109)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:06:08.038+0800][1076.544s][info][gc,phases     ] GC(109)   Other: 0.5ms
[2025-09-10T17:06:08.038+0800][1076.545s][info][gc,heap       ] GC(109) Eden regions: 357->0(356)
[2025-09-10T17:06:08.038+0800][1076.545s][info][gc,heap       ] GC(109) Survivor regions: 5->13(46)
[2025-09-10T17:06:08.038+0800][1076.545s][info][gc,heap       ] GC(109) Old regions: 492->492
[2025-09-10T17:06:08.039+0800][1076.545s][info][gc,heap       ] GC(109) Humongous regions: 2->2
[2025-09-10T17:06:08.039+0800][1076.546s][info][gc,metaspace  ] GC(109) Metaspace: 360024K(391172K)->360024K(391172K) NonClass: 314440K(335848K)->314440K(335848K) Class: 45583K(55324K)->45583K(55324K)
[2025-09-10T17:06:08.039+0800][1076.546s][info][gc            ] GC(109) Pause Young (Normal) (G1 Evacuation Pause) 853M->504M(1026M) 9.737ms
[2025-09-10T17:06:08.039+0800][1076.546s][info][gc,cpu        ] GC(109) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:06:09.974+0800][1078.481s][info][gc,start      ] GC(110) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:06:09.974+0800][1078.481s][info][gc,task       ] GC(110) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:09.980+0800][1078.487s][info][gc,phases     ] GC(110)   Pre Evacuate Collection Set: 0.8ms
[2025-09-10T17:06:09.981+0800][1078.487s][info][gc,phases     ] GC(110)   Evacuate Collection Set: 4.2ms
[2025-09-10T17:06:09.981+0800][1078.488s][info][gc,phases     ] GC(110)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:06:09.981+0800][1078.488s][info][gc,phases     ] GC(110)   Other: 0.6ms
[2025-09-10T17:06:09.981+0800][1078.488s][info][gc,heap       ] GC(110) Eden regions: 29->0(333)
[2025-09-10T17:06:09.981+0800][1078.488s][info][gc,heap       ] GC(110) Survivor regions: 13->17(47)
[2025-09-10T17:06:09.981+0800][1078.488s][info][gc,heap       ] GC(110) Old regions: 492->492
[2025-09-10T17:06:09.981+0800][1078.488s][info][gc,heap       ] GC(110) Humongous regions: 2->1
[2025-09-10T17:06:09.982+0800][1078.488s][info][gc,metaspace  ] GC(110) Metaspace: 360805K(392068K)->360805K(392068K) NonClass: 315129K(336616K)->315129K(336616K) Class: 45675K(55452K)->45675K(55452K)
[2025-09-10T17:06:09.982+0800][1078.489s][info][gc            ] GC(110) Pause Young (Concurrent Start) (G1 Humongous Allocation) 533M->507M(1026M) 7.734ms
[2025-09-10T17:06:09.982+0800][1078.489s][info][gc,cpu        ] GC(110) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:06:09.982+0800][1078.489s][info][gc            ] GC(111) Concurrent Cycle
[2025-09-10T17:06:09.983+0800][1078.489s][info][gc,marking    ] GC(111) Concurrent Clear Claimed Marks
[2025-09-10T17:06:09.984+0800][1078.491s][info][gc,marking    ] GC(111) Concurrent Clear Claimed Marks 1.511ms
[2025-09-10T17:06:09.986+0800][1078.492s][info][gc,marking    ] GC(111) Concurrent Scan Root Regions
[2025-09-10T17:06:09.988+0800][1078.495s][info][gc,marking    ] GC(111) Concurrent Scan Root Regions 2.318ms
[2025-09-10T17:06:09.988+0800][1078.495s][info][gc,marking    ] GC(111) Concurrent Mark (1078.495s)
[2025-09-10T17:06:09.990+0800][1078.496s][info][gc,marking    ] GC(111) Concurrent Mark From Roots
[2025-09-10T17:06:09.990+0800][1078.497s][info][gc,task       ] GC(111) Using 4 workers of 4 for marking
[2025-09-10T17:06:10.107+0800][1078.614s][info][gc,marking    ] GC(111) Concurrent Mark From Roots 117.519ms
[2025-09-10T17:06:10.107+0800][1078.614s][info][gc,marking    ] GC(111) Concurrent Preclean
[2025-09-10T17:06:10.108+0800][1078.615s][info][gc,marking    ] GC(111) Concurrent Preclean 0.659ms
[2025-09-10T17:06:10.108+0800][1078.615s][info][gc,marking    ] GC(111) Concurrent Mark (1078.495s, 1078.615s) 120.045ms
[2025-09-10T17:06:10.109+0800][1078.616s][info][gc,start      ] GC(111) Pause Remark
[2025-09-10T17:06:10.121+0800][1078.628s][info][gc,stringtable] GC(111) Cleaned string and symbol table, strings: 148431 processed, 7 removed, symbols: 499688 processed, 83 removed
[2025-09-10T17:06:10.122+0800][1078.628s][info][gc            ] GC(111) Pause Remark 544M->544M(1026M) 12.815ms
[2025-09-10T17:06:10.122+0800][1078.629s][info][gc,cpu        ] GC(111) User=0.16s Sys=0.00s Real=0.02s
[2025-09-10T17:06:10.123+0800][1078.629s][info][gc,marking    ] GC(111) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:10.218+0800][1078.725s][info][gc,marking    ] GC(111) Concurrent Rebuild Remembered Sets 95.225ms
[2025-09-10T17:06:10.218+0800][1078.725s][info][gc,start      ] GC(111) Pause Cleanup
[2025-09-10T17:06:10.219+0800][1078.726s][info][gc            ] GC(111) Pause Cleanup 544M->544M(1026M) 0.695ms
[2025-09-10T17:06:10.219+0800][1078.726s][info][gc,cpu        ] GC(111) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:06:10.219+0800][1078.726s][info][gc,marking    ] GC(111) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:10.220+0800][1078.727s][info][gc,marking    ] GC(111) Concurrent Cleanup for Next Mark 0.867ms
[2025-09-10T17:06:10.220+0800][1078.727s][info][gc            ] GC(111) Concurrent Cycle 238.088ms
[2025-09-10T17:06:10.935+0800][1079.442s][info][gc,start      ] GC(112) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:10.938+0800][1079.445s][info][gc,task       ] GC(112) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:10.951+0800][1079.458s][info][gc,phases     ] GC(112)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:10.951+0800][1079.458s][info][gc,phases     ] GC(112)   Evacuate Collection Set: 8.7ms
[2025-09-10T17:06:10.952+0800][1079.458s][info][gc,phases     ] GC(112)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:06:10.952+0800][1079.458s][info][gc,phases     ] GC(112)   Other: 5.9ms
[2025-09-10T17:06:10.952+0800][1079.459s][info][gc,heap       ] GC(112) Eden regions: 333->0(15)
[2025-09-10T17:06:10.952+0800][1079.459s][info][gc,heap       ] GC(112) Survivor regions: 17->36(44)
[2025-09-10T17:06:10.952+0800][1079.459s][info][gc,heap       ] GC(112) Old regions: 492->492
[2025-09-10T17:06:10.953+0800][1079.459s][info][gc,heap       ] GC(112) Humongous regions: 2->2
[2025-09-10T17:06:10.953+0800][1079.459s][info][gc,metaspace  ] GC(112) Metaspace: 366100K(399108K)->366100K(399108K) NonClass: 319776K(342504K)->319776K(342504K) Class: 46323K(56604K)->46323K(56604K)
[2025-09-10T17:06:10.953+0800][1079.460s][info][gc            ] GC(112) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 841M->527M(1026M) 17.967ms
[2025-09-10T17:06:10.953+0800][1079.460s][info][gc,cpu        ] GC(112) User=0.16s Sys=0.00s Real=0.02s
[2025-09-10T17:06:10.971+0800][1079.478s][info][gc,start      ] GC(113) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:10.973+0800][1079.480s][info][gc,task       ] GC(113) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:10.990+0800][1079.496s][info][gc,phases     ] GC(113)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:06:10.990+0800][1079.497s][info][gc,phases     ] GC(113)   Evacuate Collection Set: 11.3ms
[2025-09-10T17:06:10.990+0800][1079.497s][info][gc,phases     ] GC(113)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:06:10.990+0800][1079.497s][info][gc,phases     ] GC(113)   Other: 5.9ms
[2025-09-10T17:06:10.991+0800][1079.497s][info][gc,heap       ] GC(113) Eden regions: 15->0(327)
[2025-09-10T17:06:10.991+0800][1079.498s][info][gc,heap       ] GC(113) Survivor regions: 36->6(7)
[2025-09-10T17:06:10.991+0800][1079.498s][info][gc,heap       ] GC(113) Old regions: 492->517
[2025-09-10T17:06:10.991+0800][1079.498s][info][gc,heap       ] GC(113) Humongous regions: 2->2
[2025-09-10T17:06:10.991+0800][1079.498s][info][gc,metaspace  ] GC(113) Metaspace: 366413K(399492K)->366413K(399492K) NonClass: 320050K(342760K)->320050K(342760K) Class: 46362K(56732K)->46362K(56732K)
[2025-09-10T17:06:10.992+0800][1079.498s][info][gc            ] GC(113) Pause Young (Mixed) (G1 Evacuation Pause) 542M->522M(1026M) 20.549ms
[2025-09-10T17:06:10.992+0800][1079.498s][info][gc,cpu        ] GC(113) User=0.18s Sys=0.01s Real=0.03s
[2025-09-10T17:06:11.621+0800][1080.128s][info][gc,start      ] GC(114) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:06:11.621+0800][1080.128s][info][gc,task       ] GC(114) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:11.637+0800][1080.144s][info][gc,phases     ] GC(114)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:11.638+0800][1080.144s][info][gc,phases     ] GC(114)   Evacuate Collection Set: 11.3ms
[2025-09-10T17:06:11.638+0800][1080.144s][info][gc,phases     ] GC(114)   Post Evacuate Collection Set: 4.2ms
[2025-09-10T17:06:11.638+0800][1080.145s][info][gc,phases     ] GC(114)   Other: 0.7ms
[2025-09-10T17:06:11.638+0800][1080.145s][info][gc,heap       ] GC(114) Eden regions: 327->0(323)
[2025-09-10T17:06:11.638+0800][1080.145s][info][gc,heap       ] GC(114) Survivor regions: 6->18(42)
[2025-09-10T17:06:11.638+0800][1080.145s][info][gc,heap       ] GC(114) Old regions: 517->517
[2025-09-10T17:06:11.639+0800][1080.145s][info][gc,heap       ] GC(114) Humongous regions: 2->2
[2025-09-10T17:06:11.639+0800][1080.145s][info][gc,metaspace  ] GC(114) Metaspace: 378223K(412548K)->378223K(412548K) NonClass: 330126K(353512K)->330126K(353512K) Class: 48096K(59036K)->48096K(59036K)
[2025-09-10T17:06:11.639+0800][1080.146s][info][gc            ] GC(114) Pause Young (Normal) (G1 Evacuation Pause) 849M->534M(1026M) 17.884ms
[2025-09-10T17:06:11.639+0800][1080.146s][info][gc,cpu        ] GC(114) User=0.27s Sys=0.00s Real=0.01s
[2025-09-10T17:06:13.571+0800][1082.078s][info][gc,start      ] GC(115) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:06:13.571+0800][1082.078s][info][gc,task       ] GC(115) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:13.583+0800][1082.090s][info][gc,phases     ] GC(115)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:13.583+0800][1082.090s][info][gc,phases     ] GC(115)   Evacuate Collection Set: 10.0ms
[2025-09-10T17:06:13.584+0800][1082.090s][info][gc,phases     ] GC(115)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:06:13.584+0800][1082.090s][info][gc,phases     ] GC(115)   Other: 0.6ms
[2025-09-10T17:06:13.584+0800][1082.090s][info][gc,heap       ] GC(115) Eden regions: 323->0(309)
[2025-09-10T17:06:13.585+0800][1082.091s][info][gc,heap       ] GC(115) Survivor regions: 18->37(43)
[2025-09-10T17:06:13.585+0800][1082.091s][info][gc,heap       ] GC(115) Old regions: 517->517
[2025-09-10T17:06:13.585+0800][1082.092s][info][gc,heap       ] GC(115) Humongous regions: 2->2
[2025-09-10T17:06:13.585+0800][1082.092s][info][gc,metaspace  ] GC(115) Metaspace: 389340K(425476K)->389340K(425476K) NonClass: 339974K(364520K)->339974K(364520K) Class: 49365K(60956K)->49365K(60956K)
[2025-09-10T17:06:13.585+0800][1082.092s][info][gc            ] GC(115) Pause Young (Normal) (G1 Evacuation Pause) 857M->553M(1026M) 14.116ms
[2025-09-10T17:06:13.585+0800][1082.092s][info][gc,cpu        ] GC(115) User=0.16s Sys=0.01s Real=0.02s
[2025-09-10T17:06:13.807+0800][1082.314s][info][gc,start      ] GC(116) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:06:13.808+0800][1082.314s][info][gc,task       ] GC(116) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:13.829+0800][1082.336s][info][gc,phases     ] GC(116)   Pre Evacuate Collection Set: 1.2ms
[2025-09-10T17:06:13.829+0800][1082.336s][info][gc,phases     ] GC(116)   Evacuate Collection Set: 18.3ms
[2025-09-10T17:06:13.829+0800][1082.336s][info][gc,phases     ] GC(116)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:06:13.829+0800][1082.336s][info][gc,phases     ] GC(116)   Other: 0.7ms
[2025-09-10T17:06:13.830+0800][1082.336s][info][gc,heap       ] GC(116) Eden regions: 309->0(307)
[2025-09-10T17:06:13.830+0800][1082.336s][info][gc,heap       ] GC(116) Survivor regions: 37->31(44)
[2025-09-10T17:06:13.830+0800][1082.336s][info][gc,heap       ] GC(116) Old regions: 517->533
[2025-09-10T17:06:13.830+0800][1082.337s][info][gc,heap       ] GC(116) Humongous regions: 2->2
[2025-09-10T17:06:13.830+0800][1082.337s][info][gc,metaspace  ] GC(116) Metaspace: 397021K(434180K)->397021K(434180K) NonClass: 346772K(371944K)->346772K(371944K) Class: 50249K(62236K)->50249K(62236K)
[2025-09-10T17:06:13.830+0800][1082.337s][info][gc            ] GC(116) Pause Young (Concurrent Start) (G1 Evacuation Pause) 862M->564M(1026M) 22.882ms
[2025-09-10T17:06:13.830+0800][1082.337s][info][gc,cpu        ] GC(116) User=0.33s Sys=0.00s Real=0.02s
[2025-09-10T17:06:13.830+0800][1082.337s][info][gc            ] GC(117) Concurrent Cycle
[2025-09-10T17:06:13.831+0800][1082.338s][info][gc,marking    ] GC(117) Concurrent Clear Claimed Marks
[2025-09-10T17:06:13.837+0800][1082.344s][info][gc,marking    ] GC(117) Concurrent Clear Claimed Marks 6.158ms
[2025-09-10T17:06:13.838+0800][1082.345s][info][gc,marking    ] GC(117) Concurrent Scan Root Regions
[2025-09-10T17:06:13.846+0800][1082.353s][info][gc,marking    ] GC(117) Concurrent Scan Root Regions 7.992ms
[2025-09-10T17:06:13.847+0800][1082.354s][info][gc,marking    ] GC(117) Concurrent Mark (1082.354s)
[2025-09-10T17:06:13.847+0800][1082.354s][info][gc,marking    ] GC(117) Concurrent Mark From Roots
[2025-09-10T17:06:13.848+0800][1082.354s][info][gc,task       ] GC(117) Using 4 workers of 4 for marking
[2025-09-10T17:06:14.104+0800][1082.610s][info][gc,marking    ] GC(117) Concurrent Mark From Roots 256.380ms
[2025-09-10T17:06:14.104+0800][1082.611s][info][gc,marking    ] GC(117) Concurrent Preclean
[2025-09-10T17:06:14.106+0800][1082.612s][info][gc,marking    ] GC(117) Concurrent Preclean 1.693ms
[2025-09-10T17:06:14.106+0800][1082.613s][info][gc,marking    ] GC(117) Concurrent Mark (1082.354s, 1082.613s) 258.987ms
[2025-09-10T17:06:14.107+0800][1082.614s][info][gc,start      ] GC(117) Pause Remark
[2025-09-10T17:06:14.122+0800][1082.628s][info][gc,stringtable] GC(117) Cleaned string and symbol table, strings: 152956 processed, 15 removed, symbols: 503846 processed, 2164 removed
[2025-09-10T17:06:14.122+0800][1082.629s][info][gc            ] GC(117) Pause Remark 782M->782M(1026M) 15.428ms
[2025-09-10T17:06:14.122+0800][1082.629s][info][gc,cpu        ] GC(117) User=0.25s Sys=0.00s Real=0.02s
[2025-09-10T17:06:14.123+0800][1082.629s][info][gc,marking    ] GC(117) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:14.262+0800][1082.769s][info][gc,marking    ] GC(117) Concurrent Rebuild Remembered Sets 139.345ms
[2025-09-10T17:06:14.262+0800][1082.769s][info][gc,start      ] GC(117) Pause Cleanup
[2025-09-10T17:06:14.263+0800][1082.770s][info][gc            ] GC(117) Pause Cleanup 844M->844M(1026M) 0.753ms
[2025-09-10T17:06:14.263+0800][1082.770s][info][gc,cpu        ] GC(117) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:06:14.264+0800][1082.770s][info][gc,marking    ] GC(117) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:14.265+0800][1082.772s][info][gc,marking    ] GC(117) Concurrent Cleanup for Next Mark 1.143ms
[2025-09-10T17:06:14.265+0800][1082.772s][info][gc            ] GC(117) Concurrent Cycle 434.546ms
[2025-09-10T17:06:14.318+0800][1082.825s][info][gc,start      ] GC(118) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:14.318+0800][1082.825s][info][gc,task       ] GC(118) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:14.330+0800][1082.837s][info][gc,phases     ] GC(118)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:14.330+0800][1082.837s][info][gc,phases     ] GC(118)   Evacuate Collection Set: 10.0ms
[2025-09-10T17:06:14.330+0800][1082.837s][info][gc,phases     ] GC(118)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:06:14.331+0800][1082.837s][info][gc,phases     ] GC(118)   Other: 0.7ms
[2025-09-10T17:06:14.331+0800][1082.837s][info][gc,heap       ] GC(118) Eden regions: 307->0(27)
[2025-09-10T17:06:14.331+0800][1082.837s][info][gc,heap       ] GC(118) Survivor regions: 31->24(43)
[2025-09-10T17:06:14.331+0800][1082.838s][info][gc,heap       ] GC(118) Old regions: 533->552
[2025-09-10T17:06:14.331+0800][1082.838s][info][gc,heap       ] GC(118) Humongous regions: 2->2
[2025-09-10T17:06:14.331+0800][1082.838s][info][gc,metaspace  ] GC(118) Metaspace: 404776K(441860K)->404776K(441860K) NonClass: 353555K(378600K)->353555K(378600K) Class: 51220K(63260K)->51220K(63260K)
[2025-09-10T17:06:14.331+0800][1082.838s][info][gc            ] GC(118) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 871M->575M(1026M) 12.995ms
[2025-09-10T17:06:14.331+0800][1082.838s][info][gc,cpu        ] GC(118) User=0.17s Sys=0.00s Real=0.01s
[2025-09-10T17:06:14.366+0800][1082.873s][info][gc,start      ] GC(119) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:14.367+0800][1082.873s][info][gc,task       ] GC(119) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:14.380+0800][1082.886s][info][gc,phases     ] GC(119)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T17:06:14.380+0800][1082.887s][info][gc,phases     ] GC(119)   Evacuate Collection Set: 11.4ms
[2025-09-10T17:06:14.380+0800][1082.887s][info][gc,phases     ] GC(119)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:06:14.381+0800][1082.887s][info][gc,phases     ] GC(119)   Other: 0.8ms
[2025-09-10T17:06:14.381+0800][1082.888s][info][gc,heap       ] GC(119) Eden regions: 27->0(304)
[2025-09-10T17:06:14.381+0800][1082.888s][info][gc,heap       ] GC(119) Survivor regions: 24->5(7)
[2025-09-10T17:06:14.381+0800][1082.888s][info][gc,heap       ] GC(119) Old regions: 552->566
[2025-09-10T17:06:14.382+0800][1082.888s][info][gc,heap       ] GC(119) Humongous regions: 2->2
[2025-09-10T17:06:14.382+0800][1082.888s][info][gc,metaspace  ] GC(119) Metaspace: 405560K(443012K)->405560K(443012K) NonClass: 354256K(379624K)->354256K(379624K) Class: 51304K(63388K)->51304K(63388K)
[2025-09-10T17:06:14.382+0800][1082.889s][info][gc            ] GC(119) Pause Young (Mixed) (G1 Evacuation Pause) 602M->570M(1026M) 15.538ms
[2025-09-10T17:06:14.382+0800][1082.889s][info][gc,cpu        ] GC(119) User=0.17s Sys=0.01s Real=0.02s
[2025-09-10T17:06:14.730+0800][1083.237s][info][gc,start      ] GC(120) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:06:14.730+0800][1083.237s][info][gc,task       ] GC(120) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:14.740+0800][1083.246s][info][gc,phases     ] GC(120)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:14.740+0800][1083.247s][info][gc,phases     ] GC(120)   Evacuate Collection Set: 7.2ms
[2025-09-10T17:06:14.740+0800][1083.247s][info][gc,phases     ] GC(120)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:14.740+0800][1083.247s][info][gc,phases     ] GC(120)   Other: 0.5ms
[2025-09-10T17:06:14.740+0800][1083.247s][info][gc,heap       ] GC(120) Eden regions: 151->0(307)
[2025-09-10T17:06:14.741+0800][1083.247s][info][gc,heap       ] GC(120) Survivor regions: 5->8(39)
[2025-09-10T17:06:14.741+0800][1083.247s][info][gc,heap       ] GC(120) Old regions: 566->566
[2025-09-10T17:06:14.741+0800][1083.248s][info][gc,heap       ] GC(120) Humongous regions: 2->2
[2025-09-10T17:06:14.741+0800][1083.248s][info][gc,metaspace  ] GC(120) Metaspace: 410992K(448900K)->410992K(448900K) NonClass: 359105K(384744K)->359105K(384744K) Class: 51887K(64156K)->51887K(64156K)
[2025-09-10T17:06:14.741+0800][1083.248s][info][gc            ] GC(120) Pause Young (Concurrent Start) (G1 Humongous Allocation) 721M->574M(1026M) 10.735ms
[2025-09-10T17:06:14.741+0800][1083.248s][info][gc,cpu        ] GC(120) User=0.09s Sys=0.00s Real=0.02s
[2025-09-10T17:06:14.741+0800][1083.248s][info][gc            ] GC(121) Concurrent Cycle
[2025-09-10T17:06:14.741+0800][1083.248s][info][gc,marking    ] GC(121) Concurrent Clear Claimed Marks
[2025-09-10T17:06:14.743+0800][1083.249s][info][gc,marking    ] GC(121) Concurrent Clear Claimed Marks 1.040ms
[2025-09-10T17:06:14.743+0800][1083.249s][info][gc,marking    ] GC(121) Concurrent Scan Root Regions
[2025-09-10T17:06:14.746+0800][1083.252s][info][gc,marking    ] GC(121) Concurrent Scan Root Regions 2.945ms
[2025-09-10T17:06:14.746+0800][1083.253s][info][gc,marking    ] GC(121) Concurrent Mark (1083.253s)
[2025-09-10T17:06:14.746+0800][1083.253s][info][gc,marking    ] GC(121) Concurrent Mark From Roots
[2025-09-10T17:06:14.747+0800][1083.253s][info][gc,task       ] GC(121) Using 4 workers of 4 for marking
[2025-09-10T17:06:14.880+0800][1083.386s][info][gc,marking    ] GC(121) Concurrent Mark From Roots 133.428ms
[2025-09-10T17:06:14.880+0800][1083.387s][info][gc,marking    ] GC(121) Concurrent Preclean
[2025-09-10T17:06:14.881+0800][1083.388s][info][gc,marking    ] GC(121) Concurrent Preclean 0.975ms
[2025-09-10T17:06:14.881+0800][1083.388s][info][gc,marking    ] GC(121) Concurrent Mark (1083.253s, 1083.388s) 135.321ms
[2025-09-10T17:06:14.883+0800][1083.389s][info][gc,start      ] GC(121) Pause Remark
[2025-09-10T17:06:14.894+0800][1083.401s][info][gc,stringtable] GC(121) Cleaned string and symbol table, strings: 153131 processed, 21 removed, symbols: 502749 processed, 749 removed
[2025-09-10T17:06:14.895+0800][1083.401s][info][gc            ] GC(121) Pause Remark 594M->594M(1026M) 11.988ms
[2025-09-10T17:06:14.895+0800][1083.402s][info][gc,cpu        ] GC(121) User=0.15s Sys=0.00s Real=0.01s
[2025-09-10T17:06:14.895+0800][1083.402s][info][gc,marking    ] GC(121) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:15.020+0800][1083.527s][info][gc,marking    ] GC(121) Concurrent Rebuild Remembered Sets 125.045ms
[2025-09-10T17:06:15.022+0800][1083.529s][info][gc,start      ] GC(121) Pause Cleanup
[2025-09-10T17:06:15.023+0800][1083.530s][info][gc            ] GC(121) Pause Cleanup 659M->659M(1026M) 1.018ms
[2025-09-10T17:06:15.023+0800][1083.530s][info][gc,cpu        ] GC(121) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:06:15.024+0800][1083.530s][info][gc,marking    ] GC(121) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:15.024+0800][1083.531s][info][gc,marking    ] GC(121) Concurrent Cleanup for Next Mark 0.890ms
[2025-09-10T17:06:15.025+0800][1083.532s][info][gc            ] GC(121) Concurrent Cycle 283.733ms
[2025-09-10T17:06:15.252+0800][1083.759s][info][gc,start      ] GC(122) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:15.252+0800][1083.759s][info][gc,task       ] GC(122) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:15.260+0800][1083.767s][info][gc,phases     ] GC(122)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:15.260+0800][1083.767s][info][gc,phases     ] GC(122)   Evacuate Collection Set: 6.5ms
[2025-09-10T17:06:15.261+0800][1083.767s][info][gc,phases     ] GC(122)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:06:15.261+0800][1083.767s][info][gc,phases     ] GC(122)   Other: 0.6ms
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc,heap       ] GC(122) Eden regions: 307->0(30)
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc,heap       ] GC(122) Survivor regions: 8->21(40)
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc,heap       ] GC(122) Old regions: 566->566
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc,heap       ] GC(122) Humongous regions: 3->2
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc,metaspace  ] GC(122) Metaspace: 421337K(460036K)->421337K(460036K) NonClass: 368402K(394728K)->368402K(394728K) Class: 52935K(65308K)->52935K(65308K)
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc            ] GC(122) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 882M->586M(1026M) 9.277ms
[2025-09-10T17:06:15.261+0800][1083.768s][info][gc,cpu        ] GC(122) User=0.10s Sys=0.00s Real=0.01s
[2025-09-10T17:06:15.295+0800][1083.802s][info][gc,start      ] GC(123) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:15.296+0800][1083.803s][info][gc,task       ] GC(123) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:15.308+0800][1083.815s][info][gc,phases     ] GC(123)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T17:06:15.308+0800][1083.815s][info][gc,phases     ] GC(123)   Evacuate Collection Set: 10.3ms
[2025-09-10T17:06:15.308+0800][1083.815s][info][gc,phases     ] GC(123)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:15.309+0800][1083.815s][info][gc,phases     ] GC(123)   Other: 0.8ms
[2025-09-10T17:06:15.309+0800][1083.815s][info][gc,heap       ] GC(123) Eden regions: 30->0(299)
[2025-09-10T17:06:15.309+0800][1083.816s][info][gc,heap       ] GC(123) Survivor regions: 21->4(7)
[2025-09-10T17:06:15.309+0800][1083.816s][info][gc,heap       ] GC(123) Old regions: 566->579
[2025-09-10T17:06:15.309+0800][1083.816s][info][gc,heap       ] GC(123) Humongous regions: 2->2
[2025-09-10T17:06:15.309+0800][1083.816s][info][gc,metaspace  ] GC(123) Metaspace: 421929K(460676K)->421929K(460676K) NonClass: 368924K(395240K)->368924K(395240K) Class: 53004K(65436K)->53004K(65436K)
[2025-09-10T17:06:15.309+0800][1083.816s][info][gc            ] GC(123) Pause Young (Mixed) (G1 Evacuation Pause) 616M->583M(1026M) 13.859ms
[2025-09-10T17:06:15.310+0800][1083.817s][info][gc,cpu        ] GC(123) User=0.17s Sys=0.00s Real=0.01s
[2025-09-10T17:06:16.045+0800][1084.552s][info][gc,start      ] GC(124) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:06:16.046+0800][1084.553s][info][gc,task       ] GC(124) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:16.056+0800][1084.563s][info][gc,phases     ] GC(124)   Pre Evacuate Collection Set: 1.3ms
[2025-09-10T17:06:16.058+0800][1084.565s][info][gc,phases     ] GC(124)   Evacuate Collection Set: 7.2ms
[2025-09-10T17:06:16.058+0800][1084.565s][info][gc,phases     ] GC(124)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:06:16.059+0800][1084.565s][info][gc,phases     ] GC(124)   Other: 1.1ms
[2025-09-10T17:06:16.059+0800][1084.566s][info][gc,heap       ] GC(124) Eden regions: 299->0(293)
[2025-09-10T17:06:16.059+0800][1084.566s][info][gc,heap       ] GC(124) Survivor regions: 4->13(38)
[2025-09-10T17:06:16.059+0800][1084.566s][info][gc,heap       ] GC(124) Old regions: 579->579
[2025-09-10T17:06:16.059+0800][1084.566s][info][gc,heap       ] GC(124) Humongous regions: 2->2
[2025-09-10T17:06:16.060+0800][1084.566s][info][gc,metaspace  ] GC(124) Metaspace: 428717K(467460K)->428717K(467460K) NonClass: 374862K(401128K)->374862K(401128K) Class: 53855K(66332K)->53855K(66332K)
[2025-09-10T17:06:16.060+0800][1084.567s][info][gc            ] GC(124) Pause Young (Concurrent Start) (G1 Evacuation Pause) 882M->591M(1026M) 14.438ms
[2025-09-10T17:06:16.060+0800][1084.567s][info][gc,cpu        ] GC(124) User=0.10s Sys=0.00s Real=0.02s
[2025-09-10T17:06:16.060+0800][1084.567s][info][gc            ] GC(125) Concurrent Cycle
[2025-09-10T17:06:16.061+0800][1084.567s][info][gc,marking    ] GC(125) Concurrent Clear Claimed Marks
[2025-09-10T17:06:16.062+0800][1084.568s][info][gc,marking    ] GC(125) Concurrent Clear Claimed Marks 1.127ms
[2025-09-10T17:06:16.062+0800][1084.569s][info][gc,marking    ] GC(125) Concurrent Scan Root Regions
[2025-09-10T17:06:16.065+0800][1084.572s][info][gc,marking    ] GC(125) Concurrent Scan Root Regions 2.874ms
[2025-09-10T17:06:16.065+0800][1084.572s][info][gc,marking    ] GC(125) Concurrent Mark (1084.572s)
[2025-09-10T17:06:16.065+0800][1084.572s][info][gc,marking    ] GC(125) Concurrent Mark From Roots
[2025-09-10T17:06:16.065+0800][1084.572s][info][gc,task       ] GC(125) Using 4 workers of 4 for marking
[2025-09-10T17:06:16.213+0800][1084.720s][info][gc,marking    ] GC(125) Concurrent Mark From Roots 147.738ms
[2025-09-10T17:06:16.213+0800][1084.720s][info][gc,marking    ] GC(125) Concurrent Preclean
[2025-09-10T17:06:16.214+0800][1084.721s][info][gc,marking    ] GC(125) Concurrent Preclean 0.875ms
[2025-09-10T17:06:16.214+0800][1084.721s][info][gc,marking    ] GC(125) Concurrent Mark (1084.572s, 1084.721s) 149.190ms
[2025-09-10T17:06:16.215+0800][1084.721s][info][gc,start      ] GC(125) Pause Remark
[2025-09-10T17:06:16.225+0800][1084.732s][info][gc,stringtable] GC(125) Cleaned string and symbol table, strings: 153770 processed, 1 removed, symbols: 504203 processed, 1373 removed
[2025-09-10T17:06:16.226+0800][1084.733s][info][gc            ] GC(125) Pause Remark 695M->695M(1026M) 11.691ms
[2025-09-10T17:06:16.227+0800][1084.733s][info][gc,cpu        ] GC(125) User=0.14s Sys=0.00s Real=0.01s
[2025-09-10T17:06:16.227+0800][1084.734s][info][gc,marking    ] GC(125) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:16.350+0800][1084.857s][info][gc,marking    ] GC(125) Concurrent Rebuild Remembered Sets 122.911ms
[2025-09-10T17:06:16.351+0800][1084.857s][info][gc,start      ] GC(125) Pause Cleanup
[2025-09-10T17:06:16.351+0800][1084.858s][info][gc            ] GC(125) Pause Cleanup 709M->709M(1026M) 0.895ms
[2025-09-10T17:06:16.352+0800][1084.858s][info][gc,cpu        ] GC(125) User=0.01s Sys=0.00s Real=0.01s
[2025-09-10T17:06:16.352+0800][1084.859s][info][gc,marking    ] GC(125) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:16.353+0800][1084.860s][info][gc,marking    ] GC(125) Concurrent Cleanup for Next Mark 0.949ms
[2025-09-10T17:06:16.353+0800][1084.860s][info][gc            ] GC(125) Concurrent Cycle 292.731ms
[2025-09-10T17:06:17.005+0800][1085.512s][info][gc,start      ] GC(126) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:17.006+0800][1085.512s][info][gc,task       ] GC(126) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:17.014+0800][1085.520s][info][gc,phases     ] GC(126)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:17.014+0800][1085.521s][info][gc,phases     ] GC(126)   Evacuate Collection Set: 5.3ms
[2025-09-10T17:06:17.014+0800][1085.521s][info][gc,phases     ] GC(126)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T17:06:17.015+0800][1085.521s][info][gc,phases     ] GC(126)   Other: 1.2ms
[2025-09-10T17:06:17.015+0800][1085.522s][info][gc,heap       ] GC(126) Eden regions: 293->0(32)
[2025-09-10T17:06:17.015+0800][1085.522s][info][gc,heap       ] GC(126) Survivor regions: 13->19(39)
[2025-09-10T17:06:17.015+0800][1085.522s][info][gc,heap       ] GC(126) Old regions: 579->579
[2025-09-10T17:06:17.015+0800][1085.522s][info][gc,heap       ] GC(126) Humongous regions: 2->2
[2025-09-10T17:06:17.015+0800][1085.522s][info][gc,metaspace  ] GC(126) Metaspace: 430670K(469380K)->430670K(469380K) NonClass: 376537K(402664K)->376537K(402664K) Class: 54133K(66716K)->54133K(66716K)
[2025-09-10T17:06:17.016+0800][1085.522s][info][gc            ] GC(126) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 884M->598M(1026M) 10.462ms
[2025-09-10T17:06:17.016+0800][1085.523s][info][gc,cpu        ] GC(126) User=0.10s Sys=0.00s Real=0.01s
[2025-09-10T17:06:17.061+0800][1085.567s][info][gc,start      ] GC(127) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:17.061+0800][1085.568s][info][gc,task       ] GC(127) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:17.072+0800][1085.579s][info][gc,phases     ] GC(127)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:06:17.073+0800][1085.579s][info][gc,phases     ] GC(127)   Evacuate Collection Set: 9.4ms
[2025-09-10T17:06:17.073+0800][1085.579s][info][gc,phases     ] GC(127)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:17.073+0800][1085.580s][info][gc,phases     ] GC(127)   Other: 1.0ms
[2025-09-10T17:06:17.073+0800][1085.580s][info][gc,heap       ] GC(127) Eden regions: 32->0(296)
[2025-09-10T17:06:17.073+0800][1085.580s][info][gc,heap       ] GC(127) Survivor regions: 19->3(7)
[2025-09-10T17:06:17.073+0800][1085.580s][info][gc,heap       ] GC(127) Old regions: 579->591
[2025-09-10T17:06:17.074+0800][1085.580s][info][gc,heap       ] GC(127) Humongous regions: 2->2
[2025-09-10T17:06:17.074+0800][1085.581s][info][gc,metaspace  ] GC(127) Metaspace: 430677K(469380K)->430677K(469380K) NonClass: 376543K(402664K)->376543K(402664K) Class: 54133K(66716K)->54133K(66716K)
[2025-09-10T17:06:17.074+0800][1085.581s][info][gc            ] GC(127) Pause Young (Mixed) (G1 Evacuation Pause) 630M->594M(1026M) 13.459ms
[2025-09-10T17:06:17.074+0800][1085.581s][info][gc,cpu        ] GC(127) User=0.15s Sys=0.01s Real=0.01s
[2025-09-10T17:06:18.035+0800][1086.541s][info][gc,start      ] GC(128) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:06:18.035+0800][1086.542s][info][gc,task       ] GC(128) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:18.043+0800][1086.549s][info][gc,phases     ] GC(128)   Pre Evacuate Collection Set: 0.8ms
[2025-09-10T17:06:18.043+0800][1086.550s][info][gc,phases     ] GC(128)   Evacuate Collection Set: 5.7ms
[2025-09-10T17:06:18.044+0800][1086.551s][info][gc,phases     ] GC(128)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:18.045+0800][1086.551s][info][gc,phases     ] GC(128)   Other: 0.7ms
[2025-09-10T17:06:18.045+0800][1086.552s][info][gc,heap       ] GC(128) Eden regions: 296->0(292)
[2025-09-10T17:06:18.045+0800][1086.552s][info][gc,heap       ] GC(128) Survivor regions: 3->8(38)
[2025-09-10T17:06:18.045+0800][1086.552s][info][gc,heap       ] GC(128) Old regions: 591->591
[2025-09-10T17:06:18.045+0800][1086.552s][info][gc,heap       ] GC(128) Humongous regions: 2->2
[2025-09-10T17:06:18.045+0800][1086.552s][info][gc,metaspace  ] GC(128) Metaspace: 434543K(473348K)->434543K(473348K) NonClass: 379913K(406248K)->379913K(406248K) Class: 54629K(67100K)->54629K(67100K)
[2025-09-10T17:06:18.046+0800][1086.552s][info][gc            ] GC(128) Pause Young (Concurrent Start) (G1 Evacuation Pause) 890M->599M(1026M) 11.222ms
[2025-09-10T17:06:18.046+0800][1086.553s][info][gc,cpu        ] GC(128) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:06:18.046+0800][1086.553s][info][gc            ] GC(129) Concurrent Cycle
[2025-09-10T17:06:18.047+0800][1086.553s][info][gc,marking    ] GC(129) Concurrent Clear Claimed Marks
[2025-09-10T17:06:18.048+0800][1086.555s][info][gc,marking    ] GC(129) Concurrent Clear Claimed Marks 1.586ms
[2025-09-10T17:06:18.048+0800][1086.555s][info][gc,marking    ] GC(129) Concurrent Scan Root Regions
[2025-09-10T17:06:18.051+0800][1086.558s][info][gc,marking    ] GC(129) Concurrent Scan Root Regions 2.668ms
[2025-09-10T17:06:18.051+0800][1086.558s][info][gc,marking    ] GC(129) Concurrent Mark (1086.558s)
[2025-09-10T17:06:18.051+0800][1086.558s][info][gc,marking    ] GC(129) Concurrent Mark From Roots
[2025-09-10T17:06:18.052+0800][1086.558s][info][gc,task       ] GC(129) Using 4 workers of 4 for marking
[2025-09-10T17:06:18.187+0800][1086.693s][info][gc,marking    ] GC(129) Concurrent Mark From Roots 135.220ms
[2025-09-10T17:06:18.187+0800][1086.694s][info][gc,marking    ] GC(129) Concurrent Preclean
[2025-09-10T17:06:18.190+0800][1086.696s][info][gc,marking    ] GC(129) Concurrent Preclean 2.740ms
[2025-09-10T17:06:18.190+0800][1086.697s][info][gc,marking    ] GC(129) Concurrent Mark (1086.558s, 1086.697s) 138.693ms
[2025-09-10T17:06:18.190+0800][1086.697s][info][gc,start      ] GC(129) Pause Remark
[2025-09-10T17:06:18.202+0800][1086.709s][info][gc,stringtable] GC(129) Cleaned string and symbol table, strings: 153969 processed, 0 removed, symbols: 503457 processed, 448 removed
[2025-09-10T17:06:18.203+0800][1086.710s][info][gc            ] GC(129) Pause Remark 662M->662M(1026M) 12.619ms
[2025-09-10T17:06:18.204+0800][1086.711s][info][gc,cpu        ] GC(129) User=0.15s Sys=0.00s Real=0.02s
[2025-09-10T17:06:18.204+0800][1086.711s][info][gc,marking    ] GC(129) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:18.331+0800][1086.838s][info][gc,marking    ] GC(129) Concurrent Rebuild Remembered Sets 126.970ms
[2025-09-10T17:06:18.332+0800][1086.838s][info][gc,start      ] GC(129) Pause Cleanup
[2025-09-10T17:06:18.333+0800][1086.839s][info][gc            ] GC(129) Pause Cleanup 706M->706M(1026M) 0.896ms
[2025-09-10T17:06:18.333+0800][1086.840s][info][gc,cpu        ] GC(129) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:06:18.333+0800][1086.840s][info][gc,marking    ] GC(129) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:18.334+0800][1086.841s][info][gc,marking    ] GC(129) Concurrent Cleanup for Next Mark 0.964ms
[2025-09-10T17:06:18.337+0800][1086.844s][info][gc            ] GC(129) Concurrent Cycle 290.958ms
[2025-09-10T17:06:18.848+0800][1087.355s][info][gc,start      ] GC(130) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:18.848+0800][1087.355s][info][gc,task       ] GC(130) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:18.855+0800][1087.361s][info][gc,phases     ] GC(130)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:18.855+0800][1087.362s][info][gc,phases     ] GC(130)   Evacuate Collection Set: 4.6ms
[2025-09-10T17:06:18.855+0800][1087.362s][info][gc,phases     ] GC(130)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:18.855+0800][1087.362s][info][gc,phases     ] GC(130)   Other: 1.0ms
[2025-09-10T17:06:18.855+0800][1087.362s][info][gc,heap       ] GC(130) Eden regions: 292->0(30)
[2025-09-10T17:06:18.855+0800][1087.362s][info][gc,heap       ] GC(130) Survivor regions: 8->21(38)
[2025-09-10T17:06:18.856+0800][1087.362s][info][gc,heap       ] GC(130) Old regions: 591->591
[2025-09-10T17:06:18.856+0800][1087.363s][info][gc,heap       ] GC(130) Humongous regions: 3->2
[2025-09-10T17:06:18.856+0800][1087.363s][info][gc,metaspace  ] GC(130) Metaspace: 435691K(474628K)->435691K(474628K) NonClass: 380905K(407272K)->380905K(407272K) Class: 54786K(67356K)->54786K(67356K)
[2025-09-10T17:06:18.856+0800][1087.363s][info][gc            ] GC(130) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 892M->611M(1026M) 7.970ms
[2025-09-10T17:06:18.856+0800][1087.363s][info][gc,cpu        ] GC(130) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:06:18.904+0800][1087.411s][info][gc,start      ] GC(131) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:18.904+0800][1087.411s][info][gc,task       ] GC(131) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:18.922+0800][1087.428s][info][gc,phases     ] GC(131)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T17:06:18.922+0800][1087.429s][info][gc,phases     ] GC(131)   Evacuate Collection Set: 14.8ms
[2025-09-10T17:06:18.922+0800][1087.429s][info][gc,phases     ] GC(131)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:06:18.922+0800][1087.429s][info][gc,phases     ] GC(131)   Other: 0.7ms
[2025-09-10T17:06:18.922+0800][1087.429s][info][gc,heap       ] GC(131) Eden regions: 30->0(285)
[2025-09-10T17:06:18.922+0800][1087.429s][info][gc,heap       ] GC(131) Survivor regions: 21->3(7)
[2025-09-10T17:06:18.923+0800][1087.429s][info][gc,heap       ] GC(131) Old regions: 591->600
[2025-09-10T17:06:18.923+0800][1087.429s][info][gc,heap       ] GC(131) Humongous regions: 2->2
[2025-09-10T17:06:18.923+0800][1087.429s][info][gc,metaspace  ] GC(131) Metaspace: 435691K(474628K)->435691K(474628K) NonClass: 380905K(407272K)->380905K(407272K) Class: 54786K(67356K)->54786K(67356K)
[2025-09-10T17:06:18.923+0800][1087.430s][info][gc            ] GC(131) Pause Young (Mixed) (G1 Evacuation Pause) 641M->602M(1026M) 18.733ms
[2025-09-10T17:06:18.923+0800][1087.430s][info][gc,cpu        ] GC(131) User=0.22s Sys=0.00s Real=0.02s
[2025-09-10T17:06:19.791+0800][1088.298s][info][gc,start      ] GC(132) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:06:19.791+0800][1088.298s][info][gc,task       ] GC(132) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:19.801+0800][1088.308s][info][gc,phases     ] GC(132)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:19.802+0800][1088.308s][info][gc,phases     ] GC(132)   Evacuate Collection Set: 7.3ms
[2025-09-10T17:06:19.802+0800][1088.308s][info][gc,phases     ] GC(132)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:06:19.802+0800][1088.309s][info][gc,phases     ] GC(132)   Other: 0.5ms
[2025-09-10T17:06:19.802+0800][1088.309s][info][gc,heap       ] GC(132) Eden regions: 285->0(275)
[2025-09-10T17:06:19.802+0800][1088.309s][info][gc,heap       ] GC(132) Survivor regions: 3->12(36)
[2025-09-10T17:06:19.802+0800][1088.309s][info][gc,heap       ] GC(132) Old regions: 600->600
[2025-09-10T17:06:19.802+0800][1088.309s][info][gc,heap       ] GC(132) Humongous regions: 2->2
[2025-09-10T17:06:19.803+0800][1088.309s][info][gc,metaspace  ] GC(132) Metaspace: 436846K(476036K)->436846K(476036K) NonClass: 381909K(408552K)->381909K(408552K) Class: 54937K(67484K)->54937K(67484K)
[2025-09-10T17:06:19.803+0800][1088.310s][info][gc            ] GC(132) Pause Young (Concurrent Start) (G1 Evacuation Pause) 887M->611M(1026M) 11.508ms
[2025-09-10T17:06:19.803+0800][1088.310s][info][gc,cpu        ] GC(132) User=0.10s Sys=0.00s Real=0.02s
[2025-09-10T17:06:19.803+0800][1088.310s][info][gc            ] GC(133) Concurrent Cycle
[2025-09-10T17:06:19.803+0800][1088.310s][info][gc,marking    ] GC(133) Concurrent Clear Claimed Marks
[2025-09-10T17:06:19.804+0800][1088.311s][info][gc,marking    ] GC(133) Concurrent Clear Claimed Marks 1.119ms
[2025-09-10T17:06:19.805+0800][1088.311s][info][gc,marking    ] GC(133) Concurrent Scan Root Regions
[2025-09-10T17:06:19.807+0800][1088.314s][info][gc,marking    ] GC(133) Concurrent Scan Root Regions 2.380ms
[2025-09-10T17:06:19.807+0800][1088.314s][info][gc,marking    ] GC(133) Concurrent Mark (1088.314s)
[2025-09-10T17:06:19.807+0800][1088.314s][info][gc,marking    ] GC(133) Concurrent Mark From Roots
[2025-09-10T17:06:19.807+0800][1088.314s][info][gc,task       ] GC(133) Using 4 workers of 4 for marking
[2025-09-10T17:06:19.966+0800][1088.473s][info][gc,marking    ] GC(133) Concurrent Mark From Roots 158.537ms
[2025-09-10T17:06:19.966+0800][1088.473s][info][gc,marking    ] GC(133) Concurrent Preclean
[2025-09-10T17:06:19.967+0800][1088.474s][info][gc,marking    ] GC(133) Concurrent Preclean 0.907ms
[2025-09-10T17:06:19.967+0800][1088.474s][info][gc,marking    ] GC(133) Concurrent Mark (1088.314s, 1088.474s) 160.060ms
[2025-09-10T17:06:19.968+0800][1088.475s][info][gc,start      ] GC(133) Pause Remark
[2025-09-10T17:06:19.979+0800][1088.486s][info][gc,stringtable] GC(133) Cleaned string and symbol table, strings: 200227 processed, 41 removed, symbols: 503244 processed, 207 removed
[2025-09-10T17:06:19.980+0800][1088.487s][info][gc            ] GC(133) Pause Remark 666M->666M(1026M) 12.142ms
[2025-09-10T17:06:19.980+0800][1088.487s][info][gc,cpu        ] GC(133) User=0.14s Sys=0.00s Real=0.02s
[2025-09-10T17:06:19.981+0800][1088.487s][info][gc,marking    ] GC(133) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:20.115+0800][1088.621s][info][gc,marking    ] GC(133) Concurrent Rebuild Remembered Sets 134.040ms
[2025-09-10T17:06:20.115+0800][1088.622s][info][gc,start      ] GC(133) Pause Cleanup
[2025-09-10T17:06:20.116+0800][1088.623s][info][gc            ] GC(133) Pause Cleanup 717M->717M(1026M) 0.789ms
[2025-09-10T17:06:20.116+0800][1088.623s][info][gc,cpu        ] GC(133) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:06:20.116+0800][1088.623s][info][gc,marking    ] GC(133) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:20.117+0800][1088.624s][info][gc,marking    ] GC(133) Concurrent Cleanup for Next Mark 0.819ms
[2025-09-10T17:06:20.117+0800][1088.624s][info][gc            ] GC(133) Concurrent Cycle 314.167ms
[2025-09-10T17:06:20.522+0800][1089.028s][info][gc,start      ] GC(134) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:20.522+0800][1089.029s][info][gc,task       ] GC(134) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:20.527+0800][1089.033s][info][gc,phases     ] GC(134)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:06:20.527+0800][1089.034s][info][gc,phases     ] GC(134)   Evacuate Collection Set: 3.4ms
[2025-09-10T17:06:20.527+0800][1089.034s][info][gc,phases     ] GC(134)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:06:20.527+0800][1089.034s][info][gc,phases     ] GC(134)   Other: 0.6ms
[2025-09-10T17:06:20.527+0800][1089.034s][info][gc,heap       ] GC(134) Eden regions: 275->0(37)
[2025-09-10T17:06:20.527+0800][1089.034s][info][gc,heap       ] GC(134) Survivor regions: 12->14(36)
[2025-09-10T17:06:20.527+0800][1089.034s][info][gc,heap       ] GC(134) Old regions: 600->600
[2025-09-10T17:06:20.528+0800][1089.034s][info][gc,heap       ] GC(134) Humongous regions: 2->2
[2025-09-10T17:06:20.528+0800][1089.034s][info][gc,metaspace  ] GC(134) Metaspace: 437378K(476548K)->437378K(476548K) NonClass: 382364K(409064K)->382364K(409064K) Class: 55013K(67484K)->55013K(67484K)
[2025-09-10T17:06:20.528+0800][1089.035s][info][gc            ] GC(134) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 886M->613M(1026M) 6.144ms
[2025-09-10T17:06:20.528+0800][1089.035s][info][gc,cpu        ] GC(134) User=0.06s Sys=0.00s Real=0.00s
[2025-09-10T17:06:20.598+0800][1089.105s][info][gc,start      ] GC(135) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:06:20.599+0800][1089.105s][info][gc,task       ] GC(135) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:20.611+0800][1089.118s][info][gc,phases     ] GC(135)   Pre Evacuate Collection Set: 0.6ms
[2025-09-10T17:06:20.611+0800][1089.118s][info][gc,phases     ] GC(135)   Evacuate Collection Set: 9.1ms
[2025-09-10T17:06:20.611+0800][1089.118s][info][gc,phases     ] GC(135)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T17:06:20.611+0800][1089.118s][info][gc,phases     ] GC(135)   Other: 0.8ms
[2025-09-10T17:06:20.612+0800][1089.118s][info][gc,heap       ] GC(135) Eden regions: 37->0(285)
[2025-09-10T17:06:20.612+0800][1089.119s][info][gc,heap       ] GC(135) Survivor regions: 14->4(7)
[2025-09-10T17:06:20.612+0800][1089.119s][info][gc,heap       ] GC(135) Old regions: 600->600
[2025-09-10T17:06:20.612+0800][1089.119s][info][gc,heap       ] GC(135) Humongous regions: 2->2
[2025-09-10T17:06:20.612+0800][1089.119s][info][gc,metaspace  ] GC(135) Metaspace: 437415K(476804K)->437415K(476804K) NonClass: 382394K(409320K)->382394K(409320K) Class: 55020K(67484K)->55020K(67484K)
[2025-09-10T17:06:20.613+0800][1089.119s][info][gc            ] GC(135) Pause Young (Mixed) (G1 Evacuation Pause) 650M->603M(1026M) 14.057ms
[2025-09-10T17:06:20.613+0800][1089.119s][info][gc,cpu        ] GC(135) User=0.14s Sys=0.00s Real=0.02s
[2025-09-10T17:06:39.092+0800][1107.600s][info][gc,start      ] GC(136) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:06:39.094+0800][1107.602s][info][gc,task       ] GC(136) Using 15 workers of 15 for evacuation
[2025-09-10T17:06:39.101+0800][1107.609s][info][gc,phases     ] GC(136)   Pre Evacuate Collection Set: 0.8ms
[2025-09-10T17:06:39.102+0800][1107.610s][info][gc,phases     ] GC(136)   Evacuate Collection Set: 4.3ms
[2025-09-10T17:06:39.105+0800][1107.613s][info][gc,phases     ] GC(136)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:06:39.106+0800][1107.613s][info][gc,phases     ] GC(136)   Other: 3.5ms
[2025-09-10T17:06:39.106+0800][1107.614s][info][gc,heap       ] GC(136) Eden regions: 135->0(229)
[2025-09-10T17:06:39.108+0800][1107.616s][info][gc,heap       ] GC(136) Survivor regions: 4->6(37)
[2025-09-10T17:06:39.108+0800][1107.616s][info][gc,heap       ] GC(136) Old regions: 600->600
[2025-09-10T17:06:39.109+0800][1107.616s][info][gc,heap       ] GC(136) Humongous regions: 2->1
[2025-09-10T17:06:39.109+0800][1107.617s][info][gc,metaspace  ] GC(136) Metaspace: 437813K(477188K)->437813K(477188K) NonClass: 382745K(409576K)->382745K(409576K) Class: 55068K(67612K)->55068K(67612K)
[2025-09-10T17:06:39.109+0800][1107.617s][info][gc            ] GC(136) Pause Young (Concurrent Start) (G1 Humongous Allocation) 738M->604M(1026M) 17.740ms
[2025-09-10T17:06:39.112+0800][1107.620s][info][gc,cpu        ] GC(136) User=0.05s Sys=0.00s Real=0.02s
[2025-09-10T17:06:39.113+0800][1107.621s][info][gc            ] GC(137) Concurrent Cycle
[2025-09-10T17:06:39.115+0800][1107.623s][info][gc,marking    ] GC(137) Concurrent Clear Claimed Marks
[2025-09-10T17:06:39.117+0800][1107.625s][info][gc,marking    ] GC(137) Concurrent Clear Claimed Marks 1.743ms
[2025-09-10T17:06:39.117+0800][1107.625s][info][gc,marking    ] GC(137) Concurrent Scan Root Regions
[2025-09-10T17:06:39.120+0800][1107.628s][info][gc,marking    ] GC(137) Concurrent Scan Root Regions 2.595ms
[2025-09-10T17:06:39.120+0800][1107.628s][info][gc,marking    ] GC(137) Concurrent Mark (1107.628s)
[2025-09-10T17:06:39.120+0800][1107.628s][info][gc,marking    ] GC(137) Concurrent Mark From Roots
[2025-09-10T17:06:39.120+0800][1107.628s][info][gc,task       ] GC(137) Using 4 workers of 4 for marking
[2025-09-10T17:06:39.256+0800][1107.764s][info][gc,marking    ] GC(137) Concurrent Mark From Roots 135.437ms
[2025-09-10T17:06:39.256+0800][1107.764s][info][gc,marking    ] GC(137) Concurrent Preclean
[2025-09-10T17:06:39.257+0800][1107.765s][info][gc,marking    ] GC(137) Concurrent Preclean 1.346ms
[2025-09-10T17:06:39.257+0800][1107.765s][info][gc,marking    ] GC(137) Concurrent Mark (1107.628s, 1107.765s) 137.338ms
[2025-09-10T17:06:39.258+0800][1107.766s][info][gc,start      ] GC(137) Pause Remark
[2025-09-10T17:06:39.269+0800][1107.777s][info][gc,stringtable] GC(137) Cleaned string and symbol table, strings: 200234 processed, 15 removed, symbols: 503248 processed, 180 removed
[2025-09-10T17:06:39.270+0800][1107.778s][info][gc            ] GC(137) Pause Remark 654M->654M(1026M) 12.114ms
[2025-09-10T17:06:39.270+0800][1107.778s][info][gc,cpu        ] GC(137) User=0.14s Sys=0.00s Real=0.02s
[2025-09-10T17:06:39.271+0800][1107.778s][info][gc,marking    ] GC(137) Concurrent Rebuild Remembered Sets
[2025-09-10T17:06:39.395+0800][1107.903s][info][gc,marking    ] GC(137) Concurrent Rebuild Remembered Sets 124.669ms
[2025-09-10T17:06:39.396+0800][1107.904s][info][gc,start      ] GC(137) Pause Cleanup
[2025-09-10T17:06:39.397+0800][1107.905s][info][gc            ] GC(137) Pause Cleanup 654M->654M(1026M) 0.845ms
[2025-09-10T17:06:39.397+0800][1107.905s][info][gc,cpu        ] GC(137) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:06:39.397+0800][1107.905s][info][gc,marking    ] GC(137) Concurrent Cleanup for Next Mark
[2025-09-10T17:06:39.398+0800][1107.906s][info][gc,marking    ] GC(137) Concurrent Cleanup for Next Mark 0.872ms
[2025-09-10T17:06:39.398+0800][1107.906s][info][gc            ] GC(137) Concurrent Cycle 284.952ms
[2025-09-10T17:07:11.432+0800][1139.942s][info][gc,start      ] GC(138) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:07:11.433+0800][1139.942s][info][gc,task       ] GC(138) Using 15 workers of 15 for evacuation
[2025-09-10T17:07:11.440+0800][1139.950s][info][gc,phases     ] GC(138)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:07:11.440+0800][1139.950s][info][gc,phases     ] GC(138)   Evacuate Collection Set: 5.2ms
[2025-09-10T17:07:11.441+0800][1139.950s][info][gc,phases     ] GC(138)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:07:11.441+0800][1139.950s][info][gc,phases     ] GC(138)   Other: 0.9ms
[2025-09-10T17:07:11.441+0800][1139.951s][info][gc,heap       ] GC(138) Eden regions: 229->0(37)
[2025-09-10T17:07:11.442+0800][1139.951s][info][gc,heap       ] GC(138) Survivor regions: 6->14(30)
[2025-09-10T17:07:11.442+0800][1139.951s][info][gc,heap       ] GC(138) Old regions: 600->600
[2025-09-10T17:07:11.442+0800][1139.952s][info][gc,heap       ] GC(138) Humongous regions: 3->2
[2025-09-10T17:07:11.444+0800][1139.953s][info][gc,metaspace  ] GC(138) Metaspace: 439074K(478724K)->439074K(478724K) NonClass: 383834K(410856K)->383834K(410856K) Class: 55239K(67868K)->55239K(67868K)
[2025-09-10T17:07:11.444+0800][1139.954s][info][gc            ] GC(138) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 835M->613M(1026M) 11.618ms
[2025-09-10T17:07:11.446+0800][1139.955s][info][gc,cpu        ] GC(138) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:07:12.211+0800][1140.721s][info][gc,start      ] GC(139) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:07:12.212+0800][1140.721s][info][gc,task       ] GC(139) Using 15 workers of 15 for evacuation
[2025-09-10T17:07:12.224+0800][1140.734s][info][gc,phases     ] GC(139)   Pre Evacuate Collection Set: 0.6ms
[2025-09-10T17:07:12.224+0800][1140.734s][info][gc,phases     ] GC(139)   Evacuate Collection Set: 9.7ms
[2025-09-10T17:07:12.225+0800][1140.734s][info][gc,phases     ] GC(139)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T17:07:12.225+0800][1140.734s][info][gc,phases     ] GC(139)   Other: 1.1ms
[2025-09-10T17:07:12.225+0800][1140.734s][info][gc,heap       ] GC(139) Eden regions: 37->0(234)
[2025-09-10T17:07:12.225+0800][1140.735s][info][gc,heap       ] GC(139) Survivor regions: 14->2(7)
[2025-09-10T17:07:12.225+0800][1140.735s][info][gc,heap       ] GC(139) Old regions: 600->604
[2025-09-10T17:07:12.225+0800][1140.735s][info][gc,heap       ] GC(139) Humongous regions: 2->2
[2025-09-10T17:07:12.225+0800][1140.735s][info][gc,metaspace  ] GC(139) Metaspace: 439090K(478724K)->439090K(478724K) NonClass: 383848K(410856K)->383848K(410856K) Class: 55242K(67868K)->55242K(67868K)
[2025-09-10T17:07:12.226+0800][1140.735s][info][gc            ] GC(139) Pause Young (Mixed) (G1 Evacuation Pause) 650M->605M(1026M) 14.471ms
[2025-09-10T17:07:12.227+0800][1140.736s][info][gc,cpu        ] GC(139) User=0.14s Sys=0.00s Real=0.01s
[2025-09-10T17:07:13.072+0800][1141.581s][info][gc,start      ] GC(140) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:07:13.072+0800][1141.582s][info][gc,task       ] GC(140) Using 15 workers of 15 for evacuation
[2025-09-10T17:07:13.080+0800][1141.589s][info][gc,phases     ] GC(140)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:07:13.080+0800][1141.589s][info][gc,phases     ] GC(140)   Evacuate Collection Set: 4.8ms
[2025-09-10T17:07:13.080+0800][1141.590s][info][gc,phases     ] GC(140)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T17:07:13.080+0800][1141.590s][info][gc,phases     ] GC(140)   Other: 0.7ms
[2025-09-10T17:07:13.080+0800][1141.590s][info][gc,heap       ] GC(140) Eden regions: 234->0(242)
[2025-09-10T17:07:13.080+0800][1141.590s][info][gc,heap       ] GC(140) Survivor regions: 2->4(30)
[2025-09-10T17:07:13.081+0800][1141.590s][info][gc,heap       ] GC(140) Old regions: 604->604
[2025-09-10T17:07:13.081+0800][1141.590s][info][gc,heap       ] GC(140) Humongous regions: 2->2
[2025-09-10T17:07:13.081+0800][1141.591s][info][gc,metaspace  ] GC(140) Metaspace: 439526K(478980K)->439526K(478980K) NonClass: 384222K(411112K)->384222K(411112K) Class: 55303K(67868K)->55303K(67868K)
[2025-09-10T17:07:13.081+0800][1141.591s][info][gc            ] GC(140) Pause Young (Concurrent Start) (G1 Evacuation Pause) 839M->607M(1026M) 9.404ms
[2025-09-10T17:07:13.081+0800][1141.591s][info][gc,cpu        ] GC(140) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:07:13.081+0800][1141.591s][info][gc            ] GC(141) Concurrent Cycle
[2025-09-10T17:07:13.082+0800][1141.591s][info][gc,marking    ] GC(141) Concurrent Clear Claimed Marks
[2025-09-10T17:07:13.083+0800][1141.593s][info][gc,marking    ] GC(141) Concurrent Clear Claimed Marks 1.321ms
[2025-09-10T17:07:13.083+0800][1141.593s][info][gc,marking    ] GC(141) Concurrent Scan Root Regions
[2025-09-10T17:07:13.087+0800][1141.596s][info][gc,marking    ] GC(141) Concurrent Scan Root Regions 3.485ms
[2025-09-10T17:07:13.087+0800][1141.597s][info][gc,marking    ] GC(141) Concurrent Mark (1141.597s)
[2025-09-10T17:07:13.087+0800][1141.597s][info][gc,marking    ] GC(141) Concurrent Mark From Roots
[2025-09-10T17:07:13.087+0800][1141.597s][info][gc,task       ] GC(141) Using 4 workers of 4 for marking
[2025-09-10T17:07:13.252+0800][1141.762s][info][gc,marking    ] GC(141) Concurrent Mark From Roots 164.837ms
[2025-09-10T17:07:13.252+0800][1141.762s][info][gc,marking    ] GC(141) Concurrent Preclean
[2025-09-10T17:07:13.253+0800][1141.763s][info][gc,marking    ] GC(141) Concurrent Preclean 0.974ms
[2025-09-10T17:07:13.254+0800][1141.763s][info][gc,marking    ] GC(141) Concurrent Mark (1141.597s, 1141.763s) 166.512ms
[2025-09-10T17:07:13.254+0800][1141.763s][info][gc,start      ] GC(141) Pause Remark
[2025-09-10T17:07:13.295+0800][1141.804s][info][gc,stringtable] GC(141) Cleaned string and symbol table, strings: 200396 processed, 11035 removed, symbols: 503510 processed, 102 removed
[2025-09-10T17:07:13.296+0800][1141.805s][info][gc            ] GC(141) Pause Remark 629M->629M(1026M) 41.656ms
[2025-09-10T17:07:13.296+0800][1141.805s][info][gc,cpu        ] GC(141) User=0.49s Sys=0.00s Real=0.04s
[2025-09-10T17:07:13.296+0800][1141.806s][info][gc,marking    ] GC(141) Concurrent Rebuild Remembered Sets
[2025-09-10T17:07:13.436+0800][1141.945s][info][gc,marking    ] GC(141) Concurrent Rebuild Remembered Sets 139.564ms
[2025-09-10T17:07:13.437+0800][1141.947s][info][gc,start      ] GC(141) Pause Cleanup
[2025-09-10T17:07:13.438+0800][1141.948s][info][gc            ] GC(141) Pause Cleanup 637M->637M(1026M) 0.948ms
[2025-09-10T17:07:13.439+0800][1141.948s][info][gc,cpu        ] GC(141) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:07:13.439+0800][1141.948s][info][gc,marking    ] GC(141) Concurrent Cleanup for Next Mark
[2025-09-10T17:07:13.440+0800][1141.949s][info][gc,marking    ] GC(141) Concurrent Cleanup for Next Mark 0.904ms
[2025-09-10T17:07:13.440+0800][1141.950s][info][gc            ] GC(141) Concurrent Cycle 358.780ms
[2025-09-10T17:08:44.460+0800][1232.973s][info][gc,start      ] GC(142) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:08:44.460+0800][1232.974s][info][gc,task       ] GC(142) Using 15 workers of 15 for evacuation
[2025-09-10T17:08:44.468+0800][1232.982s][info][gc,phases     ] GC(142)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:08:44.468+0800][1232.982s][info][gc,phases     ] GC(142)   Evacuate Collection Set: 6.1ms
[2025-09-10T17:08:44.469+0800][1232.982s][info][gc,phases     ] GC(142)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:08:44.469+0800][1232.983s][info][gc,phases     ] GC(142)   Other: 1.3ms
[2025-09-10T17:08:44.469+0800][1232.983s][info][gc,heap       ] GC(142) Eden regions: 242->0(31)
[2025-09-10T17:08:44.469+0800][1232.983s][info][gc,heap       ] GC(142) Survivor regions: 4->20(31)
[2025-09-10T17:08:44.469+0800][1232.983s][info][gc,heap       ] GC(142) Old regions: 604->604
[2025-09-10T17:08:44.469+0800][1232.983s][info][gc,heap       ] GC(142) Humongous regions: 3->2
[2025-09-10T17:08:44.470+0800][1232.983s][info][gc,metaspace  ] GC(142) Metaspace: 445521K(484868K)->445521K(484868K) NonClass: 389459K(416232K)->389459K(416232K) Class: 56061K(68636K)->56061K(68636K)
[2025-09-10T17:08:44.470+0800][1232.984s][info][gc            ] GC(142) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 850M->624M(1026M) 10.193ms
[2025-09-10T17:08:44.470+0800][1232.984s][info][gc,cpu        ] GC(142) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:08:45.252+0800][1233.766s][info][gc,start      ] GC(143) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:08:45.254+0800][1233.768s][info][gc,task       ] GC(143) Using 15 workers of 15 for evacuation
[2025-09-10T17:08:45.275+0800][1233.789s][info][gc,phases     ] GC(143)   Pre Evacuate Collection Set: 1.0ms
[2025-09-10T17:08:45.276+0800][1233.790s][info][gc,phases     ] GC(143)   Evacuate Collection Set: 17.5ms
[2025-09-10T17:08:45.277+0800][1233.791s][info][gc,phases     ] GC(143)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T17:08:45.277+0800][1233.791s][info][gc,phases     ] GC(143)   Other: 2.5ms
[2025-09-10T17:08:45.277+0800][1233.791s][info][gc,heap       ] GC(143) Eden regions: 31->0(241)
[2025-09-10T17:08:45.278+0800][1233.791s][info][gc,heap       ] GC(143) Survivor regions: 20->3(7)
[2025-09-10T17:08:45.278+0800][1233.792s][info][gc,heap       ] GC(143) Old regions: 604->610
[2025-09-10T17:08:45.279+0800][1233.793s][info][gc,heap       ] GC(143) Humongous regions: 6->2
[2025-09-10T17:08:45.279+0800][1233.793s][info][gc,metaspace  ] GC(143) Metaspace: 447646K(486916K)->447646K(486916K) NonClass: 391313K(418024K)->391313K(418024K) Class: 56333K(68892K)->56333K(68892K)
[2025-09-10T17:08:45.279+0800][1233.793s][info][gc            ] GC(143) Pause Young (Mixed) (G1 Evacuation Pause) 659M->612M(1026M) 26.912ms
[2025-09-10T17:08:45.279+0800][1233.793s][info][gc,cpu        ] GC(143) User=0.27s Sys=0.00s Real=0.02s
[2025-09-10T17:08:55.122+0800][1243.636s][info][gc,start      ] GC(144) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:08:55.122+0800][1243.636s][info][gc,task       ] GC(144) Using 15 workers of 15 for evacuation
[2025-09-10T17:08:55.131+0800][1243.645s][info][gc,phases     ] GC(144)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:08:55.132+0800][1243.646s][info][gc,phases     ] GC(144)   Evacuate Collection Set: 6.3ms
[2025-09-10T17:08:55.133+0800][1243.647s][info][gc,phases     ] GC(144)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:08:55.135+0800][1243.648s][info][gc,phases     ] GC(144)   Other: 1.0ms
[2025-09-10T17:08:55.136+0800][1243.650s][info][gc,heap       ] GC(144) Eden regions: 146->0(238)
[2025-09-10T17:08:55.138+0800][1243.652s][info][gc,heap       ] GC(144) Survivor regions: 3->7(31)
[2025-09-10T17:08:55.139+0800][1243.652s][info][gc,heap       ] GC(144) Old regions: 610->610
[2025-09-10T17:08:55.139+0800][1243.653s][info][gc,heap       ] GC(144) Humongous regions: 2->1
[2025-09-10T17:08:55.139+0800][1243.653s][info][gc,metaspace  ] GC(144) Metaspace: 448269K(487812K)->448269K(487812K) NonClass: 391867K(418792K)->391867K(418792K) Class: 56402K(69020K)->56402K(69020K)
[2025-09-10T17:08:55.139+0800][1243.653s][info][gc            ] GC(144) Pause Young (Concurrent Start) (G1 Humongous Allocation) 758M->615M(1026M) 17.460ms
[2025-09-10T17:08:55.140+0800][1243.654s][info][gc,cpu        ] GC(144) User=0.07s Sys=0.00s Real=0.02s
[2025-09-10T17:08:55.140+0800][1243.654s][info][gc            ] GC(145) Concurrent Cycle
[2025-09-10T17:08:55.140+0800][1243.654s][info][gc,marking    ] GC(145) Concurrent Clear Claimed Marks
[2025-09-10T17:08:55.141+0800][1243.655s][info][gc,marking    ] GC(145) Concurrent Clear Claimed Marks 1.084ms
[2025-09-10T17:08:55.142+0800][1243.655s][info][gc,marking    ] GC(145) Concurrent Scan Root Regions
[2025-09-10T17:08:55.145+0800][1243.659s][info][gc,marking    ] GC(145) Concurrent Scan Root Regions 3.136ms
[2025-09-10T17:08:55.145+0800][1243.659s][info][gc,marking    ] GC(145) Concurrent Mark (1243.659s)
[2025-09-10T17:08:55.146+0800][1243.660s][info][gc,marking    ] GC(145) Concurrent Mark From Roots
[2025-09-10T17:08:55.146+0800][1243.660s][info][gc,task       ] GC(145) Using 4 workers of 4 for marking
[2025-09-10T17:08:55.289+0800][1243.802s][info][gc,marking    ] GC(145) Concurrent Mark From Roots 142.955ms
[2025-09-10T17:08:55.289+0800][1243.803s][info][gc,marking    ] GC(145) Concurrent Preclean
[2025-09-10T17:08:55.290+0800][1243.804s][info][gc,marking    ] GC(145) Concurrent Preclean 0.730ms
[2025-09-10T17:08:55.290+0800][1243.804s][info][gc,marking    ] GC(145) Concurrent Mark (1243.659s, 1243.804s) 144.692ms
[2025-09-10T17:08:55.290+0800][1243.804s][info][gc,start      ] GC(145) Pause Remark
[2025-09-10T17:08:55.329+0800][1243.843s][info][gc,stringtable] GC(145) Cleaned string and symbol table, strings: 201530 processed, 219 removed, symbols: 507803 processed, 85 removed
[2025-09-10T17:08:55.330+0800][1243.844s][info][gc            ] GC(145) Pause Remark 637M->637M(1026M) 39.318ms
[2025-09-10T17:08:55.330+0800][1243.844s][info][gc,cpu        ] GC(145) User=0.45s Sys=0.00s Real=0.04s
[2025-09-10T17:08:55.331+0800][1243.845s][info][gc,marking    ] GC(145) Concurrent Rebuild Remembered Sets
[2025-09-10T17:08:55.461+0800][1243.975s][info][gc,marking    ] GC(145) Concurrent Rebuild Remembered Sets 130.040ms
[2025-09-10T17:08:55.462+0800][1243.975s][info][gc,start      ] GC(145) Pause Cleanup
[2025-09-10T17:08:55.463+0800][1243.976s][info][gc            ] GC(145) Pause Cleanup 637M->637M(1026M) 1.142ms
[2025-09-10T17:08:55.463+0800][1243.977s][info][gc,cpu        ] GC(145) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:08:55.463+0800][1243.977s][info][gc,marking    ] GC(145) Concurrent Cleanup for Next Mark
[2025-09-10T17:08:55.464+0800][1243.978s][info][gc,marking    ] GC(145) Concurrent Cleanup for Next Mark 1.033ms
[2025-09-10T17:08:55.465+0800][1243.978s][info][gc            ] GC(145) Concurrent Cycle 324.587ms
[2025-09-10T17:09:13.836+0800][1262.352s][info][gc,start      ] GC(146) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:13.836+0800][1262.352s][info][gc,task       ] GC(146) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:13.841+0800][1262.357s][info][gc,phases     ] GC(146)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:13.841+0800][1262.357s][info][gc,phases     ] GC(146)   Evacuate Collection Set: 3.1ms
[2025-09-10T17:09:13.841+0800][1262.357s][info][gc,phases     ] GC(146)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:13.842+0800][1262.357s][info][gc,phases     ] GC(146)   Other: 0.8ms
[2025-09-10T17:09:13.842+0800][1262.357s][info][gc,heap       ] GC(146) Eden regions: 238->0(38)
[2025-09-10T17:09:13.842+0800][1262.357s][info][gc,heap       ] GC(146) Survivor regions: 7->13(31)
[2025-09-10T17:09:13.842+0800][1262.358s][info][gc,heap       ] GC(146) Old regions: 610->610
[2025-09-10T17:09:13.842+0800][1262.358s][info][gc,heap       ] GC(146) Humongous regions: 23->5
[2025-09-10T17:09:13.842+0800][1262.358s][info][gc,metaspace  ] GC(146) Metaspace: 449600K(489220K)->449600K(489220K) NonClass: 393015K(420072K)->393015K(420072K) Class: 56585K(69148K)->56585K(69148K)
[2025-09-10T17:09:13.843+0800][1262.358s][info][gc            ] GC(146) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 875M->626M(1026M) 6.495ms
[2025-09-10T17:09:13.843+0800][1262.358s][info][gc,cpu        ] GC(146) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:09:13.914+0800][1262.430s][info][gc,start      ] GC(147) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:13.914+0800][1262.430s][info][gc,task       ] GC(147) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:13.924+0800][1262.440s][info][gc,phases     ] GC(147)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T17:09:13.924+0800][1262.440s][info][gc,phases     ] GC(147)   Evacuate Collection Set: 7.8ms
[2025-09-10T17:09:13.925+0800][1262.440s][info][gc,phases     ] GC(147)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:13.925+0800][1262.440s][info][gc,phases     ] GC(147)   Other: 0.8ms
[2025-09-10T17:09:13.925+0800][1262.440s][info][gc,heap       ] GC(147) Eden regions: 38->0(248)
[2025-09-10T17:09:13.925+0800][1262.441s][info][gc,heap       ] GC(147) Survivor regions: 13->2(7)
[2025-09-10T17:09:13.925+0800][1262.441s][info][gc,heap       ] GC(147) Old regions: 610->604
[2025-09-10T17:09:13.925+0800][1262.441s][info][gc,heap       ] GC(147) Humongous regions: 5->5
[2025-09-10T17:09:13.926+0800][1262.441s][info][gc,metaspace  ] GC(147) Metaspace: 449608K(489220K)->449608K(489220K) NonClass: 393021K(420072K)->393021K(420072K) Class: 56586K(69148K)->56586K(69148K)
[2025-09-10T17:09:13.926+0800][1262.441s][info][gc            ] GC(147) Pause Young (Mixed) (G1 Evacuation Pause) 664M->608M(1026M) 11.681ms
[2025-09-10T17:09:13.926+0800][1262.441s][info][gc,cpu        ] GC(147) User=0.11s Sys=0.00s Real=0.01s
[2025-09-10T17:09:14.348+0800][1262.864s][info][gc,start      ] GC(148) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:09:14.348+0800][1262.864s][info][gc,task       ] GC(148) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:14.355+0800][1262.870s][info][gc,phases     ] GC(148)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:14.355+0800][1262.871s][info][gc,phases     ] GC(148)   Evacuate Collection Set: 3.8ms
[2025-09-10T17:09:14.355+0800][1262.871s][info][gc,phases     ] GC(148)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:09:14.355+0800][1262.871s][info][gc,phases     ] GC(148)   Other: 0.7ms
[2025-09-10T17:09:14.356+0800][1262.871s][info][gc,heap       ] GC(148) Eden regions: 248->0(258)
[2025-09-10T17:09:14.356+0800][1262.871s][info][gc,heap       ] GC(148) Survivor regions: 2->2(32)
[2025-09-10T17:09:14.356+0800][1262.872s][info][gc,heap       ] GC(148) Old regions: 604->604
[2025-09-10T17:09:14.356+0800][1262.872s][info][gc,heap       ] GC(148) Humongous regions: 5->5
[2025-09-10T17:09:14.356+0800][1262.872s][info][gc,metaspace  ] GC(148) Metaspace: 449660K(489220K)->449660K(489220K) NonClass: 393063K(420072K)->393063K(420072K) Class: 56597K(69148K)->56597K(69148K)
[2025-09-10T17:09:14.357+0800][1262.872s][info][gc            ] GC(148) Pause Young (Concurrent Start) (G1 Evacuation Pause) 856M->609M(1026M) 8.582ms
[2025-09-10T17:09:14.357+0800][1262.872s][info][gc,cpu        ] GC(148) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:09:14.357+0800][1262.872s][info][gc            ] GC(149) Concurrent Cycle
[2025-09-10T17:09:14.357+0800][1262.873s][info][gc,marking    ] GC(149) Concurrent Clear Claimed Marks
[2025-09-10T17:09:14.358+0800][1262.874s][info][gc,marking    ] GC(149) Concurrent Clear Claimed Marks 1.128ms
[2025-09-10T17:09:14.359+0800][1262.874s][info][gc,marking    ] GC(149) Concurrent Scan Root Regions
[2025-09-10T17:09:14.360+0800][1262.876s][info][gc,marking    ] GC(149) Concurrent Scan Root Regions 1.612ms
[2025-09-10T17:09:14.361+0800][1262.877s][info][gc,marking    ] GC(149) Concurrent Mark (1262.877s)
[2025-09-10T17:09:14.362+0800][1262.877s][info][gc,marking    ] GC(149) Concurrent Mark From Roots
[2025-09-10T17:09:14.362+0800][1262.878s][info][gc,task       ] GC(149) Using 4 workers of 4 for marking
[2025-09-10T17:09:14.497+0800][1263.012s][info][gc,marking    ] GC(149) Concurrent Mark From Roots 134.987ms
[2025-09-10T17:09:14.497+0800][1263.013s][info][gc,marking    ] GC(149) Concurrent Preclean
[2025-09-10T17:09:14.498+0800][1263.013s][info][gc,marking    ] GC(149) Concurrent Preclean 0.828ms
[2025-09-10T17:09:14.498+0800][1263.014s][info][gc,marking    ] GC(149) Concurrent Mark (1262.877s, 1263.014s) 137.035ms
[2025-09-10T17:09:14.499+0800][1263.014s][info][gc,start      ] GC(149) Pause Remark
[2025-09-10T17:09:14.510+0800][1263.025s][info][gc,stringtable] GC(149) Cleaned string and symbol table, strings: 202097 processed, 46232 removed, symbols: 509678 processed, 35 removed
[2025-09-10T17:09:14.510+0800][1263.026s][info][gc            ] GC(149) Pause Remark 692M->692M(1026M) 11.876ms
[2025-09-10T17:09:14.511+0800][1263.026s][info][gc,cpu        ] GC(149) User=0.14s Sys=0.01s Real=0.02s
[2025-09-10T17:09:14.511+0800][1263.027s][info][gc,marking    ] GC(149) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:14.636+0800][1263.152s][info][gc,marking    ] GC(149) Concurrent Rebuild Remembered Sets 124.801ms
[2025-09-10T17:09:14.988+0800][1263.503s][info][gc,start      ] GC(149) Pause Cleanup
[2025-09-10T17:09:14.989+0800][1263.504s][info][gc            ] GC(149) Pause Cleanup 700M->700M(1026M) 1.068ms
[2025-09-10T17:09:14.991+0800][1263.507s][info][gc,cpu        ] GC(149) User=0.00s Sys=0.00s Real=0.01s
[2025-09-10T17:09:14.992+0800][1263.507s][info][gc,marking    ] GC(149) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:14.993+0800][1263.509s][info][gc,marking    ] GC(149) Concurrent Cleanup for Next Mark 1.509ms
[2025-09-10T17:09:14.994+0800][1263.510s][info][gc            ] GC(149) Concurrent Cycle 637.428ms
[2025-09-10T17:09:16.561+0800][1265.076s][info][gc,start      ] GC(150) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:16.561+0800][1265.076s][info][gc,task       ] GC(150) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:16.573+0800][1265.088s][info][gc,phases     ] GC(150)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:16.574+0800][1265.090s][info][gc,phases     ] GC(150)   Evacuate Collection Set: 6.0ms
[2025-09-10T17:09:16.576+0800][1265.092s][info][gc,phases     ] GC(150)   Post Evacuate Collection Set: 5.2ms
[2025-09-10T17:09:16.577+0800][1265.092s][info][gc,phases     ] GC(150)   Other: 0.8ms
[2025-09-10T17:09:16.577+0800][1265.092s][info][gc,heap       ] GC(150) Eden regions: 258->0(38)
[2025-09-10T17:09:16.577+0800][1265.093s][info][gc,heap       ] GC(150) Survivor regions: 2->13(33)
[2025-09-10T17:09:16.578+0800][1265.093s][info][gc,heap       ] GC(150) Old regions: 604->604
[2025-09-10T17:09:16.579+0800][1265.094s][info][gc,heap       ] GC(150) Humongous regions: 5->5
[2025-09-10T17:09:16.581+0800][1265.096s][info][gc,metaspace  ] GC(150) Metaspace: 449717K(489220K)->449717K(489220K) NonClass: 393112K(420072K)->393112K(420072K) Class: 56604K(69148K)->56604K(69148K)
[2025-09-10T17:09:16.583+0800][1265.098s][info][gc            ] GC(150) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 867M->619M(1026M) 21.852ms
[2025-09-10T17:09:16.583+0800][1265.099s][info][gc,cpu        ] GC(150) User=0.15s Sys=0.00s Real=0.03s
[2025-09-10T17:09:17.480+0800][1265.995s][info][gc,start      ] GC(151) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:17.480+0800][1265.995s][info][gc,task       ] GC(151) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:17.491+0800][1266.006s][info][gc,phases     ] GC(151)   Pre Evacuate Collection Set: 0.8ms
[2025-09-10T17:09:17.491+0800][1266.007s][info][gc,phases     ] GC(151)   Evacuate Collection Set: 7.8ms
[2025-09-10T17:09:17.492+0800][1266.007s][info][gc,phases     ] GC(151)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T17:09:17.492+0800][1266.007s][info][gc,phases     ] GC(151)   Other: 1.4ms
[2025-09-10T17:09:17.492+0800][1266.008s][info][gc,heap       ] GC(151) Eden regions: 38->0(260)
[2025-09-10T17:09:17.492+0800][1266.008s][info][gc,heap       ] GC(151) Survivor regions: 13->3(7)
[2025-09-10T17:09:17.493+0800][1266.008s][info][gc,heap       ] GC(151) Old regions: 604->601
[2025-09-10T17:09:17.493+0800][1266.008s][info][gc,heap       ] GC(151) Humongous regions: 5->5
[2025-09-10T17:09:17.493+0800][1266.009s][info][gc,metaspace  ] GC(151) Metaspace: 449754K(489220K)->449754K(489220K) NonClass: 393141K(420072K)->393141K(420072K) Class: 56612K(69148K)->56612K(69148K)
[2025-09-10T17:09:17.493+0800][1266.009s][info][gc            ] GC(151) Pause Young (Mixed) (G1 Evacuation Pause) 657M->607M(1026M) 13.659ms
[2025-09-10T17:09:17.493+0800][1266.009s][info][gc,cpu        ] GC(151) User=0.12s Sys=0.00s Real=0.02s
[2025-09-10T17:09:23.809+0800][1272.325s][info][gc,start      ] GC(152) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:23.809+0800][1272.325s][info][gc,task       ] GC(152) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:23.815+0800][1272.331s][info][gc,phases     ] GC(152)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:23.816+0800][1272.331s][info][gc,phases     ] GC(152)   Evacuate Collection Set: 4.4ms
[2025-09-10T17:09:23.816+0800][1272.331s][info][gc,phases     ] GC(152)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:09:23.816+0800][1272.332s][info][gc,phases     ] GC(152)   Other: 0.5ms
[2025-09-10T17:09:23.816+0800][1272.332s][info][gc,heap       ] GC(152) Eden regions: 260->0(251)
[2025-09-10T17:09:23.816+0800][1272.332s][info][gc,heap       ] GC(152) Survivor regions: 3->16(33)
[2025-09-10T17:09:23.817+0800][1272.332s][info][gc,heap       ] GC(152) Old regions: 601->601
[2025-09-10T17:09:23.817+0800][1272.332s][info][gc,heap       ] GC(152) Humongous regions: 6->5
[2025-09-10T17:09:23.817+0800][1272.333s][info][gc,metaspace  ] GC(152) Metaspace: 456559K(496516K)->456559K(496516K) NonClass: 399038K(426216K)->399038K(426216K) Class: 57521K(70300K)->57521K(70300K)
[2025-09-10T17:09:23.817+0800][1272.333s][info][gc            ] GC(152) Pause Young (Normal) (G1 Evacuation Pause) 868M->619M(1026M) 8.216ms
[2025-09-10T17:09:23.817+0800][1272.333s][info][gc,cpu        ] GC(152) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:09:25.429+0800][1273.944s][info][gc,start      ] GC(153) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:25.429+0800][1273.945s][info][gc,task       ] GC(153) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:25.438+0800][1273.953s][info][gc,phases     ] GC(153)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:25.438+0800][1273.954s][info][gc,phases     ] GC(153)   Evacuate Collection Set: 5.1ms
[2025-09-10T17:09:25.439+0800][1273.954s][info][gc,phases     ] GC(153)   Post Evacuate Collection Set: 2.6ms
[2025-09-10T17:09:25.439+0800][1273.955s][info][gc,phases     ] GC(153)   Other: 1.3ms
[2025-09-10T17:09:25.439+0800][1273.955s][info][gc,heap       ] GC(153) Eden regions: 251->0(249)
[2025-09-10T17:09:25.440+0800][1273.955s][info][gc,heap       ] GC(153) Survivor regions: 16->23(34)
[2025-09-10T17:09:25.440+0800][1273.955s][info][gc,heap       ] GC(153) Old regions: 601->601
[2025-09-10T17:09:25.440+0800][1273.955s][info][gc,heap       ] GC(153) Humongous regions: 5->5
[2025-09-10T17:09:25.440+0800][1273.955s][info][gc,metaspace  ] GC(153) Metaspace: 457590K(497668K)->457590K(497668K) NonClass: 399902K(427240K)->399902K(427240K) Class: 57687K(70428K)->57687K(70428K)
[2025-09-10T17:09:25.440+0800][1273.956s][info][gc            ] GC(153) Pause Young (Normal) (G1 Evacuation Pause) 870M->627M(1026M) 11.119ms
[2025-09-10T17:09:25.440+0800][1273.956s][info][gc,cpu        ] GC(153) User=0.15s Sys=0.00s Real=0.01s
[2025-09-10T17:09:26.005+0800][1274.520s][info][gc,start      ] GC(154) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:09:26.005+0800][1274.521s][info][gc,task       ] GC(154) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:26.015+0800][1274.530s][info][gc,phases     ] GC(154)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:26.015+0800][1274.531s][info][gc,phases     ] GC(154)   Evacuate Collection Set: 7.5ms
[2025-09-10T17:09:26.015+0800][1274.531s][info][gc,phases     ] GC(154)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:26.015+0800][1274.531s][info][gc,phases     ] GC(154)   Other: 0.5ms
[2025-09-10T17:09:26.016+0800][1274.531s][info][gc,heap       ] GC(154) Eden regions: 249->0(249)
[2025-09-10T17:09:26.016+0800][1274.531s][info][gc,heap       ] GC(154) Survivor regions: 23->13(34)
[2025-09-10T17:09:26.016+0800][1274.531s][info][gc,heap       ] GC(154) Old regions: 601->616
[2025-09-10T17:09:26.016+0800][1274.531s][info][gc,heap       ] GC(154) Humongous regions: 5->5
[2025-09-10T17:09:26.016+0800][1274.532s][info][gc,metaspace  ] GC(154) Metaspace: 458179K(498564K)->458179K(498564K) NonClass: 400420K(428008K)->400420K(428008K) Class: 57759K(70556K)->57759K(70556K)
[2025-09-10T17:09:26.016+0800][1274.532s][info][gc            ] GC(154) Pause Young (Concurrent Start) (G1 Evacuation Pause) 876M->632M(1026M) 11.223ms
[2025-09-10T17:09:26.016+0800][1274.532s][info][gc,cpu        ] GC(154) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:09:26.016+0800][1274.532s][info][gc            ] GC(155) Concurrent Cycle
[2025-09-10T17:09:26.017+0800][1274.532s][info][gc,marking    ] GC(155) Concurrent Clear Claimed Marks
[2025-09-10T17:09:26.018+0800][1274.533s][info][gc,marking    ] GC(155) Concurrent Clear Claimed Marks 1.029ms
[2025-09-10T17:09:26.018+0800][1274.533s][info][gc,marking    ] GC(155) Concurrent Scan Root Regions
[2025-09-10T17:09:26.021+0800][1274.537s][info][gc,marking    ] GC(155) Concurrent Scan Root Regions 3.373ms
[2025-09-10T17:09:26.021+0800][1274.537s][info][gc,marking    ] GC(155) Concurrent Mark (1274.537s)
[2025-09-10T17:09:26.022+0800][1274.537s][info][gc,marking    ] GC(155) Concurrent Mark From Roots
[2025-09-10T17:09:26.022+0800][1274.537s][info][gc,task       ] GC(155) Using 4 workers of 4 for marking
[2025-09-10T17:09:26.183+0800][1274.699s][info][gc,marking    ] GC(155) Concurrent Mark From Roots 161.373ms
[2025-09-10T17:09:26.183+0800][1274.699s][info][gc,marking    ] GC(155) Concurrent Preclean
[2025-09-10T17:09:26.184+0800][1274.700s][info][gc,marking    ] GC(155) Concurrent Preclean 0.917ms
[2025-09-10T17:09:26.185+0800][1274.700s][info][gc,marking    ] GC(155) Concurrent Mark (1274.537s, 1274.700s) 163.074ms
[2025-09-10T17:09:26.185+0800][1274.701s][info][gc,start      ] GC(155) Pause Remark
[2025-09-10T17:09:26.198+0800][1274.714s][info][gc,stringtable] GC(155) Cleaned string and symbol table, strings: 204625 processed, 5 removed, symbols: 521520 processed, 100 removed
[2025-09-10T17:09:26.199+0800][1274.715s][info][gc            ] GC(155) Pause Remark 713M->713M(1026M) 14.089ms
[2025-09-10T17:09:26.199+0800][1274.715s][info][gc,cpu        ] GC(155) User=0.19s Sys=0.00s Real=0.01s
[2025-09-10T17:09:26.200+0800][1274.715s][info][gc,marking    ] GC(155) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:26.334+0800][1274.850s][info][gc,marking    ] GC(155) Concurrent Rebuild Remembered Sets 134.711ms
[2025-09-10T17:09:26.335+0800][1274.851s][info][gc,start      ] GC(155) Pause Cleanup
[2025-09-10T17:09:26.336+0800][1274.852s][info][gc            ] GC(155) Pause Cleanup 716M->716M(1026M) 1.034ms
[2025-09-10T17:09:26.336+0800][1274.852s][info][gc,cpu        ] GC(155) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:09:26.337+0800][1274.852s][info][gc,marking    ] GC(155) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:26.338+0800][1274.853s][info][gc,marking    ] GC(155) Concurrent Cleanup for Next Mark 0.981ms
[2025-09-10T17:09:26.338+0800][1274.853s][info][gc            ] GC(155) Concurrent Cycle 321.435ms
[2025-09-10T17:09:27.060+0800][1275.576s][info][gc,start      ] GC(156) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:27.062+0800][1275.578s][info][gc,task       ] GC(156) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:27.071+0800][1275.588s][info][gc,phases     ] GC(156)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:27.072+0800][1275.589s][info][gc,phases     ] GC(156)   Evacuate Collection Set: 6.1ms
[2025-09-10T17:09:27.073+0800][1275.589s][info][gc,phases     ] GC(156)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:09:27.073+0800][1275.590s][info][gc,phases     ] GC(156)   Other: 4.2ms
[2025-09-10T17:09:27.074+0800][1275.590s][info][gc,heap       ] GC(156) Eden regions: 249->0(28)
[2025-09-10T17:09:27.074+0800][1275.591s][info][gc,heap       ] GC(156) Survivor regions: 13->23(33)
[2025-09-10T17:09:27.075+0800][1275.592s][info][gc,heap       ] GC(156) Old regions: 616->616
[2025-09-10T17:09:27.076+0800][1275.592s][info][gc,heap       ] GC(156) Humongous regions: 5->5
[2025-09-10T17:09:27.076+0800][1275.593s][info][gc,metaspace  ] GC(156) Metaspace: 458464K(498820K)->458464K(498820K) NonClass: 400681K(428264K)->400681K(428264K) Class: 57782K(70556K)->57782K(70556K)
[2025-09-10T17:09:27.077+0800][1275.593s][info][gc            ] GC(156) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 881M->642M(1026M) 17.025ms
[2025-09-10T17:09:27.078+0800][1275.594s][info][gc,cpu        ] GC(156) User=0.09s Sys=0.00s Real=0.02s
[2025-09-10T17:09:27.125+0800][1275.642s][info][gc,start      ] GC(157) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:27.125+0800][1275.642s][info][gc,task       ] GC(157) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:27.138+0800][1275.654s][info][gc,phases     ] GC(157)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:09:27.138+0800][1275.655s][info][gc,phases     ] GC(157)   Evacuate Collection Set: 10.6ms
[2025-09-10T17:09:27.139+0800][1275.655s][info][gc,phases     ] GC(157)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:27.139+0800][1275.655s][info][gc,phases     ] GC(157)   Other: 0.9ms
[2025-09-10T17:09:27.139+0800][1275.656s][info][gc,heap       ] GC(157) Eden regions: 28->0(246)
[2025-09-10T17:09:27.139+0800][1275.656s][info][gc,heap       ] GC(157) Survivor regions: 23->4(7)
[2025-09-10T17:09:27.140+0800][1275.656s][info][gc,heap       ] GC(157) Old regions: 616->627
[2025-09-10T17:09:27.140+0800][1275.656s][info][gc,heap       ] GC(157) Humongous regions: 5->5
[2025-09-10T17:09:27.140+0800][1275.657s][info][gc,metaspace  ] GC(157) Metaspace: 458467K(498820K)->458467K(498820K) NonClass: 400685K(428264K)->400685K(428264K) Class: 57782K(70556K)->57782K(70556K)
[2025-09-10T17:09:27.140+0800][1275.657s][info][gc            ] GC(157) Pause Young (Mixed) (G1 Evacuation Pause) 670M->634M(1026M) 15.091ms
[2025-09-10T17:09:27.140+0800][1275.657s][info][gc,cpu        ] GC(157) User=0.17s Sys=0.00s Real=0.01s
[2025-09-10T17:09:28.261+0800][1276.777s][info][gc,start      ] GC(158) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:28.263+0800][1276.779s][info][gc,task       ] GC(158) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:28.269+0800][1276.785s][info][gc,phases     ] GC(158)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:28.269+0800][1276.786s][info][gc,phases     ] GC(158)   Evacuate Collection Set: 4.9ms
[2025-09-10T17:09:28.269+0800][1276.786s][info][gc,phases     ] GC(158)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:28.270+0800][1276.786s][info][gc,phases     ] GC(158)   Other: 2.4ms
[2025-09-10T17:09:28.270+0800][1276.787s][info][gc,heap       ] GC(158) Eden regions: 246->0(234)
[2025-09-10T17:09:28.270+0800][1276.787s][info][gc,heap       ] GC(158) Survivor regions: 4->16(32)
[2025-09-10T17:09:28.270+0800][1276.787s][info][gc,heap       ] GC(158) Old regions: 627->627
[2025-09-10T17:09:28.271+0800][1276.788s][info][gc,heap       ] GC(158) Humongous regions: 5->5
[2025-09-10T17:09:28.272+0800][1276.788s][info][gc,metaspace  ] GC(158) Metaspace: 458538K(498820K)->458538K(498820K) NonClass: 400755K(428264K)->400755K(428264K) Class: 57783K(70556K)->57783K(70556K)
[2025-09-10T17:09:28.272+0800][1276.789s][info][gc            ] GC(158) Pause Young (Normal) (G1 Evacuation Pause) 880M->646M(1026M) 11.634ms
[2025-09-10T17:09:28.273+0800][1276.790s][info][gc,cpu        ] GC(158) User=0.07s Sys=0.00s Real=0.02s
[2025-09-10T17:09:28.611+0800][1277.127s][info][gc,start      ] GC(159) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:09:28.611+0800][1277.128s][info][gc,task       ] GC(159) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:28.620+0800][1277.137s][info][gc,phases     ] GC(159)   Pre Evacuate Collection Set: 1.3ms
[2025-09-10T17:09:28.621+0800][1277.137s][info][gc,phases     ] GC(159)   Evacuate Collection Set: 6.2ms
[2025-09-10T17:09:28.621+0800][1277.137s][info][gc,phases     ] GC(159)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:28.621+0800][1277.138s][info][gc,phases     ] GC(159)   Other: 0.8ms
[2025-09-10T17:09:28.621+0800][1277.138s][info][gc,heap       ] GC(159) Eden regions: 58->0(205)
[2025-09-10T17:09:28.621+0800][1277.138s][info][gc,heap       ] GC(159) Survivor regions: 16->15(32)
[2025-09-10T17:09:28.621+0800][1277.138s][info][gc,heap       ] GC(159) Old regions: 627->627
[2025-09-10T17:09:28.622+0800][1277.138s][info][gc,heap       ] GC(159) Humongous regions: 5->4
[2025-09-10T17:09:28.622+0800][1277.138s][info][gc,metaspace  ] GC(159) Metaspace: 458624K(498820K)->458624K(498820K) NonClass: 400830K(428264K)->400830K(428264K) Class: 57793K(70556K)->57793K(70556K)
[2025-09-10T17:09:28.622+0800][1277.138s][info][gc            ] GC(159) Pause Young (Concurrent Start) (G1 Humongous Allocation) 704M->644M(1026M) 10.915ms
[2025-09-10T17:09:28.622+0800][1277.138s][info][gc,cpu        ] GC(159) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:09:28.622+0800][1277.139s][info][gc            ] GC(160) Concurrent Cycle
[2025-09-10T17:09:28.622+0800][1277.139s][info][gc,marking    ] GC(160) Concurrent Clear Claimed Marks
[2025-09-10T17:09:28.623+0800][1277.140s][info][gc,marking    ] GC(160) Concurrent Clear Claimed Marks 1.066ms
[2025-09-10T17:09:28.623+0800][1277.140s][info][gc,marking    ] GC(160) Concurrent Scan Root Regions
[2025-09-10T17:09:28.626+0800][1277.142s][info][gc,marking    ] GC(160) Concurrent Scan Root Regions 2.254ms
[2025-09-10T17:09:28.626+0800][1277.143s][info][gc,marking    ] GC(160) Concurrent Mark (1277.143s)
[2025-09-10T17:09:28.626+0800][1277.143s][info][gc,marking    ] GC(160) Concurrent Mark From Roots
[2025-09-10T17:09:28.626+0800][1277.143s][info][gc,task       ] GC(160) Using 4 workers of 4 for marking
[2025-09-10T17:09:28.786+0800][1277.303s][info][gc,marking    ] GC(160) Concurrent Mark From Roots 160.310ms
[2025-09-10T17:09:28.787+0800][1277.303s][info][gc,marking    ] GC(160) Concurrent Preclean
[2025-09-10T17:09:28.788+0800][1277.304s][info][gc,marking    ] GC(160) Concurrent Preclean 1.095ms
[2025-09-10T17:09:28.788+0800][1277.305s][info][gc,marking    ] GC(160) Concurrent Mark (1277.143s, 1277.305s) 162.153ms
[2025-09-10T17:09:28.789+0800][1277.305s][info][gc,start      ] GC(160) Pause Remark
[2025-09-10T17:09:28.830+0800][1277.347s][info][gc,stringtable] GC(160) Cleaned string and symbol table, strings: 309176 processed, 228 removed, symbols: 521601 processed, 8 removed
[2025-09-10T17:09:28.831+0800][1277.348s][info][gc            ] GC(160) Pause Remark 718M->714M(1026M) 42.507ms
[2025-09-10T17:09:28.832+0800][1277.348s][info][gc,cpu        ] GC(160) User=0.47s Sys=0.00s Real=0.05s
[2025-09-10T17:09:28.832+0800][1277.349s][info][gc,marking    ] GC(160) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:28.988+0800][1277.504s][info][gc,marking    ] GC(160) Concurrent Rebuild Remembered Sets 155.603ms
[2025-09-10T17:09:28.995+0800][1277.511s][info][gc,start      ] GC(160) Pause Cleanup
[2025-09-10T17:09:28.996+0800][1277.512s][info][gc            ] GC(160) Pause Cleanup 746M->746M(1026M) 1.068ms
[2025-09-10T17:09:28.996+0800][1277.513s][info][gc,cpu        ] GC(160) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:09:29.000+0800][1277.517s][info][gc,marking    ] GC(160) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:29.002+0800][1277.519s][info][gc,marking    ] GC(160) Concurrent Cleanup for Next Mark 2.442ms
[2025-09-10T17:09:29.003+0800][1277.519s][info][gc            ] GC(160) Concurrent Cycle 380.577ms
[2025-09-10T17:09:29.488+0800][1278.004s][info][gc,start      ] GC(161) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:29.488+0800][1278.005s][info][gc,task       ] GC(161) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:29.496+0800][1278.012s][info][gc,phases     ] GC(161)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:29.498+0800][1278.014s][info][gc,phases     ] GC(161)   Evacuate Collection Set: 6.3ms
[2025-09-10T17:09:29.499+0800][1278.016s][info][gc,phases     ] GC(161)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:29.500+0800][1278.016s][info][gc,phases     ] GC(161)   Other: 0.9ms
[2025-09-10T17:09:29.501+0800][1278.018s][info][gc,heap       ] GC(161) Eden regions: 205->0(23)
[2025-09-10T17:09:29.502+0800][1278.019s][info][gc,heap       ] GC(161) Survivor regions: 15->28(28)
[2025-09-10T17:09:29.502+0800][1278.019s][info][gc,heap       ] GC(161) Old regions: 623->625
[2025-09-10T17:09:29.502+0800][1278.019s][info][gc,heap       ] GC(161) Humongous regions: 5->5
[2025-09-10T17:09:29.503+0800][1278.019s][info][gc,metaspace  ] GC(161) Metaspace: 458662K(498820K)->458662K(498820K) NonClass: 400873K(428264K)->400873K(428264K) Class: 57789K(70556K)->57789K(70556K)
[2025-09-10T17:09:29.504+0800][1278.021s][info][gc            ] GC(161) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 846M->655M(1026M) 16.469ms
[2025-09-10T17:09:29.505+0800][1278.022s][info][gc,cpu        ] GC(161) User=0.11s Sys=0.00s Real=0.02s
[2025-09-10T17:09:29.578+0800][1278.095s][info][gc,start      ] GC(162) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:29.591+0800][1278.108s][info][gc,task       ] GC(162) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:29.601+0800][1278.118s][info][gc,phases     ] GC(162)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:09:29.602+0800][1278.118s][info][gc,phases     ] GC(162)   Evacuate Collection Set: 7.8ms
[2025-09-10T17:09:29.602+0800][1278.118s][info][gc,phases     ] GC(162)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:09:29.602+0800][1278.119s][info][gc,phases     ] GC(162)   Other: 13.6ms
[2025-09-10T17:09:29.602+0800][1278.119s][info][gc,heap       ] GC(162) Eden regions: 23->0(197)
[2025-09-10T17:09:29.603+0800][1278.119s][info][gc,heap       ] GC(162) Survivor regions: 28->6(7)
[2025-09-10T17:09:29.603+0800][1278.119s][info][gc,heap       ] GC(162) Old regions: 625->649
[2025-09-10T17:09:29.603+0800][1278.120s][info][gc,heap       ] GC(162) Humongous regions: 5->5
[2025-09-10T17:09:29.603+0800][1278.120s][info][gc,metaspace  ] GC(162) Metaspace: 458667K(498820K)->458667K(498820K) NonClass: 400877K(428264K)->400877K(428264K) Class: 57789K(70556K)->57789K(70556K)
[2025-09-10T17:09:29.603+0800][1278.120s][info][gc            ] GC(162) Pause Young (Mixed) (G1 Evacuation Pause) 678M->657M(1026M) 24.891ms
[2025-09-10T17:09:29.604+0800][1278.120s][info][gc,cpu        ] GC(162) User=0.11s Sys=0.00s Real=0.03s
[2025-09-10T17:09:30.552+0800][1279.069s][info][gc,start      ] GC(163) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:30.553+0800][1279.070s][info][gc,task       ] GC(163) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:30.560+0800][1279.077s][info][gc,phases     ] GC(163)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:30.561+0800][1279.078s][info][gc,phases     ] GC(163)   Evacuate Collection Set: 4.6ms
[2025-09-10T17:09:30.563+0800][1279.080s][info][gc,phases     ] GC(163)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T17:09:30.565+0800][1279.081s][info][gc,phases     ] GC(163)   Other: 2.8ms
[2025-09-10T17:09:30.567+0800][1279.083s][info][gc,heap       ] GC(163) Eden regions: 197->0(200)
[2025-09-10T17:09:30.567+0800][1279.084s][info][gc,heap       ] GC(163) Survivor regions: 6->10(26)
[2025-09-10T17:09:30.567+0800][1279.084s][info][gc,heap       ] GC(163) Old regions: 649->649
[2025-09-10T17:09:30.567+0800][1279.084s][info][gc,heap       ] GC(163) Humongous regions: 5->5
[2025-09-10T17:09:30.568+0800][1279.084s][info][gc,metaspace  ] GC(163) Metaspace: 458725K(499076K)->458725K(499076K) NonClass: 400934K(428520K)->400934K(428520K) Class: 57790K(70556K)->57790K(70556K)
[2025-09-10T17:09:30.568+0800][1279.084s][info][gc            ] GC(163) Pause Young (Normal) (G1 Evacuation Pause) 854M->661M(1026M) 15.402ms
[2025-09-10T17:09:30.568+0800][1279.085s][info][gc,cpu        ] GC(163) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:09:31.575+0800][1280.092s][info][gc,start      ] GC(164) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:31.577+0800][1280.094s][info][gc,task       ] GC(164) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:31.586+0800][1280.102s][info][gc,phases     ] GC(164)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:31.587+0800][1280.103s][info][gc,phases     ] GC(164)   Evacuate Collection Set: 5.5ms
[2025-09-10T17:09:31.587+0800][1280.104s][info][gc,phases     ] GC(164)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:31.587+0800][1280.104s][info][gc,phases     ] GC(164)   Other: 4.2ms
[2025-09-10T17:09:31.588+0800][1280.104s][info][gc,heap       ] GC(164) Eden regions: 200->0(198)
[2025-09-10T17:09:31.588+0800][1280.104s][info][gc,heap       ] GC(164) Survivor regions: 10->18(27)
[2025-09-10T17:09:31.588+0800][1280.105s][info][gc,heap       ] GC(164) Old regions: 649->649
[2025-09-10T17:09:31.588+0800][1280.105s][info][gc,heap       ] GC(164) Humongous regions: 5->5
[2025-09-10T17:09:31.589+0800][1280.105s][info][gc,metaspace  ] GC(164) Metaspace: 458777K(499076K)->458777K(499076K) NonClass: 400986K(428520K)->400986K(428520K) Class: 57790K(70556K)->57790K(70556K)
[2025-09-10T17:09:31.589+0800][1280.105s][info][gc            ] GC(164) Pause Young (Normal) (G1 Evacuation Pause) 861M->669M(1026M) 13.630ms
[2025-09-10T17:09:31.589+0800][1280.106s][info][gc,cpu        ] GC(164) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:09:32.310+0800][1280.826s][info][gc,start      ] GC(165) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:09:32.321+0800][1280.837s][info][gc,task       ] GC(165) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:32.332+0800][1280.848s][info][gc,phases     ] GC(165)   Pre Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:32.335+0800][1280.851s][info][gc,phases     ] GC(165)   Evacuate Collection Set: 8.1ms
[2025-09-10T17:09:32.336+0800][1280.853s][info][gc,phases     ] GC(165)   Post Evacuate Collection Set: 1.7ms
[2025-09-10T17:09:32.337+0800][1280.853s][info][gc,phases     ] GC(165)   Other: 11.3ms
[2025-09-10T17:09:32.337+0800][1280.854s][info][gc,heap       ] GC(165) Eden regions: 198->0(189)
[2025-09-10T17:09:32.337+0800][1280.854s][info][gc,heap       ] GC(165) Survivor regions: 18->25(27)
[2025-09-10T17:09:32.337+0800][1280.854s][info][gc,heap       ] GC(165) Old regions: 649->654
[2025-09-10T17:09:32.338+0800][1280.854s][info][gc,heap       ] GC(165) Humongous regions: 5->5
[2025-09-10T17:09:32.338+0800][1280.854s][info][gc,metaspace  ] GC(165) Metaspace: 458792K(499076K)->458792K(499076K) NonClass: 401001K(428520K)->401001K(428520K) Class: 57790K(70556K)->57790K(70556K)
[2025-09-10T17:09:32.338+0800][1280.855s][info][gc            ] GC(165) Pause Young (Concurrent Start) (G1 Evacuation Pause) 867M->682M(1026M) 28.305ms
[2025-09-10T17:09:32.338+0800][1280.855s][info][gc,cpu        ] GC(165) User=0.11s Sys=0.01s Real=0.02s
[2025-09-10T17:09:32.339+0800][1280.855s][info][gc            ] GC(166) Concurrent Cycle
[2025-09-10T17:09:32.339+0800][1280.855s][info][gc,marking    ] GC(166) Concurrent Clear Claimed Marks
[2025-09-10T17:09:32.340+0800][1280.856s][info][gc,marking    ] GC(166) Concurrent Clear Claimed Marks 1.080ms
[2025-09-10T17:09:32.340+0800][1280.857s][info][gc,marking    ] GC(166) Concurrent Scan Root Regions
[2025-09-10T17:09:32.343+0800][1280.860s][info][gc,marking    ] GC(166) Concurrent Scan Root Regions 3.153ms
[2025-09-10T17:09:32.344+0800][1280.860s][info][gc,marking    ] GC(166) Concurrent Mark (1280.860s)
[2025-09-10T17:09:32.344+0800][1280.860s][info][gc,marking    ] GC(166) Concurrent Mark From Roots
[2025-09-10T17:09:32.344+0800][1280.861s][info][gc,task       ] GC(166) Using 4 workers of 4 for marking
[2025-09-10T17:09:32.496+0800][1281.012s][info][gc,marking    ] GC(166) Concurrent Mark From Roots 151.681ms
[2025-09-10T17:09:32.496+0800][1281.013s][info][gc,marking    ] GC(166) Concurrent Preclean
[2025-09-10T17:09:32.497+0800][1281.014s][info][gc,marking    ] GC(166) Concurrent Preclean 1.190ms
[2025-09-10T17:09:32.498+0800][1281.014s][info][gc,marking    ] GC(166) Concurrent Mark (1280.860s, 1281.014s) 154.024ms
[2025-09-10T17:09:32.498+0800][1281.015s][info][gc,start      ] GC(166) Pause Remark
[2025-09-10T17:09:32.513+0800][1281.029s][info][gc,stringtable] GC(166) Cleaned string and symbol table, strings: 456017 processed, 119 removed, symbols: 521644 processed, 39 removed
[2025-09-10T17:09:32.514+0800][1281.030s][info][gc            ] GC(166) Pause Remark 685M->685M(1026M) 15.620ms
[2025-09-10T17:09:32.514+0800][1281.031s][info][gc,cpu        ] GC(166) User=0.18s Sys=0.01s Real=0.02s
[2025-09-10T17:09:32.515+0800][1281.031s][info][gc,marking    ] GC(166) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:32.677+0800][1281.193s][info][gc,marking    ] GC(166) Concurrent Rebuild Remembered Sets 162.185ms
[2025-09-10T17:09:32.682+0800][1281.199s][info][gc,start      ] GC(166) Pause Cleanup
[2025-09-10T17:09:32.684+0800][1281.200s][info][gc            ] GC(166) Pause Cleanup 700M->700M(1026M) 1.835ms
[2025-09-10T17:09:32.685+0800][1281.201s][info][gc,cpu        ] GC(166) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:09:32.685+0800][1281.202s][info][gc,marking    ] GC(166) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:32.686+0800][1281.203s][info][gc,marking    ] GC(166) Concurrent Cleanup for Next Mark 1.101ms
[2025-09-10T17:09:32.687+0800][1281.203s][info][gc            ] GC(166) Concurrent Cycle 348.158ms
[2025-09-10T17:09:33.339+0800][1281.855s][info][gc,start      ] GC(167) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:33.339+0800][1281.856s][info][gc,task       ] GC(167) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:33.345+0800][1281.862s][info][gc,phases     ] GC(167)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:33.346+0800][1281.862s][info][gc,phases     ] GC(167)   Evacuate Collection Set: 5.1ms
[2025-09-10T17:09:33.346+0800][1281.863s][info][gc,phases     ] GC(167)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T17:09:33.346+0800][1281.863s][info][gc,phases     ] GC(167)   Other: 0.9ms
[2025-09-10T17:09:33.346+0800][1281.863s][info][gc,heap       ] GC(167) Eden regions: 189->0(45)
[2025-09-10T17:09:33.347+0800][1281.863s][info][gc,heap       ] GC(167) Survivor regions: 25->6(27)
[2025-09-10T17:09:33.347+0800][1281.863s][info][gc,heap       ] GC(167) Old regions: 654->667
[2025-09-10T17:09:33.347+0800][1281.863s][info][gc,heap       ] GC(167) Humongous regions: 5->5
[2025-09-10T17:09:33.347+0800][1281.864s][info][gc,metaspace  ] GC(167) Metaspace: 458945K(499076K)->458945K(499076K) NonClass: 401154K(428520K)->401154K(428520K) Class: 57791K(70556K)->57791K(70556K)
[2025-09-10T17:09:33.347+0800][1281.864s][info][gc            ] GC(167) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 871M->675M(1026M) 8.309ms
[2025-09-10T17:09:33.347+0800][1281.864s][info][gc,cpu        ] GC(167) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:09:33.422+0800][1281.938s][info][gc,start      ] GC(168) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:33.422+0800][1281.939s][info][gc,task       ] GC(168) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:33.435+0800][1281.951s][info][gc,phases     ] GC(168)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T17:09:33.435+0800][1281.952s][info][gc,phases     ] GC(168)   Evacuate Collection Set: 10.5ms
[2025-09-10T17:09:33.435+0800][1281.952s][info][gc,phases     ] GC(168)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:09:33.435+0800][1281.952s][info][gc,phases     ] GC(168)   Other: 1.1ms
[2025-09-10T17:09:33.436+0800][1281.952s][info][gc,heap       ] GC(168) Eden regions: 45->0(206)
[2025-09-10T17:09:33.436+0800][1281.952s][info][gc,heap       ] GC(168) Survivor regions: 6->4(7)
[2025-09-10T17:09:33.436+0800][1281.952s][info][gc,heap       ] GC(168) Old regions: 667->660
[2025-09-10T17:09:33.436+0800][1281.952s][info][gc,heap       ] GC(168) Humongous regions: 5->5
[2025-09-10T17:09:33.436+0800][1281.953s][info][gc,metaspace  ] GC(168) Metaspace: 458946K(499076K)->458946K(499076K) NonClass: 401154K(428520K)->401154K(428520K) Class: 57791K(70556K)->57791K(70556K)
[2025-09-10T17:09:33.436+0800][1281.953s][info][gc            ] GC(168) Pause Young (Mixed) (G1 Evacuation Pause) 720M->667M(1026M) 14.488ms
[2025-09-10T17:09:33.436+0800][1281.953s][info][gc,cpu        ] GC(168) User=0.15s Sys=0.00s Real=0.01s
[2025-09-10T17:09:34.426+0800][1282.943s][info][gc,start      ] GC(169) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:34.427+0800][1282.944s][info][gc,task       ] GC(169) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:34.435+0800][1282.951s][info][gc,phases     ] GC(169)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:34.435+0800][1282.952s][info][gc,phases     ] GC(169)   Evacuate Collection Set: 5.9ms
[2025-09-10T17:09:34.435+0800][1282.952s][info][gc,phases     ] GC(169)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:09:34.436+0800][1282.952s][info][gc,phases     ] GC(169)   Other: 1.0ms
[2025-09-10T17:09:34.436+0800][1282.953s][info][gc,heap       ] GC(169) Eden regions: 206->0(198)
[2025-09-10T17:09:34.436+0800][1282.953s][info][gc,heap       ] GC(169) Survivor regions: 4->16(27)
[2025-09-10T17:09:34.436+0800][1282.953s][info][gc,heap       ] GC(169) Old regions: 660->660
[2025-09-10T17:09:34.437+0800][1282.953s][info][gc,heap       ] GC(169) Humongous regions: 5->5
[2025-09-10T17:09:34.437+0800][1282.953s][info][gc,metaspace  ] GC(169) Metaspace: 459706K(499972K)->459706K(499972K) NonClass: 401838K(429288K)->401838K(429288K) Class: 57868K(70684K)->57868K(70684K)
[2025-09-10T17:09:34.437+0800][1282.954s][info][gc            ] GC(169) Pause Young (Normal) (G1 Evacuation Pause) 873M->678M(1026M) 10.657ms
[2025-09-10T17:09:34.437+0800][1282.954s][info][gc,cpu        ] GC(169) User=0.08s Sys=0.01s Real=0.01s
[2025-09-10T17:09:35.127+0800][1283.643s][info][gc,start      ] GC(170) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:35.127+0800][1283.643s][info][gc,task       ] GC(170) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:35.134+0800][1283.650s][info][gc,phases     ] GC(170)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:35.134+0800][1283.651s][info][gc,phases     ] GC(170)   Evacuate Collection Set: 5.3ms
[2025-09-10T17:09:35.134+0800][1283.651s][info][gc,phases     ] GC(170)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:35.135+0800][1283.651s][info][gc,phases     ] GC(170)   Other: 1.1ms
[2025-09-10T17:09:35.135+0800][1283.651s][info][gc,heap       ] GC(170) Eden regions: 198->0(187)
[2025-09-10T17:09:35.135+0800][1283.652s][info][gc,heap       ] GC(170) Survivor regions: 16->26(27)
[2025-09-10T17:09:35.135+0800][1283.652s][info][gc,heap       ] GC(170) Old regions: 660->663
[2025-09-10T17:09:35.135+0800][1283.652s][info][gc,heap       ] GC(170) Humongous regions: 5->5
[2025-09-10T17:09:35.136+0800][1283.653s][info][gc,metaspace  ] GC(170) Metaspace: 459812K(499972K)->459812K(499972K) NonClass: 401928K(429288K)->401928K(429288K) Class: 57884K(70684K)->57884K(70684K)
[2025-09-10T17:09:35.137+0800][1283.653s][info][gc            ] GC(170) Pause Young (Normal) (G1 Evacuation Pause) 876M->691M(1026M) 10.114ms
[2025-09-10T17:09:35.137+0800][1283.653s][info][gc,cpu        ] GC(170) User=0.06s Sys=0.01s Real=0.01s
[2025-09-10T17:09:36.084+0800][1284.600s][info][gc,start      ] GC(171) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:09:36.084+0800][1284.600s][info][gc,task       ] GC(171) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:36.092+0800][1284.608s][info][gc,phases     ] GC(171)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:36.092+0800][1284.609s][info][gc,phases     ] GC(171)   Evacuate Collection Set: 6.1ms
[2025-09-10T17:09:36.092+0800][1284.609s][info][gc,phases     ] GC(171)   Post Evacuate Collection Set: 0.6ms
[2025-09-10T17:09:36.092+0800][1284.609s][info][gc,phases     ] GC(171)   Other: 0.6ms
[2025-09-10T17:09:36.092+0800][1284.609s][info][gc,heap       ] GC(171) Eden regions: 187->0(191)
[2025-09-10T17:09:36.092+0800][1284.609s][info][gc,heap       ] GC(171) Survivor regions: 26->14(27)
[2025-09-10T17:09:36.092+0800][1284.609s][info][gc,heap       ] GC(171) Old regions: 663->675
[2025-09-10T17:09:36.093+0800][1284.609s][info][gc,heap       ] GC(171) Humongous regions: 5->5
[2025-09-10T17:09:36.093+0800][1284.609s][info][gc,metaspace  ] GC(171) Metaspace: 459926K(500228K)->459926K(500228K) NonClass: 402038K(429544K)->402038K(429544K) Class: 57888K(70684K)->57888K(70684K)
[2025-09-10T17:09:36.093+0800][1284.609s][info][gc            ] GC(171) Pause Young (Concurrent Start) (G1 Evacuation Pause) 878M->691M(1026M) 9.184ms
[2025-09-10T17:09:36.093+0800][1284.610s][info][gc,cpu        ] GC(171) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:09:36.093+0800][1284.610s][info][gc            ] GC(172) Concurrent Cycle
[2025-09-10T17:09:36.094+0800][1284.610s][info][gc,marking    ] GC(172) Concurrent Clear Claimed Marks
[2025-09-10T17:09:36.095+0800][1284.611s][info][gc,marking    ] GC(172) Concurrent Clear Claimed Marks 0.983ms
[2025-09-10T17:09:36.095+0800][1284.611s][info][gc,marking    ] GC(172) Concurrent Scan Root Regions
[2025-09-10T17:09:36.097+0800][1284.614s][info][gc,marking    ] GC(172) Concurrent Scan Root Regions 2.614ms
[2025-09-10T17:09:36.098+0800][1284.614s][info][gc,marking    ] GC(172) Concurrent Mark (1284.614s)
[2025-09-10T17:09:36.098+0800][1284.614s][info][gc,marking    ] GC(172) Concurrent Mark From Roots
[2025-09-10T17:09:36.098+0800][1284.614s][info][gc,task       ] GC(172) Using 4 workers of 4 for marking
[2025-09-10T17:09:36.279+0800][1284.796s][info][gc,marking    ] GC(172) Concurrent Mark From Roots 181.564ms
[2025-09-10T17:09:36.285+0800][1284.802s][info][gc,marking    ] GC(172) Concurrent Preclean
[2025-09-10T17:09:36.292+0800][1284.808s][info][gc,marking    ] GC(172) Concurrent Preclean 6.357ms
[2025-09-10T17:09:36.295+0800][1284.811s][info][gc,marking    ] GC(172) Concurrent Mark (1284.614s, 1284.811s) 197.121ms
[2025-09-10T17:09:36.301+0800][1284.817s][info][gc,start      ] GC(172) Pause Remark
[2025-09-10T17:09:36.320+0800][1284.837s][info][gc,stringtable] GC(172) Cleaned string and symbol table, strings: 579991 processed, 233 removed, symbols: 523034 processed, 20 removed
[2025-09-10T17:09:36.323+0800][1284.839s][info][gc            ] GC(172) Pause Remark 758M->757M(1026M) 21.973ms
[2025-09-10T17:09:36.323+0800][1284.840s][info][gc,cpu        ] GC(172) User=0.15s Sys=0.01s Real=0.03s
[2025-09-10T17:09:36.325+0800][1284.841s][info][gc,marking    ] GC(172) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:36.464+0800][1284.981s][info][gc,marking    ] GC(172) Concurrent Rebuild Remembered Sets 139.470ms
[2025-09-10T17:09:36.466+0800][1284.983s][info][gc,start      ] GC(172) Pause Cleanup
[2025-09-10T17:09:36.467+0800][1284.984s][info][gc            ] GC(172) Pause Cleanup 758M->758M(1026M) 0.894ms
[2025-09-10T17:09:36.468+0800][1284.985s][info][gc,cpu        ] GC(172) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:09:36.468+0800][1284.985s][info][gc,marking    ] GC(172) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:36.469+0800][1284.986s][info][gc,marking    ] GC(172) Concurrent Cleanup for Next Mark 0.958ms
[2025-09-10T17:09:36.470+0800][1284.986s][info][gc            ] GC(172) Concurrent Cycle 376.165ms
[2025-09-10T17:09:37.080+0800][1285.596s][info][gc,start      ] GC(173) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:37.080+0800][1285.597s][info][gc,task       ] GC(173) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:37.088+0800][1285.605s][info][gc,phases     ] GC(173)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:37.089+0800][1285.605s][info][gc,phases     ] GC(173)   Evacuate Collection Set: 6.1ms
[2025-09-10T17:09:37.089+0800][1285.606s][info][gc,phases     ] GC(173)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:09:37.090+0800][1285.606s][info][gc,phases     ] GC(173)   Other: 1.2ms
[2025-09-10T17:09:37.090+0800][1285.607s][info][gc,heap       ] GC(173) Eden regions: 191->0(29)
[2025-09-10T17:09:37.090+0800][1285.607s][info][gc,heap       ] GC(173) Survivor regions: 14->22(26)
[2025-09-10T17:09:37.090+0800][1285.607s][info][gc,heap       ] GC(173) Old regions: 674->674
[2025-09-10T17:09:37.091+0800][1285.607s][info][gc,heap       ] GC(173) Humongous regions: 5->5
[2025-09-10T17:09:37.091+0800][1285.608s][info][gc,metaspace  ] GC(173) Metaspace: 460071K(500228K)->460071K(500228K) NonClass: 402163K(429544K)->402163K(429544K) Class: 57907K(70684K)->57907K(70684K)
[2025-09-10T17:09:37.091+0800][1285.608s][info][gc            ] GC(173) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 881M->699M(1026M) 11.444ms
[2025-09-10T17:09:37.092+0800][1285.608s][info][gc,cpu        ] GC(173) User=0.09s Sys=0.00s Real=0.02s
[2025-09-10T17:09:37.188+0800][1285.704s][info][gc,start      ] GC(174) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:37.188+0800][1285.705s][info][gc,task       ] GC(174) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:37.198+0800][1285.714s][info][gc,phases     ] GC(174)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T17:09:37.198+0800][1285.715s][info][gc,phases     ] GC(174)   Evacuate Collection Set: 8.2ms
[2025-09-10T17:09:37.198+0800][1285.715s][info][gc,phases     ] GC(174)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:37.199+0800][1285.715s][info][gc,phases     ] GC(174)   Other: 0.8ms
[2025-09-10T17:09:37.199+0800][1285.716s][info][gc,heap       ] GC(174) Eden regions: 29->0(196)
[2025-09-10T17:09:37.199+0800][1285.716s][info][gc,heap       ] GC(174) Survivor regions: 22->4(7)
[2025-09-10T17:09:37.199+0800][1285.716s][info][gc,heap       ] GC(174) Old regions: 674->680
[2025-09-10T17:09:37.199+0800][1285.716s][info][gc,heap       ] GC(174) Humongous regions: 5->5
[2025-09-10T17:09:37.199+0800][1285.716s][info][gc,metaspace  ] GC(174) Metaspace: 460071K(500228K)->460071K(500228K) NonClass: 402163K(429544K)->402163K(429544K) Class: 57907K(70684K)->57907K(70684K)
[2025-09-10T17:09:37.200+0800][1285.716s][info][gc            ] GC(174) Pause Young (Mixed) (G1 Evacuation Pause) 728M->686M(1026M) 11.786ms
[2025-09-10T17:09:37.200+0800][1285.716s][info][gc,cpu        ] GC(174) User=0.12s Sys=0.00s Real=0.01s
[2025-09-10T17:09:38.178+0800][1286.695s][info][gc,start      ] GC(175) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:38.179+0800][1286.695s][info][gc,task       ] GC(175) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:38.185+0800][1286.701s][info][gc,phases     ] GC(175)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:38.185+0800][1286.702s][info][gc,phases     ] GC(175)   Evacuate Collection Set: 2.8ms
[2025-09-10T17:09:38.185+0800][1286.702s][info][gc,phases     ] GC(175)   Post Evacuate Collection Set: 3.0ms
[2025-09-10T17:09:38.186+0800][1286.702s][info][gc,phases     ] GC(175)   Other: 0.5ms
[2025-09-10T17:09:38.186+0800][1286.702s][info][gc,heap       ] GC(175) Eden regions: 196->0(201)
[2025-09-10T17:09:38.186+0800][1286.703s][info][gc,heap       ] GC(175) Survivor regions: 4->4(25)
[2025-09-10T17:09:38.186+0800][1286.703s][info][gc,heap       ] GC(175) Old regions: 680->680
[2025-09-10T17:09:38.186+0800][1286.703s][info][gc,heap       ] GC(175) Humongous regions: 5->5
[2025-09-10T17:09:38.187+0800][1286.703s][info][gc,metaspace  ] GC(175) Metaspace: 460449K(500868K)->460449K(500868K) NonClass: 402482K(430056K)->402482K(430056K) Class: 57966K(70812K)->57966K(70812K)
[2025-09-10T17:09:38.187+0800][1286.703s][info][gc            ] GC(175) Pause Young (Normal) (G1 Evacuation Pause) 882M->687M(1026M) 8.398ms
[2025-09-10T17:09:38.187+0800][1286.703s][info][gc,cpu        ] GC(175) User=0.07s Sys=0.01s Real=0.01s
[2025-09-10T17:09:38.502+0800][1287.019s][info][gc,start      ] GC(176) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:09:38.503+0800][1287.019s][info][gc,task       ] GC(176) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:38.510+0800][1287.026s][info][gc,phases     ] GC(176)   Pre Evacuate Collection Set: 0.9ms
[2025-09-10T17:09:38.510+0800][1287.026s][info][gc,phases     ] GC(176)   Evacuate Collection Set: 4.8ms
[2025-09-10T17:09:38.510+0800][1287.027s][info][gc,phases     ] GC(176)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:38.510+0800][1287.027s][info][gc,phases     ] GC(176)   Other: 0.6ms
[2025-09-10T17:09:38.510+0800][1287.027s][info][gc,heap       ] GC(176) Eden regions: 71->0(162)
[2025-09-10T17:09:38.511+0800][1287.027s][info][gc,heap       ] GC(176) Survivor regions: 4->12(26)
[2025-09-10T17:09:38.511+0800][1287.027s][info][gc,heap       ] GC(176) Old regions: 680->680
[2025-09-10T17:09:38.511+0800][1287.027s][info][gc,heap       ] GC(176) Humongous regions: 5->4
[2025-09-10T17:09:38.511+0800][1287.028s][info][gc,metaspace  ] GC(176) Metaspace: 461118K(501380K)->461118K(501380K) NonClass: 403049K(430568K)->403049K(430568K) Class: 58069K(70812K)->58069K(70812K)
[2025-09-10T17:09:38.511+0800][1287.028s][info][gc            ] GC(176) Pause Young (Concurrent Start) (G1 Humongous Allocation) 758M->694M(1026M) 8.872ms
[2025-09-10T17:09:38.511+0800][1287.028s][info][gc,cpu        ] GC(176) User=0.05s Sys=0.01s Real=0.01s
[2025-09-10T17:09:38.512+0800][1287.028s][info][gc            ] GC(177) Concurrent Cycle
[2025-09-10T17:09:38.512+0800][1287.028s][info][gc,marking    ] GC(177) Concurrent Clear Claimed Marks
[2025-09-10T17:09:38.513+0800][1287.030s][info][gc,marking    ] GC(177) Concurrent Clear Claimed Marks 1.281ms
[2025-09-10T17:09:38.513+0800][1287.030s][info][gc,marking    ] GC(177) Concurrent Scan Root Regions
[2025-09-10T17:09:38.516+0800][1287.033s][info][gc,marking    ] GC(177) Concurrent Scan Root Regions 2.818ms
[2025-09-10T17:09:38.516+0800][1287.033s][info][gc,marking    ] GC(177) Concurrent Mark (1287.033s)
[2025-09-10T17:09:38.517+0800][1287.033s][info][gc,marking    ] GC(177) Concurrent Mark From Roots
[2025-09-10T17:09:38.517+0800][1287.033s][info][gc,task       ] GC(177) Using 4 workers of 4 for marking
[2025-09-10T17:09:38.804+0800][1287.321s][info][gc,marking    ] GC(177) Concurrent Mark From Roots 287.317ms
[2025-09-10T17:09:38.805+0800][1287.322s][info][gc,marking    ] GC(177) Concurrent Preclean
[2025-09-10T17:09:38.808+0800][1287.324s][info][gc,marking    ] GC(177) Concurrent Preclean 2.510ms
[2025-09-10T17:09:38.814+0800][1287.330s][info][gc,marking    ] GC(177) Concurrent Mark (1287.033s, 1287.330s) 297.385ms
[2025-09-10T17:09:38.823+0800][1287.339s][info][gc,start      ] GC(177) Pause Remark
[2025-09-10T17:09:38.851+0800][1287.367s][info][gc,stringtable] GC(177) Cleaned string and symbol table, strings: 623173 processed, 164 removed, symbols: 526775 processed, 74 removed
[2025-09-10T17:09:38.852+0800][1287.368s][info][gc            ] GC(177) Pause Remark 750M->750M(1026M) 28.867ms
[2025-09-10T17:09:38.852+0800][1287.369s][info][gc,cpu        ] GC(177) User=0.35s Sys=0.00s Real=0.03s
[2025-09-10T17:09:38.852+0800][1287.369s][info][gc,marking    ] GC(177) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:39.049+0800][1287.565s][info][gc,marking    ] GC(177) Concurrent Rebuild Remembered Sets 196.596ms
[2025-09-10T17:09:39.050+0800][1287.566s][info][gc,start      ] GC(177) Pause Cleanup
[2025-09-10T17:09:39.051+0800][1287.568s][info][gc            ] GC(177) Pause Cleanup 790M->790M(1026M) 1.266ms
[2025-09-10T17:09:39.051+0800][1287.568s][info][gc,cpu        ] GC(177) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:09:39.052+0800][1287.568s][info][gc,marking    ] GC(177) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:39.053+0800][1287.569s][info][gc,marking    ] GC(177) Concurrent Cleanup for Next Mark 1.153ms
[2025-09-10T17:09:39.054+0800][1287.570s][info][gc            ] GC(177) Concurrent Cycle 542.001ms
[2025-09-10T17:09:39.351+0800][1287.868s][info][gc,start      ] GC(178) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:39.351+0800][1287.868s][info][gc,task       ] GC(178) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:39.362+0800][1287.878s][info][gc,phases     ] GC(178)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:39.362+0800][1287.879s][info][gc,phases     ] GC(178)   Evacuate Collection Set: 8.8ms
[2025-09-10T17:09:39.362+0800][1287.879s][info][gc,phases     ] GC(178)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:39.363+0800][1287.879s][info][gc,phases     ] GC(178)   Other: 1.0ms
[2025-09-10T17:09:39.363+0800][1287.879s][info][gc,heap       ] GC(178) Eden regions: 162->0(29)
[2025-09-10T17:09:39.363+0800][1287.879s][info][gc,heap       ] GC(178) Survivor regions: 12->22(22)
[2025-09-10T17:09:39.363+0800][1287.879s][info][gc,heap       ] GC(178) Old regions: 680->682
[2025-09-10T17:09:39.363+0800][1287.880s][info][gc,heap       ] GC(178) Humongous regions: 8->6
[2025-09-10T17:09:39.363+0800][1287.880s][info][gc,metaspace  ] GC(178) Metaspace: 466016K(506500K)->466016K(506500K) NonClass: 407555K(435176K)->407555K(435176K) Class: 58460K(71324K)->58460K(71324K)
[2025-09-10T17:09:39.363+0800][1287.880s][info][gc            ] GC(178) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 860M->708M(1026M) 12.207ms
[2025-09-10T17:09:39.363+0800][1287.880s][info][gc,cpu        ] GC(178) User=0.17s Sys=0.01s Real=0.01s
[2025-09-10T17:09:39.473+0800][1287.990s][info][gc,start      ] GC(179) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:39.474+0800][1287.990s][info][gc,task       ] GC(179) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:39.493+0800][1288.010s][info][gc,phases     ] GC(179)   Pre Evacuate Collection Set: 0.4ms
[2025-09-10T17:09:39.494+0800][1288.010s][info][gc,phases     ] GC(179)   Evacuate Collection Set: 17.1ms
[2025-09-10T17:09:39.494+0800][1288.010s][info][gc,phases     ] GC(179)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T17:09:39.494+0800][1288.010s][info][gc,phases     ] GC(179)   Other: 1.0ms
[2025-09-10T17:09:39.494+0800][1288.011s][info][gc,heap       ] GC(179) Eden regions: 29->0(161)
[2025-09-10T17:09:39.494+0800][1288.011s][info][gc,heap       ] GC(179) Survivor regions: 22->3(7)
[2025-09-10T17:09:39.494+0800][1288.011s][info][gc,heap       ] GC(179) Old regions: 682->694
[2025-09-10T17:09:39.494+0800][1288.011s][info][gc,heap       ] GC(179) Humongous regions: 6->5
[2025-09-10T17:09:39.495+0800][1288.011s][info][gc,metaspace  ] GC(179) Metaspace: 467109K(507780K)->467109K(507780K) NonClass: 408592K(436456K)->408592K(436456K) Class: 58517K(71324K)->58517K(71324K)
[2025-09-10T17:09:39.495+0800][1288.011s][info][gc            ] GC(179) Pause Young (Mixed) (G1 Evacuation Pause) 737M->699M(1026M) 21.387ms
[2025-09-10T17:09:39.495+0800][1288.012s][info][gc,cpu        ] GC(179) User=0.33s Sys=0.00s Real=0.02s
[2025-09-10T17:09:40.029+0800][1288.545s][info][gc,start      ] GC(180) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:40.029+0800][1288.546s][info][gc,task       ] GC(180) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:40.037+0800][1288.554s][info][gc,phases     ] GC(180)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:40.038+0800][1288.554s][info][gc,phases     ] GC(180)   Evacuate Collection Set: 6.7ms
[2025-09-10T17:09:40.038+0800][1288.554s][info][gc,phases     ] GC(180)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:09:40.038+0800][1288.555s][info][gc,phases     ] GC(180)   Other: 0.9ms
[2025-09-10T17:09:40.038+0800][1288.555s][info][gc,heap       ] GC(180) Eden regions: 161->0(167)
[2025-09-10T17:09:40.038+0800][1288.555s][info][gc,heap       ] GC(180) Survivor regions: 3->5(21)
[2025-09-10T17:09:40.039+0800][1288.555s][info][gc,heap       ] GC(180) Old regions: 694->694
[2025-09-10T17:09:40.039+0800][1288.555s][info][gc,heap       ] GC(180) Humongous regions: 5->5
[2025-09-10T17:09:40.039+0800][1288.555s][info][gc,metaspace  ] GC(180) Metaspace: 470502K(511108K)->470502K(511108K) NonClass: 411698K(439528K)->411698K(439528K) Class: 58803K(71580K)->58803K(71580K)
[2025-09-10T17:09:40.039+0800][1288.556s][info][gc            ] GC(180) Pause Young (Normal) (G1 Evacuation Pause) 860M->702M(1026M) 10.306ms
[2025-09-10T17:09:40.039+0800][1288.556s][info][gc,cpu        ] GC(180) User=0.12s Sys=0.00s Real=0.01s
[2025-09-10T17:09:40.425+0800][1288.941s][info][gc,start      ] GC(181) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:40.425+0800][1288.941s][info][gc,task       ] GC(181) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:40.430+0800][1288.947s][info][gc,phases     ] GC(181)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:40.430+0800][1288.947s][info][gc,phases     ] GC(181)   Evacuate Collection Set: 3.9ms
[2025-09-10T17:09:40.430+0800][1288.947s][info][gc,phases     ] GC(181)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:09:40.431+0800][1288.947s][info][gc,phases     ] GC(181)   Other: 0.5ms
[2025-09-10T17:09:40.431+0800][1288.947s][info][gc,heap       ] GC(181) Eden regions: 167->0(174)
[2025-09-10T17:09:40.431+0800][1288.947s][info][gc,heap       ] GC(181) Survivor regions: 5->5(22)
[2025-09-10T17:09:40.431+0800][1288.947s][info][gc,heap       ] GC(181) Old regions: 694->694
[2025-09-10T17:09:40.431+0800][1288.948s][info][gc,heap       ] GC(181) Humongous regions: 5->5
[2025-09-10T17:09:40.431+0800][1288.948s][info][gc,metaspace  ] GC(181) Metaspace: 471524K(512132K)->471524K(512132K) NonClass: 412623K(440296K)->412623K(440296K) Class: 58901K(71836K)->58901K(71836K)
[2025-09-10T17:09:40.431+0800][1288.948s][info][gc            ] GC(181) Pause Young (Normal) (G1 Evacuation Pause) 869M->701M(1026M) 6.657ms
[2025-09-10T17:09:40.431+0800][1288.948s][info][gc,cpu        ] GC(181) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:09:40.828+0800][1289.345s][info][gc,start      ] GC(182) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:09:40.828+0800][1289.345s][info][gc,task       ] GC(182) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:40.836+0800][1289.353s][info][gc,phases     ] GC(182)   Pre Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:40.837+0800][1289.353s][info][gc,phases     ] GC(182)   Evacuate Collection Set: 5.4ms
[2025-09-10T17:09:40.837+0800][1289.353s][info][gc,phases     ] GC(182)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:09:40.837+0800][1289.354s][info][gc,phases     ] GC(182)   Other: 0.7ms
[2025-09-10T17:09:40.837+0800][1289.354s][info][gc,heap       ] GC(182) Eden regions: 174->0(181)
[2025-09-10T17:09:40.837+0800][1289.354s][info][gc,heap       ] GC(182) Survivor regions: 5->5(23)
[2025-09-10T17:09:40.837+0800][1289.354s][info][gc,heap       ] GC(182) Old regions: 694->694
[2025-09-10T17:09:40.838+0800][1289.354s][info][gc,heap       ] GC(182) Humongous regions: 5->5
[2025-09-10T17:09:40.838+0800][1289.354s][info][gc,metaspace  ] GC(182) Metaspace: 473232K(513668K)->473232K(513668K) NonClass: 414086K(441576K)->414086K(441576K) Class: 59145K(72092K)->59145K(72092K)
[2025-09-10T17:09:40.838+0800][1289.354s][info][gc            ] GC(182) Pause Young (Concurrent Start) (G1 Evacuation Pause) 875M->701M(1026M) 9.774ms
[2025-09-10T17:09:40.839+0800][1289.356s][info][gc,cpu        ] GC(182) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:09:40.839+0800][1289.356s][info][gc            ] GC(183) Concurrent Cycle
[2025-09-10T17:09:40.840+0800][1289.356s][info][gc,marking    ] GC(183) Concurrent Clear Claimed Marks
[2025-09-10T17:09:40.841+0800][1289.358s][info][gc,marking    ] GC(183) Concurrent Clear Claimed Marks 1.453ms
[2025-09-10T17:09:40.841+0800][1289.358s][info][gc,marking    ] GC(183) Concurrent Scan Root Regions
[2025-09-10T17:09:40.844+0800][1289.360s][info][gc,marking    ] GC(183) Concurrent Scan Root Regions 2.240ms
[2025-09-10T17:09:40.844+0800][1289.360s][info][gc,marking    ] GC(183) Concurrent Mark (1289.360s)
[2025-09-10T17:09:40.844+0800][1289.361s][info][gc,marking    ] GC(183) Concurrent Mark From Roots
[2025-09-10T17:09:40.844+0800][1289.361s][info][gc,task       ] GC(183) Using 4 workers of 4 for marking
[2025-09-10T17:09:41.064+0800][1289.581s][info][gc,marking    ] GC(183) Concurrent Mark From Roots 220.153ms
[2025-09-10T17:09:41.065+0800][1289.581s][info][gc,marking    ] GC(183) Concurrent Preclean
[2025-09-10T17:09:41.065+0800][1289.582s][info][gc,marking    ] GC(183) Concurrent Preclean 0.807ms
[2025-09-10T17:09:41.066+0800][1289.582s][info][gc,marking    ] GC(183) Concurrent Mark (1289.360s, 1289.582s) 221.718ms
[2025-09-10T17:09:41.066+0800][1289.583s][info][gc,start      ] GC(183) Pause Remark
[2025-09-10T17:09:41.114+0800][1289.630s][info][gc,stringtable] GC(183) Cleaned string and symbol table, strings: 627364 processed, 174 removed, symbols: 543557 processed, 113 removed
[2025-09-10T17:09:41.115+0800][1289.631s][info][gc            ] GC(183) Pause Remark 757M->757M(1026M) 48.639ms
[2025-09-10T17:09:41.115+0800][1289.632s][info][gc,cpu        ] GC(183) User=0.61s Sys=0.01s Real=0.05s
[2025-09-10T17:09:41.116+0800][1289.632s][info][gc,marking    ] GC(183) Concurrent Rebuild Remembered Sets
[2025-09-10T17:09:41.286+0800][1289.802s][info][gc,marking    ] GC(183) Concurrent Rebuild Remembered Sets 169.896ms
[2025-09-10T17:09:41.286+0800][1289.803s][info][gc,start      ] GC(183) Pause Cleanup
[2025-09-10T17:09:41.288+0800][1289.804s][info][gc            ] GC(183) Pause Cleanup 757M->757M(1026M) 1.090ms
[2025-09-10T17:09:41.288+0800][1289.804s][info][gc,cpu        ] GC(183) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:09:41.288+0800][1289.805s][info][gc,marking    ] GC(183) Concurrent Cleanup for Next Mark
[2025-09-10T17:09:41.289+0800][1289.806s][info][gc,marking    ] GC(183) Concurrent Cleanup for Next Mark 0.916ms
[2025-09-10T17:09:41.289+0800][1289.806s][info][gc            ] GC(183) Concurrent Cycle 449.694ms
[2025-09-10T17:09:41.572+0800][1290.089s][info][gc,start      ] GC(184) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:41.573+0800][1290.089s][info][gc,task       ] GC(184) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:41.580+0800][1290.097s][info][gc,phases     ] GC(184)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:41.581+0800][1290.097s][info][gc,phases     ] GC(184)   Evacuate Collection Set: 5.6ms
[2025-09-10T17:09:41.581+0800][1290.098s][info][gc,phases     ] GC(184)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:09:41.581+0800][1290.098s][info][gc,phases     ] GC(184)   Other: 1.1ms
[2025-09-10T17:09:41.582+0800][1290.098s][info][gc,heap       ] GC(184) Eden regions: 181->0(46)
[2025-09-10T17:09:41.582+0800][1290.098s][info][gc,heap       ] GC(184) Survivor regions: 5->5(24)
[2025-09-10T17:09:41.582+0800][1290.099s][info][gc,heap       ] GC(184) Old regions: 694->694
[2025-09-10T17:09:41.582+0800][1290.099s][info][gc,heap       ] GC(184) Humongous regions: 5->5
[2025-09-10T17:09:41.582+0800][1290.099s][info][gc,metaspace  ] GC(184) Metaspace: 473470K(513924K)->473470K(513924K) NonClass: 414300K(441832K)->414300K(441832K) Class: 59169K(72092K)->59169K(72092K)
[2025-09-10T17:09:41.583+0800][1290.099s][info][gc            ] GC(184) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 882M->701M(1026M) 10.208ms
[2025-09-10T17:09:41.583+0800][1290.099s][info][gc,cpu        ] GC(184) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:09:59.374+0800][1307.892s][info][gc,start      ] GC(185) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:09:59.374+0800][1307.893s][info][gc,task       ] GC(185) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:59.387+0800][1307.905s][info][gc,phases     ] GC(185)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:09:59.387+0800][1307.905s][info][gc,phases     ] GC(185)   Evacuate Collection Set: 8.6ms
[2025-09-10T17:09:59.389+0800][1307.907s][info][gc,phases     ] GC(185)   Post Evacuate Collection Set: 2.7ms
[2025-09-10T17:09:59.389+0800][1307.907s][info][gc,phases     ] GC(185)   Other: 1.1ms
[2025-09-10T17:09:59.389+0800][1307.908s][info][gc,heap       ] GC(185) Eden regions: 46->0(178)
[2025-09-10T17:09:59.389+0800][1307.908s][info][gc,heap       ] GC(185) Survivor regions: 5->7(7)
[2025-09-10T17:09:59.390+0800][1307.908s][info][gc,heap       ] GC(185) Old regions: 694->696
[2025-09-10T17:09:59.390+0800][1307.908s][info][gc,heap       ] GC(185) Humongous regions: 6->5
[2025-09-10T17:09:59.390+0800][1307.908s][info][gc,metaspace  ] GC(185) Metaspace: 473721K(514180K)->473721K(514180K) NonClass: 414516K(442088K)->414516K(442088K) Class: 59205K(72092K)->59205K(72092K)
[2025-09-10T17:09:59.390+0800][1307.909s][info][gc            ] GC(185) Pause Young (Mixed) (G1 Evacuation Pause) 748M->705M(1026M) 16.428ms
[2025-09-10T17:09:59.391+0800][1307.909s][info][gc,cpu        ] GC(185) User=0.13s Sys=0.00s Real=0.02s
[2025-09-10T17:09:59.653+0800][1308.171s][info][gc,start      ] GC(186) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:59.653+0800][1308.171s][info][gc,task       ] GC(186) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:59.659+0800][1308.177s][info][gc,phases     ] GC(186)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:59.659+0800][1308.177s][info][gc,phases     ] GC(186)   Evacuate Collection Set: 4.5ms
[2025-09-10T17:09:59.659+0800][1308.178s][info][gc,phases     ] GC(186)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:09:59.660+0800][1308.178s][info][gc,phases     ] GC(186)   Other: 0.6ms
[2025-09-10T17:09:59.660+0800][1308.178s][info][gc,heap       ] GC(186) Eden regions: 178->0(178)
[2025-09-10T17:09:59.660+0800][1308.178s][info][gc,heap       ] GC(186) Survivor regions: 7->11(24)
[2025-09-10T17:09:59.660+0800][1308.178s][info][gc,heap       ] GC(186) Old regions: 696->696
[2025-09-10T17:09:59.660+0800][1308.178s][info][gc,heap       ] GC(186) Humongous regions: 5->5
[2025-09-10T17:09:59.660+0800][1308.178s][info][gc,metaspace  ] GC(186) Metaspace: 475288K(516100K)->475288K(516100K) NonClass: 415848K(443624K)->415848K(443624K) Class: 59440K(72476K)->59440K(72476K)
[2025-09-10T17:09:59.660+0800][1308.179s][info][gc            ] GC(186) Pause Young (Normal) (G1 Evacuation Pause) 883M->709M(1026M) 7.610ms
[2025-09-10T17:09:59.660+0800][1308.179s][info][gc,cpu        ] GC(186) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:09:59.924+0800][1308.442s][info][gc,start      ] GC(187) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:09:59.924+0800][1308.443s][info][gc,task       ] GC(187) Using 15 workers of 15 for evacuation
[2025-09-10T17:09:59.931+0800][1308.450s][info][gc,phases     ] GC(187)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:09:59.932+0800][1308.450s][info][gc,phases     ] GC(187)   Evacuate Collection Set: 4.9ms
[2025-09-10T17:09:59.932+0800][1308.450s][info][gc,phases     ] GC(187)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T17:09:59.932+0800][1308.450s][info][gc,phases     ] GC(187)   Other: 0.6ms
[2025-09-10T17:09:59.932+0800][1308.451s][info][gc,heap       ] GC(187) Eden regions: 178->0(176)
[2025-09-10T17:09:59.932+0800][1308.451s][info][gc,heap       ] GC(187) Survivor regions: 11->17(24)
[2025-09-10T17:09:59.933+0800][1308.451s][info][gc,heap       ] GC(187) Old regions: 696->696
[2025-09-10T17:09:59.933+0800][1308.451s][info][gc,heap       ] GC(187) Humongous regions: 5->5
[2025-09-10T17:09:59.933+0800][1308.451s][info][gc,metaspace  ] GC(187) Metaspace: 475340K(516100K)->475340K(516100K) NonClass: 415896K(443624K)->415896K(443624K) Class: 59443K(72476K)->59443K(72476K)
[2025-09-10T17:09:59.933+0800][1308.451s][info][gc            ] GC(187) Pause Young (Normal) (G1 Evacuation Pause) 887M->715M(1026M) 8.944ms
[2025-09-10T17:09:59.933+0800][1308.451s][info][gc,cpu        ] GC(187) User=0.12s Sys=0.00s Real=0.01s
[2025-09-10T17:11:06.071+0800][1374.592s][info][gc,start      ] GC(188) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:11:06.071+0800][1374.592s][info][gc,task       ] GC(188) Using 15 workers of 15 for evacuation
[2025-09-10T17:11:06.082+0800][1374.603s][info][gc,phases     ] GC(188)   Pre Evacuate Collection Set: 1.5ms
[2025-09-10T17:11:06.083+0800][1374.604s][info][gc,phases     ] GC(188)   Evacuate Collection Set: 5.4ms
[2025-09-10T17:11:06.083+0800][1374.604s][info][gc,phases     ] GC(188)   Post Evacuate Collection Set: 3.9ms
[2025-09-10T17:11:06.084+0800][1374.605s][info][gc,phases     ] GC(188)   Other: 0.7ms
[2025-09-10T17:11:06.084+0800][1374.605s][info][gc,heap       ] GC(188) Eden regions: 27->0(120)
[2025-09-10T17:11:06.084+0800][1374.606s][info][gc,heap       ] GC(188) Survivor regions: 17->12(25)
[2025-09-10T17:11:06.085+0800][1374.606s][info][gc,heap       ] GC(188) Old regions: 696->698
[2025-09-10T17:11:06.085+0800][1374.606s][info][gc,heap       ] GC(188) Humongous regions: 5->4
[2025-09-10T17:11:06.086+0800][1374.607s][info][gc,metaspace  ] GC(188) Metaspace: 477034K(517892K)->477034K(517892K) NonClass: 417399K(445160K)->417399K(445160K) Class: 59634K(72732K)->59634K(72732K)
[2025-09-10T17:11:06.086+0800][1374.607s][info][gc            ] GC(188) Pause Young (Concurrent Start) (G1 Humongous Allocation) 742M->712M(1026M) 15.201ms
[2025-09-10T17:11:06.086+0800][1374.607s][info][gc,cpu        ] GC(188) User=0.10s Sys=0.00s Real=0.02s
[2025-09-10T17:11:06.086+0800][1374.607s][info][gc            ] GC(189) Concurrent Cycle
[2025-09-10T17:11:06.086+0800][1374.607s][info][gc,marking    ] GC(189) Concurrent Clear Claimed Marks
[2025-09-10T17:11:06.087+0800][1374.609s][info][gc,marking    ] GC(189) Concurrent Clear Claimed Marks 1.221ms
[2025-09-10T17:11:06.088+0800][1374.609s][info][gc,marking    ] GC(189) Concurrent Scan Root Regions
[2025-09-10T17:11:06.091+0800][1374.612s][info][gc,marking    ] GC(189) Concurrent Scan Root Regions 2.914ms
[2025-09-10T17:11:06.091+0800][1374.612s][info][gc,marking    ] GC(189) Concurrent Mark (1374.612s)
[2025-09-10T17:11:06.091+0800][1374.612s][info][gc,marking    ] GC(189) Concurrent Mark From Roots
[2025-09-10T17:11:06.091+0800][1374.612s][info][gc,task       ] GC(189) Using 4 workers of 4 for marking
[2025-09-10T17:11:06.296+0800][1374.817s][info][gc,marking    ] GC(189) Concurrent Mark From Roots 205.193ms
[2025-09-10T17:11:06.297+0800][1374.818s][info][gc,marking    ] GC(189) Concurrent Preclean
[2025-09-10T17:11:06.298+0800][1374.819s][info][gc,marking    ] GC(189) Concurrent Preclean 1.059ms
[2025-09-10T17:11:06.298+0800][1374.819s][info][gc,marking    ] GC(189) Concurrent Mark (1374.612s, 1374.819s) 207.052ms
[2025-09-10T17:11:06.299+0800][1374.820s][info][gc,start      ] GC(189) Pause Remark
[2025-09-10T17:11:06.344+0800][1374.865s][info][gc,stringtable] GC(189) Cleaned string and symbol table, strings: 628563 processed, 257 removed, symbols: 551415 processed, 45 removed
[2025-09-10T17:11:06.345+0800][1374.866s][info][gc            ] GC(189) Pause Remark 783M->783M(1026M) 46.722ms
[2025-09-10T17:11:06.346+0800][1374.867s][info][gc,cpu        ] GC(189) User=0.52s Sys=0.01s Real=0.05s
[2025-09-10T17:11:06.346+0800][1374.867s][info][gc,marking    ] GC(189) Concurrent Rebuild Remembered Sets
[2025-09-10T17:11:06.512+0800][1375.033s][info][gc,marking    ] GC(189) Concurrent Rebuild Remembered Sets 165.655ms
[2025-09-10T17:11:06.513+0800][1375.034s][info][gc,start      ] GC(189) Pause Cleanup
[2025-09-10T17:11:06.514+0800][1375.035s][info][gc            ] GC(189) Pause Cleanup 830M->830M(1026M) 1.384ms
[2025-09-10T17:11:06.515+0800][1375.036s][info][gc,cpu        ] GC(189) User=0.03s Sys=0.00s Real=0.00s
[2025-09-10T17:11:06.515+0800][1375.036s][info][gc,marking    ] GC(189) Concurrent Cleanup for Next Mark
[2025-09-10T17:11:06.516+0800][1375.037s][info][gc,marking    ] GC(189) Concurrent Cleanup for Next Mark 1.103ms
[2025-09-10T17:11:06.516+0800][1375.038s][info][gc            ] GC(189) Concurrent Cycle 430.340ms
[2025-09-10T17:11:06.526+0800][1375.047s][info][gc,start      ] GC(190) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:11:06.526+0800][1375.047s][info][gc,task       ] GC(190) Using 15 workers of 15 for evacuation
[2025-09-10T17:11:06.534+0800][1375.055s][info][gc,phases     ] GC(190)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:11:06.534+0800][1375.056s][info][gc,phases     ] GC(190)   Evacuate Collection Set: 5.8ms
[2025-09-10T17:11:06.535+0800][1375.056s][info][gc,phases     ] GC(190)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:11:06.535+0800][1375.056s][info][gc,phases     ] GC(190)   Other: 0.8ms
[2025-09-10T17:11:06.535+0800][1375.057s][info][gc,heap       ] GC(190) Eden regions: 120->0(34)
[2025-09-10T17:11:06.536+0800][1375.057s][info][gc,heap       ] GC(190) Survivor regions: 12->17(17)
[2025-09-10T17:11:06.536+0800][1375.057s][info][gc,heap       ] GC(190) Old regions: 698->702
[2025-09-10T17:11:06.536+0800][1375.057s][info][gc,heap       ] GC(190) Humongous regions: 5->5
[2025-09-10T17:11:06.536+0800][1375.057s][info][gc,metaspace  ] GC(190) Metaspace: 478138K(519044K)->478138K(519044K) NonClass: 418426K(446184K)->418426K(446184K) Class: 59711K(72860K)->59711K(72860K)
[2025-09-10T17:11:06.536+0800][1375.058s][info][gc            ] GC(190) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 833M->722M(1026M) 10.428ms
[2025-09-10T17:11:06.537+0800][1375.058s][info][gc,cpu        ] GC(190) User=0.11s Sys=0.01s Real=0.00s
[2025-09-10T17:11:06.731+0800][1375.252s][info][gc,start      ] GC(191) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:11:06.732+0800][1375.253s][info][gc,task       ] GC(191) Using 15 workers of 15 for evacuation
[2025-09-10T17:11:06.742+0800][1375.263s][info][gc,phases     ] GC(191)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:11:06.742+0800][1375.264s][info][gc,phases     ] GC(191)   Evacuate Collection Set: 8.3ms
[2025-09-10T17:11:06.743+0800][1375.264s][info][gc,phases     ] GC(191)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:11:06.743+0800][1375.264s][info][gc,phases     ] GC(191)   Other: 1.1ms
[2025-09-10T17:11:06.743+0800][1375.265s][info][gc,heap       ] GC(191) Eden regions: 34->0(126)
[2025-09-10T17:11:06.744+0800][1375.265s][info][gc,heap       ] GC(191) Survivor regions: 17->4(7)
[2025-09-10T17:11:06.744+0800][1375.265s][info][gc,heap       ] GC(191) Old regions: 702->706
[2025-09-10T17:11:06.744+0800][1375.265s][info][gc,heap       ] GC(191) Humongous regions: 5->5
[2025-09-10T17:11:06.744+0800][1375.265s][info][gc,metaspace  ] GC(191) Metaspace: 478403K(519300K)->478403K(519300K) NonClass: 418665K(446440K)->418665K(446440K) Class: 59737K(72860K)->59737K(72860K)
[2025-09-10T17:11:06.744+0800][1375.266s][info][gc            ] GC(191) Pause Young (Mixed) (G1 Evacuation Pause) 756M->712M(1026M) 13.205ms
[2025-09-10T17:11:06.745+0800][1375.266s][info][gc,cpu        ] GC(191) User=0.12s Sys=0.00s Real=0.01s
[2025-09-10T17:11:55.932+0800][1424.455s][info][gc,start      ] GC(192) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:11:55.932+0800][1424.455s][info][gc,task       ] GC(192) Using 15 workers of 15 for evacuation
[2025-09-10T17:11:55.938+0800][1424.460s][info][gc,phases     ] GC(192)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:11:55.938+0800][1424.461s][info][gc,phases     ] GC(192)   Evacuate Collection Set: 4.1ms
[2025-09-10T17:11:55.938+0800][1424.461s][info][gc,phases     ] GC(192)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:11:55.938+0800][1424.461s][info][gc,phases     ] GC(192)   Other: 0.8ms
[2025-09-10T17:11:55.939+0800][1424.461s][info][gc,heap       ] GC(192) Eden regions: 126->0(132)
[2025-09-10T17:11:55.939+0800][1424.461s][info][gc,heap       ] GC(192) Survivor regions: 4->8(17)
[2025-09-10T17:11:55.939+0800][1424.462s][info][gc,heap       ] GC(192) Old regions: 706->706
[2025-09-10T17:11:55.939+0800][1424.462s][info][gc,heap       ] GC(192) Humongous regions: 8->5
[2025-09-10T17:11:55.939+0800][1424.462s][info][gc,metaspace  ] GC(192) Metaspace: 478975K(519812K)->478975K(519812K) NonClass: 419169K(446952K)->419169K(446952K) Class: 59806K(72860K)->59806K(72860K)
[2025-09-10T17:11:55.939+0800][1424.462s][info][gc            ] GC(192) Pause Young (Normal) (G1 Evacuation Pause) 841M->717M(1026M) 7.303ms
[2025-09-10T17:11:55.940+0800][1424.462s][info][gc,cpu        ] GC(192) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:12:11.416+0800][1439.940s][info][gc,start      ] GC(193) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:12:11.416+0800][1439.940s][info][gc,task       ] GC(193) Using 15 workers of 15 for evacuation
[2025-09-10T17:12:11.424+0800][1439.947s][info][gc,phases     ] GC(193)   Pre Evacuate Collection Set: 1.4ms
[2025-09-10T17:12:11.424+0800][1439.948s][info][gc,phases     ] GC(193)   Evacuate Collection Set: 4.6ms
[2025-09-10T17:12:11.424+0800][1439.948s][info][gc,phases     ] GC(193)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:12:11.424+0800][1439.948s][info][gc,phases     ] GC(193)   Other: 0.7ms
[2025-09-10T17:12:11.424+0800][1439.948s][info][gc,heap       ] GC(193) Eden regions: 86->0(143)
[2025-09-10T17:12:11.424+0800][1439.948s][info][gc,heap       ] GC(193) Survivor regions: 8->6(18)
[2025-09-10T17:12:11.425+0800][1439.948s][info][gc,heap       ] GC(193) Old regions: 706->706
[2025-09-10T17:12:11.425+0800][1439.949s][info][gc,heap       ] GC(193) Humongous regions: 5->4
[2025-09-10T17:12:11.425+0800][1439.949s][info][gc,metaspace  ] GC(193) Metaspace: 479841K(520708K)->479841K(520708K) NonClass: 419920K(447720K)->419920K(447720K) Class: 59920K(72988K)->59920K(72988K)
[2025-09-10T17:12:11.425+0800][1439.949s][info][gc            ] GC(193) Pause Young (Concurrent Start) (G1 Humongous Allocation) 803M->714M(1026M) 9.207ms
[2025-09-10T17:12:11.425+0800][1439.949s][info][gc,cpu        ] GC(193) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:12:11.425+0800][1439.949s][info][gc            ] GC(194) Concurrent Cycle
[2025-09-10T17:12:11.426+0800][1439.949s][info][gc,marking    ] GC(194) Concurrent Clear Claimed Marks
[2025-09-10T17:12:11.427+0800][1439.951s][info][gc,marking    ] GC(194) Concurrent Clear Claimed Marks 1.284ms
[2025-09-10T17:12:11.427+0800][1439.951s][info][gc,marking    ] GC(194) Concurrent Scan Root Regions
[2025-09-10T17:12:11.431+0800][1439.955s][info][gc,marking    ] GC(194) Concurrent Scan Root Regions 3.843ms
[2025-09-10T17:12:11.431+0800][1439.955s][info][gc,marking    ] GC(194) Concurrent Mark (1439.955s)
[2025-09-10T17:12:11.431+0800][1439.955s][info][gc,marking    ] GC(194) Concurrent Mark From Roots
[2025-09-10T17:12:11.432+0800][1439.955s][info][gc,task       ] GC(194) Using 4 workers of 4 for marking
[2025-09-10T17:12:11.616+0800][1440.140s][info][gc,marking    ] GC(194) Concurrent Mark From Roots 184.714ms
[2025-09-10T17:12:11.617+0800][1440.140s][info][gc,marking    ] GC(194) Concurrent Preclean
[2025-09-10T17:12:11.618+0800][1440.141s][info][gc,marking    ] GC(194) Concurrent Preclean 1.067ms
[2025-09-10T17:12:11.618+0800][1440.142s][info][gc,marking    ] GC(194) Concurrent Mark (1439.955s, 1440.142s) 186.641ms
[2025-09-10T17:12:11.619+0800][1440.142s][info][gc,start      ] GC(194) Pause Remark
[2025-09-10T17:12:11.665+0800][1440.189s][info][gc,stringtable] GC(194) Cleaned string and symbol table, strings: 629493 processed, 122 removed, symbols: 554209 processed, 206 removed
[2025-09-10T17:12:11.666+0800][1440.190s][info][gc            ] GC(194) Pause Remark 726M->726M(1026M) 47.459ms
[2025-09-10T17:12:11.666+0800][1440.190s][info][gc,cpu        ] GC(194) User=0.54s Sys=0.00s Real=0.05s
[2025-09-10T17:12:11.668+0800][1440.192s][info][gc,marking    ] GC(194) Concurrent Rebuild Remembered Sets
[2025-09-10T17:12:11.836+0800][1440.360s][info][gc,marking    ] GC(194) Concurrent Rebuild Remembered Sets 167.829ms
[2025-09-10T17:12:11.837+0800][1440.360s][info][gc,start      ] GC(194) Pause Cleanup
[2025-09-10T17:12:11.838+0800][1440.362s][info][gc            ] GC(194) Pause Cleanup 726M->726M(1026M) 1.418ms
[2025-09-10T17:12:11.838+0800][1440.362s][info][gc,cpu        ] GC(194) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:12:11.839+0800][1440.363s][info][gc,marking    ] GC(194) Concurrent Cleanup for Next Mark
[2025-09-10T17:12:11.840+0800][1440.364s][info][gc,marking    ] GC(194) Concurrent Cleanup for Next Mark 1.039ms
[2025-09-10T17:12:11.840+0800][1440.364s][info][gc            ] GC(194) Concurrent Cycle 414.875ms
[2025-09-10T17:13:29.417+0800][1517.946s][info][gc,start      ] GC(195) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:29.419+0800][1517.948s][info][gc,task       ] GC(195) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:29.431+0800][1517.959s][info][gc,phases     ] GC(195)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:29.432+0800][1517.960s][info][gc,phases     ] GC(195)   Evacuate Collection Set: 4.9ms
[2025-09-10T17:13:29.433+0800][1517.961s][info][gc,phases     ] GC(195)   Post Evacuate Collection Set: 2.6ms
[2025-09-10T17:13:29.434+0800][1517.962s][info][gc,phases     ] GC(195)   Other: 6.0ms
[2025-09-10T17:13:29.434+0800][1517.962s][info][gc,heap       ] GC(195) Eden regions: 143->0(36)
[2025-09-10T17:13:29.434+0800][1517.962s][info][gc,heap       ] GC(195) Survivor regions: 6->15(19)
[2025-09-10T17:13:29.434+0800][1517.962s][info][gc,heap       ] GC(195) Old regions: 706->706
[2025-09-10T17:13:29.435+0800][1517.963s][info][gc,heap       ] GC(195) Humongous regions: 8->5
[2025-09-10T17:13:29.435+0800][1517.963s][info][gc,metaspace  ] GC(195) Metaspace: 481673K(522500K)->481673K(522500K) NonClass: 421518K(449256K)->421518K(449256K) Class: 60155K(73244K)->60155K(73244K)
[2025-09-10T17:13:29.435+0800][1517.963s][info][gc            ] GC(195) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 861M->723M(1026M) 17.467ms
[2025-09-10T17:13:29.435+0800][1517.963s][info][gc,cpu        ] GC(195) User=0.09s Sys=0.00s Real=0.02s
[2025-09-10T17:13:29.705+0800][1518.233s][info][gc,start      ] GC(196) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:29.705+0800][1518.233s][info][gc,task       ] GC(196) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:29.718+0800][1518.246s][info][gc,phases     ] GC(196)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T17:13:29.718+0800][1518.246s][info][gc,phases     ] GC(196)   Evacuate Collection Set: 10.2ms
[2025-09-10T17:13:29.718+0800][1518.247s][info][gc,phases     ] GC(196)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T17:13:29.719+0800][1518.247s][info][gc,phases     ] GC(196)   Other: 0.9ms
[2025-09-10T17:13:29.719+0800][1518.247s][info][gc,heap       ] GC(196) Eden regions: 36->0(147)
[2025-09-10T17:13:29.719+0800][1518.247s][info][gc,heap       ] GC(196) Survivor regions: 15->2(7)
[2025-09-10T17:13:29.719+0800][1518.247s][info][gc,heap       ] GC(196) Old regions: 706->710
[2025-09-10T17:13:29.719+0800][1518.248s][info][gc,heap       ] GC(196) Humongous regions: 5->5
[2025-09-10T17:13:29.720+0800][1518.248s][info][gc,metaspace  ] GC(196) Metaspace: 481871K(522884K)->481871K(522884K) NonClass: 421683K(449512K)->421683K(449512K) Class: 60187K(73372K)->60187K(73372K)
[2025-09-10T17:13:29.720+0800][1518.248s][info][gc            ] GC(196) Pause Young (Mixed) (G1 Evacuation Pause) 759M->714M(1026M) 15.186ms
[2025-09-10T17:13:29.720+0800][1518.248s][info][gc,cpu        ] GC(196) User=0.15s Sys=0.00s Real=0.02s
[2025-09-10T17:13:29.994+0800][1518.522s][info][gc,start      ] GC(197) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:29.994+0800][1518.523s][info][gc,task       ] GC(197) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:29.998+0800][1518.527s][info][gc,phases     ] GC(197)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:29.999+0800][1518.527s][info][gc,phases     ] GC(197)   Evacuate Collection Set: 2.9ms
[2025-09-10T17:13:29.999+0800][1518.527s][info][gc,phases     ] GC(197)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:13:29.999+0800][1518.527s][info][gc,phases     ] GC(197)   Other: 0.5ms
[2025-09-10T17:13:29.999+0800][1518.527s][info][gc,heap       ] GC(197) Eden regions: 147->0(150)
[2025-09-10T17:13:29.999+0800][1518.528s][info][gc,heap       ] GC(197) Survivor regions: 2->7(19)
[2025-09-10T17:13:30.000+0800][1518.528s][info][gc,heap       ] GC(197) Old regions: 710->710
[2025-09-10T17:13:30.000+0800][1518.528s][info][gc,heap       ] GC(197) Humongous regions: 5->5
[2025-09-10T17:13:30.000+0800][1518.528s][info][gc,metaspace  ] GC(197) Metaspace: 482960K(524036K)->482960K(524036K) NonClass: 422632K(450536K)->422632K(450536K) Class: 60328K(73500K)->60328K(73500K)
[2025-09-10T17:13:30.000+0800][1518.528s][info][gc            ] GC(197) Pause Young (Normal) (G1 Evacuation Pause) 861M->719M(1026M) 5.869ms
[2025-09-10T17:13:30.000+0800][1518.528s][info][gc,cpu        ] GC(197) User=0.05s Sys=0.01s Real=0.01s
[2025-09-10T17:13:30.160+0800][1518.688s][info][gc,start      ] GC(198) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:30.161+0800][1518.689s][info][gc,task       ] GC(198) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:30.166+0800][1518.694s][info][gc,phases     ] GC(198)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:30.168+0800][1518.696s][info][gc,phases     ] GC(198)   Evacuate Collection Set: 2.8ms
[2025-09-10T17:13:30.170+0800][1518.698s][info][gc,phases     ] GC(198)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:13:30.170+0800][1518.698s][info][gc,phases     ] GC(198)   Other: 2.9ms
[2025-09-10T17:13:30.170+0800][1518.698s][info][gc,heap       ] GC(198) Eden regions: 150->0(153)
[2025-09-10T17:13:30.170+0800][1518.698s][info][gc,heap       ] GC(198) Survivor regions: 7->8(20)
[2025-09-10T17:13:30.170+0800][1518.699s][info][gc,heap       ] GC(198) Old regions: 710->710
[2025-09-10T17:13:30.171+0800][1518.699s][info][gc,heap       ] GC(198) Humongous regions: 5->5
[2025-09-10T17:13:30.171+0800][1518.699s][info][gc,metaspace  ] GC(198) Metaspace: 483127K(524036K)->483127K(524036K) NonClass: 422786K(450536K)->422786K(450536K) Class: 60341K(73500K)->60341K(73500K)
[2025-09-10T17:13:30.171+0800][1518.699s][info][gc            ] GC(198) Pause Young (Normal) (G1 Evacuation Pause) 869M->720M(1026M) 11.264ms
[2025-09-10T17:13:30.171+0800][1518.699s][info][gc,cpu        ] GC(198) User=0.06s Sys=0.01s Real=0.01s
[2025-09-10T17:13:30.335+0800][1518.864s][info][gc,start      ] GC(199) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:13:30.336+0800][1518.864s][info][gc,task       ] GC(199) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:30.343+0800][1518.872s][info][gc,phases     ] GC(199)   Pre Evacuate Collection Set: 1.2ms
[2025-09-10T17:13:30.344+0800][1518.872s][info][gc,phases     ] GC(199)   Evacuate Collection Set: 5.4ms
[2025-09-10T17:13:30.344+0800][1518.872s][info][gc,phases     ] GC(199)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:13:30.344+0800][1518.872s][info][gc,phases     ] GC(199)   Other: 0.8ms
[2025-09-10T17:13:30.344+0800][1518.873s][info][gc,heap       ] GC(199) Eden regions: 153->0(156)
[2025-09-10T17:13:30.345+0800][1518.873s][info][gc,heap       ] GC(199) Survivor regions: 8->10(21)
[2025-09-10T17:13:30.345+0800][1518.873s][info][gc,heap       ] GC(199) Old regions: 710->710
[2025-09-10T17:13:30.345+0800][1518.873s][info][gc,heap       ] GC(199) Humongous regions: 5->5
[2025-09-10T17:13:30.345+0800][1518.873s][info][gc,metaspace  ] GC(199) Metaspace: 483356K(524420K)->483356K(524420K) NonClass: 422982K(450792K)->422982K(450792K) Class: 60374K(73628K)->60374K(73628K)
[2025-09-10T17:13:30.345+0800][1518.873s][info][gc            ] GC(199) Pause Young (Concurrent Start) (G1 Evacuation Pause) 873M->722M(1026M) 9.713ms
[2025-09-10T17:13:30.345+0800][1518.873s][info][gc,cpu        ] GC(199) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:13:30.345+0800][1518.874s][info][gc            ] GC(200) Concurrent Cycle
[2025-09-10T17:13:30.346+0800][1518.874s][info][gc,marking    ] GC(200) Concurrent Clear Claimed Marks
[2025-09-10T17:13:30.348+0800][1518.877s][info][gc,marking    ] GC(200) Concurrent Clear Claimed Marks 2.849ms
[2025-09-10T17:13:30.349+0800][1518.877s][info][gc,marking    ] GC(200) Concurrent Scan Root Regions
[2025-09-10T17:13:30.357+0800][1518.885s][info][gc,marking    ] GC(200) Concurrent Scan Root Regions 7.867ms
[2025-09-10T17:13:30.357+0800][1518.885s][info][gc,marking    ] GC(200) Concurrent Mark (1518.885s)
[2025-09-10T17:13:30.357+0800][1518.885s][info][gc,marking    ] GC(200) Concurrent Mark From Roots
[2025-09-10T17:13:30.358+0800][1518.886s][info][gc,task       ] GC(200) Using 4 workers of 4 for marking
[2025-09-10T17:13:30.568+0800][1519.097s][info][gc,start      ] GC(201) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:30.569+0800][1519.097s][info][gc,task       ] GC(201) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:30.583+0800][1519.111s][info][gc,phases     ] GC(201)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:30.586+0800][1519.114s][info][gc,phases     ] GC(201)   Evacuate Collection Set: 5.3ms
[2025-09-10T17:13:30.587+0800][1519.115s][info][gc,phases     ] GC(201)   Post Evacuate Collection Set: 2.2ms
[2025-09-10T17:13:30.587+0800][1519.115s][info][gc,phases     ] GC(201)   Other: 7.4ms
[2025-09-10T17:13:30.587+0800][1519.115s][info][gc,heap       ] GC(201) Eden regions: 156->0(157)
[2025-09-10T17:13:30.587+0800][1519.115s][info][gc,heap       ] GC(201) Survivor regions: 10->13(21)
[2025-09-10T17:13:30.587+0800][1519.115s][info][gc,heap       ] GC(201) Old regions: 710->710
[2025-09-10T17:13:30.588+0800][1519.116s][info][gc,heap       ] GC(201) Humongous regions: 5->5
[2025-09-10T17:13:30.588+0800][1519.116s][info][gc,metaspace  ] GC(201) Metaspace: 483623K(524676K)->483623K(524676K) NonClass: 423224K(451048K)->423224K(451048K) Class: 60399K(73628K)->60399K(73628K)
[2025-09-10T17:13:30.588+0800][1519.116s][info][gc            ] GC(201) Pause Young (Normal) (G1 Evacuation Pause) 878M->725M(1026M) 19.328ms
[2025-09-10T17:13:30.588+0800][1519.116s][info][gc,cpu        ] GC(201) User=0.15s Sys=0.00s Real=0.02s
[2025-09-10T17:13:30.745+0800][1519.273s][info][gc,marking    ] GC(200) Concurrent Mark From Roots 387.500ms
[2025-09-10T17:13:30.750+0800][1519.278s][info][gc,marking    ] GC(200) Concurrent Preclean
[2025-09-10T17:13:30.763+0800][1519.291s][info][gc,marking    ] GC(200) Concurrent Preclean 13.652ms
[2025-09-10T17:13:30.770+0800][1519.298s][info][gc,marking    ] GC(200) Concurrent Mark (1518.885s, 1519.298s) 412.950ms
[2025-09-10T17:13:30.780+0800][1519.308s][info][gc,start      ] GC(200) Pause Remark
[2025-09-10T17:13:30.840+0800][1519.368s][info][gc,stringtable] GC(200) Cleaned string and symbol table, strings: 630483 processed, 15 removed, symbols: 556813 processed, 147 removed
[2025-09-10T17:13:30.841+0800][1519.369s][info][gc            ] GC(200) Pause Remark 784M->784M(1026M) 60.628ms
[2025-09-10T17:13:30.841+0800][1519.369s][info][gc,cpu        ] GC(200) User=0.74s Sys=0.01s Real=0.06s
[2025-09-10T17:13:30.841+0800][1519.369s][info][gc,marking    ] GC(200) Concurrent Rebuild Remembered Sets
[2025-09-10T17:13:31.035+0800][1519.563s][info][gc,start      ] GC(202) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:31.036+0800][1519.564s][info][gc,task       ] GC(202) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:31.043+0800][1519.571s][info][gc,phases     ] GC(202)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:31.043+0800][1519.571s][info][gc,phases     ] GC(202)   Evacuate Collection Set: 5.3ms
[2025-09-10T17:13:31.043+0800][1519.571s][info][gc,phases     ] GC(202)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:13:31.043+0800][1519.571s][info][gc,phases     ] GC(202)   Other: 1.5ms
[2025-09-10T17:13:31.043+0800][1519.572s][info][gc,heap       ] GC(202) Eden regions: 157->0(160)
[2025-09-10T17:13:31.044+0800][1519.572s][info][gc,heap       ] GC(202) Survivor regions: 13->8(22)
[2025-09-10T17:13:31.044+0800][1519.572s][info][gc,heap       ] GC(202) Old regions: 710->715
[2025-09-10T17:13:31.044+0800][1519.572s][info][gc,heap       ] GC(202) Humongous regions: 5->5
[2025-09-10T17:13:31.044+0800][1519.572s][info][gc,metaspace  ] GC(202) Metaspace: 483724K(524932K)->483724K(524932K) NonClass: 423336K(451304K)->423336K(451304K) Class: 60387K(73628K)->60387K(73628K)
[2025-09-10T17:13:31.044+0800][1519.572s][info][gc            ] GC(202) Pause Young (Normal) (G1 Evacuation Pause) 882M->726M(1026M) 8.841ms
[2025-09-10T17:13:31.044+0800][1519.572s][info][gc,cpu        ] GC(202) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:13:31.051+0800][1519.579s][info][gc,marking    ] GC(200) Concurrent Rebuild Remembered Sets 209.397ms
[2025-09-10T17:13:31.052+0800][1519.580s][info][gc,start      ] GC(200) Pause Cleanup
[2025-09-10T17:13:31.053+0800][1519.581s][info][gc            ] GC(200) Pause Cleanup 732M->732M(1026M) 0.989ms
[2025-09-10T17:13:31.053+0800][1519.581s][info][gc,cpu        ] GC(200) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:13:31.053+0800][1519.581s][info][gc,marking    ] GC(200) Concurrent Cleanup for Next Mark
[2025-09-10T17:13:31.054+0800][1519.582s][info][gc,marking    ] GC(200) Concurrent Cleanup for Next Mark 0.945ms
[2025-09-10T17:13:31.054+0800][1519.582s][info][gc            ] GC(200) Concurrent Cycle 708.744ms
[2025-09-10T17:13:31.868+0800][1520.396s][info][gc,start      ] GC(203) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:31.868+0800][1520.396s][info][gc,task       ] GC(203) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:31.873+0800][1520.401s][info][gc,phases     ] GC(203)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:31.873+0800][1520.401s][info][gc,phases     ] GC(203)   Evacuate Collection Set: 3.2ms
[2025-09-10T17:13:31.874+0800][1520.402s][info][gc,phases     ] GC(203)   Post Evacuate Collection Set: 1.6ms
[2025-09-10T17:13:31.874+0800][1520.402s][info][gc,phases     ] GC(203)   Other: 0.5ms
[2025-09-10T17:13:31.874+0800][1520.402s][info][gc,heap       ] GC(203) Eden regions: 160->0(43)
[2025-09-10T17:13:31.874+0800][1520.402s][info][gc,heap       ] GC(203) Survivor regions: 8->8(21)
[2025-09-10T17:13:31.874+0800][1520.403s][info][gc,heap       ] GC(203) Old regions: 715->715
[2025-09-10T17:13:31.875+0800][1520.403s][info][gc,heap       ] GC(203) Humongous regions: 5->5
[2025-09-10T17:13:31.875+0800][1520.403s][info][gc,metaspace  ] GC(203) Metaspace: 484142K(525188K)->484142K(525188K) NonClass: 423710K(451560K)->423710K(451560K) Class: 60432K(73628K)->60432K(73628K)
[2025-09-10T17:13:31.875+0800][1520.403s][info][gc            ] GC(203) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 886M->725M(1026M) 7.248ms
[2025-09-10T17:13:31.875+0800][1520.403s][info][gc,cpu        ] GC(203) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:13:31.946+0800][1520.474s][info][gc,start      ] GC(204) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:31.946+0800][1520.474s][info][gc,task       ] GC(204) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:31.953+0800][1520.481s][info][gc,phases     ] GC(204)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:13:31.954+0800][1520.482s][info][gc,phases     ] GC(204)   Evacuate Collection Set: 5.3ms
[2025-09-10T17:13:31.954+0800][1520.482s][info][gc,phases     ] GC(204)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:13:31.954+0800][1520.482s][info][gc,phases     ] GC(204)   Other: 1.0ms
[2025-09-10T17:13:31.954+0800][1520.482s][info][gc,heap       ] GC(204) Eden regions: 43->0(162)
[2025-09-10T17:13:31.955+0800][1520.483s][info][gc,heap       ] GC(204) Survivor regions: 8->6(7)
[2025-09-10T17:13:31.955+0800][1520.483s][info][gc,heap       ] GC(204) Old regions: 715->718
[2025-09-10T17:13:31.955+0800][1520.483s][info][gc,heap       ] GC(204) Humongous regions: 5->5
[2025-09-10T17:13:31.955+0800][1520.483s][info][gc,metaspace  ] GC(204) Metaspace: 484824K(525828K)->484824K(525828K) NonClass: 424265K(452072K)->424265K(452072K) Class: 60558K(73756K)->60558K(73756K)
[2025-09-10T17:13:31.955+0800][1520.484s][info][gc            ] GC(204) Pause Young (Mixed) (G1 Evacuation Pause) 768M->726M(1026M) 9.624ms
[2025-09-10T17:13:31.956+0800][1520.484s][info][gc,cpu        ] GC(204) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:13:32.224+0800][1520.752s][info][gc,start      ] GC(205) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:32.224+0800][1520.752s][info][gc,task       ] GC(205) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:32.229+0800][1520.758s][info][gc,phases     ] GC(205)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:32.231+0800][1520.759s][info][gc,phases     ] GC(205)   Evacuate Collection Set: 3.3ms
[2025-09-10T17:13:32.235+0800][1520.763s][info][gc,phases     ] GC(205)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:13:32.237+0800][1520.765s][info][gc,phases     ] GC(205)   Other: 1.4ms
[2025-09-10T17:13:32.238+0800][1520.766s][info][gc,heap       ] GC(205) Eden regions: 162->0(166)
[2025-09-10T17:13:32.239+0800][1520.767s][info][gc,heap       ] GC(205) Survivor regions: 6->5(21)
[2025-09-10T17:13:32.239+0800][1520.767s][info][gc,heap       ] GC(205) Old regions: 718->718
[2025-09-10T17:13:32.240+0800][1520.768s][info][gc,heap       ] GC(205) Humongous regions: 6->5
[2025-09-10T17:13:32.240+0800][1520.768s][info][gc,metaspace  ] GC(205) Metaspace: 487777K(528644K)->487777K(528644K) NonClass: 426750K(454376K)->426750K(454376K) Class: 61027K(74268K)->61027K(74268K)
[2025-09-10T17:13:32.240+0800][1520.768s][info][gc            ] GC(205) Pause Young (Normal) (G1 Evacuation Pause) 889M->725M(1026M) 16.748ms
[2025-09-10T17:13:32.240+0800][1520.769s][info][gc,cpu        ] GC(205) User=0.06s Sys=0.00s Real=0.02s
[2025-09-10T17:13:32.371+0800][1520.899s][info][gc,start      ] GC(206) Pause Young (Concurrent Start) (GCLocker Initiated GC)
[2025-09-10T17:13:32.371+0800][1520.899s][info][gc,task       ] GC(206) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:32.377+0800][1520.905s][info][gc,phases     ] GC(206)   Pre Evacuate Collection Set: 1.3ms
[2025-09-10T17:13:32.377+0800][1520.905s][info][gc,phases     ] GC(206)   Evacuate Collection Set: 3.6ms
[2025-09-10T17:13:32.377+0800][1520.906s][info][gc,phases     ] GC(206)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:13:32.378+0800][1520.906s][info][gc,phases     ] GC(206)   Other: 0.6ms
[2025-09-10T17:13:32.378+0800][1520.906s][info][gc,heap       ] GC(206) Eden regions: 75->0(164)
[2025-09-10T17:13:32.378+0800][1520.906s][info][gc,heap       ] GC(206) Survivor regions: 5->5(22)
[2025-09-10T17:13:32.378+0800][1520.906s][info][gc,heap       ] GC(206) Old regions: 718->718
[2025-09-10T17:13:32.378+0800][1520.906s][info][gc,heap       ] GC(206) Humongous regions: 5->5
[2025-09-10T17:13:32.378+0800][1520.906s][info][gc,metaspace  ] GC(206) Metaspace: 488707K(529796K)->488707K(529796K) NonClass: 427538K(455400K)->427538K(455400K) Class: 61169K(74396K)->61169K(74396K)
[2025-09-10T17:13:32.378+0800][1520.907s][info][gc            ] GC(206) Pause Young (Concurrent Start) (GCLocker Initiated GC) 800M->725M(1026M) 7.695ms
[2025-09-10T17:13:32.379+0800][1520.907s][info][gc,cpu        ] GC(206) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:13:32.379+0800][1520.907s][info][gc            ] GC(207) Concurrent Cycle
[2025-09-10T17:13:32.379+0800][1520.907s][info][gc,marking    ] GC(207) Concurrent Clear Claimed Marks
[2025-09-10T17:13:32.380+0800][1520.908s][info][gc,marking    ] GC(207) Concurrent Clear Claimed Marks 1.368ms
[2025-09-10T17:13:32.380+0800][1520.909s][info][gc,marking    ] GC(207) Concurrent Scan Root Regions
[2025-09-10T17:13:32.385+0800][1520.913s][info][gc,marking    ] GC(207) Concurrent Scan Root Regions 4.110ms
[2025-09-10T17:13:32.385+0800][1520.913s][info][gc,marking    ] GC(207) Concurrent Mark (1520.913s)
[2025-09-10T17:13:32.385+0800][1520.913s][info][gc,marking    ] GC(207) Concurrent Mark From Roots
[2025-09-10T17:13:32.385+0800][1520.913s][info][gc,task       ] GC(207) Using 4 workers of 4 for marking
[2025-09-10T17:13:32.626+0800][1521.154s][info][gc,start      ] GC(208) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:32.626+0800][1521.155s][info][gc,task       ] GC(208) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:32.634+0800][1521.162s][info][gc,phases     ] GC(208)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:13:32.634+0800][1521.162s][info][gc,phases     ] GC(208)   Evacuate Collection Set: 5.9ms
[2025-09-10T17:13:32.634+0800][1521.163s][info][gc,phases     ] GC(208)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc,phases     ] GC(208)   Other: 0.8ms
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc,heap       ] GC(208) Eden regions: 164->0(156)
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc,heap       ] GC(208) Survivor regions: 5->17(22)
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc,heap       ] GC(208) Old regions: 718->718
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc,heap       ] GC(208) Humongous regions: 6->5
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc,metaspace  ] GC(208) Metaspace: 490766K(531716K)->490766K(531716K) NonClass: 429181K(456936K)->429181K(456936K) Class: 61584K(74780K)->61584K(74780K)
[2025-09-10T17:13:32.635+0800][1521.163s][info][gc            ] GC(208) Pause Young (Normal) (G1 Evacuation Pause) 890M->737M(1026M) 9.218ms
[2025-09-10T17:13:32.636+0800][1521.164s][info][gc,cpu        ] GC(208) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:13:32.637+0800][1521.165s][info][gc,marking    ] GC(207) Concurrent Mark From Roots 252.255ms
[2025-09-10T17:13:32.638+0800][1521.166s][info][gc,marking    ] GC(207) Concurrent Preclean
[2025-09-10T17:13:32.638+0800][1521.167s][info][gc,marking    ] GC(207) Concurrent Preclean 0.963ms
[2025-09-10T17:13:32.639+0800][1521.167s][info][gc,marking    ] GC(207) Concurrent Mark (1520.913s, 1521.167s) 253.821ms
[2025-09-10T17:13:32.639+0800][1521.167s][info][gc,start      ] GC(207) Pause Remark
[2025-09-10T17:13:32.655+0800][1521.183s][info][gc,stringtable] GC(207) Cleaned string and symbol table, strings: 632021 processed, 2 removed, symbols: 571263 processed, 58 removed
[2025-09-10T17:13:32.656+0800][1521.184s][info][gc            ] GC(207) Pause Remark 739M->739M(1026M) 16.478ms
[2025-09-10T17:13:32.656+0800][1521.184s][info][gc,cpu        ] GC(207) User=0.21s Sys=0.00s Real=0.01s
[2025-09-10T17:13:32.656+0800][1521.184s][info][gc,marking    ] GC(207) Concurrent Rebuild Remembered Sets
[2025-09-10T17:13:32.823+0800][1521.351s][info][gc,marking    ] GC(207) Concurrent Rebuild Remembered Sets 166.592ms
[2025-09-10T17:13:32.824+0800][1521.352s][info][gc,start      ] GC(207) Pause Cleanup
[2025-09-10T17:13:32.825+0800][1521.353s][info][gc            ] GC(207) Pause Cleanup 816M->816M(1026M) 1.150ms
[2025-09-10T17:13:32.825+0800][1521.353s][info][gc,cpu        ] GC(207) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:13:32.826+0800][1521.354s][info][gc,marking    ] GC(207) Concurrent Cleanup for Next Mark
[2025-09-10T17:13:32.826+0800][1521.355s][info][gc,marking    ] GC(207) Concurrent Cleanup for Next Mark 0.942ms
[2025-09-10T17:13:32.827+0800][1521.355s][info][gc            ] GC(207) Concurrent Cycle 447.920ms
[2025-09-10T17:13:33.009+0800][1521.537s][info][gc,start      ] GC(209) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:33.009+0800][1521.537s][info][gc,task       ] GC(209) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:33.013+0800][1521.541s][info][gc,phases     ] GC(209)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:33.013+0800][1521.541s][info][gc,phases     ] GC(209)   Evacuate Collection Set: 2.7ms
[2025-09-10T17:13:33.013+0800][1521.541s][info][gc,phases     ] GC(209)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:13:33.013+0800][1521.541s][info][gc,phases     ] GC(209)   Other: 0.6ms
[2025-09-10T17:13:33.013+0800][1521.541s][info][gc,heap       ] GC(209) Eden regions: 156->0(48)
[2025-09-10T17:13:33.013+0800][1521.542s][info][gc,heap       ] GC(209) Survivor regions: 17->3(22)
[2025-09-10T17:13:33.013+0800][1521.542s][info][gc,heap       ] GC(209) Old regions: 718->722
[2025-09-10T17:13:33.014+0800][1521.542s][info][gc,heap       ] GC(209) Humongous regions: 5->5
[2025-09-10T17:13:33.014+0800][1521.542s][info][gc,metaspace  ] GC(209) Metaspace: 493829K(534916K)->493829K(534916K) NonClass: 431660K(459496K)->431660K(459496K) Class: 62169K(75420K)->62169K(75420K)
[2025-09-10T17:13:33.014+0800][1521.542s][info][gc            ] GC(209) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 893M->727M(1026M) 5.265ms
[2025-09-10T17:13:33.014+0800][1521.542s][info][gc,cpu        ] GC(209) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:13:34.764+0800][1523.292s][info][gc,start      ] GC(210) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:34.764+0800][1523.292s][info][gc,task       ] GC(210) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:34.770+0800][1523.298s][info][gc,phases     ] GC(210)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:13:34.770+0800][1523.298s][info][gc,phases     ] GC(210)   Evacuate Collection Set: 4.2ms
[2025-09-10T17:13:34.770+0800][1523.299s][info][gc,phases     ] GC(210)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:13:34.771+0800][1523.299s][info][gc,phases     ] GC(210)   Other: 0.8ms
[2025-09-10T17:13:34.771+0800][1523.299s][info][gc,heap       ] GC(210) Eden regions: 48->0(165)
[2025-09-10T17:13:34.771+0800][1523.299s][info][gc,heap       ] GC(210) Survivor regions: 3->4(7)
[2025-09-10T17:13:34.771+0800][1523.299s][info][gc,heap       ] GC(210) Old regions: 722->722
[2025-09-10T17:13:34.771+0800][1523.299s][info][gc,heap       ] GC(210) Humongous regions: 5->5
[2025-09-10T17:13:34.771+0800][1523.299s][info][gc,metaspace  ] GC(210) Metaspace: 495295K(536324K)->495295K(536324K) NonClass: 432952K(460776K)->432952K(460776K) Class: 62342K(75548K)->62342K(75548K)
[2025-09-10T17:13:34.771+0800][1523.300s][info][gc            ] GC(210) Pause Young (Mixed) (G1 Evacuation Pause) 775M->728M(1026M) 7.564ms
[2025-09-10T17:13:34.772+0800][1523.300s][info][gc,cpu        ] GC(210) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:13:45.410+0800][1533.938s][info][gc,start      ] GC(211) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:45.411+0800][1533.939s][info][gc,task       ] GC(211) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:45.419+0800][1533.947s][info][gc,phases     ] GC(211)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:45.419+0800][1533.947s][info][gc,phases     ] GC(211)   Evacuate Collection Set: 3.3ms
[2025-09-10T17:13:45.420+0800][1533.948s][info][gc,phases     ] GC(211)   Post Evacuate Collection Set: 4.8ms
[2025-09-10T17:13:45.420+0800][1533.948s][info][gc,phases     ] GC(211)   Other: 0.7ms
[2025-09-10T17:13:45.420+0800][1533.948s][info][gc,heap       ] GC(211) Eden regions: 165->0(155)
[2025-09-10T17:13:45.420+0800][1533.948s][info][gc,heap       ] GC(211) Survivor regions: 4->14(22)
[2025-09-10T17:13:45.420+0800][1533.949s][info][gc,heap       ] GC(211) Old regions: 722->722
[2025-09-10T17:13:45.421+0800][1533.949s][info][gc,heap       ] GC(211) Humongous regions: 6->5
[2025-09-10T17:13:45.421+0800][1533.949s][info][gc,metaspace  ] GC(211) Metaspace: 495655K(536580K)->495655K(536580K) NonClass: 433280K(461032K)->433280K(461032K) Class: 62374K(75548K)->62374K(75548K)
[2025-09-10T17:13:45.421+0800][1533.949s][info][gc            ] GC(211) Pause Young (Normal) (G1 Evacuation Pause) 894M->738M(1026M) 10.720ms
[2025-09-10T17:13:45.421+0800][1533.949s][info][gc,cpu        ] GC(211) User=0.10s Sys=0.00s Real=0.01s
[2025-09-10T17:13:46.234+0800][1534.762s][info][gc,start      ] GC(212) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:13:46.235+0800][1534.763s][info][gc,task       ] GC(212) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:46.241+0800][1534.769s][info][gc,phases     ] GC(212)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:46.241+0800][1534.769s][info][gc,phases     ] GC(212)   Evacuate Collection Set: 3.9ms
[2025-09-10T17:13:46.241+0800][1534.769s][info][gc,phases     ] GC(212)   Post Evacuate Collection Set: 1.8ms
[2025-09-10T17:13:46.241+0800][1534.770s][info][gc,phases     ] GC(212)   Other: 0.9ms
[2025-09-10T17:13:46.242+0800][1534.770s][info][gc,heap       ] GC(212) Eden regions: 155->0(158)
[2025-09-10T17:13:46.242+0800][1534.770s][info][gc,heap       ] GC(212) Survivor regions: 14->13(22)
[2025-09-10T17:13:46.242+0800][1534.770s][info][gc,heap       ] GC(212) Old regions: 722->725
[2025-09-10T17:13:46.242+0800][1534.770s][info][gc,heap       ] GC(212) Humongous regions: 5->5
[2025-09-10T17:13:46.242+0800][1534.770s][info][gc,metaspace  ] GC(212) Metaspace: 496128K(537092K)->496128K(537092K) NonClass: 433724K(461544K)->433724K(461544K) Class: 62404K(75548K)->62404K(75548K)
[2025-09-10T17:13:46.242+0800][1534.771s][info][gc            ] GC(212) Pause Young (Normal) (G1 Evacuation Pause) 893M->739M(1026M) 8.352ms
[2025-09-10T17:13:46.243+0800][1534.771s][info][gc,cpu        ] GC(212) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:13:48.858+0800][1537.386s][info][gc,start      ] GC(213) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:13:48.860+0800][1537.389s][info][gc,task       ] GC(213) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:48.871+0800][1537.399s][info][gc,phases     ] GC(213)   Pre Evacuate Collection Set: 1.5ms
[2025-09-10T17:13:48.871+0800][1537.399s][info][gc,phases     ] GC(213)   Evacuate Collection Set: 5.2ms
[2025-09-10T17:13:48.871+0800][1537.399s][info][gc,phases     ] GC(213)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:13:48.872+0800][1537.400s][info][gc,phases     ] GC(213)   Other: 4.6ms
[2025-09-10T17:13:48.872+0800][1537.400s][info][gc,heap       ] GC(213) Eden regions: 156->0(164)
[2025-09-10T17:13:48.872+0800][1537.400s][info][gc,heap       ] GC(213) Survivor regions: 13->8(22)
[2025-09-10T17:13:48.872+0800][1537.400s][info][gc,heap       ] GC(213) Old regions: 725->725
[2025-09-10T17:13:48.872+0800][1537.401s][info][gc,heap       ] GC(213) Humongous regions: 5->4
[2025-09-10T17:13:48.873+0800][1537.401s][info][gc,metaspace  ] GC(213) Metaspace: 496825K(537732K)->496825K(537732K) NonClass: 434325K(462056K)->434325K(462056K) Class: 62499K(75676K)->62499K(75676K)
[2025-09-10T17:13:48.873+0800][1537.401s][info][gc            ] GC(213) Pause Young (Concurrent Start) (G1 Humongous Allocation) 895M->734M(1026M) 14.661ms
[2025-09-10T17:13:48.873+0800][1537.401s][info][gc,cpu        ] GC(213) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:13:48.873+0800][1537.401s][info][gc            ] GC(214) Concurrent Cycle
[2025-09-10T17:13:48.874+0800][1537.402s][info][gc,marking    ] GC(214) Concurrent Clear Claimed Marks
[2025-09-10T17:13:48.875+0800][1537.403s][info][gc,marking    ] GC(214) Concurrent Clear Claimed Marks 1.531ms
[2025-09-10T17:13:48.875+0800][1537.404s][info][gc,marking    ] GC(214) Concurrent Scan Root Regions
[2025-09-10T17:13:48.878+0800][1537.406s][info][gc,marking    ] GC(214) Concurrent Scan Root Regions 2.499ms
[2025-09-10T17:13:48.880+0800][1537.408s][info][gc,marking    ] GC(214) Concurrent Mark (1537.408s)
[2025-09-10T17:13:48.881+0800][1537.409s][info][gc,marking    ] GC(214) Concurrent Mark From Roots
[2025-09-10T17:13:48.882+0800][1537.410s][info][gc,task       ] GC(214) Using 4 workers of 4 for marking
[2025-09-10T17:13:49.099+0800][1537.627s][info][gc,marking    ] GC(214) Concurrent Mark From Roots 217.324ms
[2025-09-10T17:13:49.099+0800][1537.627s][info][gc,marking    ] GC(214) Concurrent Preclean
[2025-09-10T17:13:49.100+0800][1537.628s][info][gc,marking    ] GC(214) Concurrent Preclean 0.927ms
[2025-09-10T17:13:49.100+0800][1537.628s][info][gc,marking    ] GC(214) Concurrent Mark (1537.408s, 1537.628s) 220.175ms
[2025-09-10T17:13:49.101+0800][1537.629s][info][gc,start      ] GC(214) Pause Remark
[2025-09-10T17:13:49.148+0800][1537.676s][info][gc,stringtable] GC(214) Cleaned string and symbol table, strings: 633755 processed, 0 removed, symbols: 576903 processed, 195 removed
[2025-09-10T17:13:49.149+0800][1537.677s][info][gc            ] GC(214) Pause Remark 776M->776M(1026M) 48.123ms
[2025-09-10T17:13:49.149+0800][1537.677s][info][gc,cpu        ] GC(214) User=0.53s Sys=0.00s Real=0.04s
[2025-09-10T17:13:49.150+0800][1537.678s][info][gc,marking    ] GC(214) Concurrent Rebuild Remembered Sets
[2025-09-10T17:13:49.304+0800][1537.832s][info][gc,marking    ] GC(214) Concurrent Rebuild Remembered Sets 154.140ms
[2025-09-10T17:13:49.305+0800][1537.833s][info][gc,start      ] GC(214) Pause Cleanup
[2025-09-10T17:13:49.305+0800][1537.834s][info][gc            ] GC(214) Pause Cleanup 776M->776M(1026M) 0.934ms
[2025-09-10T17:13:49.306+0800][1537.834s][info][gc,cpu        ] GC(214) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:13:49.306+0800][1537.834s][info][gc,marking    ] GC(214) Concurrent Cleanup for Next Mark
[2025-09-10T17:13:49.307+0800][1537.835s][info][gc,marking    ] GC(214) Concurrent Cleanup for Next Mark 0.923ms
[2025-09-10T17:13:49.307+0800][1537.835s][info][gc            ] GC(214) Concurrent Cycle 433.760ms
[2025-09-10T17:13:52.824+0800][1541.352s][info][gc,start      ] GC(215) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:52.824+0800][1541.352s][info][gc,task       ] GC(215) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:52.830+0800][1541.358s][info][gc,phases     ] GC(215)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:13:52.830+0800][1541.358s][info][gc,phases     ] GC(215)   Evacuate Collection Set: 4.3ms
[2025-09-10T17:13:52.830+0800][1541.359s][info][gc,phases     ] GC(215)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:13:52.831+0800][1541.359s][info][gc,phases     ] GC(215)   Other: 0.9ms
[2025-09-10T17:13:52.831+0800][1541.359s][info][gc,heap       ] GC(215) Eden regions: 164->0(34)
[2025-09-10T17:13:52.831+0800][1541.359s][info][gc,heap       ] GC(215) Survivor regions: 8->17(22)
[2025-09-10T17:13:52.831+0800][1541.359s][info][gc,heap       ] GC(215) Old regions: 725->725
[2025-09-10T17:13:52.831+0800][1541.360s][info][gc,heap       ] GC(215) Humongous regions: 5->5
[2025-09-10T17:13:52.832+0800][1541.360s][info][gc,metaspace  ] GC(215) Metaspace: 497310K(538372K)->497310K(538372K) NonClass: 434761K(462568K)->434761K(462568K) Class: 62548K(75804K)->62548K(75804K)
[2025-09-10T17:13:52.832+0800][1541.360s][info][gc            ] GC(215) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 899M->744M(1026M) 7.873ms
[2025-09-10T17:13:52.832+0800][1541.360s][info][gc,cpu        ] GC(215) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:13:52.897+0800][1541.425s][info][gc,start      ] GC(216) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:13:52.897+0800][1541.426s][info][gc,task       ] GC(216) Using 15 workers of 15 for evacuation
[2025-09-10T17:13:52.908+0800][1541.436s][info][gc,phases     ] GC(216)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:13:52.909+0800][1541.437s][info][gc,phases     ] GC(216)   Evacuate Collection Set: 9.1ms
[2025-09-10T17:13:52.909+0800][1541.437s][info][gc,phases     ] GC(216)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:13:52.909+0800][1541.437s][info][gc,phases     ] GC(216)   Other: 0.8ms
[2025-09-10T17:13:52.909+0800][1541.437s][info][gc,heap       ] GC(216) Eden regions: 34->0(162)
[2025-09-10T17:13:52.909+0800][1541.437s][info][gc,heap       ] GC(216) Survivor regions: 17->2(7)
[2025-09-10T17:13:52.909+0800][1541.438s][info][gc,heap       ] GC(216) Old regions: 725->730
[2025-09-10T17:13:52.910+0800][1541.438s][info][gc,heap       ] GC(216) Humongous regions: 5->5
[2025-09-10T17:13:52.910+0800][1541.438s][info][gc,metaspace  ] GC(216) Metaspace: 497310K(538372K)->497310K(538372K) NonClass: 434762K(462568K)->434762K(462568K) Class: 62548K(75804K)->62548K(75804K)
[2025-09-10T17:13:52.910+0800][1541.438s][info][gc            ] GC(216) Pause Young (Mixed) (G1 Evacuation Pause) 777M->734M(1026M) 12.837ms
[2025-09-10T17:13:52.910+0800][1541.438s][info][gc,cpu        ] GC(216) User=0.15s Sys=0.01s Real=0.02s
[2025-09-10T17:15:00.427+0800][1608.960s][info][gc,start      ] GC(217) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:00.429+0800][1608.962s][info][gc,task       ] GC(217) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:00.437+0800][1608.970s][info][gc,phases     ] GC(217)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:15:00.440+0800][1608.973s][info][gc,phases     ] GC(217)   Evacuate Collection Set: 4.2ms
[2025-09-10T17:15:00.440+0800][1608.973s][info][gc,phases     ] GC(217)   Post Evacuate Collection Set: 3.3ms
[2025-09-10T17:15:00.440+0800][1608.973s][info][gc,phases     ] GC(217)   Other: 2.5ms
[2025-09-10T17:15:00.441+0800][1608.974s][info][gc,heap       ] GC(217) Eden regions: 162->0(153)
[2025-09-10T17:15:00.441+0800][1608.974s][info][gc,heap       ] GC(217) Survivor regions: 2->10(21)
[2025-09-10T17:15:00.442+0800][1608.974s][info][gc,heap       ] GC(217) Old regions: 730->730
[2025-09-10T17:15:00.442+0800][1608.975s][info][gc,heap       ] GC(217) Humongous regions: 12->5
[2025-09-10T17:15:00.442+0800][1608.975s][info][gc,metaspace  ] GC(217) Metaspace: 497926K(539140K)->497926K(539140K) NonClass: 435339K(463336K)->435339K(463336K) Class: 62586K(75804K)->62586K(75804K)
[2025-09-10T17:15:00.442+0800][1608.975s][info][gc            ] GC(217) Pause Young (Normal) (G1 Evacuation Pause) 903M->742M(1026M) 14.874ms
[2025-09-10T17:15:00.442+0800][1608.975s][info][gc,cpu        ] GC(217) User=0.08s Sys=0.01s Real=0.01s
[2025-09-10T17:15:00.641+0800][1609.173s][info][gc,start      ] GC(218) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:00.641+0800][1609.174s][info][gc,task       ] GC(218) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:00.645+0800][1609.178s][info][gc,phases     ] GC(218)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:00.646+0800][1609.179s][info][gc,phases     ] GC(218)   Evacuate Collection Set: 3.0ms
[2025-09-10T17:15:00.647+0800][1609.179s][info][gc,phases     ] GC(218)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:00.647+0800][1609.180s][info][gc,phases     ] GC(218)   Other: 0.5ms
[2025-09-10T17:15:00.647+0800][1609.180s][info][gc,heap       ] GC(218) Eden regions: 153->0(153)
[2025-09-10T17:15:00.648+0800][1609.181s][info][gc,heap       ] GC(218) Survivor regions: 10->13(21)
[2025-09-10T17:15:00.649+0800][1609.182s][info][gc,heap       ] GC(218) Old regions: 730->730
[2025-09-10T17:15:00.650+0800][1609.182s][info][gc,heap       ] GC(218) Humongous regions: 5->5
[2025-09-10T17:15:00.650+0800][1609.183s][info][gc,metaspace  ] GC(218) Metaspace: 498592K(539780K)->498592K(539780K) NonClass: 435916K(463848K)->435916K(463848K) Class: 62676K(75932K)->62676K(75932K)
[2025-09-10T17:15:00.650+0800][1609.183s][info][gc            ] GC(218) Pause Young (Normal) (G1 Evacuation Pause) 895M->745M(1026M) 9.317ms
[2025-09-10T17:15:00.650+0800][1609.183s][info][gc,cpu        ] GC(218) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:15:00.785+0800][1609.318s][info][gc,start      ] GC(219) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:15:00.786+0800][1609.318s][info][gc,task       ] GC(219) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:00.797+0800][1609.329s][info][gc,phases     ] GC(219)   Pre Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:00.797+0800][1609.330s][info][gc,phases     ] GC(219)   Evacuate Collection Set: 8.4ms
[2025-09-10T17:15:00.797+0800][1609.330s][info][gc,phases     ] GC(219)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:00.797+0800][1609.330s][info][gc,phases     ] GC(219)   Other: 0.7ms
[2025-09-10T17:15:00.798+0800][1609.330s][info][gc,heap       ] GC(219) Eden regions: 153->0(152)
[2025-09-10T17:15:00.800+0800][1609.333s][info][gc,heap       ] GC(219) Survivor regions: 13->7(21)
[2025-09-10T17:15:00.801+0800][1609.334s][info][gc,heap       ] GC(219) Old regions: 730->739
[2025-09-10T17:15:00.801+0800][1609.334s][info][gc,heap       ] GC(219) Humongous regions: 5->5
[2025-09-10T17:15:00.801+0800][1609.334s][info][gc,metaspace  ] GC(219) Metaspace: 498770K(540036K)->498770K(540036K) NonClass: 436077K(464104K)->436077K(464104K) Class: 62693K(75932K)->62693K(75932K)
[2025-09-10T17:15:00.802+0800][1609.334s][info][gc            ] GC(219) Pause Young (Concurrent Start) (G1 Evacuation Pause) 898M->748M(1026M) 16.236ms
[2025-09-10T17:15:00.802+0800][1609.335s][info][gc,cpu        ] GC(219) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:15:00.802+0800][1609.335s][info][gc            ] GC(220) Concurrent Cycle
[2025-09-10T17:15:00.802+0800][1609.335s][info][gc,marking    ] GC(220) Concurrent Clear Claimed Marks
[2025-09-10T17:15:00.804+0800][1609.337s][info][gc,marking    ] GC(220) Concurrent Clear Claimed Marks 1.769ms
[2025-09-10T17:15:00.804+0800][1609.337s][info][gc,marking    ] GC(220) Concurrent Scan Root Regions
[2025-09-10T17:15:00.808+0800][1609.341s][info][gc,marking    ] GC(220) Concurrent Scan Root Regions 3.529ms
[2025-09-10T17:15:00.811+0800][1609.344s][info][gc,marking    ] GC(220) Concurrent Mark (1609.344s)
[2025-09-10T17:15:00.812+0800][1609.344s][info][gc,marking    ] GC(220) Concurrent Mark From Roots
[2025-09-10T17:15:00.812+0800][1609.345s][info][gc,task       ] GC(220) Using 4 workers of 4 for marking
[2025-09-10T17:15:01.033+0800][1609.566s][info][gc,marking    ] GC(220) Concurrent Mark From Roots 221.690ms
[2025-09-10T17:15:01.043+0800][1609.576s][info][gc,marking    ] GC(220) Concurrent Preclean
[2025-09-10T17:15:01.051+0800][1609.584s][info][gc,marking    ] GC(220) Concurrent Preclean 8.055ms
[2025-09-10T17:15:01.057+0800][1609.590s][info][gc,marking    ] GC(220) Concurrent Mark (1609.344s, 1609.590s) 246.291ms
[2025-09-10T17:15:01.061+0800][1609.594s][info][gc,start      ] GC(220) Pause Remark
[2025-09-10T17:15:01.111+0800][1609.644s][info][gc,stringtable] GC(220) Cleaned string and symbol table, strings: 634725 processed, 778 removed, symbols: 578701 processed, 71 removed
[2025-09-10T17:15:01.113+0800][1609.646s][info][gc            ] GC(220) Pause Remark 825M->825M(1026M) 51.694ms
[2025-09-10T17:15:01.114+0800][1609.646s][info][gc,cpu        ] GC(220) User=0.56s Sys=0.01s Real=0.06s
[2025-09-10T17:15:01.114+0800][1609.647s][info][gc,marking    ] GC(220) Concurrent Rebuild Remembered Sets
[2025-09-10T17:15:01.190+0800][1609.723s][info][gc,start      ] GC(221) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:01.192+0800][1609.724s][info][gc,task       ] GC(221) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:01.198+0800][1609.731s][info][gc,phases     ] GC(221)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:01.198+0800][1609.731s][info][gc,phases     ] GC(221)   Evacuate Collection Set: 5.2ms
[2025-09-10T17:15:01.200+0800][1609.733s][info][gc,phases     ] GC(221)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T17:15:01.200+0800][1609.733s][info][gc,phases     ] GC(221)   Other: 1.9ms
[2025-09-10T17:15:01.200+0800][1609.733s][info][gc,heap       ] GC(221) Eden regions: 152->0(151)
[2025-09-10T17:15:01.201+0800][1609.734s][info][gc,heap       ] GC(221) Survivor regions: 7->10(20)
[2025-09-10T17:15:01.201+0800][1609.734s][info][gc,heap       ] GC(221) Old regions: 739->739
[2025-09-10T17:15:01.201+0800][1609.734s][info][gc,heap       ] GC(221) Humongous regions: 6->5
[2025-09-10T17:15:01.201+0800][1609.734s][info][gc,metaspace  ] GC(221) Metaspace: 495761K(540036K)->495761K(540036K) NonClass: 433341K(464104K)->433341K(464104K) Class: 62420K(75932K)->62420K(75932K)
[2025-09-10T17:15:01.201+0800][1609.734s][info][gc            ] GC(221) Pause Young (Normal) (G1 Evacuation Pause) 901M->751M(1026M) 11.116ms
[2025-09-10T17:15:01.202+0800][1609.735s][info][gc,cpu        ] GC(221) User=0.08s Sys=0.01s Real=0.01s
[2025-09-10T17:15:01.283+0800][1609.816s][info][gc,marking    ] GC(220) Concurrent Rebuild Remembered Sets 169.058ms
[2025-09-10T17:15:01.284+0800][1609.817s][info][gc,start      ] GC(220) Pause Cleanup
[2025-09-10T17:15:01.285+0800][1609.818s][info][gc            ] GC(220) Pause Cleanup 843M->843M(1026M) 1.082ms
[2025-09-10T17:15:01.285+0800][1609.818s][info][gc,cpu        ] GC(220) User=0.01s Sys=0.00s Real=0.01s
[2025-09-10T17:15:01.285+0800][1609.818s][info][gc,marking    ] GC(220) Concurrent Cleanup for Next Mark
[2025-09-10T17:15:01.286+0800][1609.819s][info][gc,marking    ] GC(220) Concurrent Cleanup for Next Mark 1.001ms
[2025-09-10T17:15:01.287+0800][1609.820s][info][gc            ] GC(220) Concurrent Cycle 484.840ms
[2025-09-10T17:15:01.334+0800][1609.867s][info][gc,start      ] GC(222) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:01.334+0800][1609.867s][info][gc,task       ] GC(222) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:01.340+0800][1609.873s][info][gc,phases     ] GC(222)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:01.341+0800][1609.874s][info][gc,phases     ] GC(222)   Evacuate Collection Set: 4.7ms
[2025-09-10T17:15:01.341+0800][1609.874s][info][gc,phases     ] GC(222)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:15:01.342+0800][1609.874s][info][gc,phases     ] GC(222)   Other: 0.8ms
[2025-09-10T17:15:01.342+0800][1609.875s][info][gc,heap       ] GC(222) Eden regions: 151->0(32)
[2025-09-10T17:15:01.342+0800][1609.875s][info][gc,heap       ] GC(222) Survivor regions: 10->19(21)
[2025-09-10T17:15:01.342+0800][1609.875s][info][gc,heap       ] GC(222) Old regions: 739->739
[2025-09-10T17:15:01.342+0800][1609.875s][info][gc,heap       ] GC(222) Humongous regions: 5->5
[2025-09-10T17:15:01.342+0800][1609.875s][info][gc,metaspace  ] GC(222) Metaspace: 495777K(540036K)->495777K(540036K) NonClass: 433357K(464104K)->433357K(464104K) Class: 62420K(75932K)->62420K(75932K)
[2025-09-10T17:15:01.342+0800][1609.875s][info][gc            ] GC(222) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 902M->760M(1026M) 8.763ms
[2025-09-10T17:15:01.343+0800][1609.875s][info][gc,cpu        ] GC(222) User=0.07s Sys=0.01s Real=0.00s
[2025-09-10T17:15:01.374+0800][1609.907s][info][gc,start      ] GC(223) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:01.374+0800][1609.907s][info][gc,task       ] GC(223) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:01.404+0800][1609.937s][info][gc,phases     ] GC(223)   Pre Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:01.404+0800][1609.937s][info][gc,phases     ] GC(223)   Evacuate Collection Set: 25.8ms
[2025-09-10T17:15:01.405+0800][1609.938s][info][gc,phases     ] GC(223)   Post Evacuate Collection Set: 2.7ms
[2025-09-10T17:15:01.405+0800][1609.938s][info][gc,phases     ] GC(223)   Other: 0.5ms
[2025-09-10T17:15:01.405+0800][1609.938s][info][gc,heap       ] GC(223) Eden regions: 32->0(47)
[2025-09-10T17:15:01.406+0800][1609.938s][info][gc,heap       ] GC(223) Survivor regions: 19->4(7)
[2025-09-10T17:15:01.406+0800][1609.939s][info][gc,heap       ] GC(223) Old regions: 739->716
[2025-09-10T17:15:01.406+0800][1609.939s][info][gc,heap       ] GC(223) Humongous regions: 5->5
[2025-09-10T17:15:01.406+0800][1609.939s][info][gc,metaspace  ] GC(223) Metaspace: 495777K(540036K)->495777K(540036K) NonClass: 433357K(464104K)->433357K(464104K) Class: 62420K(75932K)->62420K(75932K)
[2025-09-10T17:15:01.407+0800][1609.940s][info][gc            ] GC(223) Pause Young (Mixed) (G1 Evacuation Pause) 792M->722M(1026M) 32.637ms
[2025-09-10T17:15:01.407+0800][1609.940s][info][gc,cpu        ] GC(223) User=0.37s Sys=0.01s Real=0.03s
[2025-09-10T17:15:01.465+0800][1609.997s][info][gc,start      ] GC(224) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:01.465+0800][1609.998s][info][gc,task       ] GC(224) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:01.486+0800][1610.019s][info][gc,phases     ] GC(224)   Pre Evacuate Collection Set: 0.7ms
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,phases     ] GC(224)   Evacuate Collection Set: 18.2ms
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,phases     ] GC(224)   Post Evacuate Collection Set: 2.0ms
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,phases     ] GC(224)   Other: 0.8ms
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,heap       ] GC(224) Eden regions: 47->0(169)
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,heap       ] GC(224) Survivor regions: 4->7(7)
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,heap       ] GC(224) Old regions: 716->710
[2025-09-10T17:15:01.487+0800][1610.020s][info][gc,heap       ] GC(224) Humongous regions: 5->5
[2025-09-10T17:15:01.488+0800][1610.020s][info][gc,metaspace  ] GC(224) Metaspace: 495781K(540036K)->495781K(540036K) NonClass: 433361K(464104K)->433361K(464104K) Class: 62420K(75932K)->62420K(75932K)
[2025-09-10T17:15:01.488+0800][1610.021s][info][gc            ] GC(224) Pause Young (Mixed) (G1 Evacuation Pause) 769M->720M(1026M) 23.107ms
[2025-09-10T17:15:01.488+0800][1610.021s][info][gc,cpu        ] GC(224) User=0.30s Sys=0.00s Real=0.03s
[2025-09-10T17:15:01.651+0800][1610.184s][info][gc,start      ] GC(225) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:01.651+0800][1610.184s][info][gc,task       ] GC(225) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:01.655+0800][1610.188s][info][gc,phases     ] GC(225)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:01.655+0800][1610.188s][info][gc,phases     ] GC(225)   Evacuate Collection Set: 2.9ms
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,phases     ] GC(225)   Post Evacuate Collection Set: 0.5ms
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,phases     ] GC(225)   Other: 0.5ms
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,heap       ] GC(225) Eden regions: 169->0(177)
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,heap       ] GC(225) Survivor regions: 7->3(22)
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,heap       ] GC(225) Old regions: 710->710
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,heap       ] GC(225) Humongous regions: 5->5
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,metaspace  ] GC(225) Metaspace: 495786K(540036K)->495786K(540036K) NonClass: 433365K(464104K)->433365K(464104K) Class: 62420K(75932K)->62420K(75932K)
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc            ] GC(225) Pause Young (Normal) (G1 Evacuation Pause) 889M->716M(1026M) 5.054ms
[2025-09-10T17:15:01.656+0800][1610.189s][info][gc,cpu        ] GC(225) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:03.017+0800][1611.549s][info][gc,start      ] GC(226) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:03.017+0800][1611.550s][info][gc,task       ] GC(226) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:03.020+0800][1611.553s][info][gc,phases     ] GC(226)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:03.022+0800][1611.554s][info][gc,phases     ] GC(226)   Evacuate Collection Set: 2.2ms
[2025-09-10T17:15:03.022+0800][1611.555s][info][gc,phases     ] GC(226)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:03.022+0800][1611.555s][info][gc,phases     ] GC(226)   Other: 0.4ms
[2025-09-10T17:15:03.022+0800][1611.555s][info][gc,heap       ] GC(226) Eden regions: 177->0(181)
[2025-09-10T17:15:03.022+0800][1611.555s][info][gc,heap       ] GC(226) Survivor regions: 3->3(23)
[2025-09-10T17:15:03.022+0800][1611.555s][info][gc,heap       ] GC(226) Old regions: 710->710
[2025-09-10T17:15:03.023+0800][1611.555s][info][gc,heap       ] GC(226) Humongous regions: 5->5
[2025-09-10T17:15:03.023+0800][1611.556s][info][gc,metaspace  ] GC(226) Metaspace: 496388K(540036K)->496388K(540036K) NonClass: 433910K(464104K)->433910K(464104K) Class: 62477K(75932K)->62477K(75932K)
[2025-09-10T17:15:03.023+0800][1611.556s][info][gc            ] GC(226) Pause Young (Normal) (G1 Evacuation Pause) 893M->716M(1026M) 6.297ms
[2025-09-10T17:15:03.023+0800][1611.556s][info][gc,cpu        ] GC(226) User=0.04s Sys=0.00s Real=0.00s
[2025-09-10T17:15:03.804+0800][1612.337s][info][gc,start      ] GC(227) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:15:03.804+0800][1612.337s][info][gc,task       ] GC(227) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:03.811+0800][1612.344s][info][gc,phases     ] GC(227)   Pre Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:03.812+0800][1612.345s][info][gc,phases     ] GC(227)   Evacuate Collection Set: 3.8ms
[2025-09-10T17:15:03.812+0800][1612.345s][info][gc,phases     ] GC(227)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:03.812+0800][1612.345s][info][gc,phases     ] GC(227)   Other: 0.5ms
[2025-09-10T17:15:03.812+0800][1612.345s][info][gc,heap       ] GC(227) Eden regions: 181->0(181)
[2025-09-10T17:15:03.813+0800][1612.345s][info][gc,heap       ] GC(227) Survivor regions: 3->5(23)
[2025-09-10T17:15:03.813+0800][1612.346s][info][gc,heap       ] GC(227) Old regions: 710->710
[2025-09-10T17:15:03.813+0800][1612.346s][info][gc,heap       ] GC(227) Humongous regions: 5->5
[2025-09-10T17:15:03.813+0800][1612.346s][info][gc,metaspace  ] GC(227) Metaspace: 497631K(540036K)->497631K(540036K) NonClass: 435021K(464104K)->435021K(464104K) Class: 62609K(75932K)->62609K(75932K)
[2025-09-10T17:15:03.813+0800][1612.346s][info][gc            ] GC(227) Pause Young (Concurrent Start) (G1 Evacuation Pause) 897M->718M(1026M) 8.762ms
[2025-09-10T17:15:03.813+0800][1612.346s][info][gc,cpu        ] GC(227) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:03.813+0800][1612.346s][info][gc            ] GC(228) Concurrent Cycle
[2025-09-10T17:15:03.814+0800][1612.346s][info][gc,marking    ] GC(228) Concurrent Clear Claimed Marks
[2025-09-10T17:15:03.815+0800][1612.348s][info][gc,marking    ] GC(228) Concurrent Clear Claimed Marks 1.182ms
[2025-09-10T17:15:03.815+0800][1612.348s][info][gc,marking    ] GC(228) Concurrent Scan Root Regions
[2025-09-10T17:15:03.817+0800][1612.350s][info][gc,marking    ] GC(228) Concurrent Scan Root Regions 2.379ms
[2025-09-10T17:15:03.817+0800][1612.350s][info][gc,marking    ] GC(228) Concurrent Mark (1612.350s)
[2025-09-10T17:15:03.818+0800][1612.350s][info][gc,marking    ] GC(228) Concurrent Mark From Roots
[2025-09-10T17:15:03.818+0800][1612.350s][info][gc,task       ] GC(228) Using 4 workers of 4 for marking
[2025-09-10T17:15:04.007+0800][1612.539s][info][gc,marking    ] GC(228) Concurrent Mark From Roots 189.102ms
[2025-09-10T17:15:04.007+0800][1612.540s][info][gc,marking    ] GC(228) Concurrent Preclean
[2025-09-10T17:15:04.008+0800][1612.541s][info][gc,marking    ] GC(228) Concurrent Preclean 1.029ms
[2025-09-10T17:15:04.008+0800][1612.541s][info][gc,marking    ] GC(228) Concurrent Mark (1612.350s, 1612.541s) 190.899ms
[2025-09-10T17:15:04.009+0800][1612.542s][info][gc,start      ] GC(228) Pause Remark
[2025-09-10T17:15:04.026+0800][1612.559s][info][gc,stringtable] GC(228) Cleaned string and symbol table, strings: 638922 processed, 2 removed, symbols: 587670 processed, 486 removed
[2025-09-10T17:15:04.027+0800][1612.560s][info][gc            ] GC(228) Pause Remark 795M->795M(1026M) 17.810ms
[2025-09-10T17:15:04.028+0800][1612.561s][info][gc,cpu        ] GC(228) User=0.26s Sys=0.00s Real=0.02s
[2025-09-10T17:15:04.029+0800][1612.562s][info][gc,marking    ] GC(228) Concurrent Rebuild Remembered Sets
[2025-09-10T17:15:04.192+0800][1612.725s][info][gc,marking    ] GC(228) Concurrent Rebuild Remembered Sets 163.027ms
[2025-09-10T17:15:04.194+0800][1612.726s][info][gc,start      ] GC(228) Pause Cleanup
[2025-09-10T17:15:04.194+0800][1612.727s][info][gc            ] GC(228) Pause Cleanup 816M->816M(1026M) 0.915ms
[2025-09-10T17:15:04.195+0800][1612.728s][info][gc,cpu        ] GC(228) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:15:04.195+0800][1612.728s][info][gc,marking    ] GC(228) Concurrent Cleanup for Next Mark
[2025-09-10T17:15:04.196+0800][1612.729s][info][gc,marking    ] GC(228) Concurrent Cleanup for Next Mark 0.827ms
[2025-09-10T17:15:04.196+0800][1612.729s][info][gc            ] GC(228) Concurrent Cycle 382.605ms
[2025-09-10T17:15:07.875+0800][1616.408s][info][gc,start      ] GC(229) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:07.877+0800][1616.410s][info][gc,task       ] GC(229) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:07.885+0800][1616.418s][info][gc,phases     ] GC(229)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:07.886+0800][1616.419s][info][gc,phases     ] GC(229)   Evacuate Collection Set: 3.9ms
[2025-09-10T17:15:07.887+0800][1616.420s][info][gc,phases     ] GC(229)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:15:07.887+0800][1616.420s][info][gc,phases     ] GC(229)   Other: 4.3ms
[2025-09-10T17:15:07.888+0800][1616.420s][info][gc,heap       ] GC(229) Eden regions: 181->0(44)
[2025-09-10T17:15:07.888+0800][1616.421s][info][gc,heap       ] GC(229) Survivor regions: 5->7(24)
[2025-09-10T17:15:07.888+0800][1616.421s][info][gc,heap       ] GC(229) Old regions: 710->710
[2025-09-10T17:15:07.888+0800][1616.421s][info][gc,heap       ] GC(229) Humongous regions: 6->5
[2025-09-10T17:15:07.889+0800][1616.422s][info][gc,metaspace  ] GC(229) Metaspace: 499989K(541060K)->499989K(541060K) NonClass: 437011K(464872K)->437011K(464872K) Class: 62977K(76188K)->62977K(76188K)
[2025-09-10T17:15:07.889+0800][1616.422s][info][gc            ] GC(229) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 900M->720M(1026M) 13.399ms
[2025-09-10T17:15:07.889+0800][1616.422s][info][gc,cpu        ] GC(229) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:15:08.231+0800][1616.764s][info][gc,start      ] GC(230) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:08.231+0800][1616.764s][info][gc,task       ] GC(230) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:08.240+0800][1616.773s][info][gc,phases     ] GC(230)   Pre Evacuate Collection Set: 0.3ms
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,phases     ] GC(230)   Evacuate Collection Set: 6.8ms
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,phases     ] GC(230)   Post Evacuate Collection Set: 1.4ms
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,phases     ] GC(230)   Other: 0.7ms
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,heap       ] GC(230) Eden regions: 44->0(184)
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,heap       ] GC(230) Survivor regions: 7->3(7)
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,heap       ] GC(230) Old regions: 710->710
[2025-09-10T17:15:08.241+0800][1616.774s][info][gc,heap       ] GC(230) Humongous regions: 5->5
[2025-09-10T17:15:08.242+0800][1616.774s][info][gc,metaspace  ] GC(230) Metaspace: 500072K(541316K)->500072K(541316K) NonClass: 437086K(465128K)->437086K(465128K) Class: 62985K(76188K)->62985K(76188K)
[2025-09-10T17:15:08.242+0800][1616.774s][info][gc            ] GC(230) Pause Young (Mixed) (G1 Evacuation Pause) 764M->716M(1026M) 10.420ms
[2025-09-10T17:15:08.242+0800][1616.775s][info][gc,cpu        ] GC(230) User=0.11s Sys=0.01s Real=0.01s
[2025-09-10T17:15:10.772+0800][1619.305s][info][gc,start      ] GC(231) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:10.773+0800][1619.306s][info][gc,task       ] GC(231) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:10.780+0800][1619.312s][info][gc,phases     ] GC(231)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:10.780+0800][1619.313s][info][gc,phases     ] GC(231)   Evacuate Collection Set: 4.9ms
[2025-09-10T17:15:10.781+0800][1619.314s][info][gc,phases     ] GC(231)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:10.781+0800][1619.314s][info][gc,phases     ] GC(231)   Other: 1.5ms
[2025-09-10T17:15:10.781+0800][1619.314s][info][gc,heap       ] GC(231) Eden regions: 184->0(176)
[2025-09-10T17:15:10.781+0800][1619.314s][info][gc,heap       ] GC(231) Survivor regions: 3->12(24)
[2025-09-10T17:15:10.781+0800][1619.314s][info][gc,heap       ] GC(231) Old regions: 710->710
[2025-09-10T17:15:10.782+0800][1619.314s][info][gc,heap       ] GC(231) Humongous regions: 6->5
[2025-09-10T17:15:10.782+0800][1619.315s][info][gc,metaspace  ] GC(231) Metaspace: 500406K(541572K)->500406K(541572K) NonClass: 437397K(465384K)->437397K(465384K) Class: 63009K(76188K)->63009K(76188K)
[2025-09-10T17:15:10.782+0800][1619.315s][info][gc            ] GC(231) Pause Young (Normal) (G1 Evacuation Pause) 901M->724M(1026M) 9.825ms
[2025-09-10T17:15:10.782+0800][1619.315s][info][gc,cpu        ] GC(231) User=0.07s Sys=0.00s Real=0.01s
[2025-09-10T17:15:18.625+0800][1627.157s][info][gc,start      ] GC(232) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:15:18.627+0800][1627.159s][info][gc,task       ] GC(232) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:18.639+0800][1627.172s][info][gc,phases     ] GC(232)   Pre Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:18.640+0800][1627.173s][info][gc,phases     ] GC(232)   Evacuate Collection Set: 7.8ms
[2025-09-10T17:15:18.641+0800][1627.173s][info][gc,phases     ] GC(232)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:15:18.641+0800][1627.174s][info][gc,phases     ] GC(232)   Other: 4.3ms
[2025-09-10T17:15:18.643+0800][1627.176s][info][gc,heap       ] GC(232) Eden regions: 8->0(182)
[2025-09-10T17:15:18.644+0800][1627.177s][info][gc,heap       ] GC(232) Survivor regions: 12->5(24)
[2025-09-10T17:15:18.645+0800][1627.177s][info][gc,heap       ] GC(232) Old regions: 710->710
[2025-09-10T17:15:18.646+0800][1627.179s][info][gc,heap       ] GC(232) Humongous regions: 5->4
[2025-09-10T17:15:18.648+0800][1627.181s][info][gc,metaspace  ] GC(232) Metaspace: 500487K(541572K)->500487K(541572K) NonClass: 437467K(465384K)->437467K(465384K) Class: 63019K(76188K)->63019K(76188K)
[2025-09-10T17:15:18.649+0800][1627.181s][info][gc            ] GC(232) Pause Young (Concurrent Start) (G1 Humongous Allocation) 732M->717M(1026M) 24.009ms
[2025-09-10T17:15:18.649+0800][1627.182s][info][gc,cpu        ] GC(232) User=0.08s Sys=0.00s Real=0.03s
[2025-09-10T17:15:18.650+0800][1627.183s][info][gc            ] GC(233) Concurrent Cycle
[2025-09-10T17:15:18.650+0800][1627.183s][info][gc,marking    ] GC(233) Concurrent Clear Claimed Marks
[2025-09-10T17:15:18.651+0800][1627.184s][info][gc,marking    ] GC(233) Concurrent Clear Claimed Marks 1.358ms
[2025-09-10T17:15:18.652+0800][1627.185s][info][gc,marking    ] GC(233) Concurrent Scan Root Regions
[2025-09-10T17:15:18.655+0800][1627.188s][info][gc,marking    ] GC(233) Concurrent Scan Root Regions 2.895ms
[2025-09-10T17:15:18.656+0800][1627.188s][info][gc,marking    ] GC(233) Concurrent Mark (1627.188s)
[2025-09-10T17:15:18.656+0800][1627.189s][info][gc,marking    ] GC(233) Concurrent Mark From Roots
[2025-09-10T17:15:18.657+0800][1627.190s][info][gc,task       ] GC(233) Using 4 workers of 4 for marking
[2025-09-10T17:15:18.828+0800][1627.360s][info][gc,marking    ] GC(233) Concurrent Mark From Roots 171.485ms
[2025-09-10T17:15:18.828+0800][1627.361s][info][gc,marking    ] GC(233) Concurrent Preclean
[2025-09-10T17:15:18.829+0800][1627.362s][info][gc,marking    ] GC(233) Concurrent Preclean 1.297ms
[2025-09-10T17:15:18.830+0800][1627.363s][info][gc,marking    ] GC(233) Concurrent Mark (1627.188s, 1627.363s) 174.771ms
[2025-09-10T17:15:18.831+0800][1627.364s][info][gc,start      ] GC(233) Pause Remark
[2025-09-10T17:15:18.877+0800][1627.409s][info][gc,stringtable] GC(233) Cleaned string and symbol table, strings: 639378 processed, 44 removed, symbols: 589089 processed, 30 removed
[2025-09-10T17:15:18.878+0800][1627.410s][info][gc            ] GC(233) Pause Remark 736M->729M(1026M) 46.263ms
[2025-09-10T17:15:18.878+0800][1627.411s][info][gc,cpu        ] GC(233) User=0.51s Sys=0.00s Real=0.05s
[2025-09-10T17:15:18.878+0800][1627.411s][info][gc,marking    ] GC(233) Concurrent Rebuild Remembered Sets
[2025-09-10T17:15:19.058+0800][1627.591s][info][gc,marking    ] GC(233) Concurrent Rebuild Remembered Sets 179.929ms
[2025-09-10T17:15:19.061+0800][1627.594s][info][gc,start      ] GC(233) Pause Cleanup
[2025-09-10T17:15:19.063+0800][1627.596s][info][gc            ] GC(233) Pause Cleanup 837M->837M(1026M) 1.263ms
[2025-09-10T17:15:19.064+0800][1627.596s][info][gc,cpu        ] GC(233) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:15:19.064+0800][1627.597s][info][gc,marking    ] GC(233) Concurrent Cleanup for Next Mark
[2025-09-10T17:15:19.065+0800][1627.598s][info][gc,marking    ] GC(233) Concurrent Cleanup for Next Mark 1.111ms
[2025-09-10T17:15:19.065+0800][1627.598s][info][gc            ] GC(233) Concurrent Cycle 415.563ms
[2025-09-10T17:15:24.615+0800][1633.148s][info][gc,start      ] GC(234) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:24.617+0800][1633.150s][info][gc,task       ] GC(234) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:24.621+0800][1633.154s][info][gc,phases     ] GC(234)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:24.622+0800][1633.155s][info][gc,phases     ] GC(234)   Evacuate Collection Set: 3.1ms
[2025-09-10T17:15:24.622+0800][1633.155s][info][gc,phases     ] GC(234)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:24.622+0800][1633.155s][info][gc,phases     ] GC(234)   Other: 2.1ms
[2025-09-10T17:15:24.623+0800][1633.156s][info][gc,heap       ] GC(234) Eden regions: 182->0(38)
[2025-09-10T17:15:24.624+0800][1633.157s][info][gc,heap       ] GC(234) Survivor regions: 5->13(24)
[2025-09-10T17:15:24.624+0800][1633.157s][info][gc,heap       ] GC(234) Old regions: 703->703
[2025-09-10T17:15:24.624+0800][1633.157s][info][gc,heap       ] GC(234) Humongous regions: 5->5
[2025-09-10T17:15:24.625+0800][1633.157s][info][gc,metaspace  ] GC(234) Metaspace: 500709K(541572K)->500709K(541572K) NonClass: 437668K(465384K)->437668K(465384K) Class: 63041K(76188K)->63041K(76188K)
[2025-09-10T17:15:24.625+0800][1633.158s][info][gc            ] GC(234) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 893M->718M(1026M) 9.467ms
[2025-09-10T17:15:24.625+0800][1633.158s][info][gc,cpu        ] GC(234) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:24.749+0800][1633.282s][info][gc,start      ] GC(235) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:24.749+0800][1633.282s][info][gc,task       ] GC(235) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:24.768+0800][1633.301s][info][gc,phases     ] GC(235)   Pre Evacuate Collection Set: 0.7ms
[2025-09-10T17:15:24.768+0800][1633.301s][info][gc,phases     ] GC(235)   Evacuate Collection Set: 14.5ms
[2025-09-10T17:15:24.769+0800][1633.302s][info][gc,phases     ] GC(235)   Post Evacuate Collection Set: 3.0ms
[2025-09-10T17:15:24.769+0800][1633.302s][info][gc,phases     ] GC(235)   Other: 1.1ms
[2025-09-10T17:15:24.770+0800][1633.303s][info][gc,heap       ] GC(235) Eden regions: 38->0(185)
[2025-09-10T17:15:24.770+0800][1633.303s][info][gc,heap       ] GC(235) Survivor regions: 13->3(7)
[2025-09-10T17:15:24.770+0800][1633.303s][info][gc,heap       ] GC(235) Old regions: 703->702
[2025-09-10T17:15:24.771+0800][1633.303s][info][gc,heap       ] GC(235) Humongous regions: 5->5
[2025-09-10T17:15:24.771+0800][1633.304s][info][gc,metaspace  ] GC(235) Metaspace: 500755K(541828K)->500755K(541828K) NonClass: 437713K(465640K)->437713K(465640K) Class: 63042K(76188K)->63042K(76188K)
[2025-09-10T17:15:24.771+0800][1633.304s][info][gc            ] GC(235) Pause Young (Mixed) (G1 Evacuation Pause) 756M->707M(1026M) 22.246ms
[2025-09-10T17:15:24.771+0800][1633.304s][info][gc,cpu        ] GC(235) User=0.25s Sys=0.01s Real=0.02s
[2025-09-10T17:15:26.797+0800][1635.330s][info][gc,start      ] GC(236) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:26.800+0800][1635.333s][info][gc,task       ] GC(236) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:26.811+0800][1635.343s][info][gc,phases     ] GC(236)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:26.816+0800][1635.349s][info][gc,phases     ] GC(236)   Evacuate Collection Set: 3.2ms
[2025-09-10T17:15:26.820+0800][1635.353s][info][gc,phases     ] GC(236)   Post Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:26.822+0800][1635.355s][info][gc,phases     ] GC(236)   Other: 9.0ms
[2025-09-10T17:15:26.827+0800][1635.360s][info][gc,heap       ] GC(236) Eden regions: 185->0(187)
[2025-09-10T17:15:26.832+0800][1635.365s][info][gc,heap       ] GC(236) Survivor regions: 3->4(24)
[2025-09-10T17:15:26.838+0800][1635.371s][info][gc,heap       ] GC(236) Old regions: 702->702
[2025-09-10T17:15:26.841+0800][1635.374s][info][gc,heap       ] GC(236) Humongous regions: 5->5
[2025-09-10T17:15:26.843+0800][1635.376s][info][gc,metaspace  ] GC(236) Metaspace: 500815K(541828K)->500815K(541828K) NonClass: 437770K(465640K)->437770K(465640K) Class: 63045K(76188K)->63045K(76188K)
[2025-09-10T17:15:26.849+0800][1635.382s][info][gc            ] GC(236) Pause Young (Normal) (G1 Evacuation Pause) 892M->707M(1026M) 51.702ms
[2025-09-10T17:15:26.852+0800][1635.385s][info][gc,cpu        ] GC(236) User=0.06s Sys=0.00s Real=0.05s
[2025-09-10T17:15:27.162+0800][1635.695s][info][gc,start      ] GC(237) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:27.162+0800][1635.696s][info][gc,task       ] GC(237) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:27.166+0800][1635.700s][info][gc,phases     ] GC(237)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:27.166+0800][1635.700s][info][gc,phases     ] GC(237)   Evacuate Collection Set: 2.3ms
[2025-09-10T17:15:27.168+0800][1635.702s][info][gc,phases     ] GC(237)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:27.168+0800][1635.702s][info][gc,phases     ] GC(237)   Other: 0.9ms
[2025-09-10T17:15:27.168+0800][1635.702s][info][gc,heap       ] GC(237) Eden regions: 187->0(190)
[2025-09-10T17:15:27.168+0800][1635.702s][info][gc,heap       ] GC(237) Survivor regions: 4->4(24)
[2025-09-10T17:15:27.168+0800][1635.702s][info][gc,heap       ] GC(237) Old regions: 702->702
[2025-09-10T17:15:27.169+0800][1635.702s][info][gc,heap       ] GC(237) Humongous regions: 5->5
[2025-09-10T17:15:27.169+0800][1635.703s][info][gc,metaspace  ] GC(237) Metaspace: 500866K(541828K)->500866K(541828K) NonClass: 437816K(465640K)->437816K(465640K) Class: 63050K(76188K)->63050K(76188K)
[2025-09-10T17:15:27.169+0800][1635.703s][info][gc            ] GC(237) Pause Young (Normal) (G1 Evacuation Pause) 894M->708M(1026M) 7.236ms
[2025-09-10T17:15:27.169+0800][1635.703s][info][gc,cpu        ] GC(237) User=0.03s Sys=0.00s Real=0.01s
[2025-09-10T17:15:27.501+0800][1636.035s][info][gc,start      ] GC(238) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:15:27.502+0800][1636.036s][info][gc,task       ] GC(238) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:27.511+0800][1636.045s][info][gc,phases     ] GC(238)   Pre Evacuate Collection Set: 1.2ms
[2025-09-10T17:15:27.512+0800][1636.046s][info][gc,phases     ] GC(238)   Evacuate Collection Set: 4.5ms
[2025-09-10T17:15:27.512+0800][1636.046s][info][gc,phases     ] GC(238)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T17:15:27.512+0800][1636.046s][info][gc,phases     ] GC(238)   Other: 3.8ms
[2025-09-10T17:15:27.512+0800][1636.046s][info][gc,heap       ] GC(238) Eden regions: 190->0(194)
[2025-09-10T17:15:27.514+0800][1636.048s][info][gc,heap       ] GC(238) Survivor regions: 4->3(25)
[2025-09-10T17:15:27.515+0800][1636.049s][info][gc,heap       ] GC(238) Old regions: 702->702
[2025-09-10T17:15:27.516+0800][1636.050s][info][gc,heap       ] GC(238) Humongous regions: 5->5
[2025-09-10T17:15:27.516+0800][1636.050s][info][gc,metaspace  ] GC(238) Metaspace: 500868K(541828K)->500868K(541828K) NonClass: 437818K(465640K)->437818K(465640K) Class: 63050K(76188K)->63050K(76188K)
[2025-09-10T17:15:27.516+0800][1636.050s][info][gc            ] GC(238) Pause Young (Concurrent Start) (G1 Evacuation Pause) 898M->707M(1026M) 15.107ms
[2025-09-10T17:15:27.516+0800][1636.050s][info][gc,cpu        ] GC(238) User=0.05s Sys=0.00s Real=0.02s
[2025-09-10T17:15:27.517+0800][1636.051s][info][gc            ] GC(239) Concurrent Cycle
[2025-09-10T17:15:27.517+0800][1636.051s][info][gc,marking    ] GC(239) Concurrent Clear Claimed Marks
[2025-09-10T17:15:27.518+0800][1636.052s][info][gc,marking    ] GC(239) Concurrent Clear Claimed Marks 1.466ms
[2025-09-10T17:15:27.519+0800][1636.053s][info][gc,marking    ] GC(239) Concurrent Scan Root Regions
[2025-09-10T17:15:27.521+0800][1636.055s][info][gc,marking    ] GC(239) Concurrent Scan Root Regions 2.284ms
[2025-09-10T17:15:27.521+0800][1636.055s][info][gc,marking    ] GC(239) Concurrent Mark (1636.055s)
[2025-09-10T17:15:27.521+0800][1636.055s][info][gc,marking    ] GC(239) Concurrent Mark From Roots
[2025-09-10T17:15:27.521+0800][1636.055s][info][gc,task       ] GC(239) Using 4 workers of 4 for marking
[2025-09-10T17:15:27.710+0800][1636.244s][info][gc,marking    ] GC(239) Concurrent Mark From Roots 188.497ms
[2025-09-10T17:15:27.710+0800][1636.244s][info][gc,marking    ] GC(239) Concurrent Preclean
[2025-09-10T17:15:27.711+0800][1636.245s][info][gc,marking    ] GC(239) Concurrent Preclean 1.215ms
[2025-09-10T17:15:27.711+0800][1636.245s][info][gc,marking    ] GC(239) Concurrent Mark (1636.055s, 1636.245s) 190.346ms
[2025-09-10T17:15:27.712+0800][1636.246s][info][gc,start      ] GC(239) Pause Remark
[2025-09-10T17:15:27.727+0800][1636.261s][info][gc,stringtable] GC(239) Cleaned string and symbol table, strings: 639436 processed, 4 removed, symbols: 589319 processed, 12 removed
[2025-09-10T17:15:27.728+0800][1636.262s][info][gc            ] GC(239) Pause Remark 756M->756M(1026M) 16.205ms
[2025-09-10T17:15:27.729+0800][1636.263s][info][gc,cpu        ] GC(239) User=0.19s Sys=0.02s Real=0.02s
[2025-09-10T17:15:27.730+0800][1636.263s][info][gc,marking    ] GC(239) Concurrent Rebuild Remembered Sets
[2025-09-10T17:15:27.884+0800][1636.418s][info][gc,marking    ] GC(239) Concurrent Rebuild Remembered Sets 154.114ms
[2025-09-10T17:15:27.886+0800][1636.419s][info][gc,start      ] GC(239) Pause Cleanup
[2025-09-10T17:15:27.887+0800][1636.421s][info][gc            ] GC(239) Pause Cleanup 806M->806M(1026M) 1.381ms
[2025-09-10T17:15:27.887+0800][1636.421s][info][gc,cpu        ] GC(239) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:15:27.888+0800][1636.422s][info][gc,marking    ] GC(239) Concurrent Cleanup for Next Mark
[2025-09-10T17:15:27.889+0800][1636.423s][info][gc,marking    ] GC(239) Concurrent Cleanup for Next Mark 0.993ms
[2025-09-10T17:15:27.889+0800][1636.423s][info][gc            ] GC(239) Concurrent Cycle 372.222ms
[2025-09-10T17:15:28.820+0800][1637.354s][info][gc,start      ] GC(240) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:28.820+0800][1637.354s][info][gc,task       ] GC(240) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:28.829+0800][1637.363s][info][gc,phases     ] GC(240)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:28.829+0800][1637.363s][info][gc,phases     ] GC(240)   Evacuate Collection Set: 3.2ms
[2025-09-10T17:15:28.829+0800][1637.363s][info][gc,phases     ] GC(240)   Post Evacuate Collection Set: 4.1ms
[2025-09-10T17:15:28.830+0800][1637.363s][info][gc,phases     ] GC(240)   Other: 1.9ms
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc,heap       ] GC(240) Eden regions: 194->0(46)
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc,heap       ] GC(240) Survivor regions: 3->5(25)
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc,heap       ] GC(240) Old regions: 702->702
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc,heap       ] GC(240) Humongous regions: 5->4
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc,metaspace  ] GC(240) Metaspace: 500888K(541828K)->500888K(541828K) NonClass: 437837K(465640K)->437837K(465640K) Class: 63051K(76188K)->63051K(76188K)
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc            ] GC(240) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 901M->708M(1026M) 10.512ms
[2025-09-10T17:15:28.830+0800][1637.364s][info][gc,cpu        ] GC(240) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:15:28.877+0800][1637.411s][info][gc,start      ] GC(241) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:28.879+0800][1637.413s][info][gc,task       ] GC(241) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:28.886+0800][1637.420s][info][gc,phases     ] GC(241)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:15:28.887+0800][1637.421s][info][gc,phases     ] GC(241)   Evacuate Collection Set: 5.9ms
[2025-09-10T17:15:28.887+0800][1637.421s][info][gc,phases     ] GC(241)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:15:28.887+0800][1637.421s][info][gc,phases     ] GC(241)   Other: 2.1ms
[2025-09-10T17:15:28.887+0800][1637.421s][info][gc,heap       ] GC(241) Eden regions: 46->0(196)
[2025-09-10T17:15:28.887+0800][1637.421s][info][gc,heap       ] GC(241) Survivor regions: 5->3(7)
[2025-09-10T17:15:28.887+0800][1637.421s][info][gc,heap       ] GC(241) Old regions: 702->701
[2025-09-10T17:15:28.888+0800][1637.422s][info][gc,heap       ] GC(241) Humongous regions: 4->4
[2025-09-10T17:15:28.888+0800][1637.422s][info][gc,metaspace  ] GC(241) Metaspace: 500889K(541828K)->500889K(541828K) NonClass: 437837K(465640K)->437837K(465640K) Class: 63051K(76188K)->63051K(76188K)
[2025-09-10T17:15:28.888+0800][1637.422s][info][gc            ] GC(241) Pause Young (Mixed) (G1 Evacuation Pause) 754M->705M(1026M) 10.660ms
[2025-09-10T17:15:28.888+0800][1637.422s][info][gc,cpu        ] GC(241) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:15:29.110+0800][1637.644s][info][gc,start      ] GC(242) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:29.111+0800][1637.645s][info][gc,task       ] GC(242) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:29.115+0800][1637.649s][info][gc,phases     ] GC(242)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:29.116+0800][1637.650s][info][gc,phases     ] GC(242)   Evacuate Collection Set: 2.3ms
[2025-09-10T17:15:29.117+0800][1637.651s][info][gc,phases     ] GC(242)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:29.117+0800][1637.651s][info][gc,phases     ] GC(242)   Other: 2.1ms
[2025-09-10T17:15:29.117+0800][1637.651s][info][gc,heap       ] GC(242) Eden regions: 196->0(196)
[2025-09-10T17:15:29.117+0800][1637.651s][info][gc,heap       ] GC(242) Survivor regions: 3->5(25)
[2025-09-10T17:15:29.118+0800][1637.652s][info][gc,heap       ] GC(242) Old regions: 701->701
[2025-09-10T17:15:29.118+0800][1637.652s][info][gc,heap       ] GC(242) Humongous regions: 5->4
[2025-09-10T17:15:29.118+0800][1637.652s][info][gc,metaspace  ] GC(242) Metaspace: 500891K(541828K)->500891K(541828K) NonClass: 437840K(465640K)->437840K(465640K) Class: 63051K(76188K)->63051K(76188K)
[2025-09-10T17:15:29.118+0800][1637.652s][info][gc            ] GC(242) Pause Young (Normal) (G1 Evacuation Pause) 902M->708M(1026M) 8.740ms
[2025-09-10T17:15:29.119+0800][1637.653s][info][gc,cpu        ] GC(242) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:29.422+0800][1637.956s][info][gc,start      ] GC(243) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:29.428+0800][1637.962s][info][gc,task       ] GC(243) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:29.437+0800][1637.971s][info][gc,phases     ] GC(243)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:29.442+0800][1637.976s][info][gc,phases     ] GC(243)   Evacuate Collection Set: 5.8ms
[2025-09-10T17:15:29.449+0800][1637.983s][info][gc,phases     ] GC(243)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:15:29.461+0800][1637.994s][info][gc,phases     ] GC(243)   Other: 8.6ms
[2025-09-10T17:15:29.470+0800][1638.004s][info][gc,heap       ] GC(243) Eden regions: 196->0(174)
[2025-09-10T17:15:29.471+0800][1638.004s][info][gc,heap       ] GC(243) Survivor regions: 5->22(26)
[2025-09-10T17:15:29.471+0800][1638.005s][info][gc,heap       ] GC(243) Old regions: 701->701
[2025-09-10T17:15:29.473+0800][1638.007s][info][gc,heap       ] GC(243) Humongous regions: 4->4
[2025-09-10T17:15:29.476+0800][1638.010s][info][gc,metaspace  ] GC(243) Metaspace: 500893K(541828K)->500893K(541828K) NonClass: 437842K(465640K)->437842K(465640K) Class: 63051K(76188K)->63051K(76188K)
[2025-09-10T17:15:29.477+0800][1638.011s][info][gc            ] GC(243) Pause Young (Normal) (G1 Evacuation Pause) 904M->725M(1026M) 55.158ms
[2025-09-10T17:15:29.478+0800][1638.012s][info][gc,cpu        ] GC(243) User=0.09s Sys=0.00s Real=0.06s
[2025-09-10T17:15:33.482+0800][1642.016s][info][gc,start      ] GC(244) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:15:33.483+0800][1642.016s][info][gc,task       ] GC(244) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:33.490+0800][1642.024s][info][gc,phases     ] GC(244)   Pre Evacuate Collection Set: 1.5ms
[2025-09-10T17:15:33.490+0800][1642.024s][info][gc,phases     ] GC(244)   Evacuate Collection Set: 3.8ms
[2025-09-10T17:15:33.490+0800][1642.024s][info][gc,phases     ] GC(244)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T17:15:33.491+0800][1642.025s][info][gc,phases     ] GC(244)   Other: 0.8ms
[2025-09-10T17:15:33.491+0800][1642.025s][info][gc,heap       ] GC(244) Eden regions: 52->0(190)
[2025-09-10T17:15:33.491+0800][1642.025s][info][gc,heap       ] GC(244) Survivor regions: 22->4(25)
[2025-09-10T17:15:33.491+0800][1642.025s][info][gc,heap       ] GC(244) Old regions: 701->702
[2025-09-10T17:15:33.491+0800][1642.025s][info][gc,heap       ] GC(244) Humongous regions: 4->4
[2025-09-10T17:15:33.491+0800][1642.025s][info][gc,metaspace  ] GC(244) Metaspace: 500899K(541828K)->500899K(541828K) NonClass: 437848K(465640K)->437848K(465640K) Class: 63051K(76188K)->63051K(76188K)
[2025-09-10T17:15:33.492+0800][1642.025s][info][gc            ] GC(244) Pause Young (Concurrent Start) (G1 Humongous Allocation) 776M->707M(1026M) 9.397ms
[2025-09-10T17:15:33.492+0800][1642.026s][info][gc,cpu        ] GC(244) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:15:33.492+0800][1642.026s][info][gc            ] GC(245) Concurrent Cycle
[2025-09-10T17:15:33.492+0800][1642.026s][info][gc,marking    ] GC(245) Concurrent Clear Claimed Marks
[2025-09-10T17:15:33.493+0800][1642.027s][info][gc,marking    ] GC(245) Concurrent Clear Claimed Marks 1.347ms
[2025-09-10T17:15:33.494+0800][1642.028s][info][gc,marking    ] GC(245) Concurrent Scan Root Regions
[2025-09-10T17:15:33.496+0800][1642.030s][info][gc,marking    ] GC(245) Concurrent Scan Root Regions 2.221ms
[2025-09-10T17:15:33.498+0800][1642.031s][info][gc,marking    ] GC(245) Concurrent Mark (1642.031s)
[2025-09-10T17:15:33.498+0800][1642.032s][info][gc,marking    ] GC(245) Concurrent Mark From Roots
[2025-09-10T17:15:33.499+0800][1642.033s][info][gc,task       ] GC(245) Using 4 workers of 4 for marking
[2025-09-10T17:15:33.698+0800][1642.232s][info][gc,marking    ] GC(245) Concurrent Mark From Roots 199.624ms
[2025-09-10T17:15:33.699+0800][1642.233s][info][gc,marking    ] GC(245) Concurrent Preclean
[2025-09-10T17:15:33.700+0800][1642.234s][info][gc,marking    ] GC(245) Concurrent Preclean 1.374ms
[2025-09-10T17:15:33.701+0800][1642.235s][info][gc,marking    ] GC(245) Concurrent Mark (1642.031s, 1642.235s) 203.162ms
[2025-09-10T17:15:33.701+0800][1642.235s][info][gc,start      ] GC(245) Pause Remark
[2025-09-10T17:15:33.719+0800][1642.252s][info][gc,stringtable] GC(245) Cleaned string and symbol table, strings: 639457 processed, 0 removed, symbols: 589318 processed, 0 removed
[2025-09-10T17:15:33.719+0800][1642.253s][info][gc            ] GC(245) Pause Remark 801M->801M(1026M) 18.233ms
[2025-09-10T17:15:33.720+0800][1642.254s][info][gc,cpu        ] GC(245) User=0.25s Sys=0.00s Real=0.02s
[2025-09-10T17:15:33.720+0800][1642.254s][info][gc,marking    ] GC(245) Concurrent Rebuild Remembered Sets
[2025-09-10T17:15:33.811+0800][1642.345s][info][gc,start      ] GC(246) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:33.811+0800][1642.345s][info][gc,task       ] GC(246) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:33.818+0800][1642.352s][info][gc,phases     ] GC(246)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:33.819+0800][1642.353s][info][gc,phases     ] GC(246)   Evacuate Collection Set: 5.1ms
[2025-09-10T17:15:33.819+0800][1642.353s][info][gc,phases     ] GC(246)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:15:33.819+0800][1642.353s][info][gc,phases     ] GC(246)   Other: 0.7ms
[2025-09-10T17:15:33.819+0800][1642.353s][info][gc,heap       ] GC(246) Eden regions: 190->0(185)
[2025-09-10T17:15:33.819+0800][1642.353s][info][gc,heap       ] GC(246) Survivor regions: 4->10(25)
[2025-09-10T17:15:33.819+0800][1642.353s][info][gc,heap       ] GC(246) Old regions: 702->702
[2025-09-10T17:15:33.820+0800][1642.354s][info][gc,heap       ] GC(246) Humongous regions: 5->5
[2025-09-10T17:15:33.820+0800][1642.354s][info][gc,metaspace  ] GC(246) Metaspace: 500960K(541828K)->500960K(541828K) NonClass: 437907K(465640K)->437907K(465640K) Class: 63053K(76188K)->63053K(76188K)
[2025-09-10T17:15:33.820+0800][1642.354s][info][gc            ] GC(246) Pause Young (Normal) (G1 Evacuation Pause) 898M->714M(1026M) 9.283ms
[2025-09-10T17:15:33.820+0800][1642.354s][info][gc,cpu        ] GC(246) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:15:33.897+0800][1642.431s][info][gc,marking    ] GC(245) Concurrent Rebuild Remembered Sets 177.107ms
[2025-09-10T17:15:33.899+0800][1642.433s][info][gc,start      ] GC(245) Pause Cleanup
[2025-09-10T17:15:33.900+0800][1642.434s][info][gc            ] GC(245) Pause Cleanup 787M->787M(1026M) 1.106ms
[2025-09-10T17:15:33.900+0800][1642.434s][info][gc,cpu        ] GC(245) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:15:33.901+0800][1642.435s][info][gc,marking    ] GC(245) Concurrent Cleanup for Next Mark
[2025-09-10T17:15:33.902+0800][1642.435s][info][gc,marking    ] GC(245) Concurrent Cleanup for Next Mark 0.835ms
[2025-09-10T17:15:33.902+0800][1642.436s][info][gc            ] GC(245) Concurrent Cycle 409.811ms
[2025-09-10T17:15:34.014+0800][1642.548s][info][gc,start      ] GC(247) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:34.014+0800][1642.548s][info][gc,task       ] GC(247) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:34.018+0800][1642.552s][info][gc,phases     ] GC(247)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:34.019+0800][1642.553s][info][gc,phases     ] GC(247)   Evacuate Collection Set: 2.3ms
[2025-09-10T17:15:34.019+0800][1642.553s][info][gc,phases     ] GC(247)   Post Evacuate Collection Set: 0.6ms
[2025-09-10T17:15:34.019+0800][1642.553s][info][gc,phases     ] GC(247)   Other: 0.6ms
[2025-09-10T17:15:34.019+0800][1642.553s][info][gc,heap       ] GC(247) Eden regions: 185->0(40)
[2025-09-10T17:15:34.019+0800][1642.553s][info][gc,heap       ] GC(247) Survivor regions: 10->11(25)
[2025-09-10T17:15:34.019+0800][1642.553s][info][gc,heap       ] GC(247) Old regions: 702->702
[2025-09-10T17:15:34.020+0800][1642.554s][info][gc,heap       ] GC(247) Humongous regions: 6->5
[2025-09-10T17:15:34.020+0800][1642.554s][info][gc,metaspace  ] GC(247) Metaspace: 501008K(541828K)->501008K(541828K) NonClass: 437955K(465640K)->437955K(465640K) Class: 63053K(76188K)->63053K(76188K)
[2025-09-10T17:15:34.020+0800][1642.554s][info][gc            ] GC(247) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 900M->715M(1026M) 5.799ms
[2025-09-10T17:15:34.020+0800][1642.554s][info][gc,cpu        ] GC(247) User=0.03s Sys=0.00s Real=0.00s
[2025-09-10T17:15:34.055+0800][1642.589s][info][gc,start      ] GC(248) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:34.055+0800][1642.589s][info][gc,task       ] GC(248) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:34.062+0800][1642.596s][info][gc,phases     ] GC(248)   Pre Evacuate Collection Set: 0.2ms
[2025-09-10T17:15:34.063+0800][1642.597s][info][gc,phases     ] GC(248)   Evacuate Collection Set: 6.1ms
[2025-09-10T17:15:34.063+0800][1642.597s][info][gc,phases     ] GC(248)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:15:34.063+0800][1642.597s][info][gc,phases     ] GC(248)   Other: 0.6ms
[2025-09-10T17:15:34.063+0800][1642.597s][info][gc,heap       ] GC(248) Eden regions: 40->0(189)
[2025-09-10T17:15:34.064+0800][1642.598s][info][gc,heap       ] GC(248) Survivor regions: 11->4(7)
[2025-09-10T17:15:34.064+0800][1642.598s][info][gc,heap       ] GC(248) Old regions: 702->704
[2025-09-10T17:15:34.064+0800][1642.598s][info][gc,heap       ] GC(248) Humongous regions: 5->5
[2025-09-10T17:15:34.064+0800][1642.598s][info][gc,metaspace  ] GC(248) Metaspace: 501008K(541828K)->501008K(541828K) NonClass: 437955K(465640K)->437955K(465640K) Class: 63053K(76188K)->63053K(76188K)
[2025-09-10T17:15:34.064+0800][1642.598s][info][gc            ] GC(248) Pause Young (Mixed) (G1 Evacuation Pause) 755M->710M(1026M) 9.559ms
[2025-09-10T17:15:34.065+0800][1642.599s][info][gc,cpu        ] GC(248) User=0.08s Sys=0.00s Real=0.01s
[2025-09-10T17:15:34.214+0800][1642.748s][info][gc,start      ] GC(249) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:34.214+0800][1642.748s][info][gc,task       ] GC(249) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:34.219+0800][1642.753s][info][gc,phases     ] GC(249)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:34.221+0800][1642.755s][info][gc,phases     ] GC(249)   Evacuate Collection Set: 3.9ms
[2025-09-10T17:15:34.222+0800][1642.756s][info][gc,phases     ] GC(249)   Post Evacuate Collection Set: 0.7ms
[2025-09-10T17:15:34.222+0800][1642.756s][info][gc,phases     ] GC(249)   Other: 0.6ms
[2025-09-10T17:15:34.223+0800][1642.757s][info][gc,heap       ] GC(249) Eden regions: 189->0(181)
[2025-09-10T17:15:34.224+0800][1642.758s][info][gc,heap       ] GC(249) Survivor regions: 4->13(25)
[2025-09-10T17:15:34.225+0800][1642.759s][info][gc,heap       ] GC(249) Old regions: 704->704
[2025-09-10T17:15:34.225+0800][1642.759s][info][gc,heap       ] GC(249) Humongous regions: 5->5
[2025-09-10T17:15:34.225+0800][1642.759s][info][gc,metaspace  ] GC(249) Metaspace: 501017K(541828K)->501017K(541828K) NonClass: 437964K(465640K)->437964K(465640K) Class: 63053K(76188K)->63053K(76188K)
[2025-09-10T17:15:34.225+0800][1642.759s][info][gc            ] GC(249) Pause Young (Normal) (G1 Evacuation Pause) 899M->719M(1026M) 11.113ms
[2025-09-10T17:15:34.225+0800][1642.759s][info][gc,cpu        ] GC(249) User=0.05s Sys=0.00s Real=0.01s
[2025-09-10T17:15:34.481+0800][1643.015s][info][gc,start      ] GC(250) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:34.483+0800][1643.017s][info][gc,task       ] GC(250) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:34.486+0800][1643.020s][info][gc,phases     ] GC(250)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:34.488+0800][1643.022s][info][gc,phases     ] GC(250)   Evacuate Collection Set: 1.5ms
[2025-09-10T17:15:34.489+0800][1643.022s][info][gc,phases     ] GC(250)   Post Evacuate Collection Set: 0.5ms
[2025-09-10T17:15:34.489+0800][1643.023s][info][gc,phases     ] GC(250)   Other: 3.2ms
[2025-09-10T17:15:34.489+0800][1643.023s][info][gc,heap       ] GC(250) Eden regions: 181->0(192)
[2025-09-10T17:15:34.489+0800][1643.023s][info][gc,heap       ] GC(250) Survivor regions: 13->2(25)
[2025-09-10T17:15:34.491+0800][1643.025s][info][gc,heap       ] GC(250) Old regions: 704->704
[2025-09-10T17:15:34.492+0800][1643.026s][info][gc,heap       ] GC(250) Humongous regions: 5->5
[2025-09-10T17:15:34.494+0800][1643.028s][info][gc,metaspace  ] GC(250) Metaspace: 501017K(541828K)->501017K(541828K) NonClass: 437964K(465640K)->437964K(465640K) Class: 63053K(76188K)->63053K(76188K)
[2025-09-10T17:15:34.494+0800][1643.028s][info][gc            ] GC(250) Pause Young (Normal) (G1 Evacuation Pause) 900M->708M(1026M) 13.792ms
[2025-09-10T17:15:34.496+0800][1643.030s][info][gc,cpu        ] GC(250) User=0.02s Sys=0.00s Real=0.02s
[2025-09-10T17:15:35.983+0800][1644.517s][info][gc,start      ] GC(251) Pause Young (Concurrent Start) (G1 Evacuation Pause)
[2025-09-10T17:15:35.984+0800][1644.518s][info][gc,task       ] GC(251) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:35.989+0800][1644.523s][info][gc,phases     ] GC(251)   Pre Evacuate Collection Set: 1.1ms
[2025-09-10T17:15:35.991+0800][1644.525s][info][gc,phases     ] GC(251)   Evacuate Collection Set: 3.2ms
[2025-09-10T17:15:35.991+0800][1644.525s][info][gc,phases     ] GC(251)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:15:35.991+0800][1644.525s][info][gc,phases     ] GC(251)   Other: 0.6ms
[2025-09-10T17:15:35.991+0800][1644.525s][info][gc,heap       ] GC(251) Eden regions: 192->0(196)
[2025-09-10T17:15:35.991+0800][1644.525s][info][gc,heap       ] GC(251) Survivor regions: 2->1(25)
[2025-09-10T17:15:35.991+0800][1644.525s][info][gc,heap       ] GC(251) Old regions: 704->704
[2025-09-10T17:15:35.992+0800][1644.525s][info][gc,heap       ] GC(251) Humongous regions: 5->5
[2025-09-10T17:15:35.992+0800][1644.526s][info][gc,metaspace  ] GC(251) Metaspace: 501078K(541828K)->501078K(541828K) NonClass: 438025K(465640K)->438025K(465640K) Class: 63053K(76188K)->63053K(76188K)
[2025-09-10T17:15:35.992+0800][1644.526s][info][gc            ] GC(251) Pause Young (Concurrent Start) (G1 Evacuation Pause) 900M->707M(1026M) 8.319ms
[2025-09-10T17:15:35.992+0800][1644.526s][info][gc,cpu        ] GC(251) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:35.992+0800][1644.526s][info][gc            ] GC(252) Concurrent Cycle
[2025-09-10T17:15:35.992+0800][1644.526s][info][gc,marking    ] GC(252) Concurrent Clear Claimed Marks
[2025-09-10T17:15:35.994+0800][1644.528s][info][gc,marking    ] GC(252) Concurrent Clear Claimed Marks 1.298ms
[2025-09-10T17:15:35.994+0800][1644.528s][info][gc,marking    ] GC(252) Concurrent Scan Root Regions
[2025-09-10T17:15:35.996+0800][1644.530s][info][gc,marking    ] GC(252) Concurrent Scan Root Regions 2.140ms
[2025-09-10T17:15:35.996+0800][1644.530s][info][gc,marking    ] GC(252) Concurrent Mark (1644.530s)
[2025-09-10T17:15:35.996+0800][1644.530s][info][gc,marking    ] GC(252) Concurrent Mark From Roots
[2025-09-10T17:15:35.997+0800][1644.531s][info][gc,task       ] GC(252) Using 4 workers of 4 for marking
[2025-09-10T17:15:36.186+0800][1644.720s][info][gc,marking    ] GC(252) Concurrent Mark From Roots 189.932ms
[2025-09-10T17:15:36.201+0800][1644.735s][info][gc,marking    ] GC(252) Concurrent Preclean
[2025-09-10T17:15:36.226+0800][1644.760s][info][gc,marking    ] GC(252) Concurrent Preclean 25.091ms
[2025-09-10T17:15:36.251+0800][1644.785s][info][gc,marking    ] GC(252) Concurrent Mark (1644.530s, 1644.785s) 254.684ms
[2025-09-10T17:15:36.267+0800][1644.801s][info][gc,start      ] GC(252) Pause Remark
[2025-09-10T17:15:36.295+0800][1644.829s][info][gc,stringtable] GC(252) Cleaned string and symbol table, strings: 639501 processed, 0 removed, symbols: 589323 processed, 1 removed
[2025-09-10T17:15:36.321+0800][1644.855s][info][gc            ] GC(252) Pause Remark 716M->716M(1026M) 54.683ms
[2025-09-10T17:15:36.347+0800][1644.881s][info][gc,cpu        ] GC(252) User=0.23s Sys=0.00s Real=0.08s
[2025-09-10T17:15:36.387+0800][1644.921s][info][gc,marking    ] GC(252) Concurrent Rebuild Remembered Sets
[2025-09-10T17:15:36.571+0800][1645.105s][info][gc,marking    ] GC(252) Concurrent Rebuild Remembered Sets 184.393ms
[2025-09-10T17:15:36.576+0800][1645.110s][info][gc,start      ] GC(252) Pause Cleanup
[2025-09-10T17:15:36.582+0800][1645.116s][info][gc            ] GC(252) Pause Cleanup 717M->717M(1026M) 6.419ms
[2025-09-10T17:15:36.592+0800][1645.126s][info][gc,cpu        ] GC(252) User=0.01s Sys=0.00s Real=0.01s
[2025-09-10T17:15:36.617+0800][1645.151s][info][gc,marking    ] GC(252) Concurrent Cleanup for Next Mark
[2025-09-10T17:15:36.645+0800][1645.179s][info][gc,marking    ] GC(252) Concurrent Cleanup for Next Mark 28.059ms
[2025-09-10T17:15:36.664+0800][1645.198s][info][gc            ] GC(252) Concurrent Cycle 671.611ms
[2025-09-10T17:15:46.570+0800][1655.104s][info][gc,start      ] GC(253) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:46.571+0800][1655.105s][info][gc,task       ] GC(253) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:46.575+0800][1655.109s][info][gc,phases     ] GC(253)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:46.576+0800][1655.110s][info][gc,phases     ] GC(253)   Evacuate Collection Set: 3.0ms
[2025-09-10T17:15:46.577+0800][1655.110s][info][gc,phases     ] GC(253)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:15:46.577+0800][1655.111s][info][gc,phases     ] GC(253)   Other: 0.8ms
[2025-09-10T17:15:46.577+0800][1655.111s][info][gc,heap       ] GC(253) Eden regions: 196->0(41)
[2025-09-10T17:15:46.577+0800][1655.111s][info][gc,heap       ] GC(253) Survivor regions: 1->10(25)
[2025-09-10T17:15:46.577+0800][1655.111s][info][gc,heap       ] GC(253) Old regions: 704->704
[2025-09-10T17:15:46.577+0800][1655.111s][info][gc,heap       ] GC(253) Humongous regions: 6->5
[2025-09-10T17:15:46.578+0800][1655.111s][info][gc,metaspace  ] GC(253) Metaspace: 501359K(542084K)->501359K(542084K) NonClass: 438292K(465896K)->438292K(465896K) Class: 63067K(76188K)->63067K(76188K)
[2025-09-10T17:15:46.578+0800][1655.112s][info][gc            ] GC(253) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 904M->716M(1026M) 7.303ms
[2025-09-10T17:15:46.578+0800][1655.112s][info][gc,cpu        ] GC(253) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:46.609+0800][1655.143s][info][gc,start      ] GC(254) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:15:46.609+0800][1655.143s][info][gc,task       ] GC(254) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:46.615+0800][1655.149s][info][gc,phases     ] GC(254)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:15:46.616+0800][1655.150s][info][gc,phases     ] GC(254)   Evacuate Collection Set: 4.2ms
[2025-09-10T17:15:46.616+0800][1655.150s][info][gc,phases     ] GC(254)   Post Evacuate Collection Set: 0.9ms
[2025-09-10T17:15:46.617+0800][1655.151s][info][gc,phases     ] GC(254)   Other: 1.0ms
[2025-09-10T17:15:46.617+0800][1655.151s][info][gc,heap       ] GC(254) Eden regions: 41->0(186)
[2025-09-10T17:15:46.617+0800][1655.151s][info][gc,heap       ] GC(254) Survivor regions: 10->2(7)
[2025-09-10T17:15:46.617+0800][1655.151s][info][gc,heap       ] GC(254) Old regions: 704->711
[2025-09-10T17:15:46.618+0800][1655.152s][info][gc,heap       ] GC(254) Humongous regions: 5->5
[2025-09-10T17:15:46.618+0800][1655.152s][info][gc,metaspace  ] GC(254) Metaspace: 501360K(542084K)->501360K(542084K) NonClass: 438293K(465896K)->438293K(465896K) Class: 63067K(76188K)->63067K(76188K)
[2025-09-10T17:15:46.618+0800][1655.152s][info][gc            ] GC(254) Pause Young (Mixed) (G1 Evacuation Pause) 757M->715M(1026M) 9.075ms
[2025-09-10T17:15:46.618+0800][1655.152s][info][gc,cpu        ] GC(254) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:15:46.902+0800][1655.436s][info][gc,start      ] GC(255) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:46.903+0800][1655.437s][info][gc,task       ] GC(255) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:46.907+0800][1655.441s][info][gc,phases     ] GC(255)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:46.907+0800][1655.441s][info][gc,phases     ] GC(255)   Evacuate Collection Set: 2.4ms
[2025-09-10T17:15:46.908+0800][1655.441s][info][gc,phases     ] GC(255)   Post Evacuate Collection Set: 1.3ms
[2025-09-10T17:15:46.908+0800][1655.442s][info][gc,phases     ] GC(255)   Other: 0.6ms
[2025-09-10T17:15:46.908+0800][1655.442s][info][gc,heap       ] GC(255) Eden regions: 186->0(182)
[2025-09-10T17:15:46.908+0800][1655.442s][info][gc,heap       ] GC(255) Survivor regions: 2->7(24)
[2025-09-10T17:15:46.909+0800][1655.442s][info][gc,heap       ] GC(255) Old regions: 711->711
[2025-09-10T17:15:46.909+0800][1655.443s][info][gc,heap       ] GC(255) Humongous regions: 6->5
[2025-09-10T17:15:46.909+0800][1655.443s][info][gc,metaspace  ] GC(255) Metaspace: 501361K(542084K)->501361K(542084K) NonClass: 438294K(465896K)->438294K(465896K) Class: 63067K(76188K)->63067K(76188K)
[2025-09-10T17:15:46.909+0800][1655.443s][info][gc            ] GC(255) Pause Young (Normal) (G1 Evacuation Pause) 902M->720M(1026M) 6.602ms
[2025-09-10T17:15:46.909+0800][1655.443s][info][gc,cpu        ] GC(255) User=0.04s Sys=0.00s Real=0.01s
[2025-09-10T17:15:47.159+0800][1655.693s][info][gc,start      ] GC(256) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:47.162+0800][1655.696s][info][gc,task       ] GC(256) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:47.168+0800][1655.702s][info][gc,phases     ] GC(256)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:47.169+0800][1655.703s][info][gc,phases     ] GC(256)   Evacuate Collection Set: 4.6ms
[2025-09-10T17:15:47.169+0800][1655.703s][info][gc,phases     ] GC(256)   Post Evacuate Collection Set: 0.8ms
[2025-09-10T17:15:47.171+0800][1655.705s][info][gc,phases     ] GC(256)   Other: 4.1ms
[2025-09-10T17:15:47.173+0800][1655.707s][info][gc,heap       ] GC(256) Eden regions: 182->0(163)
[2025-09-10T17:15:47.174+0800][1655.708s][info][gc,heap       ] GC(256) Survivor regions: 7->22(24)
[2025-09-10T17:15:47.175+0800][1655.709s][info][gc,heap       ] GC(256) Old regions: 711->711
[2025-09-10T17:15:47.175+0800][1655.709s][info][gc,heap       ] GC(256) Humongous regions: 5->5
[2025-09-10T17:15:47.176+0800][1655.710s][info][gc,metaspace  ] GC(256) Metaspace: 501361K(542084K)->501361K(542084K) NonClass: 438294K(465896K)->438294K(465896K) Class: 63067K(76188K)->63067K(76188K)
[2025-09-10T17:15:47.176+0800][1655.710s][info][gc            ] GC(256) Pause Young (Normal) (G1 Evacuation Pause) 902M->735M(1026M) 17.116ms
[2025-09-10T17:15:47.176+0800][1655.710s][info][gc,cpu        ] GC(256) User=0.05s Sys=0.01s Real=0.02s
[2025-09-10T17:15:58.825+0800][1667.361s][info][gc,start      ] GC(257) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:15:58.826+0800][1667.362s][info][gc,task       ] GC(257) Using 15 workers of 15 for evacuation
[2025-09-10T17:15:58.831+0800][1667.367s][info][gc,phases     ] GC(257)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:15:58.832+0800][1667.368s][info][gc,phases     ] GC(257)   Evacuate Collection Set: 3.4ms
[2025-09-10T17:15:58.832+0800][1667.368s][info][gc,phases     ] GC(257)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:15:58.832+0800][1667.368s][info][gc,phases     ] GC(257)   Other: 1.4ms
[2025-09-10T17:15:58.832+0800][1667.368s][info][gc,heap       ] GC(257) Eden regions: 163->0(166)
[2025-09-10T17:15:58.832+0800][1667.368s][info][gc,heap       ] GC(257) Survivor regions: 22->14(24)
[2025-09-10T17:15:58.833+0800][1667.369s][info][gc,heap       ] GC(257) Old regions: 711->711
[2025-09-10T17:15:58.833+0800][1667.369s][info][gc,heap       ] GC(257) Humongous regions: 7->5
[2025-09-10T17:15:58.833+0800][1667.369s][info][gc,metaspace  ] GC(257) Metaspace: 502707K(543236K)->502707K(543236K) NonClass: 439444K(466920K)->439444K(466920K) Class: 63262K(76316K)->63262K(76316K)
[2025-09-10T17:15:58.833+0800][1667.369s][info][gc            ] GC(257) Pause Young (Normal) (G1 Evacuation Pause) 900M->727M(1026M) 7.939ms
[2025-09-10T17:15:58.833+0800][1667.369s][info][gc,cpu        ] GC(257) User=0.05s Sys=0.01s Real=0.01s
[2025-09-10T17:16:05.998+0800][1674.534s][info][gc,start      ] GC(258) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:16:05.999+0800][1674.535s][info][gc,task       ] GC(258) Using 15 workers of 15 for evacuation
[2025-09-10T17:16:06.005+0800][1674.541s][info][gc,phases     ] GC(258)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T17:16:06.007+0800][1674.543s][info][gc,phases     ] GC(258)   Evacuate Collection Set: 3.8ms
[2025-09-10T17:16:06.007+0800][1674.543s][info][gc,phases     ] GC(258)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T17:16:06.008+0800][1674.544s][info][gc,phases     ] GC(258)   Other: 1.5ms
[2025-09-10T17:16:06.008+0800][1674.544s][info][gc,heap       ] GC(258) Eden regions: 166->0(172)
[2025-09-10T17:16:06.008+0800][1674.545s][info][gc,heap       ] GC(258) Survivor regions: 14->13(23)
[2025-09-10T17:16:06.009+0800][1674.545s][info][gc,heap       ] GC(258) Old regions: 711->711
[2025-09-10T17:16:06.009+0800][1674.545s][info][gc,heap       ] GC(258) Humongous regions: 5->5
[2025-09-10T17:16:06.010+0800][1674.546s][info][gc,metaspace  ] GC(258) Metaspace: 502739K(543492K)->502739K(543492K) NonClass: 439475K(467176K)->439475K(467176K) Class: 63263K(76316K)->63263K(76316K)
[2025-09-10T17:16:06.010+0800][1674.546s][info][gc            ] GC(258) Pause Young (Normal) (G1 Evacuation Pause) 893M->726M(1026M) 11.694ms
[2025-09-10T17:16:06.010+0800][1674.546s][info][gc,cpu        ] GC(258) User=0.06s Sys=0.00s Real=0.02s
[2025-09-10T17:16:20.477+0800][1689.013s][info][gc,start      ] GC(259) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T17:16:20.477+0800][1689.013s][info][gc,task       ] GC(259) Using 15 workers of 15 for evacuation
[2025-09-10T17:16:20.485+0800][1689.021s][info][gc,phases     ] GC(259)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:16:20.485+0800][1689.021s][info][gc,phases     ] GC(259)   Evacuate Collection Set: 4.6ms
[2025-09-10T17:16:20.485+0800][1689.021s][info][gc,phases     ] GC(259)   Post Evacuate Collection Set: 2.3ms
[2025-09-10T17:16:20.485+0800][1689.021s][info][gc,phases     ] GC(259)   Other: 0.8ms
[2025-09-10T17:16:20.485+0800][1689.022s][info][gc,heap       ] GC(259) Eden regions: 172->0(151)
[2025-09-10T17:16:20.486+0800][1689.022s][info][gc,heap       ] GC(259) Survivor regions: 13->21(24)
[2025-09-10T17:16:20.486+0800][1689.022s][info][gc,heap       ] GC(259) Old regions: 711->711
[2025-09-10T17:16:20.486+0800][1689.022s][info][gc,heap       ] GC(259) Humongous regions: 10->8
[2025-09-10T17:16:20.486+0800][1689.022s][info][gc,metaspace  ] GC(259) Metaspace: 506686K(547716K)->506686K(547716K) NonClass: 442931K(470760K)->442931K(470760K) Class: 63755K(76956K)->63755K(76956K)
[2025-09-10T17:16:20.486+0800][1689.022s][info][gc            ] GC(259) Pause Young (Normal) (G1 Evacuation Pause) 903M->737M(1026M) 9.190ms
[2025-09-10T17:16:20.487+0800][1689.023s][info][gc,cpu        ] GC(259) User=0.09s Sys=0.00s Real=0.01s
[2025-09-10T17:16:20.570+0800][1689.106s][info][gc,start      ] GC(260) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:16:20.570+0800][1689.107s][info][gc,task       ] GC(260) Using 15 workers of 15 for evacuation
[2025-09-10T17:16:20.580+0800][1689.116s][info][gc,phases     ] GC(260)   Pre Evacuate Collection Set: 1.4ms
[2025-09-10T17:16:20.580+0800][1689.116s][info][gc,phases     ] GC(260)   Evacuate Collection Set: 6.8ms
[2025-09-10T17:16:20.581+0800][1689.117s][info][gc,phases     ] GC(260)   Post Evacuate Collection Set: 1.0ms
[2025-09-10T17:16:20.581+0800][1689.117s][info][gc,phases     ] GC(260)   Other: 0.9ms
[2025-09-10T17:16:20.581+0800][1689.117s][info][gc,heap       ] GC(260) Eden regions: 18->0(157)
[2025-09-10T17:16:20.582+0800][1689.118s][info][gc,heap       ] GC(260) Survivor regions: 21->3(22)
[2025-09-10T17:16:20.583+0800][1689.119s][info][gc,heap       ] GC(260) Old regions: 711->730
[2025-09-10T17:16:20.584+0800][1689.120s][info][gc,heap       ] GC(260) Humongous regions: 34->8
[2025-09-10T17:16:20.584+0800][1689.120s][info][gc,metaspace  ] GC(260) Metaspace: 506768K(547716K)->506768K(547716K) NonClass: 443005K(470760K)->443005K(470760K) Class: 63762K(76956K)->63762K(76956K)
[2025-09-10T17:16:20.584+0800][1689.120s][info][gc            ] GC(260) Pause Young (Concurrent Start) (G1 Humongous Allocation) 781M->738M(1026M) 14.250ms
[2025-09-10T17:16:20.585+0800][1689.121s][info][gc,cpu        ] GC(260) User=0.07s Sys=0.00s Real=0.02s
[2025-09-10T17:16:20.585+0800][1689.121s][info][gc            ] GC(261) Concurrent Cycle
[2025-09-10T17:16:20.585+0800][1689.121s][info][gc,marking    ] GC(261) Concurrent Clear Claimed Marks
[2025-09-10T17:16:20.587+0800][1689.123s][info][gc,marking    ] GC(261) Concurrent Clear Claimed Marks 2.201ms
[2025-09-10T17:16:20.588+0800][1689.124s][info][gc,marking    ] GC(261) Concurrent Scan Root Regions
[2025-09-10T17:16:20.591+0800][1689.127s][info][gc,marking    ] GC(261) Concurrent Scan Root Regions 3.207ms
[2025-09-10T17:16:20.591+0800][1689.127s][info][gc,marking    ] GC(261) Concurrent Mark (1689.127s)
[2025-09-10T17:16:20.591+0800][1689.127s][info][gc,marking    ] GC(261) Concurrent Mark From Roots
[2025-09-10T17:16:20.592+0800][1689.128s][info][gc,task       ] GC(261) Using 4 workers of 4 for marking
[2025-09-10T17:16:20.777+0800][1689.314s][info][gc,marking    ] GC(261) Concurrent Mark From Roots 186.038ms
[2025-09-10T17:16:20.778+0800][1689.314s][info][gc,marking    ] GC(261) Concurrent Preclean
[2025-09-10T17:16:20.779+0800][1689.315s][info][gc,marking    ] GC(261) Concurrent Preclean 1.368ms
[2025-09-10T17:16:20.779+0800][1689.315s][info][gc,marking    ] GC(261) Concurrent Mark (1689.127s, 1689.315s) 188.192ms
[2025-09-10T17:16:20.780+0800][1689.316s][info][gc,start      ] GC(261) Pause Remark
[2025-09-10T17:16:20.825+0800][1689.361s][info][gc,stringtable] GC(261) Cleaned string and symbol table, strings: 667593 processed, 32 removed, symbols: 619257 processed, 202 removed
[2025-09-10T17:16:20.826+0800][1689.362s][info][gc            ] GC(261) Pause Remark 786M->785M(1026M) 46.110ms
[2025-09-10T17:16:20.826+0800][1689.363s][info][gc,cpu        ] GC(261) User=0.52s Sys=0.00s Real=0.05s
[2025-09-10T17:16:20.827+0800][1689.363s][info][gc,marking    ] GC(261) Concurrent Rebuild Remembered Sets
[2025-09-10T17:16:20.981+0800][1689.518s][info][gc,marking    ] GC(261) Concurrent Rebuild Remembered Sets 154.391ms
[2025-09-10T17:16:20.983+0800][1689.519s][info][gc,start      ] GC(261) Pause Cleanup
[2025-09-10T17:16:20.984+0800][1689.520s][info][gc            ] GC(261) Pause Cleanup 785M->785M(1026M) 1.624ms
[2025-09-10T17:16:20.985+0800][1689.521s][info][gc,cpu        ] GC(261) User=0.01s Sys=0.00s Real=0.00s
[2025-09-10T17:16:20.985+0800][1689.521s][info][gc,marking    ] GC(261) Concurrent Cleanup for Next Mark
[2025-09-10T17:16:20.986+0800][1689.522s][info][gc,marking    ] GC(261) Concurrent Cleanup for Next Mark 0.974ms
[2025-09-10T17:16:20.986+0800][1689.522s][info][gc            ] GC(261) Concurrent Cycle 401.490ms
[2025-09-10T17:16:21.509+0800][1690.045s][info][gc,start      ] GC(262) Pause Young (Prepare Mixed) (G1 Evacuation Pause)
[2025-09-10T17:16:21.509+0800][1690.046s][info][gc,task       ] GC(262) Using 15 workers of 15 for evacuation
[2025-09-10T17:16:21.515+0800][1690.051s][info][gc,phases     ] GC(262)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T17:16:21.515+0800][1690.051s][info][gc,phases     ] GC(262)   Evacuate Collection Set: 2.6ms
[2025-09-10T17:16:21.515+0800][1690.051s][info][gc,phases     ] GC(262)   Post Evacuate Collection Set: 2.4ms
[2025-09-10T17:16:21.515+0800][1690.052s][info][gc,phases     ] GC(262)   Other: 0.6ms
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc,heap       ] GC(262) Eden regions: 157->0(46)
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc,heap       ] GC(262) Survivor regions: 3->5(20)
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc,heap       ] GC(262) Old regions: 729->729
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc,heap       ] GC(262) Humongous regions: 50->8
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc,metaspace  ] GC(262) Metaspace: 506764K(548100K)->506764K(548100K) NonClass: 443022K(471016K)->443022K(471016K) Class: 63742K(77084K)->63742K(77084K)
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc            ] GC(262) Pause Young (Prepare Mixed) (G1 Evacuation Pause) 936M->740M(1026M) 6.718ms
[2025-09-10T17:16:21.516+0800][1690.052s][info][gc,cpu        ] GC(262) User=0.06s Sys=0.00s Real=0.01s
[2025-09-10T17:16:21.826+0800][1690.362s][info][gc,start      ] GC(263) Pause Young (Mixed) (G1 Evacuation Pause)
[2025-09-10T17:16:21.826+0800][1690.362s][info][gc,task       ] GC(263) Using 15 workers of 15 for evacuation
[2025-09-10T17:16:21.840+0800][1690.376s][info][gc,phases     ] GC(263)   Pre Evacuate Collection Set: 0.5ms
[2025-09-10T17:16:21.840+0800][1690.376s][info][gc,phases     ] GC(263)   Evacuate Collection Set: 9.1ms
[2025-09-10T17:16:21.840+0800][1690.376s][info][gc,phases     ] GC(263)   Post Evacuate Collection Set: 1.5ms
[2025-09-10T17:16:21.841+0800][1690.377s][info][gc,phases     ] GC(263)   Other: 2.8ms
[2025-09-10T17:16:21.841+0800][1690.377s][info][gc,heap       ] GC(263) Eden regions: 46->0(174)
[2025-09-10T17:16:21.841+0800][1690.377s][info][gc,heap       ] GC(263) Survivor regions: 5->2(7)
[2025-09-10T17:16:21.841+0800][1690.377s][info][gc,heap       ] GC(263) Old regions: 729->716
[2025-09-10T17:16:21.844+0800][1690.380s][info][gc,heap       ] GC(263) Humongous regions: 8->8
[2025-09-10T17:16:21.845+0800][1690.381s][info][gc,metaspace  ] GC(263) Metaspace: 506836K(548100K)->506836K(548100K) NonClass: 443081K(471016K)->443081K(471016K) Class: 63754K(77084K)->63754K(77084K)
[2025-09-10T17:16:21.845+0800][1690.381s][info][gc            ] GC(263) Pause Young (Mixed) (G1 Evacuation Pause) 786M->723M(1026M) 19.453ms
[2025-09-10T17:16:21.845+0800][1690.381s][info][gc,cpu        ] GC(263) User=0.14s Sys=0.00s Real=0.02s
[2025-09-10T17:16:42.289+0800][1710.826s][info][gc,start      ] GC(264) Pause Young (Concurrent Start) (G1 Humongous Allocation)
[2025-09-10T17:16:42.289+0800][1710.826s][info][gc,task       ] GC(264) Using 15 workers of 15 for evacuation
[2025-09-10T17:16:42.299+0800][1710.836s][info][gc,phases     ] GC(264)   Pre Evacuate Collection Set: 1.2ms
[2025-09-10T17:16:42.299+0800][1710.836s][info][gc,phases     ] GC(264)   Evacuate Collection Set: 7.4ms
[2025-09-10T17:16:42.300+0800][1710.837s][info][gc,phases     ] GC(264)   Post Evacuate Collection Set: 1.1ms
[2025-09-10T17:16:42.300+0800][1710.837s][info][gc,phases     ] GC(264)   Other: 0.7ms
[2025-09-10T17:16:42.300+0800][1710.837s][info][gc,heap       ] GC(264) Eden regions: 67->0(142)
[2025-09-10T17:16:42.300+0800][1710.837s][info][gc,heap       ] GC(264) Survivor regions: 2->3(22)
[2025-09-10T17:16:42.300+0800][1710.837s][info][gc,heap       ] GC(264) Old regions: 716->716
[2025-09-10T17:16:42.301+0800][1710.838s][info][gc,heap       ] GC(264) Humongous regions: 8->7
[2025-09-10T17:16:42.301+0800][1710.838s][info][gc,metaspace  ] GC(264) Metaspace: 506990K(548100K)->506990K(548100K) NonClass: 443224K(471016K)->443224K(471016K) Class: 63765K(77084K)->63765K(77084K)
[2025-09-10T17:16:42.301+0800][1710.838s][info][gc            ] GC(264) Pause Young (Concurrent Start) (G1 Humongous Allocation) 789M->723M(1026M) 12.280ms
[2025-09-10T17:16:42.301+0800][1710.838s][info][gc,cpu        ] GC(264) User=0.08s Sys=0.01s Real=0.02s
[2025-09-10T17:16:42.301+0800][1710.838s][info][gc            ] GC(265) Concurrent Cycle
[2025-09-10T17:16:42.302+0800][1710.839s][info][gc,marking    ] GC(265) Concurrent Clear Claimed Marks
[2025-09-10T17:16:42.303+0800][1710.840s][info][gc,marking    ] GC(265) Concurrent Clear Claimed Marks 1.386ms
[2025-09-10T17:16:42.303+0800][1710.840s][info][gc,marking    ] GC(265) Concurrent Scan Root Regions
[2025-09-10T17:16:42.309+0800][1710.845s][info][gc,marking    ] GC(265) Concurrent Scan Root Regions 5.053ms
[2025-09-10T17:16:42.310+0800][1710.847s][info][gc,marking    ] GC(265) Concurrent Mark (1710.847s)
[2025-09-10T17:16:42.317+0800][1710.854s][info][gc,marking    ] GC(265) Concurrent Mark From Roots
[2025-09-10T17:16:42.319+0800][1710.856s][info][gc,task       ] GC(265) Using 4 workers of 4 for marking
[2025-09-10T17:16:42.505+0800][1711.042s][info][gc,marking    ] GC(265) Concurrent Mark From Roots 187.988ms
[2025-09-10T17:16:42.506+0800][1711.043s][info][gc,marking    ] GC(265) Concurrent Preclean
[2025-09-10T17:16:42.507+0800][1711.044s][info][gc,marking    ] GC(265) Concurrent Preclean 0.998ms
[2025-09-10T17:16:42.507+0800][1711.044s][info][gc,marking    ] GC(265) Concurrent Mark (1710.847s, 1711.044s) 197.600ms
[2025-09-10T17:16:42.508+0800][1711.045s][info][gc,start      ] GC(265) Pause Remark
[2025-09-10T17:16:42.550+0800][1711.087s][info][gc,stringtable] GC(265) Cleaned string and symbol table, strings: 668004 processed, 21 removed, symbols: 619788 processed, 131 removed
[2025-09-10T17:16:42.552+0800][1711.089s][info][gc            ] GC(265) Pause Remark 726M->726M(1026M) 44.143ms
[2025-09-10T17:16:42.552+0800][1711.089s][info][gc,cpu        ] GC(265) User=0.52s Sys=0.00s Real=0.05s
[2025-09-10T17:16:42.553+0800][1711.090s][info][gc,marking    ] GC(265) Concurrent Rebuild Remembered Sets
[2025-09-10T17:16:42.713+0800][1711.250s][info][gc,marking    ] GC(265) Concurrent Rebuild Remembered Sets 159.804ms
[2025-09-10T17:16:42.714+0800][1711.251s][info][gc,start      ] GC(265) Pause Cleanup
[2025-09-10T17:16:42.714+0800][1711.251s][info][gc            ] GC(265) Pause Cleanup 726M->726M(1026M) 0.893ms
[2025-09-10T17:16:42.715+0800][1711.252s][info][gc,cpu        ] GC(265) User=0.00s Sys=0.00s Real=0.00s
[2025-09-10T17:16:42.716+0800][1711.253s][info][gc,marking    ] GC(265) Concurrent Cleanup for Next Mark
[2025-09-10T17:16:42.717+0800][1711.254s][info][gc,marking    ] GC(265) Concurrent Cleanup for Next Mark 1.006ms
[2025-09-10T17:16:42.717+0800][1711.254s][info][gc            ] GC(265) Concurrent Cycle 415.745ms
