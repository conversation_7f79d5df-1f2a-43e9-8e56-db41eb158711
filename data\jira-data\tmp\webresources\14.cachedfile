WRMCB=function(e){var c=console;if(c&&c.log&&c.error){c.log('Error running batched script.');c.error(e);}}
;
try {
/* module-key = 'com.atlassian.auiplugin:split_aui.component.expander', location = 'aui.chunk.5c5eaea990a93c588c82--70abe8501f1550f764d3.js' */
(window.__auiJsonp=window.__auiJsonp||[]).push([["aui.component.expander"],{HTrG:function(e,t,r){},ufFX:function(e,t,r){"use strict";r.r(t);r("HTrG");var a=r("+x/D"),n=Object(a.a)(document),i=function(e){var t={};return t.$trigger=Object(a.a)(e.currentTarget),t.$content=n.find("#"+t.$trigger.attr("aria-controls")),t.triggerIsParent=0!==t.$content.parent().filter(t.$trigger).length,t.$shortContent=t.triggerIsParent?t.$trigger.find(".aui-expander-short-content"):null,t.height=t.$content.css("min-height"),t.isCollapsible=!1!==t.$trigger.data("collapsible"),t.replaceText=t.$trigger.attr("data-replace-text"),t.replaceSelector=t.$trigger.data("replace-selector"),t},c=function(e){if(e.replaceText){var t=e.replaceSelector?e.$trigger.find(e.replaceSelector):e.$trigger;e.$trigger.attr("data-replace-text",t.text()),t.text(e.replaceText)}},g={"aui-expander-invoke":function(e){var t=Object(a.a)(e.currentTarget),r=n.find("#"+t.attr("aria-controls")),i=!1!==t.data("collapsible");"true"===r.attr("aria-expanded")&&i?t.trigger("aui-expander-collapse"):t.trigger("aui-expander-expand")},"aui-expander-expand":function(e){var t=i(e);"true"!==t.$content.attr("aria-expanded")&&(t.$content.attr("aria-expanded","true"),t.$trigger.attr("aria-expanded","true"),t.$content.get(0).removeAttribute("hidden"),c(t),t.triggerIsParent&&t.$shortContent.hide(),t.$trigger.trigger("aui-expander-expanded"))},"aui-expander-collapse":function(e){var t=i(e);"true"===t.$content.attr("aria-expanded")&&(c(t),t.$content.attr("aria-expanded","false"),t.$trigger.attr("aria-expanded","false"),t.triggerIsParent&&t.$shortContent.show(),0===t.$content.outerHeight()&&t.$content.get(0).setAttribute("hidden",""),t.$trigger.trigger("aui-expander-collapsed"))},"click.aui-expander":function(e){Object(a.a)(e.currentTarget).trigger("aui-expander-invoke",e.currentTarget)}};n.on(g,".aui-expander-trigger")}},[["ufFX","runtime","aui.splitchunk.0d131bcbf1","aui.splitchunk.739b9ec8cc"]]]);
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugin.jslibs:underscore-1.8.3', location = 'libs/underscore/1.8.3/underscore-1.8.3.js' */
'use strict';(function(z){define("atlassian/libs/underscore-1.8.3",function(){var u={};z.call(u);return"undefined"!==typeof exports?"undefined"!==typeof module&&module.exports?module.exports.noConflict():exports.noConflict():u._.noConflict()})})(function(){(function(){function z(a){return function(c,d,e,f){d=v(d,f,4);var g=!n(c)&&b.keys(c),h=(g||c).length,k=0<a?0:h-1;3>arguments.length&&(e=c[g?g[k]:k],k+=a);for(var p=d,m=e;0<=k&&k<h;k+=a){var l=g?g[k]:k;m=p(m,c[l],l,c)}return m}}function u(a){return function(c,
b,e){b=l(b,e);e=r(c);for(var d=0<a?0:e-1;0<=d&&d<e;d+=a)if(b(c[d],d,c))return d;return-1}}function K(a,c,d){return function(e,f,g){var h=0,k=r(e);if("number"==typeof g)0<a?h=0<=g?g:Math.max(g+k,h):k=0<=g?Math.min(g+1,k):g+k+1;else if(d&&g&&k)return g=d(e,f),e[g]===f?g:-1;if(f!==f)return g=c(q.call(e,h,k),b.isNaN),0<=g?g+h:-1;for(g=0<a?h:k-1;0<=g&&g<k;g+=a)if(e[g]===f)return g;return-1}}function L(a,c){var d=M.length,e=a.constructor;e=b.isFunction(e)&&e.prototype||C;var f="constructor";for(b.has(a,
f)&&!b.contains(c,f)&&c.push(f);d--;)f=M[d],f in a&&a[f]!==e[f]&&!b.contains(c,f)&&c.push(f)}var D=this,S=D._,A=Array.prototype,C=Object.prototype,T=A.push,q=A.slice,w=C.toString,U=C.hasOwnProperty,B=Array.isArray,N=Object.keys,E=Function.prototype.bind,O=Object.create,F=function(){},b=function(a){if(a instanceof b)return a;if(!(this instanceof b))return new b(a);this._wrapped=a};"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(exports=module.exports=b),exports._=b):D._=
b;b.VERSION="1.8.3";var v=function(a,c,b){if(void 0===c)return a;switch(null==b?3:b){case 1:return function(b){return a.call(c,b)};case 2:return function(b,d){return a.call(c,b,d)};case 3:return function(b,d,g){return a.call(c,b,d,g)};case 4:return function(b,d,g,h){return a.call(c,b,d,g,h)}}return function(){return a.apply(c,arguments)}},l=function(a,c,d){return null==a?b.identity:b.isFunction(a)?v(a,c,d):b.isObject(a)?b.matcher(a):b.property(a)};b.iteratee=function(a,c){return l(a,c,Infinity)};
var x=function(a,c){return function(b){var d=arguments.length;if(2>d||null==b)return b;for(var f=1;f<d;f++)for(var g=arguments[f],h=a(g),k=h.length,p=0;p<k;p++){var m=h[p];c&&void 0!==b[m]||(b[m]=g[m])}return b}},P=function(a){if(!b.isObject(a))return{};if(O)return O(a);F.prototype=a;a=new F;F.prototype=null;return a},y=function(a){return function(c){return null==c?void 0:c[a]}},V=Math.pow(2,53)-1,r=y("length"),n=function(a){a=r(a);return"number"==typeof a&&0<=a&&a<=V};b.each=b.forEach=function(a,
c,d){c=v(c,d);var e;if(n(a))for(d=0,e=a.length;d<e;d++)c(a[d],d,a);else{var f=b.keys(a);d=0;for(e=f.length;d<e;d++)c(a[f[d]],f[d],a)}return a};b.map=b.collect=function(a,c,d){c=l(c,d);d=!n(a)&&b.keys(a);for(var e=(d||a).length,f=Array(e),g=0;g<e;g++){var h=d?d[g]:g;f[g]=c(a[h],h,a)}return f};b.reduce=b.foldl=b.inject=z(1);b.reduceRight=b.foldr=z(-1);b.find=b.detect=function(a,c,d){c=n(a)?b.findIndex(a,c,d):b.findKey(a,c,d);if(void 0!==c&&-1!==c)return a[c]};b.filter=b.select=function(a,c,d){var e=
[];c=l(c,d);b.each(a,function(a,b,d){c(a,b,d)&&e.push(a)});return e};b.reject=function(a,c,d){return b.filter(a,b.negate(l(c)),d)};b.every=b.all=function(a,c,d){c=l(c,d);d=!n(a)&&b.keys(a);for(var e=(d||a).length,f=0;f<e;f++){var g=d?d[f]:f;if(!c(a[g],g,a))return!1}return!0};b.some=b.any=function(a,c,d){c=l(c,d);d=!n(a)&&b.keys(a);for(var e=(d||a).length,f=0;f<e;f++){var g=d?d[f]:f;if(c(a[g],g,a))return!0}return!1};b.contains=b.includes=b.include=function(a,c,d,e){n(a)||(a=b.values(a));if("number"!=
typeof d||e)d=0;return 0<=b.indexOf(a,c,d)};b.invoke=function(a,c){var d=q.call(arguments,2),e=b.isFunction(c);return b.map(a,function(a){var b=e?c:a[c];return null==b?b:b.apply(a,d)})};b.pluck=function(a,c){return b.map(a,b.property(c))};b.where=function(a,c){return b.filter(a,b.matcher(c))};b.findWhere=function(a,c){return b.find(a,b.matcher(c))};b.max=function(a,c,d){var e=-Infinity,f=-Infinity,g;if(null==c&&null!=a){a=n(a)?a:b.values(a);for(var h=0,k=a.length;h<k;h++)d=a[h],d>e&&(e=d)}else c=
l(c,d),b.each(a,function(a,b,d){g=c(a,b,d);if(g>f||-Infinity===g&&-Infinity===e)e=a,f=g});return e};b.min=function(a,c,d){var e=Infinity,f=Infinity,g;if(null==c&&null!=a){a=n(a)?a:b.values(a);for(var h=0,k=a.length;h<k;h++)d=a[h],d<e&&(e=d)}else c=l(c,d),b.each(a,function(a,b,d){g=c(a,b,d);if(g<f||Infinity===g&&Infinity===e)e=a,f=g});return e};b.shuffle=function(a){a=n(a)?a:b.values(a);for(var c=a.length,d=Array(c),e=0,f;e<c;e++)f=b.random(0,e),f!==e&&(d[e]=d[f]),d[f]=a[e];return d};b.sample=function(a,
c,d){return null==c||d?(n(a)||(a=b.values(a)),a[b.random(a.length-1)]):b.shuffle(a).slice(0,Math.max(0,c))};b.sortBy=function(a,c,d){c=l(c,d);return b.pluck(b.map(a,function(a,b,d){return{value:a,index:b,criteria:c(a,b,d)}}).sort(function(a,b){var c=a.criteria,d=b.criteria;if(c!==d){if(c>d||void 0===c)return 1;if(c<d||void 0===d)return-1}return a.index-b.index}),"value")};var G=function(a){return function(c,d,e){var f={};d=l(d,e);b.each(c,function(b,e){e=d(b,e,c);a(f,b,e)});return f}};b.groupBy=G(function(a,
c,d){b.has(a,d)?a[d].push(c):a[d]=[c]});b.indexBy=G(function(a,b,d){a[d]=b});b.countBy=G(function(a,c,d){b.has(a,d)?a[d]++:a[d]=1});b.toArray=function(a){return a?b.isArray(a)?q.call(a):n(a)?b.map(a,b.identity):b.values(a):[]};b.size=function(a){return null==a?0:n(a)?a.length:b.keys(a).length};b.partition=function(a,c,d){c=l(c,d);var e=[],f=[];b.each(a,function(a,b,d){(c(a,b,d)?e:f).push(a)});return[e,f]};b.first=b.head=b.take=function(a,c,d){if(null!=a)return null==c||d?a[0]:b.initial(a,a.length-
c)};b.initial=function(a,b,d){return q.call(a,0,Math.max(0,a.length-(null==b||d?1:b)))};b.last=function(a,c,d){if(null!=a)return null==c||d?a[a.length-1]:b.rest(a,Math.max(0,a.length-c))};b.rest=b.tail=b.drop=function(a,b,d){return q.call(a,null==b||d?1:b)};b.compact=function(a){return b.filter(a,b.identity)};var t=function(a,c,d,e){var f=[],g=0;e=e||0;for(var h=r(a);e<h;e++){var k=a[e];if(n(k)&&(b.isArray(k)||b.isArguments(k))){c||(k=t(k,c,d));var p=0,m=k.length;for(f.length+=m;p<m;)f[g++]=k[p++]}else d||
(f[g++]=k)}return f};b.flatten=function(a,b){return t(a,b,!1)};b.without=function(a){return b.difference(a,q.call(arguments,1))};b.uniq=b.unique=function(a,c,d,e){b.isBoolean(c)||(e=d,d=c,c=!1);null!=d&&(d=l(d,e));e=[];for(var f=[],g=0,h=r(a);g<h;g++){var k=a[g],p=d?d(k,g,a):k;c?(g&&f===p||e.push(k),f=p):d?b.contains(f,p)||(f.push(p),e.push(k)):b.contains(e,k)||e.push(k)}return e};b.union=function(){return b.uniq(t(arguments,!0,!0))};b.intersection=function(a){for(var c=[],d=arguments.length,e=0,
f=r(a);e<f;e++){var g=a[e];if(!b.contains(c,g)){for(var h=1;h<d&&b.contains(arguments[h],g);h++);h===d&&c.push(g)}}return c};b.difference=function(a){var c=t(arguments,!0,!0,1);return b.filter(a,function(a){return!b.contains(c,a)})};b.zip=function(){return b.unzip(arguments)};b.unzip=function(a){for(var c=a&&b.max(a,r).length||0,d=Array(c),e=0;e<c;e++)d[e]=b.pluck(a,e);return d};b.object=function(a,b){for(var c={},e=0,f=r(a);e<f;e++)b?c[a[e]]=b[e]:c[a[e][0]]=a[e][1];return c};b.findIndex=u(1);b.findLastIndex=
u(-1);b.sortedIndex=function(a,b,d,e){d=l(d,e,1);b=d(b);e=0;for(var c=r(a);e<c;){var g=Math.floor((e+c)/2);d(a[g])<b?e=g+1:c=g}return e};b.indexOf=K(1,b.findIndex,b.sortedIndex);b.lastIndexOf=K(-1,b.findLastIndex);b.range=function(a,b,d){null==b&&(b=a||0,a=0);d=d||1;b=Math.max(Math.ceil((b-a)/d),0);for(var c=Array(b),f=0;f<b;f++,a+=d)c[f]=a;return c};var Q=function(a,c,d,e,f){if(!(e instanceof c))return a.apply(d,f);c=P(a.prototype);a=a.apply(c,f);return b.isObject(a)?a:c};b.bind=function(a,c){if(E&&
a.bind===E)return E.apply(a,q.call(arguments,1));if(!b.isFunction(a))throw new TypeError("Bind must be called on a function");var d=q.call(arguments,2),e=function(){return Q(a,e,c,this,d.concat(q.call(arguments)))};return e};b.partial=function(a){var c=q.call(arguments,1),d=function(){for(var e=0,f=c.length,g=Array(f),h=0;h<f;h++)g[h]=c[h]===b?arguments[e++]:c[h];for(;e<arguments.length;)g.push(arguments[e++]);return Q(a,d,this,this,g)};return d};b.bindAll=function(a){var c,d=arguments.length;if(1>=
d)throw Error("bindAll must be passed function names");for(c=1;c<d;c++){var e=arguments[c];a[e]=b.bind(a[e],a)}return a};b.memoize=function(a,c){var d=function(e){var f=d.cache,g=""+(c?c.apply(this,arguments):e);b.has(f,g)||(f[g]=a.apply(this,arguments));return f[g]};d.cache={};return d};b.delay=function(a,b){var c=q.call(arguments,2);return setTimeout(function(){return a.apply(null,c)},b)};b.defer=b.partial(b.delay,b,1);b.throttle=function(a,c,d){var e,f,g,h=null,k=0;d||(d={});var p=function(){k=
!1===d.leading?0:b.now();h=null;g=a.apply(e,f);h||(e=f=null)};return function(){var m=b.now();k||!1!==d.leading||(k=m);var l=c-(m-k);e=this;f=arguments;0>=l||l>c?(h&&(clearTimeout(h),h=null),k=m,g=a.apply(e,f),h||(e=f=null)):h||!1===d.trailing||(h=setTimeout(p,l));return g}};b.debounce=function(a,c,d){var e,f,g,h,k,l=function(){var m=b.now()-h;m<c&&0<=m?e=setTimeout(l,c-m):(e=null,d||(k=a.apply(g,f),e||(g=f=null)))};return function(){g=this;f=arguments;h=b.now();var m=d&&!e;e||(e=setTimeout(l,c));
m&&(k=a.apply(g,f),g=f=null);return k}};b.wrap=function(a,c){return b.partial(c,a)};b.negate=function(a){return function(){return!a.apply(this,arguments)}};b.compose=function(){var a=arguments,b=a.length-1;return function(){for(var c=b,e=a[b].apply(this,arguments);c--;)e=a[c].call(this,e);return e}};b.after=function(a,b){return function(){if(1>--a)return b.apply(this,arguments)}};b.before=function(a,b){var c;return function(){0<--a&&(c=b.apply(this,arguments));1>=a&&(b=null);return c}};b.once=b.partial(b.before,
2);var R=!{toString:null}.propertyIsEnumerable("toString"),M="valueOf isPrototypeOf toString propertyIsEnumerable hasOwnProperty toLocaleString".split(" ");b.keys=function(a){if(!b.isObject(a))return[];if(N)return N(a);var c=[],d;for(d in a)b.has(a,d)&&c.push(d);R&&L(a,c);return c};b.allKeys=function(a){if(!b.isObject(a))return[];var c=[],d;for(d in a)c.push(d);R&&L(a,c);return c};b.values=function(a){for(var c=b.keys(a),d=c.length,e=Array(d),f=0;f<d;f++)e[f]=a[c[f]];return e};b.mapObject=function(a,
c,d){c=l(c,d);d=b.keys(a);for(var e=d.length,f={},g,h=0;h<e;h++)g=d[h],f[g]=c(a[g],g,a);return f};b.pairs=function(a){for(var c=b.keys(a),d=c.length,e=Array(d),f=0;f<d;f++)e[f]=[c[f],a[c[f]]];return e};b.invert=function(a){for(var c={},d=b.keys(a),e=0,f=d.length;e<f;e++)c[a[d[e]]]=d[e];return c};b.functions=b.methods=function(a){var c=[],d;for(d in a)b.isFunction(a[d])&&c.push(d);return c.sort()};b.extend=x(b.allKeys);b.extendOwn=b.assign=x(b.keys);b.findKey=function(a,c,d){c=l(c,d);d=b.keys(a);for(var e,
f=0,g=d.length;f<g;f++)if(e=d[f],c(a[e],e,a))return e};b.pick=function(a,c,d){var e={},f=a;if(null==f)return e;if(b.isFunction(c)){var g=b.allKeys(f);var h=v(c,d)}else g=t(arguments,!1,!1,1),h=function(a,b,c){return b in c},f=Object(f);for(var k=0,l=g.length;k<l;k++){var m=g[k],n=f[m];h(n,m,f)&&(e[m]=n)}return e};b.omit=function(a,c,d){if(b.isFunction(c))c=b.negate(c);else{var e=b.map(t(arguments,!1,!1,1),String);c=function(a,c){return!b.contains(e,c)}}return b.pick(a,c,d)};b.defaults=x(b.allKeys,
!0);b.create=function(a,c){a=P(a);c&&b.extendOwn(a,c);return a};b.clone=function(a){return b.isObject(a)?b.isArray(a)?a.slice():b.extend({},a):a};b.tap=function(a,b){b(a);return a};b.isMatch=function(a,c){var d=b.keys(c),e=d.length;if(null==a)return!e;a=Object(a);for(var f=0;f<e;f++){var g=d[f];if(c[g]!==a[g]||!(g in a))return!1}return!0};var H=function(a,c,d,e){if(a===c)return 0!==a||1/a===1/c;if(null==a||null==c)return a===c;a instanceof b&&(a=a._wrapped);c instanceof b&&(c=c._wrapped);var f=w.call(a);
if(f!==w.call(c))return!1;switch(f){case "[object RegExp]":case "[object String]":return""+a===""+c;case "[object Number]":return+a!==+a?+c!==+c:0===+a?1/+a===1/c:+a===+c;case "[object Date]":case "[object Boolean]":return+a===+c}f="[object Array]"===f;if(!f){if("object"!=typeof a||"object"!=typeof c)return!1;var g=a.constructor,h=c.constructor;if(g!==h&&!(b.isFunction(g)&&g instanceof g&&b.isFunction(h)&&h instanceof h)&&"constructor"in a&&"constructor"in c)return!1}d=d||[];e=e||[];for(g=d.length;g--;)if(d[g]===
a)return e[g]===c;d.push(a);e.push(c);if(f){g=a.length;if(g!==c.length)return!1;for(;g--;)if(!H(a[g],c[g],d,e))return!1}else{f=b.keys(a);g=f.length;if(b.keys(c).length!==g)return!1;for(;g--;)if(h=f[g],!b.has(c,h)||!H(a[h],c[h],d,e))return!1}d.pop();e.pop();return!0};b.isEqual=function(a,b){return H(a,b)};b.isEmpty=function(a){return null==a?!0:n(a)&&(b.isArray(a)||b.isString(a)||b.isArguments(a))?0===a.length:0===b.keys(a).length};b.isElement=function(a){return!(!a||1!==a.nodeType)};b.isArray=B||
function(a){return"[object Array]"===w.call(a)};b.isObject=function(a){var b=typeof a;return"function"===b||"object"===b&&!!a};b.each("Arguments Function String Number Date RegExp Error".split(" "),function(a){b["is"+a]=function(b){return w.call(b)==="[object "+a+"]"}});b.isArguments(arguments)||(b.isArguments=function(a){return b.has(a,"callee")});"function"!=typeof/./&&"object"!=typeof Int8Array&&(b.isFunction=function(a){return"function"==typeof a||!1});b.isFinite=function(a){return isFinite(a)&&
!isNaN(parseFloat(a))};b.isNaN=function(a){return b.isNumber(a)&&a!==+a};b.isBoolean=function(a){return!0===a||!1===a||"[object Boolean]"===w.call(a)};b.isNull=function(a){return null===a};b.isUndefined=function(a){return void 0===a};b.has=function(a,b){return null!=a&&U.call(a,b)};b.noConflict=function(){D._=S;return this};b.identity=function(a){return a};b.constant=function(a){return function(){return a}};b.noop=function(){};b.property=y;b.propertyOf=function(a){return null==a?function(){}:function(b){return a[b]}};
b.matcher=b.matches=function(a){a=b.extendOwn({},a);return function(c){return b.isMatch(c,a)}};b.times=function(a,b,d){var c=Array(Math.max(0,a));b=v(b,d,1);for(d=0;d<a;d++)c[d]=b(d);return c};b.random=function(a,b){null==b&&(b=a,a=0);return a+Math.floor(Math.random()*(b-a+1))};b.now=Date.now||function(){return(new Date).getTime()};B={"\x26":"\x26amp;","\x3c":"\x26lt;","\x3e":"\x26gt;",'"':"\x26quot;","'":"\x26#x27;","`":"\x26#x60;"};x=b.invert(B);y=function(a){var c=function(b){return a[b]},d="(?:"+
b.keys(a).join("|")+")",e=RegExp(d),f=RegExp(d,"g");return function(a){a=null==a?"":""+a;return e.test(a)?a.replace(f,c):a}};b.escape=y(B);b.unescape=y(x);b.result=function(a,c,d){c=null==a?void 0:a[c];void 0===c&&(c=d);return b.isFunction(c)?c.call(a):c};var W=0;b.uniqueId=function(a){var b=++W+"";return a?a+b:b};b.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var I=/(.)^/,X={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},
Y=/\\|'|\r|\n|\u2028|\u2029/g,Z=function(a){return"\\"+X[a]};b.template=function(a,c,d){!c&&d&&(c=d);c=b.defaults({},c,b.templateSettings);d=RegExp([(c.escape||I).source,(c.interpolate||I).source,(c.evaluate||I).source].join("|")+"|$","g");var e=0,f="__p+\x3d'";a.replace(d,function(b,c,d,g,l){f+=a.slice(e,l).replace(Y,Z);e=l+b.length;c?f+="'+\n((__t\x3d("+c+"))\x3d\x3dnull?'':_.escape(__t))+\n'":d?f+="'+\n((__t\x3d("+d+"))\x3d\x3dnull?'':__t)+\n'":g&&(f+="';\n"+g+"\n__p+\x3d'");return b});f+="';\n";
c.variable||(f="with(obj||{}){\n"+f+"}\n");f="var __t,__p\x3d'',__j\x3dArray.prototype.join,print\x3dfunction(){__p+\x3d__j.call(arguments,'');};\n"+f+"return __p;\n";try{var g=new Function(c.variable||"obj","_",f)}catch(h){throw h.source=f,h;}d=function(a){return g.call(this,a,b)};d.source="function("+(c.variable||"obj")+"){\n"+f+"}";return d};b.chain=function(a){a=b(a);a._chain=!0;return a};var J=function(a,c){return a._chain?b(c).chain():c};b.mixin=function(a){b.each(b.functions(a),function(c){var d=
b[c]=a[c];b.prototype[c]=function(){var a=[this._wrapped];T.apply(a,arguments);return J(this,d.apply(b,a))}})};b.mixin(b);b.each("pop push reverse shift sort splice unshift".split(" "),function(a){var c=A[a];b.prototype[a]=function(){var b=this._wrapped;c.apply(b,arguments);"shift"!==a&&"splice"!==a||0!==b.length||delete b[0];return J(this,b)}});b.each(["concat","join","slice"],function(a){var c=A[a];b.prototype[a]=function(){return J(this,c.apply(this._wrapped,arguments))}});b.prototype.value=function(){return this._wrapped};
b.prototype.valueOf=b.prototype.toJSON=b.prototype.value;b.prototype.toString=function(){return""+this._wrapped};"function"===typeof define&&define.amd&&define("underscore",[],function(){return b})}).call(this)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.plugin.jslibs:backbone-1.3.3-factory', location = 'factories/backbone/1.3.3/backbone-1.3.3-factory.js' */
'use strict';(function(x){define("atlassian/libs/factories/backbone-1.3.3",function(){return function(h,d){return x({},{},h,d).noConflict()}})})(function(x,h,d,k){var K=x.Backbone,B=Array.prototype.slice;h.VERSION="1.3.3";h.$=k;h.noConflict=function(){x.Backbone=K;return this};h.emulateHTTP=!1;h.emulateJSON=!1;var L=function(a,b,c){switch(a){case 1:return function(){return d[b](this[c])};case 2:return function(a){return d[b](this[c],a)};case 3:return function(a,f){return d[b](this[c],C(a,this),f)};
case 4:return function(a,f,g){return d[b](this[c],C(a,this),f,g)};default:return function(){var a=B.call(arguments);a.unshift(this[c]);return d[b].apply(d,a)}}},p=function(a,b,c){d.each(b,function(b,f){d[f]&&(a.prototype[f]=L(b,f,c))})},C=function(a,b){return d.isFunction(a)?a:d.isObject(a)&&!b._isModel(a)?M(a):d.isString(a)?function(b){return b.get(a)}:a},M=function(a){var b=d.matches(a);return function(a){return b(a.attributes)}};k=h.Events={};var D=/\s+/,v=function(a,b,c,e,f){var g=0,t;if(c&&"object"===
typeof c)for(void 0!==e&&("context"in f)&&void 0===f.context&&(f.context=e),t=d.keys(c);g<t.length;g++)b=v(a,b,t[g],c[t[g]],f);else if(c&&D.test(c))for(t=c.split(D);g<t.length;g++)b=a(b,t[g],e,f);else b=a(b,c,e,f);return b};k.on=function(a,b,c){return E(this,a,b,c)};var E=function(a,b,c,e,f){a._events=v(N,a._events||{},b,c,{context:e,ctx:a,listening:f});f&&((a._listeners||(a._listeners={}))[f.id]=f);return a};k.listenTo=function(a,b,c){if(!a)return this;var e=a._listenId||(a._listenId=d.uniqueId("l")),
f=this._listeningTo||(this._listeningTo={}),g=f[e];g||(g=this._listenId||(this._listenId=d.uniqueId("l")),g=f[e]={obj:a,objId:e,id:g,listeningTo:f,count:0});E(a,b,c,this,g);return this};var N=function(a,b,c,e){if(c){b=a[b]||(a[b]=[]);var f=e.context,d=e.ctx;(e=e.listening)&&e.count++;b.push({callback:c,context:f,ctx:f||d,listening:e})}return a};k.off=function(a,b,c){if(!this._events)return this;this._events=v(O,this._events,a,b,{context:c,listeners:this._listeners});return this};k.stopListening=function(a,
b,c){var e=this._listeningTo;if(!e)return this;a=a?[a._listenId]:d.keys(e);for(var f=0;f<a.length;f++){var g=e[a[f]];if(!g)break;g.obj.off(b,c,this)}return this};var O=function(a,b,c,e){if(a){var f=0,g=e.context;e=e.listeners;if(b||c||g){for(var t=b?[b]:d.keys(a);f<t.length;f++){b=t[f];var h=a[b];if(!h)break;for(var q=[],k=0;k<h.length;k++){var l=h[k];c&&c!==l.callback&&c!==l.callback._callback||g&&g!==l.context?q.push(l):(l=l.listening)&&0===--l.count&&(delete e[l.id],delete l.listeningTo[l.objId])}q.length?
a[b]=q:delete a[b]}return a}for(a=d.keys(e);f<a.length;f++)l=e[a[f]],delete e[l.id],delete l.listeningTo[l.objId]}};k.once=function(a,b,c){var e=v(F,{},a,b,d.bind(this.off,this));"string"===typeof a&&null==c&&(b=void 0);return this.on(e,b,c)};k.listenToOnce=function(a,b,c){b=v(F,{},b,c,d.bind(this.stopListening,this,a));return this.listenTo(a,b)};var F=function(a,b,c,e){if(c){var f=a[b]=d.once(function(){e(b,f);c.apply(this,arguments)});f._callback=c}return a};k.trigger=function(a){if(!this._events)return this;
for(var b=Math.max(0,arguments.length-1),c=Array(b),e=0;e<b;e++)c[e]=arguments[e+1];v(Q,this._events,a,void 0,c);return this};var Q=function(a,b,c,e){if(a){c=a[b];var f=a.all;c&&f&&(f=f.slice());c&&G(c,e);f&&G(f,[b].concat(e))}return a},G=function(a,b){var c,e=-1,f=a.length,d=b[0],h=b[1],y=b[2];switch(b.length){case 0:for(;++e<f;)(c=a[e]).callback.call(c.ctx);break;case 1:for(;++e<f;)(c=a[e]).callback.call(c.ctx,d);break;case 2:for(;++e<f;)(c=a[e]).callback.call(c.ctx,d,h);break;case 3:for(;++e<f;)(c=
a[e]).callback.call(c.ctx,d,h,y);break;default:for(;++e<f;)(c=a[e]).callback.apply(c.ctx,b)}};k.bind=k.on;k.unbind=k.off;d.extend(h,k);var w=h.Model=function(a,b){var c=a||{};b||(b={});this.cid=d.uniqueId(this.cidPrefix);this.attributes={};b.collection&&(this.collection=b.collection);b.parse&&(c=this.parse(c,b)||{});var e=d.result(this,"defaults");c=d.defaults(d.extend({},e,c),e);this.set(c,b);this.changed={};this.initialize.apply(this,arguments)};d.extend(w.prototype,k,{changed:null,validationError:null,
idAttribute:"id",cidPrefix:"c",initialize:function(){},toJSON:function(a){return d.clone(this.attributes)},sync:function(){return h.sync.apply(this,arguments)},get:function(a){return this.attributes[a]},escape:function(a){return d.escape(this.get(a))},has:function(a){return null!=this.get(a)},matches:function(a){return!!d.iteratee(a,this)(this.attributes)},set:function(a,b,c){if(null==a)return this;if("object"===typeof a){var e=a;c=b}else(e={})[a]=b;c||(c={});if(!this._validate(e,c))return!1;var f=
c.unset;a=c.silent;var g=[],h=this._changing;this._changing=!0;h||(this._previousAttributes=d.clone(this.attributes),this.changed={});var y=this.attributes,k=this.changed,P=this._previousAttributes,l;for(l in e)b=e[l],d.isEqual(y[l],b)||g.push(l),d.isEqual(P[l],b)?delete k[l]:k[l]=b,f?delete y[l]:y[l]=b;this.idAttribute in e&&(this.id=this.get(this.idAttribute));if(!a)for(g.length&&(this._pending=c),b=0;b<g.length;b++)this.trigger("change:"+g[b],this,y[g[b]],c);if(h)return this;if(!a)for(;this._pending;)c=
this._pending,this._pending=!1,this.trigger("change",this,c);this._changing=this._pending=!1;return this},unset:function(a,b){return this.set(a,void 0,d.extend({},b,{unset:!0}))},clear:function(a){var b={},c;for(c in this.attributes)b[c]=void 0;return this.set(b,d.extend({},a,{unset:!0}))},hasChanged:function(a){return null==a?!d.isEmpty(this.changed):d.has(this.changed,a)},changedAttributes:function(a){if(!a)return this.hasChanged()?d.clone(this.changed):!1;var b=this._changing?this._previousAttributes:
this.attributes,c={},e;for(e in a){var f=a[e];d.isEqual(b[e],f)||(c[e]=f)}return d.size(c)?c:!1},previous:function(a){return null!=a&&this._previousAttributes?this._previousAttributes[a]:null},previousAttributes:function(){return d.clone(this._previousAttributes)},fetch:function(a){a=d.extend({parse:!0},a);var b=this,c=a.success;a.success=function(e){var f=a.parse?b.parse(e,a):e;if(!b.set(f,a))return!1;c&&c.call(a.context,b,e,a);b.trigger("sync",b,e,a)};z(this,a);return this.sync("read",this,a)},
save:function(a,b,c){if(null==a||"object"===typeof a){var e=a;c=b}else(e={})[a]=b;c=d.extend({validate:!0,parse:!0},c);var f=c.wait;if(e&&!f){if(!this.set(e,c))return!1}else if(!this._validate(e,c))return!1;var g=this,h=c.success,k=this.attributes;c.success=function(a){g.attributes=k;var b=c.parse?g.parse(a,c):a;f&&(b=d.extend({},e,b));if(b&&!g.set(b,c))return!1;h&&h.call(c.context,g,a,c);g.trigger("sync",g,a,c)};z(this,c);e&&f&&(this.attributes=d.extend({},k,e));a=this.isNew()?"create":c.patch?"patch":
"update";"patch"!==a||c.attrs||(c.attrs=e);a=this.sync(a,this,c);this.attributes=k;return a},destroy:function(a){a=a?d.clone(a):{};var b=this,c=a.success,e=a.wait,f=function(){b.stopListening();b.trigger("destroy",b,b.collection,a)};a.success=function(d){e&&f();c&&c.call(a.context,b,d,a);b.isNew()||b.trigger("sync",b,d,a)};var g=!1;this.isNew()?d.defer(a.success):(z(this,a),g=this.sync("delete",this,a));e||f();return g},url:function(){var a=d.result(this,"urlRoot")||d.result(this.collection,"url")||
H();if(this.isNew())return a;var b=this.get(this.idAttribute);return a.replace(/[^\/]$/,"$\x26/")+encodeURIComponent(b)},parse:function(a,b){return a},clone:function(){return new this.constructor(this.attributes)},isNew:function(){return!this.has(this.idAttribute)},isValid:function(a){return this._validate({},d.extend({},a,{validate:!0}))},_validate:function(a,b){if(!b.validate||!this.validate)return!0;a=d.extend({},this.attributes,a);a=this.validationError=this.validate(a,b)||null;if(!a)return!0;
this.trigger("invalid",this,a,d.extend(b,{validationError:a}));return!1}});p(w,{keys:1,values:1,pairs:1,invert:1,pick:0,omit:0,chain:1,isEmpty:1},"attributes");var A=h.Collection=function(a,b){b||(b={});b.model&&(this.model=b.model);void 0!==b.comparator&&(this.comparator=b.comparator);this._reset();this.initialize.apply(this,arguments);a&&this.reset(a,d.extend({silent:!0},b))},R={add:!0,remove:!0,merge:!0},S={add:!0,remove:!1},I=function(a,b,c){c=Math.min(Math.max(c,0),a.length);var e=Array(a.length-
c),f=b.length,d;for(d=0;d<e.length;d++)e[d]=a[d+c];for(d=0;d<f;d++)a[d+c]=b[d];for(d=0;d<e.length;d++)a[d+f+c]=e[d]};d.extend(A.prototype,k,{model:w,initialize:function(){},toJSON:function(a){return this.map(function(b){return b.toJSON(a)})},sync:function(){return h.sync.apply(this,arguments)},add:function(a,b){return this.set(a,d.extend({merge:!1},b,S))},remove:function(a,b){b=d.extend({},b);var c=!d.isArray(a);a=c?[a]:a.slice();a=this._removeModels(a,b);!b.silent&&a.length&&(b.changes={added:[],
merged:[],removed:a},this.trigger("update",this,b));return c?a[0]:a},set:function(a,b){if(null!=a){b=d.extend({},R,b);b.parse&&!this._isModel(a)&&(a=this.parse(a,b)||[]);var c=!d.isArray(a);a=c?[a]:a.slice();var e=b.at;null!=e&&(e=+e);e>this.length&&(e=this.length);0>e&&(e+=this.length+1);var f=[],g=[],h=[],k=[],q={},r=b.add,l=b.merge,v=b.remove,p=!1,w=this.comparator&&null==e&&!1!==b.sort,x=d.isString(this.comparator)?this.comparator:null,n;for(n=0;n<a.length;n++){var m=a[n];var u=this.get(m);u?
(l&&m!==u&&(m=this._isModel(m)?m.attributes:m,b.parse&&(m=u.parse(m,b)),u.set(m,b),h.push(u),w&&!p&&(p=u.hasChanged(x))),q[u.cid]||(q[u.cid]=!0,f.push(u)),a[n]=u):r&&(m=a[n]=this._prepareModel(m,b))&&(g.push(m),this._addReference(m,b),q[m.cid]=!0,f.push(m))}if(v){for(n=0;n<this.length;n++)m=this.models[n],q[m.cid]||k.push(m);k.length&&this._removeModels(k,b)}q=!1;f.length&&!w&&r&&v?(q=this.length!==f.length||d.some(this.models,function(a,b){return a!==f[b]}),this.models.length=0,I(this.models,f,0),
this.length=this.models.length):g.length&&(w&&(p=!0),I(this.models,g,null==e?this.length:e),this.length=this.models.length);p&&this.sort({silent:!0});if(!b.silent){for(n=0;n<g.length;n++)null!=e&&(b.index=e+n),m=g[n],m.trigger("add",m,this,b);(p||q)&&this.trigger("sort",this,b);if(g.length||k.length||h.length)b.changes={added:g,removed:k,merged:h},this.trigger("update",this,b)}return c?a[0]:a}},reset:function(a,b){b=b?d.clone(b):{};for(var c=0;c<this.models.length;c++)this._removeReference(this.models[c],
b);b.previousModels=this.models;this._reset();a=this.add(a,d.extend({silent:!0},b));b.silent||this.trigger("reset",this,b);return a},push:function(a,b){return this.add(a,d.extend({at:this.length},b))},pop:function(a){var b=this.at(this.length-1);return this.remove(b,a)},unshift:function(a,b){return this.add(a,d.extend({at:0},b))},shift:function(a){var b=this.at(0);return this.remove(b,a)},slice:function(){return B.apply(this.models,arguments)},get:function(a){if(null!=a)return this._byId[a]||this._byId[this.modelId(a.attributes||
a)]||a.cid&&this._byId[a.cid]},has:function(a){return null!=this.get(a)},at:function(a){0>a&&(a+=this.length);return this.models[a]},where:function(a,b){return this[b?"find":"filter"](a)},findWhere:function(a){return this.where(a,!0)},sort:function(a){var b=this.comparator;if(!b)throw Error("Cannot sort a set without a comparator");a||(a={});var c=b.length;d.isFunction(b)&&(b=d.bind(b,this));1===c||d.isString(b)?this.models=this.sortBy(b):this.models.sort(b);a.silent||this.trigger("sort",this,a);
return this},pluck:function(a){return this.map(a+"")},fetch:function(a){a=d.extend({parse:!0},a);var b=a.success,c=this;a.success=function(e){c[a.reset?"reset":"set"](e,a);b&&b.call(a.context,c,e,a);c.trigger("sync",c,e,a)};z(this,a);return this.sync("read",this,a)},create:function(a,b){b=b?d.clone(b):{};var c=b.wait;a=this._prepareModel(a,b);if(!a)return!1;c||this.add(a,b);var e=this,f=b.success;b.success=function(a,b,d){c&&e.add(a,d);f&&f.call(d.context,a,b,d)};a.save(null,b);return a},parse:function(a,
b){return a},clone:function(){return new this.constructor(this.models,{model:this.model,comparator:this.comparator})},modelId:function(a){return a[this.model.prototype.idAttribute||"id"]},_reset:function(){this.length=0;this.models=[];this._byId={}},_prepareModel:function(a,b){if(this._isModel(a))return a.collection||(a.collection=this),a;b=b?d.clone(b):{};b.collection=this;a=new this.model(a,b);if(!a.validationError)return a;this.trigger("invalid",this,a.validationError,b);return!1},_removeModels:function(a,
b){for(var c=[],e=0;e<a.length;e++){var d=this.get(a[e]);if(d){var g=this.indexOf(d);this.models.splice(g,1);this.length--;delete this._byId[d.cid];var h=this.modelId(d.attributes);null!=h&&delete this._byId[h];b.silent||(b.index=g,d.trigger("remove",d,this,b));c.push(d);this._removeReference(d,b)}}return c},_isModel:function(a){return a instanceof w},_addReference:function(a,b){this._byId[a.cid]=a;b=this.modelId(a.attributes);null!=b&&(this._byId[b]=a);a.on("all",this._onModelEvent,this)},_removeReference:function(a,
b){delete this._byId[a.cid];b=this.modelId(a.attributes);null!=b&&delete this._byId[b];this===a.collection&&delete a.collection;a.off("all",this._onModelEvent,this)},_onModelEvent:function(a,b,c,e){if(b){if(("add"===a||"remove"===a)&&c!==this)return;"destroy"===a&&this.remove(b,e);if("change"===a){var d=this.modelId(b.previousAttributes()),g=this.modelId(b.attributes);d!==g&&(null!=d&&delete this._byId[d],null!=g&&(this._byId[g]=b))}}this.trigger.apply(this,arguments)}});p(A,{forEach:3,each:3,map:3,
collect:3,reduce:0,foldl:0,inject:0,reduceRight:0,foldr:0,find:3,detect:3,filter:3,select:3,reject:3,every:3,all:3,some:3,any:3,include:3,includes:3,contains:3,invoke:0,max:3,min:3,toArray:1,size:1,first:3,head:3,take:3,initial:3,rest:3,tail:3,drop:3,last:3,without:0,difference:0,indexOf:3,shuffle:1,lastIndexOf:3,isEmpty:1,chain:1,sample:3,partition:3,groupBy:3,countBy:3,sortBy:3,indexBy:3,findIndex:3,findLastIndex:3},"models");p=h.View=function(a){this.cid=d.uniqueId("view");d.extend(this,d.pick(a,
T));this._ensureElement();this.initialize.apply(this,arguments)};var U=/^(\S+)\s*(.*)$/,T="model collection el id attributes className tagName events".split(" ");d.extend(p.prototype,k,{tagName:"div",$:function(a){return this.$el.find(a)},initialize:function(){},render:function(){return this},remove:function(){this._removeElement();this.stopListening();return this},_removeElement:function(){this.$el.remove()},setElement:function(a){this.undelegateEvents();this._setElement(a);this.delegateEvents();
return this},_setElement:function(a){this.$el=a instanceof h.$?a:h.$(a);this.el=this.$el[0]},delegateEvents:function(a){a||(a=d.result(this,"events"));if(!a)return this;this.undelegateEvents();for(var b in a){var c=a[b];d.isFunction(c)||(c=this[c]);if(c){var e=b.match(U);this.delegate(e[1],e[2],d.bind(c,this))}}return this},delegate:function(a,b,c){this.$el.on(a+".delegateEvents"+this.cid,b,c);return this},undelegateEvents:function(){this.$el&&this.$el.off(".delegateEvents"+this.cid);return this},
undelegate:function(a,b,c){this.$el.off(a+".delegateEvents"+this.cid,b,c);return this},_createElement:function(a){return document.createElement(a)},_ensureElement:function(){if(this.el)this.setElement(d.result(this,"el"));else{var a=d.extend({},d.result(this,"attributes"));this.id&&(a.id=d.result(this,"id"));this.className&&(a["class"]=d.result(this,"className"));this.setElement(this._createElement(d.result(this,"tagName")));this._setAttributes(a)}},_setAttributes:function(a){this.$el.attr(a)}});
h.sync=function(a,b,c){var e=V[a];d.defaults(c||(c={}),{emulateHTTP:h.emulateHTTP,emulateJSON:h.emulateJSON});var f={type:e,dataType:"json"};c.url||(f.url=d.result(b,"url")||H());null!=c.data||!b||"create"!==a&&"update"!==a&&"patch"!==a||(f.contentType="application/json",f.data=JSON.stringify(c.attrs||b.toJSON(c)));c.emulateJSON&&(f.contentType="application/x-www-form-urlencoded",f.data=f.data?{model:f.data}:{});if(c.emulateHTTP&&("PUT"===e||"DELETE"===e||"PATCH"===e)){f.type="POST";c.emulateJSON&&
(f.data._method=e);var g=c.beforeSend;c.beforeSend=function(a){a.setRequestHeader("X-HTTP-Method-Override",e);if(g)return g.apply(this,arguments)}}"GET"===f.type||c.emulateJSON||(f.processData=!1);var k=c.error;c.error=function(a,b,d){c.textStatus=b;c.errorThrown=d;k&&k.call(c.context,a,b,d)};a=c.xhr=h.ajax(d.extend(f,c));b.trigger("request",b,a,c);return a};var V={create:"POST",update:"PUT",patch:"PATCH","delete":"DELETE",read:"GET"};h.ajax=function(){return h.$.ajax.apply(h.$,arguments)};var J=
h.Router=function(a){a||(a={});a.routes&&(this.routes=a.routes);this._bindRoutes();this.initialize.apply(this,arguments)},W=/\((.*?)\)/g,X=/(\(\?)?:\w+/g,Y=/\*\w+/g,Z=/[\-{}\[\]+?.,\\\^$|#\s]/g;d.extend(J.prototype,k,{initialize:function(){},route:function(a,b,c){d.isRegExp(a)||(a=this._routeToRegExp(a));d.isFunction(b)&&(c=b,b="");c||(c=this[b]);var e=this;h.history.route(a,function(d){d=e._extractParameters(a,d);!1!==e.execute(c,d,b)&&(e.trigger.apply(e,["route:"+b].concat(d)),e.trigger("route",
b,d),h.history.trigger("route",e,b,d))});return this},execute:function(a,b,c){a&&a.apply(this,b)},navigate:function(a,b){h.history.navigate(a,b);return this},_bindRoutes:function(){if(this.routes){this.routes=d.result(this,"routes");for(var a,b=d.keys(this.routes);null!=(a=b.pop());)this.route(a,this.routes[a])}},_routeToRegExp:function(a){a=a.replace(Z,"\\$\x26").replace(W,"(?:$1)?").replace(X,function(a,c){return c?a:"([^/?]+)"}).replace(Y,"([^?]*?)");return new RegExp("^"+a+"(?:\\?([\\s\\S]*))?$")},
_extractParameters:function(a,b){var c=a.exec(b).slice(1);return d.map(c,function(a,b){return b===c.length-1?a||null:a?decodeURIComponent(a):null})}});var r=h.History=function(){this.handlers=[];this.checkUrl=d.bind(this.checkUrl,this);"undefined"!==typeof window&&(this.location=window.location,this.history=window.history)},aa=/^[#\/]|\s+$/g,ba=/^\/+|\/+$/g,ca=/#.*$/;r.started=!1;d.extend(r.prototype,k,{interval:50,atRoot:function(){return this.location.pathname.replace(/[^\/]$/,"$\x26/")===this.root&&
!this.getSearch()},matchRoot:function(){return this.decodeFragment(this.location.pathname).slice(0,this.root.length-1)+"/"===this.root},decodeFragment:function(a){return decodeURI(a.replace(/%25/g,"%2525"))},getSearch:function(){var a=this.location.href.replace(/#.*/,"").match(/\?.+/);return a?a[0]:""},getHash:function(a){return(a=(a||this).location.href.match(/#(.*)$/))?a[1]:""},getPath:function(){var a=this.decodeFragment(this.location.pathname+this.getSearch()).slice(this.root.length-1);return"/"===
a.charAt(0)?a.slice(1):a},getFragment:function(a){null==a&&(a=this._usePushState||!this._wantsHashChange?this.getPath():this.getHash());return a.replace(aa,"")},start:function(a){if(r.started)throw Error("Backbone.history has already been started");r.started=!0;this.options=d.extend({root:"/"},this.options,a);this.root=this.options.root;this._wantsHashChange=!1!==this.options.hashChange;this._hasHashChange="onhashchange"in window&&(void 0===document.documentMode||7<document.documentMode);this._useHashChange=
this._wantsHashChange&&this._hasHashChange;this._wantsPushState=!!this.options.pushState;this._hasPushState=!(!this.history||!this.history.pushState);this._usePushState=this._wantsPushState&&this._hasPushState;this.fragment=this.getFragment();this.root=("/"+this.root+"/").replace(ba,"/");if(this._wantsHashChange&&this._wantsPushState)if(this._hasPushState||this.atRoot())this._hasPushState&&this.atRoot()&&this.navigate(this.getHash(),{replace:!0});else return a=this.root.slice(0,-1)||"/",this.location.replace(a+
"#"+this.getPath()),!0;this._hasHashChange||!this._wantsHashChange||this._usePushState||(this.iframe=document.createElement("iframe"),this.iframe.src="javascript:0",this.iframe.style.display="none",this.iframe.tabIndex=-1,a=document.body,a=a.insertBefore(this.iframe,a.firstChild).contentWindow,a.document.open(),a.document.close(),a.location.hash="#"+this.fragment);a=window.addEventListener||function(a,c){return attachEvent("on"+a,c)};this._usePushState?a("popstate",this.checkUrl,!1):this._useHashChange&&
!this.iframe?a("hashchange",this.checkUrl,!1):this._wantsHashChange&&(this._checkUrlInterval=setInterval(this.checkUrl,this.interval));if(!this.options.silent)return this.loadUrl()},stop:function(){var a=window.removeEventListener||function(a,c){return detachEvent("on"+a,c)};this._usePushState?a("popstate",this.checkUrl,!1):this._useHashChange&&!this.iframe&&a("hashchange",this.checkUrl,!1);this.iframe&&(document.body.removeChild(this.iframe),this.iframe=null);this._checkUrlInterval&&clearInterval(this._checkUrlInterval);
r.started=!1},route:function(a,b){this.handlers.unshift({route:a,callback:b})},checkUrl:function(a){a=this.getFragment();a===this.fragment&&this.iframe&&(a=this.getHash(this.iframe.contentWindow));if(a===this.fragment)return!1;this.iframe&&this.navigate(a);this.loadUrl()},loadUrl:function(a){if(!this.matchRoot())return!1;a=this.fragment=this.getFragment(a);return d.some(this.handlers,function(b){if(b.route.test(a))return b.callback(a),!0})},navigate:function(a,b){if(!r.started)return!1;b&&!0!==b||
(b={trigger:!!b});a=this.getFragment(a||"");var c=this.root;if(""===a||"?"===a.charAt(0))c=c.slice(0,-1)||"/";c+=a;a=this.decodeFragment(a.replace(ca,""));if(this.fragment!==a){this.fragment=a;if(this._usePushState)this.history[b.replace?"replaceState":"pushState"]({},document.title,c);else if(this._wantsHashChange)this._updateHash(this.location,a,b.replace),this.iframe&&a!==this.getHash(this.iframe.contentWindow)&&(c=this.iframe.contentWindow,b.replace||(c.document.open(),c.document.close()),this._updateHash(c.location,
a,b.replace));else return this.location.assign(c);if(b.trigger)return this.loadUrl(a)}},_updateHash:function(a,b,c){c?(c=a.href.replace(/(javascript:|#).*$/,""),a.replace(c+"#"+b)):a.hash="#"+b}});h.history=new r;w.extend=A.extend=J.extend=p.extend=r.extend=function(a,b){var c=this;var e=a&&d.has(a,"constructor")?a.constructor:function(){return c.apply(this,arguments)};d.extend(e,c,b);e.prototype=d.create(c.prototype,a);e.prototype.constructor=e;e.__super__=c.prototype;return e};var H=function(){throw Error('A "url" property or function must be specified');
},z=function(a,b){var c=b.error;b.error=function(d){c&&c.call(b.context,a,d,b);a.trigger("error",a,d,b)}};return h});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'jira.webresources:backbone-1.3.3', location = '/static/lib/backbone/backbone-1.3.3.js' */
define("jira/backbone-1.3.3",["jquery","atlassian/libs/underscore-1.8.3","atlassian/libs/factories/backbone-1.3.3"],function(a,e,n){return n(e,a)});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-transition-triggers-plugin:dev-status-common-resources', location = 'js/workflow/TriggersPluginBackboneDefine.js' */
define("jira-triggers/backbone-define",["jira/backbone-1.3.3","underscore"],function(Backbone,_){if(Backbone&&!Backbone.define){Backbone.define=function(name,ctor){eval("Backbone['Class: "+name+"'] = function() { Backbone['Class: "+name+"'].__ctor.apply(this, arguments); }");var cls=Backbone["Class: "+name];cls.__ctor=ctor;ctor.prototype.name=name;cls.prototype=ctor.prototype;_.extend(cls,ctor);_.each(ctor.prototype,function(fn,fnName){if(typeof fn==="function"){fn.displayName=name+"."+fnName}});var context=window;var parts=name.split(".");_.each(parts,function(part,i){if(i===parts.length-1){context[part]=cls}else{context=context[part]==null?(context[part]={}):context[part]}});return cls}}return Backbone});require("jira-triggers/backbone-define");
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-transition-triggers-plugin:dev-status-common-resources', location = 'js/devstatus/FeedbackDialog.js' */
require(["jira/backbone-1.3.3","jquery","underscore"],function(c,b,a){c.define("JIRA.DevStatus.FeedbackDialog",c.Model.extend({properties:["collectorId","summary"],defaults:{summary:"\u8bf7\u628a\u60a8\u7684\u89c2\u70b9\u544a\u8bc9\u6211\u4eec\u3002",collectorId:"effe8b72"},show:function(){var d=this.get("collectorId");window.ATL_JQ_PAGE_PROPS=window.ATL_JQ_PAGE_PROPS||{};window.ATL_JQ_PAGE_PROPS[d]={fieldValues:{summary:this.get("summary")},triggerFunction:function(e){a.defer(function(){e()})}};b.getScript(this._collectorUrlFor(d))},_collectorUrlFor:function(d){return"https://jira.atlassian.com/s/d41d8cd98f00b204e9800998ecf8427e/en_UK-7m3tmj-1988229788/6307/131/1.4.8/_/download/batch/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector.js?collectorId="+d}}))});
}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/review/devstatus-panel-review.soy' */
// This file was automatically generated from devstatus-panel-review.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.Review.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.Review == 'undefined') { JIRA.Templates.DevStatus.Review = {}; }


JIRA.Templates.DevStatus.Review.summaryPanel = function(opt_data, opt_ignored) {
  return '<dl><dt>' + JIRA.Templates.DevStatus.transitionContainer({content: '<div class="summary-content"><a class="summary" href="#" title="' + soy.$$escapeHtml(AJS.format('\u6709\u5173\u7684\u5ba1\u67e5{0}',opt_data.issueKey)) + '">' + JIRA.Templates.FusionWidget.Common.reviewsText(opt_data) + '</a> ' + JIRA.Templates.FusionWidget.Common.reviewsStateViewIssue(opt_data) + '</div>'}) + '</dt>' + JIRA.Templates.DevStatus.Review.reviewDate(soy.$$augmentMap(opt_data, {showUpdatedText: true})) + '</dl>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Review.summaryPanel.soyTemplateName = 'JIRA.Templates.DevStatus.Review.summaryPanel';
}


JIRA.Templates.DevStatus.Review.reviewDate = function(opt_data, opt_ignored) {
  return '' + ((opt_data.dueDate && ! opt_data.completed) ? JIRA.Templates.DevStatus.lastUpdated({text: '\u5230\u671f\u65e5', lastUpdated: opt_data.dueDate, extraClass: opt_data.overDue ? 'overdue' : undefined, datetimeFormat: 'fullAge'}) : (opt_data.lastUpdated) ? JIRA.Templates.DevStatus.lastUpdated({text: opt_data.showUpdatedText ? '\u5df2\u66f4\u65b0' : '', lastUpdated: opt_data.lastUpdated, datetimeFormat: 'fullAge'}) : '');
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Review.reviewDate.soyTemplateName = 'JIRA.Templates.DevStatus.Review.reviewDate';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/pullrequest/devstatus-panel-pullrequest.soy' */
// This file was automatically generated from devstatus-panel-pullrequest.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.PullRequest.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.PullRequest == 'undefined') { JIRA.Templates.DevStatus.PullRequest = {}; }


JIRA.Templates.DevStatus.PullRequest.summaryPanel = function(opt_data, opt_ignored) {
  return '<dl><dt>' + JIRA.Templates.DevStatus.transitionContainer({content: '<div class="summary-content"><a class="summary" href="#" title="' + soy.$$escapeHtml(AJS.format('\u62c9\u51fa\u8bf7\u6c42\u76f8\u5173\u7684\u5230{0}',opt_data.issueKey)) + '">' + JIRA.Templates.FusionWidget.Common.pullRequestsText(opt_data) + '</a> ' + JIRA.Templates.FusionWidget.Common.pullRequestsStateViewIssue(opt_data) + '</div>'}) + '</dt>' + JIRA.Templates.DevStatus.lastUpdated({text: '\u5df2\u66f4\u65b0', lastUpdated: opt_data.lastUpdated, datetimeFormat: 'fullAge'}) + '</dl>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.PullRequest.summaryPanel.soyTemplateName = 'JIRA.Templates.DevStatus.PullRequest.summaryPanel';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/devstatus-panel.soy' */
// This file was automatically generated from devstatus-panel.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }


JIRA.Templates.DevStatus.connectionProblemAsInfoWithoutIcon = function(opt_data, opt_ignored) {
  return '' + aui.message.info({content: '' + JIRA.Templates.DevStatus.connectionAndConfigErrorsMessages({errorInstances: opt_data.instances, notConfiguredInstances: opt_data.notConfiguredInstances, showContactAdminForm: opt_data.showContactAdminForm})});
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionProblemAsInfoWithoutIcon.soyTemplateName = 'JIRA.Templates.DevStatus.connectionProblemAsInfoWithoutIcon';
}


JIRA.Templates.DevStatus.connectionProblemAsWarning = function(opt_data, opt_ignored) {
  return '' + aui.message.warning({content: '' + JIRA.Templates.DevStatus.connectionAndConfigErrorsMessages({errorInstances: opt_data.instances, notConfiguredInstances: opt_data.notConfiguredInstances, showContactAdminForm: opt_data.showContactAdminForm})});
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionProblemAsWarning.soyTemplateName = 'JIRA.Templates.DevStatus.connectionProblemAsWarning';
}


JIRA.Templates.DevStatus.connectionAndConfigErrorsMessages = function(opt_data, opt_ignored) {
  return '' + (((! opt_data.errorInstances || opt_data.errorInstances.length == 0) && (! opt_data.notConfiguredInstances || opt_data.notConfiguredInstances.length == 0)) ? '<p class="connection-error">' + soy.$$filterNoAutoescape('Jira \u662f\u96be\u4ee5\u63a5\u89e6\u5230\u7684\u5e94\u7528\u7a0b\u5e8f\u7684\u4f9b\u5e94\u7684\u53d1\u5c55\u72b6\u6001\u7684\u4fe1\u606f\u3002') + ' ' + JIRA.Templates.DevStatus.contactAdmin(opt_data) + '</p>' : ((opt_data.errorInstances.length > 0) ? '<p class="connection-error">' + JIRA.Templates.DevStatus.connectionProblemMultipleInstances({instances: opt_data.errorInstances}) + ' ' + JIRA.Templates.DevStatus.contactAdmin(opt_data) + '</p>' : '') + ((opt_data.notConfiguredInstances && opt_data.notConfiguredInstances.length > 0) ? '<p class="config-errors">' + JIRA.Templates.DevStatus.configProblem(opt_data) + '</p>' : ''));
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionAndConfigErrorsMessages.soyTemplateName = 'JIRA.Templates.DevStatus.connectionAndConfigErrorsMessages';
}


JIRA.Templates.DevStatus.contactAdmin = function(opt_data, opt_ignored) {
  opt_data = opt_data || {};
  return '' + soy.$$filterNoAutoescape(AJS.format('\u5982\u679c\u8fd9\u79cd\u60c5\u51b5\u6301\u7eed\uff0c\u8bf7\u8054\u7cfb {0}Jira \u7ba1\u7406\u5458{1}\u3002',opt_data.showContactAdminForm ? '<a id="contact-admin" href="' + "" + '/secure/ContactAdministrators!default.jspa">' : '',opt_data.showContactAdminForm ? '</a>' : ''));
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.contactAdmin.soyTemplateName = 'JIRA.Templates.DevStatus.contactAdmin';
}


JIRA.Templates.DevStatus.connectionProblem = function(opt_data, opt_ignored) {
  var output = '';
  if (opt_data.adminError) {
    output += JIRA.Templates.DevStatus.connectionProblemAdminError(opt_data);
  } else {
    output += '<p class="connection-error">' + ((! opt_data.instances || opt_data.instances.length == 0) ? soy.$$filterNoAutoescape('Jira \u662f\u96be\u4ee5\u63a5\u89e6\u5230\u7684\u5e94\u7528\u7a0b\u5e8f\u7684\u4f9b\u5e94\u7684\u53d1\u5c55\u72b6\u6001\u7684\u4fe1\u606f\u3002') : JIRA.Templates.DevStatus.connectionProblemMultipleInstances(opt_data)) + ' ';
    var contactAdminAnchor__soy57 = '<a id="contact-admin" href="' + soy.$$escapeHtml("") + '/secure/ContactAdministrators!default.jspa">';
    output += soy.$$filterNoAutoescape(AJS.format('\u5982\u679c\u8fd9\u79cd\u60c5\u51b5\u6301\u7eed\uff0c\u8bf7\u8054\u7cfb {0}Jira \u7ba1\u7406\u5458{1}\u3002',opt_data.showContactAdminForm ? contactAdminAnchor__soy57 : '',opt_data.showContactAdminForm ? '</a>' : '')) + '</p>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionProblem.soyTemplateName = 'JIRA.Templates.DevStatus.connectionProblem';
}


JIRA.Templates.DevStatus.connectionProblemAdminError = function(opt_data, opt_ignored) {
  var output = '<p class="connection-error admin-error">' + soy.$$escapeHtml(opt_data.adminError.message) + '</p><p class="configure-applinks-link"><a href="' + soy.$$escapeHtml("") + '/plugins/servlet/applinks/listApplicationLinks">' + soy.$$escapeHtml('\u914d\u7f6e\u5e94\u7528\u7a0b\u5e8f\u94fe\u63a5') + '</a></p>';
  if (opt_data.adminError.details) {
    output += '<p class="connection-error"><a id="replace-text-trigger" data-replace-text="' + soy.$$escapeHtml('\u9690\u85cf\u9519\u8bef\u8be6\u60c5') + '" class="aui-expander-trigger" aria-controls="expander-with-replace-text-content">' + soy.$$escapeHtml('\u663e\u793a\u8be6\u7ec6\u9519\u8bef\u4fe1\u606f') + '</a></p><div id="expander-with-replace-text-content" class="aui-expander-content admin-error-details"><ul>';
    var detailList79 = opt_data.adminError.details;
    var detailListLen79 = detailList79.length;
    for (var detailIndex79 = 0; detailIndex79 < detailListLen79; detailIndex79++) {
      var detailData79 = detailList79[detailIndex79];
      output += '<li>' + soy.$$escapeHtml(detailData79) + '</li>';
    }
    output += '</ul></div>';
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionProblemAdminError.soyTemplateName = 'JIRA.Templates.DevStatus.connectionProblemAdminError';
}


JIRA.Templates.DevStatus.configProblem = function(opt_data, opt_ignored) {
  var output = '';
  var servers__soy86 = opt_data.notConfiguredInstances;
  output += '<span class="description">';
  var server0__soy88 = '' + JIRA.Templates.DevStatus.connectionProblemSingleInstance({instance: servers__soy86[0]});
  if (servers__soy86.length == 1) {
    output += soy.$$filterNoAutoescape(AJS.format('\u65e0\u6cd5\u4ece {0} \u8bfb\u53d6\u6570\u636e\u3002',server0__soy88));
  } else {
    var server1__soy96 = '' + JIRA.Templates.DevStatus.connectionProblemSingleInstance({instance: servers__soy86[1]});
    if (servers__soy86.length == 2) {
      output += soy.$$filterNoAutoescape(AJS.format('\u65e0\u6cd5\u4ece {0} \u548c {1} \u8bfb\u53d6\u6570\u636e\u3002',server0__soy88,server1__soy96));
    } else {
      var server2__soy104 = '' + JIRA.Templates.DevStatus.connectionProblemSingleInstance({instance: servers__soy86[2]});
      output += (servers__soy86.length == 3) ? soy.$$filterNoAutoescape(AJS.format('\u65e0\u6cd5\u4ece {0}, {1} \u548c {2} \u8bfb\u53d6\u6570\u636e\u3002',server0__soy88,server1__soy96,server2__soy104)) : soy.$$filterNoAutoescape(AJS.format('\u65e0\u6cd5\u8bfb\u53d6\u7684\u6570\u636e\u4ece{0}\u3001{1}\u3001{2}\u548c{3}\u5176\u4ed6{3,choice,1#application|1\x3capplications}\u3002',server0__soy88,server1__soy96,server2__soy104,servers__soy86.length - 3));
    }
  }
  output += '</span> <span class="hints">';
  var helpUrl__soy115 = ({"alt":"Get help!","title":"Jira Application Development panel displays error - Couldn''t read data","url":"https://confluence.atlassian.com/display/JIRAKB/JIRA+Application+Development+panel+displays+error+-+Couldn%27t+read+data"});
  output += soy.$$filterNoAutoescape(AJS.format('\u4e86\u89e3\u66f4\u591a\u6709\u5173 {0}\u8fd9\u4e2a\u95ee\u9898{1} \u7684\u4fe1\u606f\uff0c\u6216\u8005\u524d\u5f80 {2}\u5e94\u7528\u7a0b\u5e8f\u94fe\u63a5{3} \u4fee\u590d\u5b83\u3002','<a href="' + helpUrl__soy115.url + '" target="_blank">','</a>','<a href="' + "" + '/plugins/servlet/applinks/listApplicationLinks">','</a>')) + '</span>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.configProblem.soyTemplateName = 'JIRA.Templates.DevStatus.configProblem';
}


JIRA.Templates.DevStatus.connectionProblemMultipleInstances = function(opt_data, opt_ignored) {
  var output = '';
  var instance0__soy120 = '' + JIRA.Templates.DevStatus.connectionProblemSingleInstance({instance: opt_data.instances[0]});
  if (opt_data.instances.length == 1) {
    output += soy.$$filterNoAutoescape(AJS.format('Jira \u662f\u9047\u5230\u56f0\u96be\u8054\u7cfb{0}\u3002',instance0__soy120));
  } else {
    var instance1__soy128 = '' + JIRA.Templates.DevStatus.connectionProblemSingleInstance({instance: opt_data.instances[1]});
    if (opt_data.instances.length == 2) {
      output += soy.$$filterNoAutoescape(AJS.format('Jira \u662f\u9047\u5230\u56f0\u96be\u8054\u7cfb{0}\u548c{1}\u3002',instance0__soy120,instance1__soy128));
    } else {
      var instance2__soy136 = '' + JIRA.Templates.DevStatus.connectionProblemSingleInstance({instance: opt_data.instances[2]});
      output += soy.$$filterNoAutoescape(AJS.format('Jira \u662f\u9047\u5230\u56f0\u96be\u8054\u7cfb{0}\u3001{1}\u3001{3,choice,0#and {2}|1#{2}, and another application|1\x3c{2}, and {3} more applications}\u3002',instance0__soy120,instance1__soy128,instance2__soy136,opt_data.instances.length - 3));
    }
  }
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionProblemMultipleInstances.soyTemplateName = 'JIRA.Templates.DevStatus.connectionProblemMultipleInstances';
}


JIRA.Templates.DevStatus.connectionProblemSingleInstance = function(opt_data, opt_ignored) {
  return '<span class="instance" data-name="' + soy.$$escapeHtml(opt_data.instance.name) + '"><a href="' + soy.$$escapeHtml(opt_data.instance.baseUrl) + '" title="' + soy.$$escapeHtml(opt_data.instance.name) + '">' + soy.$$escapeHtml(opt_data.instance.name) + '</a></span>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.connectionProblemSingleInstance.soyTemplateName = 'JIRA.Templates.DevStatus.connectionProblemSingleInstance';
}


JIRA.Templates.DevStatus.labsOnOff = function(opt_data, opt_ignored) {
  return '' + ((! opt_data.isOptedInByAdmin && ! opt_data.isOptedIn) ? '<div class="on-off-panel aui-message closeable"><h2 class="title">' + soy.$$escapeHtml('Jira\u5b9e\u9a8c\u5ba4') + '</h2><p>' + soy.$$escapeHtml('\u65b0\u7684\u65b9\u5f0f\u6765\u67e5\u770b\u60a8\u7684\u6709\u5173\u627f\u8bfa\u548c\u62c9\u51fa\u8bf7\u6c42\u53d1\u751f\u7684\u3002') + '</p><span class="aui-icon icon-close labs-close" role="button" tabindex="0" title="' + soy.$$escapeHtml('\u5173\u95ed Jira \u5b9e\u9a8c\u5ba4\u9762\u677f') + '"></span><div class="aui-group"><div class="aui-item"><button class="toggle-labs aui-button" title="' + soy.$$escapeHtml('\u542f\u7528 Jira Labs \u5f00\u53d1\u9762\u677f') + '">' + soy.$$escapeHtml('\u5f00\u542f\u5b9e\u9a8c\u5ba4\u529f\u80fd') + '</button></div></div>' : '<div class="on-off-panel opted-in">' + ((! opt_data.isOptedInByAdmin) ? ' | <span class="disable-labs"><a class="toggle-labs" href="#" title="' + soy.$$escapeHtml('\u7981\u7528 Jira \u5b9e\u9a8c\u5ba4\u7684\u53d1\u5c55\u9762\u677f') + '">' + soy.$$escapeHtml('\u7981\u7528\u5b9e\u9a8c') + '</span></span>' : '') + '</div>');
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.labsOnOff.soyTemplateName = 'JIRA.Templates.DevStatus.labsOnOff';
}


JIRA.Templates.DevStatus.tryLabs = function(opt_data, opt_ignored) {
  return '<span href="#" class="try-labs" title="' + soy.$$escapeHtml('\u663e\u793a\u4e86 Jira \u5b9e\u9a8c\u5ba4\u9762\u677f') + '">' + soy.$$escapeHtml('\u5c1d\u8bd5\u5b9e\u9a8c') + '</span>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.tryLabs.soyTemplateName = 'JIRA.Templates.DevStatus.tryLabs';
}


JIRA.Templates.DevStatus.lastUpdated = function(opt_data, opt_ignored) {
  return '' + ((opt_data.lastUpdated) ? '<dd class="sub-text' + ((opt_data.extraClass) ? ' ' + soy.$$escapeHtml(opt_data.extraClass) : '') + '">' + JIRA.Templates.DevStatus.transitionContainer({content: '<div>' + soy.$$escapeHtml(opt_data.text) + ' <time class="livestamp date user-tz allow-future" ' + ((opt_data.datetimeFormat) ? 'data-datetime-format="' + soy.$$escapeHtml(opt_data.datetimeFormat) + '" ' : '') + 'datetime="' + soy.$$escapeHtml(opt_data.lastUpdated) + '">$' + soy.$$escapeHtml(opt_data.lastUpdated) + '</time></div>'}) + '</dd>' : '');
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.lastUpdated.soyTemplateName = 'JIRA.Templates.DevStatus.lastUpdated';
}


JIRA.Templates.DevStatus.transitionContainer = function(opt_data, opt_ignored) {
  return '<div class="rolling-container sliding-container"><div class="rolling-content">' + soy.$$filterNoAutoescape(opt_data.content) + '</div></div>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.transitionContainer.soyTemplateName = 'JIRA.Templates.DevStatus.transitionContainer';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/devstatus-cta-tooltips.soy' */
// This file was automatically generated from devstatus-cta-tooltips.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.Tooltip.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.Tooltip == 'undefined') { JIRA.Templates.DevStatus.Tooltip = {}; }


JIRA.Templates.DevStatus.Tooltip.createBranch = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.DevStatus.Tooltip.tooltipLayout({extraClasses: 'create-branch', content: '<span class="branch-illustration"></span><div class="branch-text"><p>' + soy.$$escapeHtml('\u5728\u6e90\u4ee3\u7801\u50a8\u5b58\u5e93\u4e2d\u4e3a\u6b64\u95ee\u9898\u521b\u5efa\u4e00\u4e2a\u5206\u652f') + '.<br /> <a class="cta-learn-more-link" href="https://www.atlassian.com/git/workflows" target="_blank">' + soy.$$escapeHtml('\u4e86\u89e3\u66f4\u591a\u6709\u5173\u5206\u652f\u7b56\u7565') + '.</a></p></div>'});
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Tooltip.createBranch.soyTemplateName = 'JIRA.Templates.DevStatus.Tooltip.createBranch';
}


JIRA.Templates.DevStatus.Tooltip.automaticTransitions = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.DevStatus.Tooltip.tooltipLayout({extraClasses: 'dev-summary-automatic-transitions', content: '<p>' + soy.$$escapeHtml('\u73b0\u5728\u7684 Jira \u53ef\u4ee5\u81ea\u52a8\u66f4\u65b0\u60a8\u7684\u95ee\u9898\u72b6\u6001\u5f53\u62c9\u8bf7\u6c42\u548c\u5176\u4ed6\u884c\u52a8\u7684\u53d1\u751f\u3002') + '</p><p><a href="' + soy.$$escapeHtml(opt_data.aitHelpLinkUrl.url) + '" id="trigger-docs-link" target="_blank" title="' + soy.$$escapeHtml('\u4e86\u89e3\u6709\u5173\u81ea\u52a8\u5316\u60a8\u7684\u5de5\u4f5c\u6d41\u7a0b\u3002') + '">' + soy.$$escapeHtml('\u4e86\u89e3\u6709\u5173\u81ea\u52a8\u5316\u60a8\u7684\u5de5\u4f5c\u6d41\u7a0b\u3002') + '</a></p>'});
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Tooltip.automaticTransitions.soyTemplateName = 'JIRA.Templates.DevStatus.Tooltip.automaticTransitions';
}


JIRA.Templates.DevStatus.Tooltip.tooltipLayout = function(opt_data, opt_ignored) {
  return '<div class="cta-tooltip' + ((opt_data.extraClasses) ? ' ' + soy.$$escapeHtml(opt_data.extraClasses) : '') + '">' + soy.$$filterNoAutoescape(opt_data.content) + '</div>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Tooltip.tooltipLayout.soyTemplateName = 'JIRA.Templates.DevStatus.Tooltip.tooltipLayout';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/deployment/devstatus-panel-deployment.soy' */
// This file was automatically generated from devstatus-panel-deployment.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.Deployment.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.Deployment == 'undefined') { JIRA.Templates.DevStatus.Deployment = {}; }


JIRA.Templates.DevStatus.Deployment.summaryPanel = function(opt_data, opt_ignored) {
  return '<dl><dd>' + JIRA.Templates.DevStatus.transitionContainer({content: '<div class="summary-content" title="' + soy.$$escapeHtml(AJS.format('\u90e8\u7f72\u4e0e{0}',opt_data.issueKey)) + '">' + ((opt_data.topEnvironments.length) ? JIRA.Templates.FusionWidget.Common.deployments({anchorStart: '<a class="summary">', anchorEnd: '</a>', showProjects: opt_data.showProjects, successfulCount: opt_data.successfulCount, topEnvironments: opt_data.topEnvironments}) : soy.$$escapeHtml('\u65e0\u90e8\u7f72\u5305\u542b\u6700\u65b0\u7684\u627f\u8bfa')) + '</div>'}) + '</dd></dl>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Deployment.summaryPanel.soyTemplateName = 'JIRA.Templates.DevStatus.Deployment.summaryPanel';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/commit/devstatus-panel-commit.soy' */
// This file was automatically generated from devstatus-panel-commit.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.Commit.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.Commit == 'undefined') { JIRA.Templates.DevStatus.Commit = {}; }


JIRA.Templates.DevStatus.Commit.summaryPanel = function(opt_data, opt_ignored) {
  return '<dl><dt>' + JIRA.Templates.DevStatus.transitionContainer({content: '<div class="summary-content"><a class="summary" href="#" title="' + soy.$$escapeHtml(AJS.format('\u5373\u5c5e\u4e0e{0}',opt_data.issueKey)) + '">' + JIRA.Templates.FusionWidget.Common.commits(opt_data) + '</a></div>'}) + '</dt>' + JIRA.Templates.DevStatus.lastUpdated({text: '\u6700\u540e', lastUpdated: opt_data.lastUpdated, datetimeFormat: 'fullAge'}) + '</dl>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Commit.summaryPanel.soyTemplateName = 'JIRA.Templates.DevStatus.Commit.summaryPanel';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/build/devstatus-panel-build.soy' */
// This file was automatically generated from devstatus-panel-build.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.Build.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.Build == 'undefined') { JIRA.Templates.DevStatus.Build = {}; }


JIRA.Templates.DevStatus.Build.summaryPanel = function(opt_data, opt_ignored) {
  var output = '<dl><dt>';
  var param4 = '<div class="summary-content"><a class="summary" href="#" title="' + soy.$$escapeHtml(AJS.format('\u6784\u5efa\u4e0e{0}',opt_data.issueKey)) + '">';
  var countText__soy8 = '' + JIRA.Templates.FusionWidget.Common.buildsText(opt_data);
  param4 += soy.$$filterNoAutoescape(countText__soy8) + '</a> ' + JIRA.Templates.DevStatus.Build.statusIcon(opt_data) + '</div>';
  output += JIRA.Templates.DevStatus.transitionContainer({content: param4});
  output += '</dt>' + JIRA.Templates.DevStatus.lastUpdated({text: '\u6700\u540e', lastUpdated: opt_data.lastUpdated, datetimeFormat: 'fullAge'}) + '</dl>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Build.summaryPanel.soyTemplateName = 'JIRA.Templates.DevStatus.Build.summaryPanel';
}


JIRA.Templates.DevStatus.Build.statusIcon = function(opt_data, opt_ignored) {
  var output = '';
  var iconClass__soy27 = '' + JIRA.Templates.FusionWidget.Common.buildsIconClass(opt_data);
  output += '<span class="aui-icon aui-icon-small ' + soy.$$escapeHtml(iconClass__soy27) + '">' + soy.$$escapeHtml(opt_data.iconText) + '</span>';
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Build.statusIcon.soyTemplateName = 'JIRA.Templates.DevStatus.Build.statusIcon';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/viewissue/branch/devstatus-panel-branch.soy' */
// This file was automatically generated from devstatus-panel-branch.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.DevStatus.Branches.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.DevStatus == 'undefined') { JIRA.Templates.DevStatus = {}; }
if (typeof JIRA.Templates.DevStatus.Branches == 'undefined') { JIRA.Templates.DevStatus.Branches = {}; }


JIRA.Templates.DevStatus.Branches.summaryPanel = function(opt_data, opt_ignored) {
  return '<dl><dt>' + JIRA.Templates.DevStatus.transitionContainer({content: '<div class="summary-content"><a class="summary" href="#" title="' + soy.$$escapeHtml(AJS.format('\u5206\u652f\u673a\u6784\u4e0e{0}',opt_data.issueKey)) + '">' + JIRA.Templates.FusionWidget.Common.branches(opt_data) + '</a></div>'}) + '</dt>' + JIRA.Templates.DevStatus.lastUpdated({text: '\u5df2\u66f4\u65b0', lastUpdated: opt_data.lastUpdated, datetimeFormat: 'fullAge'}) + '</dl>';
};
if (goog.DEBUG) {
  JIRA.Templates.DevStatus.Branches.summaryPanel.soyTemplateName = 'JIRA.Templates.DevStatus.Branches.summaryPanel';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'templates/fusion-widget/fusion-widget-common.soy' */
// This file was automatically generated from fusion-widget-common.soy.
// Please don't edit this file by hand.

/**
 * @fileoverview Templates in namespace JIRA.Templates.FusionWidget.Common.
 */

if (typeof JIRA == 'undefined') { var JIRA = {}; }
if (typeof JIRA.Templates == 'undefined') { JIRA.Templates = {}; }
if (typeof JIRA.Templates.FusionWidget == 'undefined') { JIRA.Templates.FusionWidget = {}; }
if (typeof JIRA.Templates.FusionWidget.Common == 'undefined') { JIRA.Templates.FusionWidget.Common = {}; }


JIRA.Templates.FusionWidget.Common.branches = function(opt_data, opt_ignored) {
  return '' + soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#commit|1\x3ccommits}','<span class="count">',opt_data.count,'</span>'));
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.branches.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.branches';
}


JIRA.Templates.FusionWidget.Common.buildsText = function(opt_data, opt_ignored) {
  var output = '';
  var tagStart__soy6 = '<span class="count">';
  var tagEnd__soy7 = '</span>';
  output += (opt_data.successfulBuildCount > 0) ? (opt_data.failedBuildCount > 0) ? soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#build|1\x3cbuilds} \u5df2\u5931\u8d25',tagStart__soy6,opt_data.failedBuildCount,tagEnd__soy7)) : (opt_data.unknownBuildCount > 0) ? soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#build succeeded|1\x3cbuilds successful}',tagStart__soy6,opt_data.successfulBuildCount,tagEnd__soy7)) : soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#build succeeded|1\x3cbuilds successful}',tagStart__soy6,opt_data.count,tagEnd__soy7)) : (opt_data.failedBuildCount > 0) ? (opt_data.unknownBuildCount > 0) ? soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#build|1\x3cbuilds} \u5df2\u5931\u8d25',tagStart__soy6,opt_data.failedBuildCount,tagEnd__soy7)) : soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#build|1\x3cbuilds} \u5df2\u5931\u8d25',tagStart__soy6,opt_data.count,tagEnd__soy7)) : soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#build|1\x3cbuilds}\u672a\u5b8c\u6210',tagStart__soy6,opt_data.count,tagEnd__soy7));
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.buildsText.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.buildsText';
}


JIRA.Templates.FusionWidget.Common.buildsIconClass = function(opt_data, opt_ignored) {
  return '' + ((opt_data.failedBuildCount > 0) ? 'aui-iconfont-error' : (opt_data.successfulBuildCount > 0) ? 'aui-iconfont-approve' : (opt_data.unknownBuildCount > 0) ? 'aui-iconfont-devtools-task-cancelled' : '');
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.buildsIconClass.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.buildsIconClass';
}


JIRA.Templates.FusionWidget.Common.commits = function(opt_data, opt_ignored) {
  return '' + soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#commit|1\x3ccommits}','<span class="count">',opt_data.count,'</span>'));
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.commits.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.commits';
}


JIRA.Templates.FusionWidget.Common.deployments = function(opt_data, opt_ignored) {
  var output = '';
  var instanceOne__soy43 = '' + JIRA.Templates.FusionWidget.Common.deploymentsInstance({environment: opt_data.topEnvironments[0], showProjects: opt_data.showProjects});
  var instanceTwo__soy47 = '' + JIRA.Templates.FusionWidget.Common.deploymentsInstance({environment: opt_data.topEnvironments[1], showProjects: opt_data.showProjects});
  var instanceThree__soy51 = '' + JIRA.Templates.FusionWidget.Common.deploymentsInstance({environment: opt_data.topEnvironments[2], showProjects: opt_data.showProjects});
  output += soy.$$filterNoAutoescape(AJS.format('{5}\u5df2\u90e8\u7f72{6}\u5230{0,choice,1#{1}|2#{1} and {2}|3#{1}, {2} and {3}|3\x3c{1}, {2}, {3}, and {4} more}',opt_data.successfulCount,instanceOne__soy43,instanceTwo__soy47,instanceThree__soy51,opt_data.successfulCount - 3,opt_data.anchorStart,opt_data.anchorEnd));
  return output;
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.deployments.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.deployments';
}


JIRA.Templates.FusionWidget.Common.deploymentsInstance = function(opt_data, opt_ignored) {
  return '' + ((opt_data.environment) ? '<span class="environment" ' + ((opt_data.showProjects) ? 'title="' + soy.$$escapeHtml(AJS.format('\u9879\u76ee\uff1a{0}',opt_data.environment.project)) + '"' : '') + '>' + soy.$$escapeHtml(opt_data.environment.title) + '</span>' : '');
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.deploymentsInstance.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.deploymentsInstance';
}


JIRA.Templates.FusionWidget.Common.pullRequestsText = function(opt_data, opt_ignored) {
  return '' + soy.$$filterNoAutoescape(AJS.format('{0}{1}{2}{1,choice,1#pull request|1\x3cpull requests}','<span class="count">',opt_data.stateCount,'</span>'));
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.pullRequestsText.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.pullRequestsText';
}


JIRA.Templates.FusionWidget.Common.reviewsText = function(opt_data, opt_ignored) {
  return '' + soy.$$filterNoAutoescape(AJS.format('{0}{1}{2} {1,choice,1#review|1\x3creviews}','<span class="count">',opt_data.stateCount,'</span>'));
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.reviewsText.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.reviewsText';
}


JIRA.Templates.FusionWidget.Common.reviewsState = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.FusionWidget.Common.stateLozenge({isSubtle: opt_data.isSubtle, lozengeClass: opt_data.stateClasses[opt_data.state], text: opt_data.stateText[opt_data.state], classes: opt_data.classes, tooltip: opt_data.tooltip, tooltipClass: opt_data.tooltipClass, tooltipText: opt_data.tooltipText});
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.reviewsState.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.reviewsState';
}


JIRA.Templates.FusionWidget.Common.pullRequestsStateViewIssue = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.FusionWidget.Common.reviewsState({isSubtle: true, state: opt_data.state, stateClasses: {OPEN: 'aui-lozenge-current', MERGED: 'aui-lozenge-success', DECLINED: 'aui-lozenge-error'}, stateText: {OPEN: '\u5f00\u653e', MERGED: '\u5408\u5e76\u3002', DECLINED: '\u62d2\u7edd'}, classes: 'pullrequest-state'});
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.pullRequestsStateViewIssue.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.pullRequestsStateViewIssue';
}


JIRA.Templates.FusionWidget.Common.pullRequestsStateReleaseReport = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.FusionWidget.Common.reviewsState({isSubtle: true, state: opt_data.state, stateClasses: {OPEN: 'aui-lozenge-current', MERGED: 'aui-lozenge-success', DECLINED: 'aui-lozenge-error'}, stateText: {OPEN: '\u5f85\u5ba1\u67e5', MERGED: '\u5408\u5e76\u3002', DECLINED: '\u62d2\u7edd'}, tooltip: true, tooltipClass: 'fusion-widget-tooltip', tooltipText: opt_data.tooltipText});
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.pullRequestsStateReleaseReport.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.pullRequestsStateReleaseReport';
}


JIRA.Templates.FusionWidget.Common.reviewsStateViewIssue = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.FusionWidget.Common.reviewsState({isSubtle: true, state: opt_data.state, stateClasses: {REVIEW: 'aui-lozenge-complete', CLOSED: 'aui-lozenge-success', DRAFT: 'aui-lozenge-complete', DEAD: 'aui-lozenge-error', REJECTED: 'aui-lozenge-error', UNKNOWN: 'aui-lozenge-error', APPROVAL: 'aui-lozenge-current', SUMMARIZE: 'aui-lozenge-complete'}, stateText: {REVIEW: '\u5f00\u653e', CLOSED: '\u5df2\u5173\u95ed', DRAFT: '\u8349\u7a3f', DEAD: '\u5df2\u653e\u5f03', REJECTED: '\u88ab\u62d2\u7edd', UNKNOWN: '\u672a\u77e5', APPROVAL: '\u6279\u51c6', SUMMARIZE: '\u6982\u89c8'}});
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.reviewsStateViewIssue.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.reviewsStateViewIssue';
}


JIRA.Templates.FusionWidget.Common.reviewsStateReleaseReport = function(opt_data, opt_ignored) {
  return '' + JIRA.Templates.FusionWidget.Common.reviewsState({isSubtle: true, state: opt_data.state, stateClasses: {REVIEW: 'aui-lozenge-complete', CLOSED: 'aui-lozenge-success', DRAFT: 'aui-lozenge-complete', DEAD: 'aui-lozenge-error', REJECTED: 'aui-lozenge-error', UNKNOWN: 'aui-lozenge-error', APPROVAL: 'aui-lozenge-complete', SUMMARIZE: 'aui-lozenge-complete'}, stateText: {REVIEW: '\u5f85\u5ba1\u67e5', CLOSED: '\u5df2\u5173\u95ed', DRAFT: '\u8349\u7a3f', DEAD: '\u5df2\u653e\u5f03', REJECTED: '\u88ab\u62d2\u7edd', UNKNOWN: '\u672a\u77e5', APPROVAL: '\u5f85\u5ba1\u67e5', SUMMARIZE: '\u5f85\u5ba1\u67e5'}, tooltip: true, tooltipClass: 'fusion-widget-tooltip', tooltipText: opt_data.tooltipText});
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.reviewsStateReleaseReport.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.reviewsStateReleaseReport';
}


JIRA.Templates.FusionWidget.Common.stateLozenge = function(opt_data, opt_ignored) {
  return '<span class="aui-lozenge aui-lozenge-overflow ' + ((opt_data.isSubtle) ? 'aui-lozenge-subtle' : '') + ' ' + soy.$$escapeHtml(opt_data.lozengeClass) + ' ' + ((opt_data.tooltip) ? soy.$$escapeHtml(opt_data.tooltipClass) : '') + ' ' + ((opt_data.classes) ? soy.$$escapeHtml(opt_data.classes) : '') + '"' + ((opt_data.tooltip) ? 'data-tooltip="' + soy.$$escapeHtml(opt_data.tooltipText) + '"' : '') + '>' + soy.$$escapeHtml(opt_data.text) + '</span>';
};
if (goog.DEBUG) {
  JIRA.Templates.FusionWidget.Common.stateLozenge.soyTemplateName = 'JIRA.Templates.FusionWidget.Common.stateLozenge';
}

}catch(e){WRMCB(e)};
;
try {
/* module-key = 'com.atlassian.jira.plugins.jira-development-integration-plugin:0', location = 'dist/0.075180f0a8e207fec1f8.js' */
jiraDevelopmentIntegrationPluginJsonp([0],{"./js/analytics/AnalyticsModel.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')")],t=function(e,i){return e.Model.extend({TO_ANALYTIC_KEY_OVERRIDE:{repository:"commit"},properties:["issue","summary"],initialize:function(e){this.devStatusContainer=e.devStatusContainer,this.devStatusData=e.devStatusData,this._getAllAnalyticData()},getIssue:function(){return this.get("issue")},getSummary:function(){return this.get("summary")},_getAllAnalyticData:function(){this.set("issue",this._getAnalyticIssueData()),this._initListenerToData()},_getAnalyticIssueData:function(){return{isAssignee:this.devStatusContainer.data("is-assignee"),isAssignable:this.devStatusContainer.data("is-assignable"),issueStatus:this.devStatusContainer.data("issue-status"),issueType:this.devStatusContainer.data("issue-type")}},_getAnalyticSummaryData:function(e){var a=this,s={};return i.each(e.summary,function(e,i){s[a.TO_ANALYTIC_KEY_OVERRIDE[i]||i]=0<e.overall.count}),s},_initListenerToData:function(){var e=this;this.devStatusData.on("requestSuccess",function(){e.set("summary",e._getAnalyticSummaryData(e.devStatusData.get("aggregatedData")))})}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/analytics/devstatus-analytics.js":function(e,i,a){"use strict";var s,t,r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};s=[a("jira.webresources:jira-global/require('jira/ajs/dark-features')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("jira.webresources:jquery/require('jquery')"),a,i],t=function(e,i,s,t,n){function l(){return!e.isEnabled("jira.plugin.devstatus.analytics.disabled")}var o=a("jira.webresources:jira-events/require('jira/util/events')"),d=a("jira.webresources:jira-events/require('jira/util/events/types')"),u=a("jira.webresources:jira-metadata/require('jira/util/data/meta')"),m=a("./js/util/analytics.js"),c=".edit-permission-link";n.Analytics={fireEvent:function(e,i){if(l()){m.sendEvent("devstatus."+e,i)}},fireAgileDevStatusLinkClicked:function(){return n.Analytics.fireEvent("panel.agile.icon.clicked")}},n.LabsAnalytics={fireLabsEvent:function(e){var i=e?"on":"off";return n.Analytics.fireEvent("labs.toggle."+i)}},n.SummaryAnalytics={fireSummaryShownEvent:function(e,a,s,t){var r=e?"assignee":"nonassignee",l=i.extend({},t);return n.Analytics.fireEvent("summary.shown."+r,l)},fireSummaryClickedEvent:function(e,i){var a=u.get("fusion-analytics-new-context-link"),t=".clicked";return a&&(u.set("fusion-analytics-new-context-link",void 0),t+=".newcontext"),n.Analytics.fireEvent(e+".summary"+t,s.extend({},i))},fireDetailTabClicked:function(e,i){return n.Analytics.fireEvent(i+".detail."+e+".tab.clicked")}},n.BuildsAnalytics={fireSummaryClickedEvent:function(e){return n.SummaryAnalytics.fireSummaryClickedEvent("builds",e)},fireDetailProjectClicked:function(){return n.Analytics.fireEvent("builds.detail.project.clicked")},fireDetailPlanClicked:function(){return n.Analytics.fireEvent("builds.detail.plan.clicked")},fireDetailBuildClicked:function(){return n.Analytics.fireEvent("builds.detail.build.clicked")}},n.DeploymentsAnalytics={fireSummaryClickedEvent:function(e){return n.SummaryAnalytics.fireSummaryClickedEvent("deployments",e)},fireDetailProjectClicked:function(){return n.Analytics.fireEvent("deployments.detail.project.clicked")},fireDetailEnvironmentClicked:function(){return n.Analytics.fireEvent("deployments.detail.environment.clicked")},fireDetailReleaseClicked:function(){return n.Analytics.fireEvent("deployments.detail.release.clicked")}},n.CommitsAnalytics={fireSummaryClickedEvent:function(e){return n.SummaryAnalytics.fireSummaryClickedEvent("commits",e)},fireDetailRepoClicked:function(e){return n.Analytics.fireEvent("commits.detail."+e+".repo.clicked")},fireDetailCommitClicked:function(e){return n.Analytics.fireEvent("commits.detail."+e+".commit.clicked")},fireDetailTabClicked:function(e){return n.SummaryAnalytics.fireDetailTabClicked(e,"commits")},fireDetailFileExpandedClicked:function(e){return n.Analytics.fireEvent("commits.detail."+e+".file.expanded.clicked")},fireDetailFilesExpandedClicked:function(e){return n.Analytics.fireEvent("commits.detail."+e+".files.expanded.clicked")},fireDetailFileClicked:function(e){return n.Analytics.fireEvent("commits.detail."+e+".file.clicked")},fireDetailReviewsShown:function(e){return n.Analytics.fireEvent("commits.detail."+e+".reviews.shown")},fireDetailReviewClicked:function(e){return n.Analytics.fireEvent("commits.detail."+e+".review.clicked")},fireDetailBranchesShown:function(e){return n.Analytics.fireEvent("commits.detail."+e+".branches.shown")},fireDetailCreateReviewClicked:function(e,i){return n.Analytics.fireEvent("commits.detail."+e+".review.create"+(i?".all":""))}},n.ReviewsAnalytics={fireSummaryClickedEvent:function(e){return n.SummaryAnalytics.fireSummaryClickedEvent("reviews",e)},fireDetailReviewClicked:function(){return n.Analytics.fireEvent("reviews.detail.review.clicked")}},n.BranchesAnalytics={fireSummaryClickedEvent:function(e){return n.SummaryAnalytics.fireSummaryClickedEvent("branches",e)},_fireDetailClickedEvent:function(e,i){return n.Analytics.fireEvent("branches.detail."+e+"."+i+".clicked")},_fireDetailClickedEventAssignable:function(e,i,a,s){"undefined"!=typeof a&&"undefined"!=typeof s&&(a?e+=".assignee":(e+=".nonassignee",e+=s?".assignable":".nonassignable")),this._fireDetailClickedEvent(i,e)},fireDetailRepoClicked:function(e){this._fireDetailClickedEvent(e,"repo")},fireDetailBranchClicked:function(e){this._fireDetailClickedEvent(e,"branch")},fireDetailPullRequestLozengeClick:function(e){this._fireDetailClickedEvent(e,"pullrequest.lozenge")},fireDetailReviewLozengeClick:function(e){this._fireDetailClickedEvent(e,"review.lozenge")},fireDetailTabClicked:function(e){return n.SummaryAnalytics.fireDetailTabClicked(e,"branches")},fireDetailCreatePullRequestClicked:function(e,i,a){this._fireDetailClickedEventAssignable("pullrequest.create",e,i,a)},fireDetailCreateReviewClicked:function(e,i,a){this._fireDetailClickedEventAssignable("review.create",e,i,a)}},n.PullRequestsAnalytics={fireSummaryClickedEvent:function(e){return n.SummaryAnalytics.fireSummaryClickedEvent("pullrequests",e)},fireDetailPullRequestClicked:function(e){return n.Analytics.fireEvent("pullrequests.detail."+e+".pullrequest.clicked")},fireDetailTabClicked:function(e){return n.SummaryAnalytics.fireDetailTabClicked(e,"pullrequests")}},n.LearnMoreAnalytics={fireLearnMoreEvent:function(e,i){return n.Analytics.fireEvent("learnmore."+e,i)}},n.CtaDialogAnalytics={issueDetails:{},fireCreateBranchEvent:function(e,i){return i="object"===("undefined"==typeof i?"undefined":r(i))?s.extend({},i):{},n.Analytics.fireEvent("createbranch."+e+"."+(this.issueDetails.isAssignee?"assignee":"nonassignee"),i)},_populateIssueDetails:function(e){this.issueDetails={status:""+e.data("issueStatus"),type:""+e.data("issueType"),isAssignee:e.data("isAssignee")}},_initHandler:function(i,e,a,t){if(a&&e.not(document).is(a)){var r=this;e.find(t).on("simpleClick",function(){var e=s(this);r._fireCreateBranchEvent(e)})}},_fireCreateBranchEvent:function(e){this._populateIssueDetails(e);var i="click",a=u.get("fusion-analytics-new-context-link");a&&(u.set("fusion-analytics-new-context-link",void 0),i+=".newcontext"),this.fireCreateBranchEvent(i)},initialize:function(a,t,e){if(l()){var r=this;o.bind(d.NEW_CONTENT_ADDED,i.bind(function(i,e){this._initHandler(i,e,a,t)},this)),o.bind("GH.DetailView.updated",i.bind(function(i){this._initHandler(i,s(a),a,t)},this)),s(t).on("simpleClick",function(){var e=s(this);r._fireCreateBranchEvent(e)}),s(document).on("click","#"+e+" .jira-dialog-content .target",function(){var e=s(this);r.fireCreateBranchEvent("dialog.click",{title:e.find(".title").attr("title"),applicationType:e.data("applicationType")})}),s(document).on("click","#"+e+" .jira-dialog-content button.cancel",function(){r.fireCreateBranchEvent("dialog.cancel")}),s(document).on("click",".cta-tooltip .cta-learn-more-link",function(){n.LearnMoreAnalytics.fireLearnMoreEvent("click.ctahelptipbranching")})}}},n.AdminAnalytics={fireAdminEvent:function(e,i){return n.Analytics.fireEvent("admin."+e,i)},initialize:function(){if(l()){var e=this,i=s("#project-config-panel-dev-status");i.find(c).on("click",function(){e.fireAdminEvent("editpermissionlink.config."+(0<s("ul.permissions-list li.devstatus-admin-permission-item").length?"withpermissions":"nopermissions"))}),i.find(".learn-more-link").on("click",function(){n.LearnMoreAnalytics.fireLearnMoreEvent("click.adminappvertisementlink")}),i.find("#project-config-applinks-connect").on("click",function(){e.fireAdminEvent("connect")}),i.find(".devstatus-admin-connect-button-option").on("click",function(){var i=s(this).data("key")||"unknown";e.fireAdminEvent("connect."+i)})}}},n.AdminSummaryAnalytics={initialize:function(){l()&&s("#project-config-webpanel-devstatus-admin-summary-panel").find(c).on("click",function(){n.AdminAnalytics.fireAdminEvent("editpermissionlink.summary.nopermissions")})}}}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/util/DateUtils.js":function(e,i,a){"use strict";var s,t;(function(){var r="LLL";s=[a("jira.webresources:jquery/require('jquery')"),a("jira.webresources:momentjs/require('jira/moment')")],t=function(e,i){return{addTooltip:function(a){var s=a.find("time.livestamp");s.livestamp(),s.each(function(){var a=e(this),s=a.attr("datetime");if(s){var t=isNaN(s)?i(s).utcOffset(s):i(parseInt(s,10));a.attr("title",t.format(r))}})},setFormat:function(e){r=e}}}.apply(i,s),!(void 0!==t&&(e.exports=t))})()},"./js/util/EventPublisher.js":function(e,i,a){"use strict";var s;s=function(){return{trigger:AJS.trigger}}.call(i,a,i,e),!(void 0!==s&&(e.exports=s))},"./js/util/Listeners.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("jira.webresources:jira-base-control/require('jira/lib/class')")],t=function(e,i){return i.extend({init:function(){this._listening=[]},startListening:function(e,i,a,s){this._listening.push({start:function(){return e.on(i,a,s),this},stop:function(){return e.off(i,a,s),this}}.start())},stopListening:function(){e.each(this._listening,function(e){e.stop()}),this._listening=[]}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/util/analytics.js":function(e,i,a){"use strict";var s,t;s=[a("./js/util/EventPublisher.js"),i],t=function(e,i){i.sendEvent=function(i){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};e.trigger("analyticsEvent",{name:i,data:a})}}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/DevStatusApp.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:jira-events/require('jira/util/events/types')"),a("jira.webresources:jira-global/require('jira/ajs/dark-features')"),a("jira.webresources:jira-metadata/require('jira/util/data/meta')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a,i,a("jira.webresources:jquery/require('jquery')")],t=function(e,i,s,t,r,n,l){function o(e){return e.find("body").hasClass("page-type-dashboard")}function d(e){return e.find("body").hasClass("ghx-agile")}function u(e){return e.find("body").hasClass("ka ajax-issue-search-and-view")}function m(){return s.get("is-servicedesk-rendered")}function c(e,a,s){n.devStatusModule.initializePanel({container:e,showTooltip:n.showTooltip,headerSelector:a,devStatusPanelVisibilityToggler:s,phaseTwoDisabled:!i.isEnabled("jira.plugin.devstatus.phasetwo.enabled")})}function p(e,i,a,s){t.defer(function(){var t=a?1e3:0,r={duration:t,complete:s};i?e.slideDown(r):e.slideUp(r)})}var y=a("./js/analytics/devstatus-analytics.js"),j=a("jira.webresources:jira-events/require('jira/util/events')");n.showTooltip=!0,n.showAITTooltip=!0;var v={devStatusContainer:"#devstatus-container",parentContainer:"[id$='devstatus-panel']",link:".devstatus-cta-link",statusPanel:".status-panels",viewIssueHeader:".mod-header",agileHeader:".ghx-header",agileNavMenuLink:"a[href=\"#ghx-tab-com-atlassian-jira-plugins-jira-development-integration-plugin-greenhopper-devstatus-panel\"]"};n.documentReadyInit=function(i){var s=a("./js/viewissue/DevStatusModule.js");n.devStatusModule=new s({parentContainerSelector:v.parentContainer,linkSelector:v.link,panelSelector:v.devStatusContainer,statusPanelSelector:v.statusPanel,shouldShowAITTooltip:function(){return n.showAITTooltip}}),o(i)||d(i)?(i.find("body").on("click",v.agileNavMenuLink,y.Analytics.fireAgileDevStatusLinkClicked),n.showTooltip=!1,n.showAITTooltip=!1):u(i)?l(function(){if(JIRA.Issues.Api.isFullScreenIssueVisible()){var e=l(v.devStatusContainer);0<e.length&&c(e,v.viewIssueHeader,p)}}):l(v.parentContainer).length&&(c(i.find(v.devStatusContainer),v.viewIssueHeader,p),n.showTooltip=!1),m()&&(n.showTooltip=!1,n.showAITTooltip=!1),j.bind(e.NEW_CONTENT_ADDED,function(i,e){return!!e.not(document).is(v.parentContainer)&&(c(e.find(v.devStatusContainer),v.viewIssueHeader,p),n.showTooltip=!1,!0)}),j.bind("GH.DetailView.updated",function(){c(l(v.devStatusContainer),v.agileHeader)})}}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/DevStatusData.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:wrm-context-path/require('wrm/context-path')"),a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:jquery/require('jquery')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')")],t=function(e,i,a,s){return i.Model.extend({properties:["aggregatedData"],namedEvents:["beforeRequest","requestFail","requestSuccess"],initialize:function(e){this.issueId=e.issueId,this.issueKey=e.issueKey},retrieveAggregateData:function(){var i=e()+"/rest/dev-status/1.0/issue/summary?issueId="+this.issueId;return this.trigger("beforeRequest"),a.ajax(i).done(s.bind(this.setAggregateData,this)).fail(s.bind(this.setAggregateDataError,this)).promise()},setAggregateData:function(e,i,a){this.set("aggregatedData",e),this.trigger("requestSuccess",this,!0===i,!!a)},setAggregateDataError:function(e){this.set("aggregatedData",void 0),this.trigger("requestFail",this,e)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/DevStatusModule.js":function(e,i,a){"use strict";var s,t;a(25),s=[a("jira.webresources:jira-formatter/require('jira/util/formatter')"),a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:jquery/require('jquery')"),a,a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("jira.webresources:jira-metadata/require('jira/util/data/meta')"),a("./js/util/analytics.js")],t=function(e,i,s,t,r,n,l){var o=a("./js/util/DevStatusURLUtils.js"),d=a("./js/analytics/devstatus-analytics.js"),u=a("com.atlassian.plugins.helptips.jira-help-tips:help-tip/require('jira-help-tips/feature/help-tip')"),m=a("com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager/require('jira-help-tips/feature/help-tip-manager')"),c=a("./js/analytics/AnalyticsModel.js"),p=a("./js/util/Listeners.js"),y=a("./js/viewissue/summary/SummaryErrorModule.js"),j=a("./js/viewissue/DevStatusData.js"),v=a("./js/viewissue/labs/LabsOptInModel.js"),g=a("./js/viewissue/labs/LabsOptInView.js"),w=a("./js/viewissue/labs/TryLabsView.js"),h=a("./js/viewissue/summary/branch/BranchModule.js"),b=a("./js/viewissue/summary/build/BuildModule.js"),D=a("./js/viewissue/summary/commit/CommitModule.js"),f=a("./js/viewissue/summary/deployment/DeploymentModule.js"),S=a("./js/viewissue/summary/pullrequest/PullRequestModule.js"),C=a("./js/viewissue/summary/review/ReviewModule.js"),k=a("./js/viewissue/summary/CreateBranchView.js"),_=a("./js/viewissue/dialog/CreateBranchFormDialog.js"),A=i.Model.extend({initialize:function(e){r.defaults(this,e),this._listeners=new p,this.createBranchFormDialog=new _(e),this.devSummaryJson=null,this.triggerHelpTipShown=!1,this.aitHelptip=null,this._initFocusHandler()},initializePanel:function(e){return this._listeners.stopListening(),this._renderedData=null,this._getContainers(e),this.labsOptIn=v.createFromDOM(this.parentContainer),e.showTooltip=e.showTooltip&&(!this.labsOptIn.isAllowed()||this.labsOptIn.isOptedIn()||this.labsOptIn.isDismissed()),this._createCreateBranchView(e),this.devSummaryJson=this.devStatusContainer.find(".dev-summary.json-blob").data("json"),this._initLabsOptInView(),this._initTryLabsView(),this._createDevStatusData(e),this._createAnalyticsModel(e),this._createSummaryViews(e,A.moduleMap),this._setupDataEventHandlers(),this.devStatusPanelVisibilityToggler=e.devStatusPanelVisibilityToggler,e.phaseTwoDisabled?this._postRender(!1,!0):this._startDevStatus(),this.labsOptIn.on("change:optedIn",this._onChangeLabsOptIn,this),this.labsOptIn.isAllowed()&&this.labsOptIn.isOptedIn()&&this._onChangeLabsOptIn(this.labsOptIn,!0),this},_createCreateBranchView:function(e){this.createBranchView=new k({el:this.createBranchContainer,showTooltip:e.showTooltip})},_createDevStatusData:function(){this.devStatusData=new j({issueId:this.devStatusContainer.data("issue-id"),issueKey:this.devStatusContainer.data("issue-key")})},_createSummaryViews:function(e,i){var a=this,t=this.devStatusContainer.data();this._summaryModules=r.clone(this.statusPanelContainer.find(".status-panel").map(function(){var e=s(this),r=i[e.data("module")];return new r({dataAttrs:t,el:e,labsOptIn:a.labsOptIn,analyticsModel:a.analyticsModel})})),this._errorModule=new y({el:this.messagePanelContainer,dataAttrs:t})},_createAnalyticsModel:function(){this.analyticsModel=new c({devStatusContainer:this.devStatusContainer,devStatusData:this.devStatusData})},_getContainers:function(e){this.devStatusContainer=e.container,this.parentContainer=e.container.closest(this.parentContainerSelector),this.createBranchContainer=e.container.find(this.linkSelector),this.statusPanelContainer=e.container.find(this.statusPanelSelector),this.headerContainer=this.parentContainer.find(e.headerSelector),this.messagePanelContainer=e.container.find(".message-panel")},_onLoadingStarted:function(){this._startLoadingModuleData()},_onLoadingSuccess:function(e,i,a){var s=this.devStatusData.get("aggregatedData");this._renderModules({success:!0,renderData:s}),this._postRender(!i,a,s)},_onLoadingFailure:function(){this._renderModules({success:!1}),this._postRender(!1,!0)},_setupDataEventHandlers:function(){this._listeners.startListening(this.devStatusData,"beforeRequest",this._onLoadingStarted,this),this._listeners.startListening(this.devStatusData,"requestSuccess",this._onLoadingSuccess,this),this._listeners.startListening(this.devStatusData,"requestFail",this._onLoadingFailure,this)},_startDevStatus:function(){var e=!this.devSummaryJson||!0===this.devSummaryJson.isStale;this.devSummaryJson&&this.devStatusData.setAggregateData(this.devSummaryJson.cachedValue,!0,!e),e&&(this._startLoadingModuleData(),this.devStatusData.retrieveAggregateData())},_initFocusHandler:function(){s(window).on("focus",r.throttle(r.bind(this.refreshSummaryData,this),3e4,{trailing:!1}))},refreshSummaryData:function(){this.devStatusData&&this.devStatusData.retrieveAggregateData()},_onChangeLabsOptIn:function(e,i){},_initLabsOptInView:function(){this.labsOptInView=new g({el:this.parentContainer.find(".labs-on-off-container"),labsOptIn:this.labsOptIn}).render()},_initTryLabsView:function(){var e=s("<div class=\"try-labs-container\"></div>").appendTo(this.headerContainer);new w({el:e,labsOptIn:this.labsOptIn}).render()},_postRender:function(e,i,a){function s(){i&&(l.addClass("js-animation-completed"),r._attachAutomaticTransitionsHelpTip(a))}var t=this._isAnySummaryViewVisibleOnPanel()||this._isAnyErrorViewVisible(),r=this;if(t){var n=this.analyticsModel.getIssue();d.SummaryAnalytics.fireSummaryShownEvent(n.isAssignee,n.issueType,n.issueStatus,this.analyticsModel.getSummary()),this._openDetailDialogUrlLink()}this._togglePanelEmptyStatus(!this._isAnySummaryViewVisibleOnPanel());var l=this.parentContainer,o=t||0<this.createBranchContainer.size();this.devStatusPanelVisibilityToggler&&l.is(":visible")!==o?(this.devStatusPanelVisibilityToggler(this.parentContainer,o,e,s),!o&&this.aitHelptip&&this.aitHelptip.isVisible()&&this.aitHelptip.hide()):s()},_attachAutomaticTransitionsHelpTip:function(i){var a=this.parentContainer.is(":visible"),t=this._shouldRenderAITTooltip(i);!t&&this.aitHelptip&&this.aitHelptip.isVisible()?this.aitHelptip.hide():t&&!this.triggerHelpTipShown&&this.shouldShowAITTooltip()&&a&&(r.defer(r.bind(function(){this.aitHelptip=new u({id:"automaticTransitionDevSummaryTooltip",title:"\u81ea\u52a8\u5316\u60a8\u7684\u72b6\u6001\u66f4\u65b0\u3002",bodyHtml:JIRA.Templates.DevStatus.Tooltip.automaticTransitions({aitHelpLinkUrl:this.devStatusContainer.data("ait-help-url-json")}),anchor:"#viewissue-devstatus-panel_heading .toggle-title",isSequence:!0,callbacks:{init:function(){s("#trigger-docs-link").on("click",function(){l.sendEvent("devstatus.automatic.transitions.learn.more.clicked")})}},inlineDialogOpts:{width:400}}),m.showSequences()},this)),this.triggerHelpTipShown=!0)},_shouldRenderAITTooltip:function(e){return!!e&&(0<e.summary.branch.overall.count||0<e.summary.repository.overall.count||0<e.summary.pullrequest.overall.count||0<e.summary.review.overall.count)},_getModuleByType:function(e){return r.find(this._summaryModules,function(i){return i.data&&i.data.getType&&r.isEqual(i.data.getType(),e)})},_openDetailDialogUrlLink:function(){var e=n.get("fusion-open-detail-dialog");if(e){var i;if(o.isCreateReviewDetailDialogLink(e)?i=this._getModuleByType("repository"):(i=this._getModuleByType(e),i&&n.set("fusion-open-detail-dialog",void 0)),i){var a=i.view;a&&a.isVisible()&&(n.set("fusion-analytics-new-context-link",!0),a.getSummaryLink().click())}}},_togglePanelEmptyStatus:function(e){this.statusPanelContainer&&this.statusPanelContainer.toggleClass("empty-status",e)},_isAnySummaryViewVisibleOnPanel:function(){return!!r.find(this._summaryModules,function(e){return e.isViewVisible()})},_isAnyErrorViewVisible:function(){return this._errorModule.isViewVisible()},_renderModules:function(e){var i=this._renderedData=e.renderData||this._renderedData;r.each(this._summaryModules,function(e){e.render(i)}),this._errorModule.render(e.success?i:void 0)},_startLoadingModuleData:function(){r.each(this._summaryModules,function(e){e.startLoading()})}},{moduleMap:{BranchModule:h,BuildModule:b,CommitModule:D,DeploymentModule:f,PullRequestModule:S,ReviewModule:C}});return A}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/dialog/CreateBranchFormDialog.js":function(e,i,a){"use strict";var s,t;a(5),s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:jquery/require('jquery')"),a("jira.webresources:dialogs/require('jira/dialog/form-dialog')"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s,t){return e.View.extend({initialize:function(e){this.parentContainerSelector=e.parentContainerSelector,this.panelSelector=e.panelSelector,this.linkSelector=e.linkSelector,this.dialogId="devstatus-cta-dialog",t.CtaDialogAnalytics.initialize(this.parentContainerSelector,this.linkSelector,this.dialogId),this._initFormDialog()},_initFormDialog:function(){var e=this,r=new s({id:this.dialogId,width:560,content:function(s){var n=this;a.e(9).then(function(){var l=[a("./js/viewissue/dialog/InstancePickerView.js")];(function(a){n.pickerView=new a({el:n.$popup,activeTrigger:n.$activeTrigger,cta:e._getCta(n.$activeTrigger)}),n.pickerView.render().always(function(e){s(e),i(r.$popup).find(".target").eq(0).focus(),t.CtaDialogAnalytics.fireCreateBranchEvent("dialog.open")})}).apply(null,l)}).catch(a.oe)},trigger:this.linkSelector,autoClose:!0})},_getCta:function(e){var i=e.data("cta");if(i)return i;var a=e.attr("href"),s=a&&/\#([a-zA-Z\.]+)/.exec(a);return s&&s[1]}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/labs/LabsOptInModel.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:wrm-context-path/require('wrm/context-path')"),a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:jquery/require('jquery')"),a],t=function(e,i,s){var t=a("./js/analytics/devstatus-analytics.js"),r=i.Model.extend({defaults:{allowed:!1,optedIn:!1,optedInByAdmin:!1,dismissed:!1,id:1},isAllowed:function(){return this.get("allowed")},isOptedIn:function(){return this.get("optedIn")},isOptedInByAdmin:function(){return this.get("optedInByAdmin")},setOptedIn:function(e){return this.save({optedIn:e},{wait:!0}).done(function(){t.LabsAnalytics.fireLabsEvent(e)})},toggleOptedIn:function(){var e=this.isOptedIn();return this.setOptedIn(!e)},setDismissed:function(e){return this.save({dismissed:e},{wait:!0})},isDismissed:function(){return this.get("dismissed")},url:function(){return e()+"/rest/dev-status/1.0/labs-opt-in/"}},{createFromDOM:function(e){var i=s(e).find("#devstatus-container");return new r(i.data("labs-json"))}});return r}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/labs/LabsOptInView.js":function(e,i,a){"use strict";var s,t;a(2),s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')")],t=function(e){return e.View.extend({events:{"click .toggle-labs":"_onClickToggle","click .labs-close":"_onClickDismiss"},initialize:function(e){this.labsOptIn=e.labsOptIn,this.labsOptIn.on("change",this.render,this),this.labsOptIn.on("change:optedIn",this._onChangeOptedIn,this)},render:function(){var e=this.labsOptIn;return e.isAllowed()&&(e.isOptedInByAdmin()||!e.isDismissed())?this._show():this._hide(),this},_onClickToggle:function(i){i.preventDefault(),this.labsOptIn.toggleOptedIn()},_onClickDismiss:function(i){i.preventDefault(),this.labsOptIn.setDismissed(!0)},_show:function(){this.$el.html(JIRA.Templates.DevStatus.labsOnOff({isOptedIn:this.labsOptIn.isOptedIn(),isOptedInByAdmin:this.labsOptIn.isOptedInByAdmin()})),this.$el.removeClass("hidden")},_hide:function(){this.$el.empty(),this.$el.addClass("hidden")}},{COLLECTOR_ID:"effe8b72"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/labs/TryLabsView.js":function(e,i,a){"use strict";var s,t;a(2),s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')")],t=function(e){return e.View.extend({events:{"click .try-labs":"_onClickTryLabs"},initialize:function(e){this.labsOptIn=e.labsOptIn,this.labsOptIn.on("change:dismissed",this.render,this)},render:function(){return this.labsOptIn.isAllowed()&&!this.labsOptIn.isOptedIn()&&this.labsOptIn.isDismissed()?this.$el.html(JIRA.Templates.DevStatus.tryLabs()):this.$el.empty(),this},_onClickTryLabs:function(i){i.preventDefault(),this.labsOptIn.setDismissed(!1)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/BasePanelModule.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')")],t=function(e,i){return e.Model.extend({initialize:function(e){this.options=i.defaults({},e),this.data=this.createModel()},render:function(e){this.startViews(),this.data.updateData(e),this.view.render()},startLoading:function(){this.data.startLoadingData&&this.data.startLoadingData()},startViews:function(){this.view||(this.view=this.createView())},stopViews:function(){this.view&&(this.view.undelegateEvents(),this.view.$el.empty(),this.view.hide(),this.view=null)},isViewVisible:function(){return this.view&&this.view.isVisible()},createView:function(){},createModel:function(){}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/BaseSummaryModel.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')")],t=function(e){return e.Model.extend({typeId:void 0,properties:["byInstanceType","overall","previousOverall","hasData","dataLoading"],startLoadingData:function(){this.set("dataLoading",!0)},updateData:function(e){if(this.set("hasData",void 0!==e),this.set("dataLoading",!1),this.set("previousOverall",this.getOverall()),e&&e.summary&&e.summary[this.typeId]){var i=e.summary[this.typeId];this.set("byInstanceType",i.byInstanceType),this.set("overall",i.overall)}else this.set("byInstanceType",{}),this.set("overall",{count:0})},getType:function(){return this.typeId},getOverall:function(){return this.get("overall")},getPreviousOverall:function(){return this.get("previousOverall")}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/BaseSummaryModule.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BasePanelModule.js")],t=function(e,i){return i.extend({model:void 0,viewType:void 0,createModel:function(){return new this.model({})},createView:function(){return new this.viewType(e.extend(e.clone(this.options),{model:this.data}))}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/BaseSummaryView.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:jira-logger/require('jira/util/logger')"),a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:jquery/require('jquery')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a],t=function(i,e,s,t){var r=a("./js/util/DateUtils.js"),n=a("./js/util/DevStatusURLUtils.js"),l=a("./js/viewissue/summary/SummaryTransitionView.js");return e.View.extend({template:void 0,events:{"simpleClick .summary":"_onSummaryLinkClick"},initialize:function(e){this.options=t.clone(e),this.model.on("change:hasData",this._addOrRemoveHasDataClass,this),this.model.on("change:dataLoading",this._addOrRemoveDataLoadingClass,this),this.transitionView=new l({model:this.model,el:this.$el})},render:function(){var e=this.isVisible();if(e){var i=s(this.template(t.extend({issueKey:this.options.dataAttrs.issueKey},this.model.getOverall())));this._shouldAnimateDataTransition()?this.transitionView.renderVisible(i):(this.$el.html(i),r.addTooltip(i)),this._renderNavigableSummaryLink(),this.show()}else this._isHidden()||(this._shouldAnimateDataTransition()?this.transitionView.renderHidden(t.bind(this.hide,this)):this.hide());this._addOrRemoveDataLoadingClass(),this._addOrRemoveHasDataClass()},getDefaultDetailDialogParameters:function(){return{issueKey:this.options.dataAttrs.issueKey,issueId:this.options.dataAttrs.issueId,entityLinks:this.options.dataAttrs.entityLinks,tabs:this.model.get("byInstanceType"),dataType:this.model.getType(),showContactAdminForm:this.options.dataAttrs&&this.options.dataAttrs.showContactAdminForm}},getSummaryLink:function(){return this.$el.find("a.summary")},isVisible:function(){return 0<this.model.getOverall().count},_addOrRemoveHasDataClass:function(){var e=this.model.get("hasData");this.$el.toggleClass("js-has-data",e)},_renderNavigableSummaryLink:function(){this.getSummaryLink().attr("href",n.getUrlWithDetailDialogParam(this.model.getType()))},_addOrRemoveDataLoadingClass:function(){var e=this.model.get("dataLoading");this.$el.toggleClass("data-loading",e)},_onSummaryLinkClick:function(a){a.preventDefault(),i.log("Summary view not supported: ",this)},_shouldAnimateDataTransition:function(){return!!this.model.getPreviousOverall()},_isHidden:function(){return this.$el.hasClass("hidden")},show:function(){this.$el.removeClass("hidden")},hide:function(){this.$el.addClass("hidden")}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/CreateBranchView.js":function(e,i,a){"use strict";var s,t;a(25),s=[a("jira.webresources:jira-formatter/require('jira/util/formatter')"),a("com.atlassian.plugins.helptips.jira-help-tips:help-tip/require('jira-help-tips/feature/help-tip')"),a("com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager/require('jira-help-tips/feature/help-tip-manager')"),a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("jira.webresources:jira-metadata/require('jira/util/data/meta')"),a],t=function(e,i,s,t,r,n){var l=a("./js/util/DevStatusURLUtils.js");return t.View.extend({initialize:function(e){this.tooltipShown=!e.showTooltip,this.linkTextSelector=".devstatus-cta-link-text",this.issueContainer=".issue-container",this._processLinkForNavigableURL(),this._initTooltip(),r.defer(r.bind(this._openCreateBranchDialog,this))},getCreateBranchLink:function(){return this.$el},_processLinkForNavigableURL:function(){var e=this._getCta(this.$el);e&&this.getCreateBranchLink().data("cta",e),this.getCreateBranchLink().attr("href",l.getUrlWithDetailDialogParam("create-branch"))},_openCreateBranchDialog:function(){var e=n.get("fusion-open-detail-dialog");"create-branch"===e&&(n.set("fusion-open-detail-dialog",void 0),n.set("fusion-analytics-new-context-link",!0),this.getCreateBranchLink().click())},_initTooltip:function(){!this.tooltipShown&&0<this.$el.length&&r.defer(r.bind(function(){var a=this.$el.data("cta");a&&(new i({id:a+".tooltip",title:"\u5f00\u59cb\u5f00\u53d1",bodyHtml:JIRA.Templates.DevStatus.Tooltip.createBranch(),anchor:this.$el.find(this.linkTextSelector),isSequence:!0,inlineDialogOpts:{container:this.issueContainer,width:400}}),this.tooltipShown=!0,s.showSequences())},this))},_getCta:function(e){var i=e.attr("href"),a=i&&/\#([a-zA-Z\.]+)/.exec(i);return a&&a[1]}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/SummaryErrorModel.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')")],t=function(e,i){return e.Model.extend({properties:["hasErrors","errorInstances","configInstances"],defaults:function(){return{hasErrors:!1,errorInstances:[],configInstances:[]}},updateData:function(e){var a=this._extractErrors(e&&e.errors),s=this._extractErrors(e&&e.configErrors);this.set("hasErrors",!e||!i.isEmpty(a)||!i.isEmpty(s)),this.set("errorInstances",a),this.set("configInstances",s)},_extractErrors:function(e){return e?i.map(i.filter(e,function(e){return e.error}),function(e){return e.instance}):[]}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/SummaryErrorModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BasePanelModule.js"),a],t=function(e){var i=a("./js/viewissue/summary/SummaryErrorModel.js"),s=a("./js/viewissue/summary/SummaryErrorView.js");return e.extend({createModel:function(){return new i({})},createView:function(){var e=this.options.dataAttrs&&this.options.dataAttrs.showContactAdminForm;return new s({model:this.data,el:this.options.el,showContactAdminForm:e})}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/SummaryErrorView.js":function(e,i,a){"use strict";var s,t;a(2),s=[a("./js/viewissue/summary/BaseSummaryView.js")],t=function(e){return e.extend({initialize:function(e){this.showContactAdminForm=e.showContactAdminForm},render:function(){return this.isVisible()?(this.$el.html(JIRA.Templates.DevStatus.connectionProblemAsInfoWithoutIcon({instances:this.model.get("errorInstances"),notConfiguredInstances:this.model.get("configInstances"),showContactAdminForm:this.showContactAdminForm})),this.show()):this.hide(),this},isVisible:function(){return this.model.get("hasErrors")}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/SummaryTransitionView.js":function(e,i,a){"use strict";var s,t;s=[a("jira.webresources:ajs-backbone-amd-shim/require('backbone')"),a("jira.webresources:jquery/require('jquery')"),a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/util/DateUtils.js")],t=function(e,i,a,s){return e.View.extend({slideTransitionTime:500,initialize:function(){},renderVisible:function(e){var t=!i.contains(document,this.$el[0]);this._hasPreviousData()?this._hasDataChanged()&&(this._prepareContentsForTransition(e,this.$el),this.$el.addClass("roll-transition")):(this._preStartSlideTransition(),this.$el.addClass("slide-down-transition"),t=!0),this.$el.html(e),s.addTooltip(e),t?a.defer(a.bind(this._initiateTransition,this)):this._initiateTransition()},renderHidden:function(e){var i=this;this.$el.slideUp(this.slideTransitionTime,function(){i.$el.css("display",""),e()})},_initiateTransition:function(){if(this.$el.hasClass("slide-down-transition"))this._postStartSlideTransition(),this.$el.removeClass("slide-down-transition"),this._startSlideDownTransition(this.$el,0);else{this.$el.hasClass("roll-transition")&&(this.$el.removeClass("roll-transition"),this._startRollTransition());var e=this.$el.find(".sliding-container");if(0<e.length){var a=this;e.each(function(e,s){var t=i(s),r=t.data("prevHeight");a._startSlideDownTransition(t,r)})}}},_startSlideDownTransition:function(e,i){var a=e.height();i!==a&&e.height(i).animate({height:a},this.slideTransitionTime,function(){e.css("height","auto"),e.find(".rolling-content").removeClass("transit")})},_startRollTransition:function(){this.$el.find(".rolling-container").each(a.bind(function(e,a){var s=i(a),t=s.find(".rolling-content"),r=t.find(":not(.old-content)"),n=r.height();s.height(n),t.addClass("transit"),r.css("margin-top","-"+n+"px").animate({"margin-top":"0"},this.slideTransitionTime,function(){s.css("height","auto"),s.find(".old-content").remove(),t.removeClass("transit"),r.css("margin-top","")})},this))},_prepareContentsForTransition:function(e,a){e=i("<div></div>").append(e);var s=e.find(".sliding-container"),t=a.find(".sliding-container");s.each(function(e,a){var s=t.get(e);if(s){var r=i(s);i(a).data("prevHeight",r.height())}});var r=e.find(".rolling-container"),n=a.find(".rolling-container");r.each(function(e,a){var s=i(a).find(".rolling-content"),t=n.get(e);if(t){var r=i(t).find(".rolling-content").children();r.addClass("old-content"),s.append(r)}})},_hasPreviousData:function(){var e=this.model.getPreviousOverall();return e&&0<e.count},_hasDataChanged:function(){return!a.isEqual(this.model.getOverall(),this.model.getPreviousOverall())},_preStartSlideTransition:function(){this.$el.css("height","0"),this.$el.css("opacity","0")},_postStartSlideTransition:function(){this.$el.css("height",""),this.$el.css("opacity","")}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/branch/BranchModel.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModel.js")],t=function(e){return e.extend({typeId:"branch"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/branch/BranchModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModule.js"),a("./js/viewissue/summary/branch/BranchModel.js"),a("./js/viewissue/summary/branch/BranchView.js")],t=function(e,i,a){return e.extend({model:i,viewType:a})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/branch/BranchView.js":function(e,i,a){"use strict";var s,t;a(2),a(16),a(26),a(5),s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BaseSummaryView.js"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s){return i.extend({template:JIRA.Templates.DevStatus.Branches.summaryPanel,_onSummaryLinkClick:function(i){i.preventDefault();var t=this;a.e(14).then(function(){var i=[a("./js/viewissue/dialog/DetailDialogBranchView.js")];(function(i){t.detailDialogBranchView=new i(e.extend(t.getDefaultDetailDialogParameters(),{id:"devstatus-branch-detail-dialog",count:t.model.getOverall().count,analyticIssueData:t.options.analyticsModel.getIssue()})),s.BranchesAnalytics.fireSummaryClickedEvent(t.options.analyticsModel.getSummary()),t.detailDialogBranchView.show()}).apply(null,i)}).catch(a.oe)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/build/BuildModel.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModel.js")],t=function(e){return e.extend({typeId:"build"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/build/BuildModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModule.js"),a("./js/viewissue/summary/build/BuildModel.js"),a("./js/viewissue/summary/build/BuildView.js")],t=function(e,i,a){return e.extend({model:i,viewType:a})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/build/BuildView.js":function(e,i,a){"use strict";var s,t;a(2),a(16),a(27),a(5),s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BaseSummaryView.js"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s){return i.extend({template:JIRA.Templates.DevStatus.Build.summaryPanel,_onSummaryLinkClick:function(i){i.preventDefault();var t=this;a.e(13).then(function(){var i=[a("./js/viewissue/dialog/build/DetailDialogBuildView.js")];(function(i){t.detailDialogBuildView=new i(e.extend(t.getDefaultDetailDialogParameters(),{id:"devstatus-build-detail-dialog",count:t.model.getOverall().count})),s.BuildsAnalytics.fireSummaryClickedEvent(t.options.analyticsModel.getSummary()),t.detailDialogBuildView.show()}).apply(null,i)}).catch(a.oe)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/commit/CommitModel.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModel.js")],t=function(e){return e.extend({typeId:"repository"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/commit/CommitModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModule.js"),a("./js/viewissue/summary/commit/CommitModel.js"),a("./js/viewissue/summary/commit/CommitView.js")],t=function(e,i,a){return e.extend({model:i,viewType:a})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/commit/CommitView.js":function(e,i,a){"use strict";var s,t;a(2),a(16),a(28),a(5),s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BaseSummaryView.js"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s){return i.extend({template:JIRA.Templates.DevStatus.Commit.summaryPanel,_onSummaryLinkClick:function(i){i.preventDefault();var t=this;a.e(15).then(function(){var i=[a("./js/viewissue/dialog/DetailDialogCommitView.js")];(function(i){t.detailDialogCommitView=new i(e.extend(t.getDefaultDetailDialogParameters(),{id:"devstatus-commit-detail-dialog",count:t.model.getOverall().count})),s.CommitsAnalytics.fireSummaryClickedEvent(t.options.analyticsModel.getSummary()),t.detailDialogCommitView.show()}).apply(null,i)}).catch(a.oe)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/deployment/DeploymentModel.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModel.js")],t=function(e){return e.extend({typeId:"deployment-environment"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/deployment/DeploymentModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModule.js"),a("./js/viewissue/summary/deployment/DeploymentModel.js"),a("./js/viewissue/summary/deployment/DeploymentView.js")],t=function(e,i,a){return e.extend({model:i,viewType:a})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/deployment/DeploymentView.js":function(e,i,a){"use strict";var s,t;a(2),a(16),a(30),a(5),s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BaseSummaryView.js"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s){return i.extend({template:JIRA.Templates.DevStatus.Deployment.summaryPanel,_onSummaryLinkClick:function(i){i.preventDefault();var t=this;a.e(12).then(function(){var i=[a("./js/viewissue/dialog/deployment/DetailDialogDeploymentView.js")];(function(i){t.detailDialogDeploymentView=new i(e.extend(t.getDefaultDetailDialogParameters(),{id:"devstatus-deployment-detail-dialog",count:t.model.getOverall().count})),s.DeploymentsAnalytics.fireSummaryClickedEvent(t.options.analyticsModel.getSummary()),t.detailDialogDeploymentView.show()}).apply(null,i)}).catch(a.oe)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/pullrequest/PullRequestModel.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModel.js")],t=function(e){return e.extend({typeId:"pullrequest"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/pullrequest/PullRequestModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModule.js"),a("./js/viewissue/summary/pullrequest/PullRequestModel.js"),a("./js/viewissue/summary/pullrequest/PullRequestView.js")],t=function(e,i,a){return e.extend({model:i,viewType:a})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/pullrequest/PullRequestView.js":function(e,i,a){"use strict";var s,t;a(2),a(16),a(31),a(5),s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BaseSummaryView.js"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s){return i.extend({template:JIRA.Templates.DevStatus.PullRequest.summaryPanel,_onSummaryLinkClick:function(i){i.preventDefault();var t=this;a.e(11).then(function(){var i=[a("./js/viewissue/dialog/DetailDialogPullRequestView.js")];(function(i){t.detailDialogPullRequestView=new i(e.extend(t.getDefaultDetailDialogParameters(),{id:"devstatus-pullrequest-detail-dialog",count:t.model.getOverall().count,reviewersThreshold:2})),s.PullRequestsAnalytics.fireSummaryClickedEvent(t.options.analyticsModel.getSummary()),t.detailDialogPullRequestView.show()}).apply(null,i)}).catch(a.oe)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/review/ReviewModel.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModel.js")],t=function(e){return e.extend({typeId:"review"})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/review/ReviewModule.js":function(e,i,a){"use strict";var s,t;s=[a("./js/viewissue/summary/BaseSummaryModule.js"),a("./js/viewissue/summary/review/ReviewModel.js"),a("./js/viewissue/summary/review/ReviewView.js")],t=function(e,i,a){return e.extend({model:i,viewType:a})}.apply(i,s),!(void 0!==t&&(e.exports=t))},"./js/viewissue/summary/review/ReviewView.js":function(e,i,a){"use strict";var s,t;a(2),a(16),a(21),a(5),s=[a("jira.webresources:ajs-underscorejs-amd-shim/require('underscore')"),a("./js/viewissue/summary/BaseSummaryView.js"),a("./js/analytics/devstatus-analytics.js")],t=function(e,i,s){return i.extend({template:JIRA.Templates.DevStatus.Review.summaryPanel,_onSummaryLinkClick:function(i){i.preventDefault();var t=this;a.e(10).then(function(){var i=[a("./js/viewissue/dialog/DetailDialogReviewView.js")];(function(i){t.detailDialogReviewView=new i(e.extend(t.getDefaultDetailDialogParameters(),{id:"devstatus-review-detail-dialog",count:t.model.getOverall().count,reviewersThreshold:2,completed:t.model.getOverall().completed})),s.ReviewsAnalytics.fireSummaryClickedEvent(t.options.analyticsModel.getSummary()),t.detailDialogReviewView.show()}).apply(null,i)}).catch(a.oe)}})}.apply(i,s),!(void 0!==t&&(e.exports=t))},16:function(e){e.exports=void 0},2:function(e){e.exports=void 0},21:function(e){e.exports=void 0},25:function(e){e.exports=void 0},26:function(e){e.exports=void 0},27:function(e){e.exports=void 0},28:function(e){e.exports=void 0},30:function(e){e.exports=void 0},31:function(e){e.exports=void 0},5:function(e){e.exports=void 0},"com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager/require('jira-help-tips/feature/help-tip-manager')":function(e){e.exports=require("jira-help-tips/feature/help-tip-manager")},"com.atlassian.plugins.helptips.jira-help-tips:help-tip/require('jira-help-tips/feature/help-tip')":function(e){e.exports=require("jira-help-tips/feature/help-tip")},"jira.webresources:ajs-backbone-amd-shim/require('backbone')":function(e){e.exports=require("backbone")},"jira.webresources:dialogs/require('jira/dialog/form-dialog')":function(e){e.exports=require("jira/dialog/form-dialog")},"jira.webresources:jira-base-control/require('jira/lib/class')":function(e){e.exports=require("jira/lib/class")},"jira.webresources:jira-events/require('jira/util/events')":function(e){e.exports=require("jira/util/events")},"jira.webresources:jira-events/require('jira/util/events/types')":function(e){e.exports=require("jira/util/events/types")},"jira.webresources:jira-formatter/require('jira/util/formatter')":function(e){e.exports=require("jira/util/formatter")},"jira.webresources:jira-global/require('jira/ajs/dark-features')":function(e){e.exports=require("jira/ajs/dark-features")},"jira.webresources:jira-logger/require('jira/util/logger')":function(e){e.exports=require("jira/util/logger")},"jira.webresources:momentjs/require('jira/moment')":function(e){e.exports=require("jira/moment")}});
//# sourceMappingURL=0.075180f0a8e207fec1f8.js.map
}catch(e){WRMCB(e)};